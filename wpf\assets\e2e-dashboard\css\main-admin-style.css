/* Custom Styles for E2E Dashboard Plugin */

.wrap h1 {
	color: #0073aa;
}

#run-github-action {
	background-color: #28a745;
	color: #ffffff;
	padding: 10px 20px;
	border: none;
	cursor: pointer;
	margin-top: 10px;
}

#cancel-all {
	background-color: #ffc800;
	color: #ffffff;
	padding: 4px 20px;
	border: none;
	cursor: pointer;
	margin-top: 10px;
}

#run-github-action:hover {
	background-color: #218838;
}

#jstree_search_input {
	margin-bottom: 10px;
	padding: 5px;
	width: 30%;
}

#result {
	margin-top: 20px;
	padding: 10px;
	border: 1px solid #e0e0e0;
	background-color: #f9f9f9;
}

/* Accordion Styles */

/* Use the WordPress admin font stack */
.cbp-ntaccordion,
.cbp-ntaccordion * {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
	Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
	box-sizing: border-box;
}

/* Accordion Container */
#cbp-ntaccordion,
.cbp-ntsubaccordion {
	list-style: none;
	margin: 0;
	padding: 0;
}

/* Accordion Items */
#cbp-ntaccordion li,
.cbp-ntsubaccordion li {
	margin: 0;
	padding: 0;
}

/* Triggers */
.cbp-nttrigger {
	cursor: pointer;
	display: block;
	padding: 8px 12px;
	background: #f1f1f1;
	color: #23282d;
	border-bottom: 1px solid #ccc;
	font-size: 20px;
	font-weight: 600;
}

.cbp-nttrigger:hover {
	background: #e1e1e1;
}

/* Dashicons for Accordion Indicators */
.cbp-nttrigger:before {
	content: "\f132"; /* Dashicon arrow-right */
	font-family: "Dashicons";
	speak: none;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	margin-right: 8px;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.cbp-ntopen > .cbp-nttrigger:before {
	content: "\f140"; /* Dashicon arrow-down */
}

/* Nested Triggers */
.cbp-ntsubaccordion .cbp-nttrigger {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	padding: 8px 12px;
	background: #f9f9f9;
	font-size: 14px;
	font-weight: 500;
}

.cbp-ntsubaccordion .cbp-nttrigger:hover {
	background: #ececec;
}

/* Adjustments for Nested Triggers */
.cbp-ntsubaccordion .cbp-nttrigger:before {
	content: "\f132"; /* Dashicon arrow-right */
}

.cbp-ntsubaccordion .cbp-ntopen > .cbp-nttrigger:before {
	content: "\f140"; /* Dashicon arrow-down */
}

/* Content Areas */
.cbp-ntcontent {
	display: none;
	padding: 10px 12px;
	background: #fff;
	border: 1px solid #ccc;
	overflow: hidden;
}

/* Open Content */
.cbp-ntopen > .cbp-ntcontent {
	display: block;
}

/* Transition for smooth opening/closing */
.cbp-ntcontent {
	transition: height 0.3s ease;
}

/* Job Status Styling */
.job-status {
	font-weight: bold;
}

.job-conclusion {
	font-weight: bold;
}

/* Completion Message */
.completion-message {
	font-weight: bold;
	color: #28a745;
	padding: 10px;
}

/* Cancel Button Styling */
.cancel-workflow-run {
	background-color: #ffba00;
	color: #ffffff;
	border: none;
	padding: 6px 12px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	border-radius: 3px;
	margin-left: auto;
	position: static;
	transform: none;
}

.cancel-workflow-run:hover {
	background-color: #e09600;
}

.cancel-workflow-run:disabled {
	background-color: #ccc;
	cursor: not-allowed;
}

.cbp-nttrigger {
	padding-right: 15px;
	position: relative;
}

.cbp-nttrigger .cancel-workflow-run {

}

/* Status Container Styling */
.status-container {
	background: #f8f9fa;
	padding: 15px;
	border-radius: 4px;
	margin-bottom: 10px;
	border-left: 4px solid #0073aa;
}

.status-container strong {
	color: #23282d;
	display: inline-block;
	width: 100px;
	margin-right: 10px;
}

/* Test Name Styling */
.cbp-nttrigger {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	gap: 10px;
	padding: 12px 15px;
}

.workflow-name {
	font-weight: 600;
}

.test-name {
	color: #666;
	margin-left: 10px;
}

/* Status Colors */
.status-container[data-status="completed"] {
	border-left-color: #28a745;
}

.status-container[data-status="in_progress"] {
	border-left-color: #ffc107;
}

.status-container[data-status="queued"] {
	border-left-color: #6c757d;
}

.status-container[data-status="failed"] {
	border-left-color: #dc3545;
}

/* Job Details Styling */
.job-details {
	padding: 15px;
	background: #f8f9fa;
	border-radius: 4px;
	border: 1px solid #e9ecef;
}

.job-status-row,
.job-conclusion-row {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.job-conclusion-row {
	margin-bottom: 0;
}

.status-label,
.conclusion-label {
	width: 100px;
	font-weight: 600;
	color: #495057;
}

/* Status Badge Styling */
.status-badge,
.conclusion-badge {
	display: inline-flex;
	align-items: center;
	padding: 4px 8px;
	border-radius: 12px;
	font-size: 13px;
	font-weight: 500;
	line-height: 1.2;
}

/* Status Colors */
.status-badge.status-completed {
	background-color: #e6f4ea;
	color: #1e7e34;
}

.status-badge.status-queued {
	background-color: #e9ecef;
	color: #495057;
}

.status-badge.status-in_progress {
	background-color: #fff3cd;
	color: #856404;
}

/* Conclusion Colors */
.conclusion-badge.conclusion-success {
	background-color: #e6f4ea;
	color: #1e7e34;
}

.conclusion-badge.conclusion-failure {
	background-color: #f8d7da;
	color: #721c24;
}

.conclusion-badge.conclusion-cancelled {
	background-color: #e9ecef;
	color: #495057;
}

.conclusion-badge.conclusion-in_progress {
	background-color: #fff3cd;
	color: #856404;
}

.conclusion-badge.conclusion-neutral {
	background-color: #e9ecef;
	color: #495057;
}

/* Animation for in-progress status */
@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.status-in_progress::before,
.conclusion-in_progress::before {
	display: inline-block;
	animation: rotate 1.5s linear infinite;
}

/* Hover effects */
.status-badge:hover,
.conclusion-badge:hover {
	filter: brightness(95%);
}

/* Content spacing */
.cbp-ntcontent p {
	margin: 0;
	line-height: 1.6;
}

/* Job name styling in accordion */
.cbp-ntsubaccordion .cbp-nttrigger {
	border-left: 4px solid transparent;
}

.cbp-ntsubaccordion .cbp-nttrigger:hover {
	border-left-color: #0073aa;
}

/* Job Steps Styling */
.job-steps {
	margin-top: 15px;
	border-top: 1px solid #e9ecef;
	padding-top: 15px;
}

.step-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px 0;
	border-bottom: 1px solid #f1f1f1;
}

.step-row:last-child {
	border-bottom: none;
}

.step-name {
	flex-grow: 1;
	font-size: 13px;
	color: #495057;
	padding-right: 15px;
}

.step-badge {
	display: inline-flex;
	align-items: center;
	padding: 4px 8px;
	border-radius: 12px;
	font-size: 12px;
	font-weight: 500;
	line-height: 1.2;
	min-width: 80px;
	justify-content: center;
}

/* Step Status Colors */
.step-badge.step-completed.conclusion-success {
	background-color: #e6f4ea;
	color: #1e7e34;
}

.step-badge.step-completed.conclusion-failure {
	background-color: #f8d7da;
	color: #721c24;
}

.step-badge.step-completed.conclusion-skipped {
	background-color: #e9ecef;
	color: #6c757d;
}

.step-badge.step-in_progress {
	background-color: #fff3cd;
	color: #856404;
}

.step-badge.step-queued {
	background-color: #e9ecef;
	color: #495057;
}

/* Animation for steps in progress */
.step-badge.step-in_progress::before {
	content: '↻';
	display: inline-block;
	margin-right: 4px;
	animation: rotate 1.5s linear infinite;
}

/* Hover effect for steps */
.step-row:hover {
	background-color: #f8f9fa;
}

/* Status icon spacing */
.step-badge::before {
	margin-right: 4px;
}

/* Adjust job details padding for steps */
.job-details {
	padding: 15px;
	background: #ffffff;
	border-radius: 4px;
	border: 1px solid #e9ecef;
}

/* Make the status badge more compact when steps are shown */
.job-status-row {
	margin-bottom: 0;
	padding-bottom: 10px;
}

/* Status Colors */
.status-badge {
	padding: 4px 8px;
	border-radius: 4px;
	font-weight: 500;
	margin-left: 8px;
}

.status-success, .step-badge.conclusion-success {
	background-color: #e6f4ea;
	color: #1e7e34;
}

.status-failure, .step-badge.conclusion-failure {
	background-color: #fde7e9;
	color: #dc3545;
}

.status-cancelled, .step-badge.conclusion-cancelled {
	background-color: #f8f9fa;
	color: #6c757d;
}

.status-in_progress, .step-badge.step-in_progress {
	background-color: #e8f4fd;
	color: #0d6efd;
}

.status-pending, .step-badge.step-pending, .step-badge.conclusion-in_progress {
	background-color: #fff3cd;
	color: #856404;
}

.step-badge.conclusion-skipped {
	background-color: #f8f9fa;
	color: #6c757d;
}

/* Job Steps */
.job-steps {
	margin-top: 10px;
}

.step-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 5px 0;
	border-bottom: 1px solid #f0f0f1;
}

.step-badge {
	padding: 3px 8px;
	border-radius: 3px;
	font-size: 12px;
	font-weight: 500;
}

/* Workflow Status */
.workflow-status {
	margin-left: auto;
	padding: 4px 8px;
	border-radius: 4px;
	font-weight: 500;
}

/* Status Icons */
.status-success ✓, .conclusion-success ✓ {
	color: #1e7e34;
}

.status-failure ✕, .conclusion-failure ✕ {
	color: #dc3545;
}

.status-cancelled ⊘, .conclusion-cancelled ⊘ {
	color: #6c757d;
}

.status-in_progress ↻, .step-in_progress ↻ {
	color: #0d6efd;
}

.status-pending ⋯, .conclusion-in_progress ⋯ {
	color: #856404;
}

/* Job Details */
.job-details {
	padding: 10px;
	background-color: #f8f9fa;
	border-radius: 4px;
}

.job-status-row {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.status-label {
	font-weight: 500;
	color: #495057;
}

/* Accordion Styling */
.cbp-ntaccordion {
	list-style: none;
	margin: 0;
	padding: 0;
}

.cbp-ntaccordion > li {
	margin: 0 0 0.5em;
	background: #fff;
	border: 1px solid #ddd;
	border-radius: 4px;
}

.cbp-ntsubaccordion {
	list-style: none;
	margin: 0;
	padding: 0;
}

.cbp-ntsubaccordion > li {
	margin: 0.5em;
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 4px;
}

.cbp-nttrigger {
	display: flex;
	align-items: center;
	padding: 15px;
	margin: 0;
	cursor: pointer;
	background: #fff;
}

.cbp-ntcontent {
	padding: 0 15px 15px;
}

/* Test Name */
.test-name {
	margin: 0 10px;
	color: #495057;
	font-weight: 500;
}

/* Workflow Name */
.workflow-name {
	color: #212529;
	font-weight: 500;
}

.download-artifact-btn {
	display: inline-flex;
	align-items: center;
	margin-left: 10px;
	padding: 6px 12px;
	background-color: #2271b1;
	color: #fff;
	border-radius: 3px;
	text-decoration: none;
	font-size: 13px;
	line-height: 20px;
	cursor: pointer;
	border: none;
	vertical-align: middle;
	transition: all 0.3s ease;
}

.download-artifact-btn .dashicons {
	width: 16px;
	height: 16px;
	font-size: 16px;
	margin-right: 4px;
	line-height: 1;
	display: inline-block;
	font-family: dashicons;
	text-decoration: inherit;
	font-weight: 400;
	font-style: normal;
	vertical-align: top;
	text-align: center;
	transition: color .1s ease-in;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.download-artifact-btn:hover {
	background-color: #135e96;
	color: #fff;
}

.download-artifact-btn:focus {
	box-shadow: 0 0 0 1px #fff, 0 0 0 3px #2271b1;
	color: #fff;
}

.download-artifact-btn:disabled {
	background-color: #ccc;
	cursor: not-allowed;
}

.download-artifact-btn.error {
	background-color: #d63638;
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.download-artifact-btn .dashicons.rotating {
	animation: rotate 1.5s linear infinite;
}

.cbp-nttrigger {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.cbp-nttrigger .title-section {
	display: flex;
	align-items: center;
	flex-grow: 1;
}

/* Artifact Download Status */
.artifact-status {
	margin-top: 10px;
	padding: 8px 12px;
	border-radius: 4px;
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 13px;
	line-height: 1.4;
}

.artifact-status.success {
	background-color: #edfaef;
	color: #1e7e34;
	border: 1px solid #c3e6cb;
}

.artifact-status.exists {
	background-color: #fff3cd;
	color: #856404;
	border: 1px solid #ffeeba;
}

.artifact-status .dashicons {
	font-size: 16px;
	width: 16px;
	height: 16px;
}

.artifact-status .filepath {
	background: rgba(0, 0, 0, 0.05);
	padding: 2px 6px;
	border-radius: 3px;
	font-family: monospace;
	margin-left: 4px;
}

.download-artifact-btn.downloaded {
	background-color: #6c757d;
}

.download-artifact-btn.downloaded:hover {
	background-color: #5a6268;
}

/* Artifact Screenshots */
.artifact-screenshots {
	margin-top: 15px;
	padding: 10px;
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 4px;
}

.artifact-screenshots h4 {
	margin: 0 0 10px;
	color: #23282d;
	font-size: 14px;
}

.view-screenshot-btn,
.view-report-btn {
	display: inline-flex;
	align-items: center;
	margin: 5px;
	padding: 8px 12px;
	background: #f8f9fa;
	border: 1px solid #dee2e6;
	border-radius: 4px;
	color: #495057;
	text-decoration: none;
	font-size: 13px;
	line-height: 1.4;
	transition: all 0.2s ease;
}

.view-screenshot-btn:hover,
.view-report-btn:hover {
	background: #e9ecef;
	color: #212529;
	border-color: #adb5bd;
}

.view-screenshot-btn .dashicons,
.view-report-btn .dashicons {
	margin-right: 5px;
	font-size: 16px;
	width: 16px;
	height: 16px;
	line-height: 1;
}

.view-report-btn {
	background: #e7f5ff;
	border-color: #a5d8ff;
	color: #1864ab;
}

.view-report-btn:hover {
	background: #d0ebff;
	border-color: #74c0fc;
	color: #1864ab;
}

.artifact-screenshots h4 {
	margin: 15px 0 10px;
	color: #495057;
	font-size: 14px;
}

.screenshot-buttons {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	margin-top: 10px;
}

.dashicons {
	display: inline-block;
	width: 20px;
	height: 20px;
	font-size: 20px;
	line-height: 1;
	font-family: dashicons !important;
	text-decoration: inherit;
	font-weight: 400;
	font-style: normal;
	vertical-align: top;
	text-align: center;
	transition: color .1s ease-in;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* Ensure proper vertical alignment for dashicons in buttons and status messages */
.artifact-status .dashicons,
.download-artifact-btn .dashicons,
.view-screenshot-btn .dashicons,
.view-report-btn .dashicons {
	vertical-align: text-bottom;
	line-height: 1.2;
}

/* Ensure proper sizing for dashicons in different contexts */
.artifact-status .dashicons {
	width: 18px;
	height: 18px;
	font-size: 18px;
}

.view-screenshot-btn .dashicons,
.view-report-btn .dashicons {
	width: 16px;
	height: 16px;
	font-size: 16px;
	margin-right: 5px;
}

.download-artifact-btn .dashicons {
	width: 16px;
	height: 16px;
	font-size: 16px;
	margin-right: 4px;
}

/* Ensure proper animation for rotating dashicon */
.dashicons.rotating {
	animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

/* Override any potential conflicts */
.dashicons:before {
	font-family: dashicons !important;
}

.import-sql-btn {
	background-color: #d63638 !important;
	color: #fff !important;
	border-color: #d63638 !important;
}

.import-sql-btn:hover {
	background-color: #b32d2e !important;
	border-color: #b32d2e !important;
}

.import-sql-btn:disabled {
	background-color: #e5e5e5 !important;
	border-color: #ddd !important;
	color: #a7aaad !important;
	cursor: not-allowed;
}

.import-sql-btn .dashicons-database-import {
	font-size: 16px;
	width: 16px;
	height: 16px;
	margin-right: 5px;
}

/* Ensure proper animation for the rotating icon during import */
.import-sql-btn .dashicons-update.rotating {
	animation: rotate 1.5s linear infinite;
}

/* Override any potential conflicts for the import button */
.import-sql-btn.view-report-btn {
	display: inline-flex;
	align-items: center;
	margin: 5px;
	padding: 8px 12px;
	text-decoration: none;
	font-size: 13px;
	line-height: 1.4;
	transition: all 0.2s ease;
	cursor: pointer;
}

.import-status {
	margin-top: 10px;
	padding: 8px 12px;
	border-radius: 4px;
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 13px;
	line-height: 1.4;
}

.import-status.success {
	background-color: #edfaef;
	color: #1e7e34;
	border: 1px solid #c3e6cb;
}

.import-status.error {
	background-color: #fde7e9;
	color: #dc3545;
	border: 1px solid #f5c6cb;
}

.import-status .dashicons {
	font-size: 16px;
	width: 16px;
	height: 16px;
}

.import-sql-btn.imported {
	background-color: #28a745 !important;
	border-color: #28a745 !important;
	cursor: default;
}

.import-sql-btn.imported:hover {
	background-color: #28a745 !important;
	border-color: #28a745 !important;
}
