var WPFormsConditionals=window.WPFormsConditionals||((d,r)=>{let o={arraySplitIntoChunks(e,i){return e.length?[e.slice(0,i)].concat(o.arraySplitIntoChunks(e.slice(i),i)):[]}},s={allFields:{},$ruleRows:{},conditionalFields:{},fieldsListTemplate:"",fieldValuesListTemplates:{},cacheAllFields(e){s.allFields=e},cacheRuleRows(e){s.$ruleRows=e||r(".wpforms-conditional-row")},setConditionalFields(){s.conditionalFields=s.removeUnsupportedFields()},removeUnsupportedFields(){let i=wpforms_builder.cl_fields_supported,o={...s.allFields};return o=wp.hooks.applyFilters("wpforms.ConditionalLogicCore.BeforeRemoveUnsupportedFields",o),Object.keys(o).forEach(e=>{i.includes(o[e].type)&&!o[e].dynamic_choices||delete o[e]}),o},setTemplates(){s.setFieldsListTemplate(),s.fieldValuesListTemplates={}},setFieldsListTemplate(){var i,o=r("<select>").append(r("<option>",{value:"",text:wpforms_builder.select_field}));for(i in s.conditionalFields){var t=s.conditionalFields[i];let e;e=void 0!==t.label&&""!==t.label.toString().trim()?wpf.sanitizeHTML(t.label.toString().trim()):wpforms_builder.field+" #"+t.id,o.append(r("<option>",{value:t.id,text:e,id:"option-"+t.id}))}s.fieldsListTemplate=o.html()},getFieldValuesListTemplate(e,i){if(s.fieldValuesListTemplates[i])return s.fieldValuesListTemplates[i];var o,t=wpf.orders.choices["field_"+i],l=r("<select>"),n=Object.values(wpf.getFields()).find(e=>e.id.toString()===i.toString());for(o in t){var d=t[o],a=void 0!==n.choices[d]&&""!==n.choices[d].label.toString().trim()?wpf.sanitizeHTML(n.choices[d].label.toString().trim()):wpforms_builder.choice_empty_label_tpl.replace("{number}",d);l.append(r("<option>",{value:d,text:a,id:"choice-"+d}))}return s.fieldValuesListTemplates[i]=l.html()},updateConditionalRuleRows(){o.arraySplitIntoChunks(s.$ruleRows,20).map(function(i){return setTimeout(function(){for(let e=0;e<i.length;++e)s.updateConditionalRuleRow(i[e])},0),i})},updateConditionalRuleRow(e){var e=r(e),i=e.attr("data-field-id"),o=e.find(".wpforms-conditional-field"),t=o.val();o[0].innerHTML=s.fieldsListTemplate,o.find("#option-"+i).remove(),(t?(i=e.find(".wpforms-conditional-value"),o.find("#option-"+t).length?s.restorePreviousRuleRowSelection(e,o,t,i):s.removeRuleRow(e),o.find("option").removeAttr("id"),i):o).find("option").removeAttr("id")},fieldDeleteConfirmAlert(e){let i=wpforms_builder.conditionals_change+"<br>",o;r(".wpforms-conditional-field").each(function(){e.id!==Number(r(this).val())||e.choiceId&&e.choiceId!==Number(r(this).closest(".wpforms-conditional-row").find(".wpforms-conditional-value").val())||(i+=s.getChangedFieldNameForAlert(s.getReferenceName(this)),o=!0,e.trigger=!0)}),o&&(e.message="<strong>"+e.message+"</strong><br><br>"+i)},getReferenceName(e){var e=r(e),i=e.closest(".wpforms-builder-provider").data("provider-name");return i?wpforms_builder.cl_reference.replace("{integration}",i):e.closest(".wpforms-conditional-group").data("reference")},restorePreviousRuleRowSelection(e,i,o,t){i.find("#option-"+o).prop("selected",!0),t.length&&t.is("select")&&(i=t.val(),t[0].innerHTML=s.getFieldValuesListTemplate(s.conditionalFields,o),t.find("#choice-"+i).length)&&t.find("#choice-"+i).prop("selected",!0)},removeRuleRow(e){var i=e.closest(".wpforms-conditional-group");1===i.find("table >tbody >tr").length?1<e.closest(".wpforms-conditional-groups").find(".wpforms-conditional-group").length?i.remove():(e.find(".wpforms-conditional-value").remove(),e.find(".value").append("<select>")):e.remove()},getChangedFieldNameForAlert(e){var i;return wpf.isNumber(e)?(((i=wpf.formObject("#wpforms-field-options")).fields[e]||{}).label||"").length?"<br/>"+wpf.sanitizeHTML(i.fields[e].label)+" ("+wpforms_builder.field+" #"+e+")":"<br>"+wpforms_builder.field+" #"+e:"<br>"+e}},a={init(){r(WPFormsConditionals.ready)},ready(){WPFormsConditionals.bindUIActions()},getLayoutFieldsToExclude(e){e=e.parents(".wpforms-field-option").find(".wpforms-field-option-hidden-id").val();let i=wpf.formObject("#wpforms-field-options");e=e&&i?.fields?.[e]?i.fields[e]:[];let o={};return Object.values(e["columns-json"]??{}).forEach(e=>{Object.values(e?.fields??[]).forEach(e=>{i.fields[e]&&(o[e]=i.fields[e])})}),o},bindUIActions(){var e=r("#wpforms-builder");e.on("change",".wpforms-conditionals-enable-toggle input[type=checkbox]",function(e){WPFormsConditionals.conditionalToggle(this,e)}),e.on("click",".wpforms-field-option-group-conditionals",function(){var e,i=r(this);"layout"===i.parents(".wpforms-field-option").find(".wpforms-field-option-hidden-type").val()&&(e=i.find(".wpforms-conditional-block"),i=wpf.getFields(!1,!0,!1,a.getLayoutFieldsToExclude(i)),WPFormsConditionals.conditionalUpdateOptions(!1,i,e.find(".wpforms-conditional-row")))}),e.on("change",".wpforms-conditional-field",function(e){WPFormsConditionals.conditionalField(this,e)}),e.on("change",".wpforms-conditional-operator",function(e){WPFormsConditionals.conditionalOperator(this,e)}),e.on("click",".wpforms-conditional-rule-add",function(e){WPFormsConditionals.conditionalRuleAdd(this,e)}),e.on("click",".wpforms-conditional-rule-delete",function(e){WPFormsConditionals.conditionalRuleDelete(this,e)}),e.on("click",".wpforms-conditional-groups-add",function(e){WPFormsConditionals.conditionalGroupAdd(this,e)}),r(d).on("wpformsFieldUpdate",WPFormsConditionals.conditionalUpdateOptions),e.on("wpformsBeforeFieldDeleteAlert",function(e,i){s.fieldDeleteConfirmAlert(i)})},conditionalUpdateOptions(e,i,o){wpf.empty(i)||(s.cacheAllFields(i),s.cacheRuleRows(o),s.setConditionalFields(),s.setTemplates(),s.updateConditionalRuleRows())},conditionalToggle(e,i){i.preventDefault();let o=r(e),t=o.closest(".wpforms-conditional-block"),l=wp.template("wpforms-conditional-block"),n={fieldID:o.closest(".wpforms-field-option-row").data("field-id"),fieldName:o.data("name"),actions:o.data("actions"),actionDesc:o.data("action-desc"),reference:o.data("reference")};o.is(":checked")?(t.append(l(n)),i=wpf.getFields(!1,!0,!1,a.getLayoutFieldsToExclude(o)),WPFormsConditionals.conditionalUpdateOptions(!1,i,t.find(".wpforms-conditional-row"))):r.confirm({title:!1,content:wpforms_builder.conditionals_disable,backgroundDismiss:!1,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){t.find(".wpforms-conditional-groups").remove(),r(d).trigger("wpformsRemoveConditionalLogicRules",[o])}},cancel:{text:wpforms_builder.cancel,action(){o.prop("checked",!0)}}}})},conditionalField(e,i){i.preventDefault();var i=r(e),e=i.parent().parent(),o=e.find(".wpforms-conditional-operator"),t=o.find("option:selected").val(),l=WPFormsConditionals.conditionalData(i),i=l.inputName+"["+l.groupID+"]["+l.ruleID+"][value]";let n;if(l.field)if("select"===l.field.type||"radio"===l.field.type||"checkbox"===l.field.type||"payment-multiple"===l.field.type||"payment-checkbox"===l.field.type||"payment-select"===l.field.type){if((n=r("<select>").attr({name:i,class:"wpforms-conditional-value"})).append(r("<option>",{value:"",text:wpforms_builder.select_choice})),l.field.choices)for(var d in wpf.orders.choices["field_"+l.field.id]){var d=wpf.orders.choices["field_"+l.field.id][d],a=void 0!==l.field.choices[d].label&&""!==l.field.choices[d].label.toString().trim()?wpf.sanitizeHTML(l.field.choices[d].label.toString().trim()):wpforms_builder.choice_empty_label_tpl.replace("{number}",d);n.append(r("<option>",{value:d,text:wpf.sanitizeHTML(a)}))}o.find("option:not([value='=='],[value='!='],[value='e'],[value='!e'])").prop("disabled",!0).prop("selected",!1)}else{let e="text";"rating"!==l.field.type&&"net_promoter_score"!==l.field.type&&"number-slider"!==l.field.type||(e="number"),n=r("<input>").attr({type:e,name:i,class:"wpforms-conditional-value"}),o.find("option").prop("disabled",!1)}else n=r("<select>");"e"!==t&&"!e"!==t||n.prop("disabled",!0),e.find(".value").empty().append(n)},conditionalOperator(e,i){i.preventDefault();i=r(e),e=i.parent().parent().find(".wpforms-conditional-value"),i=i.find("option:selected").val();"e"===i||"!e"===i?(e.prop("disabled",!0),e.is("select")?e.find("option:selected").prop("selected",!1):e.val("")):e.prop("disabled",!1)},conditionalRuleAdd(e,i){i.preventDefault();var i=r(e).closest(".wpforms-conditional-group").find("tr").last(),e=i.clone(),o=e.find(".wpforms-conditional-field"),t=e.find(".wpforms-conditional-operator"),l=WPFormsConditionals.conditionalData(o),n=Number(l.ruleID)+1,l=l.inputName+"["+l.groupID+"]["+n+"]";e.find("option:selected").prop("selected",!1),e.find(".value").empty().append(r("<select>")),o.attr("name",l+"[field]").attr("data-ruleid",n),t.attr("name",l+"[operator]"),i.after(e)},conditionalRuleDelete(e,i){i.preventDefault();var i=r(e),e=i.closest(".wpforms-conditional-group"),o=e.find("table >tbody >tr");o&&1===o.length?1<i.closest(".wpforms-conditional-groups").find(".wpforms-conditional-group").length?e.remove():(o.find(".wpforms-conditional-operator").val("==").trigger("change"),o.find(".wpforms-conditional-value").val("").trigger("change"),o.find(".wpforms-conditional-field").val("").trigger("change")):i.parent().parent().remove()},conditionalGroupAdd(e,i){i.preventDefault();var i=r(e),e=i.parent().find(".wpforms-conditional-group").last().clone(),o=(e.find("tr").slice(1).remove(),e.find(".wpforms-conditional-field")),t=e.find(".wpforms-conditional-operator"),l=WPFormsConditionals.conditionalData(o),n=Number(l.groupID)+1,l=l.inputName+"["+n+"][0]";e.find("option:selected").prop("selected",!1),e.find(".value").empty().append(r("<select>")),o.attr("name",l+"[field]").attr("data-ruleid",0).attr("data-groupid",n),t.attr("name",l+"[operator]"),i.before(e)},conditionalData(e){e=r(e),e={fields:wpf.getFields(!1,!0),inputBase:e.closest(".wpforms-conditional-row").attr("data-input-name"),fieldID:e.closest(".wpforms-conditional-row").attr("data-field-id"),ruleID:e.attr("data-ruleid"),groupID:e.attr("data-groupid"),selectedID:e.find(":selected").val()};return e.inputName=e.inputBase+"[conditionals]",e.selectedID.length?e.field=wpf.getField(e.selectedID):e.field=!1,e}};return a})(document,(window,jQuery));WPFormsConditionals.init();