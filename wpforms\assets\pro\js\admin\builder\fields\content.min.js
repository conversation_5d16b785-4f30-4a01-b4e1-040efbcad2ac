var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.ContentField=WPForms.Admin.Builder.ContentField||((t,i,p)=>{let d={duplicatedFieldContent:void 0,contentPreviewEndClass:"wpforms-field-content-preview-end",updatedLayoutFieldId:!1,_pluginManager:{},textareaId:function(e){return`wpforms-field-option-${e}-content`},contentWrap:function(e){return`wp-wpforms-field-option-${e}-content-wrap`},init:function(){d.bindInitInstanceCallback(),p(d.ready)},ready:function(){p("#wpforms-builder").on("wpformsFieldAdd",d.onWpformsFieldAdd).on("wpformsFieldMove",d.onWpformsFieldMove).on("wpformsBeforeFieldDuplicate",d.onWpformsBeforeFieldDuplicate).on("wpformsFieldDuplicated",d.onWpformsFieldDuplicated).on("wpformsBeforeSave",d.onWpformsBeforeSave).on("click",".wpforms-content-button-update-preview.update-preview",d.updatePreview).on("click",".wpforms-content-button-expand-editor.expand-editor",d.expandEditor).on("click",".wpforms-panel-sidebar-toggle:not(.wpforms-panel-sidebar-closed)",d.collapseExpandedEditor).on("wpformsLayoutAfterReceiveFieldToColumn",d.onWpformsLayoutAfterReceiveFieldToColumn).on("wpformsLayoutAfterUpdateColumnsData",d.layoutChanged).on("click",".wpforms-expandable-editor .insert-media",d.onInsertMediaButtonClicked).on("click",".wpforms-expandable-editor .mce-toolbar button, .wpforms-expandable-editor .quicktags-toolbar input",d.onContentUpdated).on("input",".wpforms-expandable-editor .wp-editor-area",d.onContentUpdated).on("click",".wpforms-expandable-editor .wp-switch-editor.switch-html",d.setTextareaVisible).on("click",".wpforms-panel-content-wrap .wpforms-field",d.hideImageToolbar).on("wpformsBuilderConfirmationsReady",d.setPluginManager)},onWpformsFieldAdd:function(e,t,n){d.updateContentFieldInColumns(),d.hasEditor(n)&&d.resetEditor(t,d.duplicatedFieldContent)},onWpformsLayoutAfterReceiveFieldToColumn:function(e,t){t=t.column.closest(".wpforms-field.wpforms-field-layout");0<t.length&&(d.updatedLayoutFieldId=t.data("field-id"))},layoutChanged:function(e,t){void 0===d.duplicatedFieldContent&&0<t.fieldId&&d.resetFieldsInLayout(t.fieldId)},updateContentFieldInColumns:function(){d.updatedLayoutFieldId&&(d.resetFieldsInLayout(d.updatedLayoutFieldId),d.updatedLayoutFieldId=!1)},onWpformsFieldMove:function(e,t){var n;"layout"===t.item.data("field-type")?d.resetFieldsInLayout(t.item.data("field-id")):d.hasEditor(t.item.data("field-type"))&&(t=t.item.data("field-id"),n=d.getEditorContent(t),d.resetEditor(t,n),d.updateContentFieldInColumns())},resetFieldsInLayout:function(t){p(".wpforms-field-content.wpforms-field").each(function(){var e=p(this).data("field-id");WPForms.Admin.Builder.FieldLayout.columnsHasFieldID(t,e)&&d.resetEditor(e,d.getEditorContent(e))})},onWpformsBeforeFieldDuplicate:function(e,t,n){var o;"content"===n.data("field-type")&&(o=p(`.wpforms-field-has-tinymce[data-field-id=${t}]`),d.renderPreview(o,t),d.duplicatedFieldContent=n.find(".wpforms-field-content-preview").html().replace(`<div class="${d.contentPreviewEndClass}"></div>`,""))},onWpformsFieldDuplicated:function(e,t,n,o,i){d.duplicatedFieldContent=void 0,"layout"===n.data("field-type")&&(d.resetFieldsInLayout(t),d.resetFieldsInLayout(o))},onWpformsBeforeSave:function(){p(".wpforms-field-has-tinymce textarea.wp-editor-area").each(function(){p(this).val(wpf.sanitizeHTML(p(this).val(),wpforms_builder.content_field.allowed_html))}),p(".wpforms-field-has-tinymce").each(function(){d.renderPreview(null,p(this).data("field-id"))})},updatePreview:function(e){e.preventDefault(),d.renderPreview(p(this).closest(".wpforms-field-has-tinymce"))},renderPreview:function(e,t=null){"object"!=typeof e&&"number"!=typeof t?console.log("Cannot update preview. ContentField.renderPreview requires valid $settings object or valid fieldId"):(t=0<t?t:e.data("field-id"),e=p("#wpforms-field-"+t).find(".wpforms-field-content-preview"),t=d.parseShortcode(wpf.sanitizeHTML(d.getContentFromActiveView(t),wpforms_builder.content_field.allowed_html)),e.html(`${t}<div class="${d.contentPreviewEndClass}"></div>`))},getContentFromActiveView:function(e){return p("#"+d.contentWrap(e)).hasClass("html-active")?wpf.wpautop(p("#"+d.textareaId(e)).val()):d.getEditorContent(e)},getEditorContent:function(e){var t=tinymce.get(d.textareaId(e));return t?t.getContent():p("#"+d.textareaId(e)).val()},expandEditor:function(e){e.preventDefault();let t="wpforms-content-editor-expanded",n=p(this),o=n.closest(".wpforms-panel-sidebar"),i=n.closest(".wpforms-field-options.wpforms-tab-content"),d=n.closest(".wpforms-field-option.wpforms-field-has-tinymce"),r=n.closest(".wpforms-field-content-action-buttons"),a=n.closest(".wpforms-expandable-editor"),l=a.next(".wpforms-expandable-editor-clear"),s=a.outerHeight(!0)+20;n.toggleClass(t),n.hasClass(t)?(r.width(a.width()||360),l.css("margin-bottom",s+"px")):l.css("margin-bottom",0),[o,i,d].forEach(function(e){n.hasClass(t)?(e.addClass(t),n.find("span").text(wpforms_builder.content_field.collapse)):(p("."+t).removeClass(t),p("button.expand-editor").each(function(){p(this).find("span").text(wpforms_builder.content_field.expand)}))})},collapseExpandedEditor(){p(".wpforms-content-button-expand-editor.wpforms-content-editor-expanded").trigger("click")},setPluginManager:function(){var e;0<p("#wpforms-builder").length&&"undefined"!=typeof tinymce&&(e="wpforms-content-field-fake-div",p(t.createElement("textarea")).attr("id",e).css("display","none").appendTo("body"),tinymce.init({selector:"#"+e,init_instance_callback:function(e){d._pluginManager=tinymce.PluginManager.get("wpeditimage")(e)}}))},bindInitInstanceCallback:function(){i.wpformsContentFieldTinyMCECallback=function(e){e&&(e.on("dirty",d.onContentUpdated),e.on("keyup",d.onContentUpdated))}},onContentUpdated:function(){d.showUpdatePreviewButton(p(this).attr("id"))},setTextareaVisible:function(){var e=p(this).data("wp-editor-id");p("#"+e).css("visibility","visible")},hideImageToolbar:function(){p(".mce-toolbar-grp.mce-inline-toolbar-grp").hide()},onInsertMediaButtonClicked:function(){var e=d.textareaId(p(this).closest(".wpforms-field-has-tinymce").data("field-id"));d.showUpdatePreviewButton(e)},showUpdatePreviewButton:function(e){p("#"+e).closest(".wpforms-field-option").find(".update-preview").show()},resetEditor:function(e,t){var n=d.textareaId(e);tinymce.get(n)?tinymce.execCommand("mceRemoveEditor",!1,n):d.cleanEditorWrap(e),d.initTinyMCE(e,t)},initTinyMCE:function(t,n){let o=d.textareaId(t);tinymce.init({selector:"#"+o,textarea_name:`fields[${t}][content]`,media_buttons:!0,drag_drop_upload:!0,relative_urls:!1,remove_script_host:!1,menubar:!1,branding:!1,object_resizing:!1,height:wpforms_builder.content_field.editor_height,plugins:wpforms_builder.content_field.content_editor_plugins.join(),toolbar:wpforms_builder.content_field.content_editor_toolbar.join(),imagetools_toolbar:"rotateleft rotateright | flipv fliph | editimage imageoptions",content_css:wpforms_builder.content_field.content_editor_css_url+"?"+(new Date).getTime(),invalid_elements:wpforms_builder.content_field.invalid_elements,wp_shortcut_labels:i.wp?.editor?.getDefaultSettings?.()?.tinymce?.wp_shortcut_labels,body_class:wpforms_builder.content_field.body_class,init_instance_callback:function(e){e.setContent(void 0!==n?n:wpforms_builder.content_field.editor_default_value),i.wpformsContentFieldTinyMCECallback(e),d.setWrapperClasses(tinymce.$("#"+d.contentWrap(t))),quicktags({id:o,buttons:wpforms_builder.content_field.quicktags_buttons})}})},hasEditor:function(e){return wpforms_builder.content_input.supported_field_types.includes(e)&&"undefined"!=typeof tinymce},cleanEditorWrap:function(e){var t=p("#"+d.textareaId(e)),n=t.closest(".wp-editor-wrap"),o=wp.template("wpforms-content-editor-tools")({optionId:"option-"+e});t.css("display","block"),d.setWrapperClasses(n).empty().append(o).append(t).attr("id",""+d.contentWrap(e))},setWrapperClasses:function(e){return e.addClass("tmce-active tmce-initialized").removeClass("html-active")},parseShortcode:function(e){return void 0===e?e:d._pluginManager?._do_shcode?.(e)??e}};return d})(document,window,jQuery),WPForms.Admin.Builder.ContentField.init();