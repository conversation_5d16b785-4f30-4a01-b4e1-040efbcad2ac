@import 'layout_media_queries';

$darkprimary: #ffffff;
$darkSecondary: #999999;
$darkBlue: #3389bd;
$darkFontColor: #dddddd;
$darkTableBorder: #424446;
$darkOrangeBackground: #383230;
$darkBackgroundColor: #2d2f31;
$darkBlueBackground: #2e353b;

@media only screen and (max-width: 700px) {
	.header {
		.header-image {
			max-width: 221px;
		}
	}

	.body-inner {
		padding-bottom: 0 !important;
	}

	.summary-content {
		border-radius: 0 !important;
		padding: 30px !important;
	}

	.summary-header,
	.email-summaries-overview-wrapper {
		margin-bottom: 30px !important;
	}

	.email-summaries-overview {
		h5 {
			font-size: 18px !important;
			line-height: 26px !important;
		}

		p {
			font-size: 14px !important;
			line-height: 20px !important;
		}
	}

	.email-summaries {
		.entry-count,
		.form-name {
			font-size: 14px !important;
		}
	}

	.summary-notice {
		h4 {
			font-size: 20px !important;
			line-height: 30px !important;
		}
	}

	.summary-notification-block,
	.summary-info-block {
		border-radius: 0 !important;
		padding: 30px 30px 6px 30px !important;
	}
}

@media only screen and (max-width: 320px) {
	.email-summaries {
		th {
			padding: 15px !important;
		}

		.form-name,
		.entry-count,
		.summary-trend {
			padding: 12px !important;
		}
	}
}

// Light mode.
@media (prefers-color-scheme: light) {
	tr.dark-mode {
		display: none !important;
	}
}

// Dark mode.
@mixin darkModeStyles() {
	tr.dark-mode {
		display: table-row !important;
	}

	tr.light-mode {
		display: none !important;
	}

	body,
	table.body,
	.email-summaries th {
		background-color: $darkBackgroundColor !important;
	}

	.email-summaries td,
	.summary-header,
	.summary-content {
		background-color: #1f1f1f !important;
	}

	body, table.body, h4, h6, p, td, th {
		color: $darkFontColor !important;
	}

	.email-summaries {

		th, td {
			border: 1px solid $darkTableBorder !important;
		}
	}

	.email-summaries-overview {
		border: 1px solid $darkTableBorder !important;
		background: $darkBackgroundColor !important;

		h5 {
			color: $darkFontColor !important;
		}

		p {
			color: $darkSecondary !important;
		}
	}

	.footer,
	.entry-count {
		color: $darkSecondary !important;

		a {
			color: $darkSecondary !important;

			&:hover {
				color: $darkSecondary !important;
			}
		}
	}

	.summary-notice {
		color: $darkprimary !important;

		h4, p {
			color: $darkprimary !important;
		}
	}

	.summary-info-block {
		background-color: $darkOrangeBackground !important;
	}

	.summary-notification-block {
		background-color: $darkBlueBackground !important;

		.summary-notice-content a {
			color: $darkBlue !important;
		}
	}

	.button-blue,
	.button-orange {
		a {
			color: $darkprimary !important;
		}
	}

	.button-blue-outline {
		border: 1px solid $darkBlue !important;

		a {
			color: $darkBlue !important;
		}
	}
}

// Add support for dark mode.
@media (prefers-color-scheme: dark) {
	@include darkModeStyles();
}

// Add support for legacy Outlook dark mode.
[data-ogsc] {
	 @include darkModeStyles();
 }
