/* eslint-disable */
jQuery( document ).ready( function( $ ) {
	/**
	 * Initializes the main functionalities of the page.
	 * Binds click events, fetches branches, and initializes Select2.
	 */
	function init() {
		// Initial check for active workflows
		checkActiveWorkflows();

		// Set up periodic check for active workflows
		setInterval(checkActiveWorkflows, 10000); // Check every 10 seconds

		// Disable "Cancel All" button by default
		$( '#cancel-all' ).prop( 'disabled', true );

		// Bind the click event to the run GitHub action button
		$( '#run-github-action' ).click( function() {
			runGitHubAction();
		} );

		// Fetch branches on page load
		fetchBranches();

		// Initialize Select2 on the branch dropdown
		$( '#branch' ).select2( {
			placeholder: 'Select a branch',
			allowClear: true,
		} );

		// Bind the change event on branch selection to fetch files
		$( '#branch' ).on( 'change', function() {
			const selectedBranch = $( this ).val();
			if ( selectedBranch ) {
				fetchFilesForBranch( selectedBranch );
			}
		} );

		// Bind keyup event to the search input to filter jsTree nodes
		let searchTimeout = false;
		$( '#jstree_search_input' ).keyup( function() {
			if ( searchTimeout ) {
				clearTimeout( searchTimeout );
			}
			searchTimeout = setTimeout( function() {
				const query = $( '#jstree_search_input' ).val();
				$( '#jstree_demo_div' ).jstree( true ).search( query );
			}, 250 );
		} );
	}

	/**
	 * Check for active workflows and display them.
	 */
	function checkActiveWorkflows() {
		$.ajax({
			url: e2eDashboard.ajax_url,
			type: 'POST',
			data: {
				action: 'get_active_workflows',
				nonce: e2eDashboardAdmin.nonce
			},
			success: function(response) {
				if (response.success && response.data.length > 0) {
					// Enable "Cancel All" button if there are active workflows
					$('#cancel-all').prop('disabled', false);

					// Ensure the accordion container exists
					if ($('#cbp-ntaccordion').length === 0) {
						$('#result').html('<ul id="cbp-ntaccordion" class="cbp-ntaccordion"></ul>');
						$('#cbp-ntaccordion').cbpNTAccordion();
					}

					// Keep track of workflow IDs we've seen
					const seenWorkflows = new Set();

					// Display each active workflow
					response.data.forEach(function(workflow) {
						// Skip if we've already processed this workflow
						if (seenWorkflows.has(workflow.id)) {
							return;
						}
						seenWorkflows.add(workflow.id);

						const testContainerId = 'test-' + workflow.id;
						
						// Check if this test block already exists
						if ($('#' + testContainerId).length === 0) {
							const newTestBlock = '<li id="' + testContainerId + '" data-workflow-run-id="' + workflow.id + '">' +
								'<h3 class="cbp-nttrigger">' +
								'<span class="workflow-name">Specified e2e Tests (' + workflow.branch + ')</span>' +
								'<span class="test-name">Specified e2e Tests</span>' +
								'<button class="cancel-workflow-run" data-test-name="Specified e2e Tests">Cancel</button>' +
								'</h3>' +
								'<div class="cbp-ntcontent">' +
								'<ul class="cbp-ntsubaccordion">' +
								'<li><div class="step-row"><span class="step-name">Loading job details...</span></div></li>' +
								'</ul>' +
								'</div>' +
								'</li>';
							
							$('#cbp-ntaccordion').prepend(newTestBlock);

							// Bind cancel button click event
							$('#' + testContainerId + ' .cancel-workflow-run').click(function(e) {
								e.stopPropagation();
								const workflowRunId = $(this).closest('li').attr('data-workflow-run-id');
								if (workflowRunId) {
									cancelWorkflowRun(workflowRunId);
								}
							});
						}

						// Start polling for this workflow's status
						pollJobStatus(workflow.id, 'Specified e2e Tests');
					});

					// Remove any test blocks that are no longer active
					$('#cbp-ntaccordion > li').each(function() {
						const workflowId = $(this).attr('data-workflow-run-id');
						if (workflowId && !seenWorkflows.has(parseInt(workflowId))) {
							$(this).remove();
						}
					});
				} else {
					// If no active workflows, disable "Cancel All" button
					$('#cancel-all').prop('disabled', true);
					
					// Remove the accordion if there are no active workflows
					$('#cbp-ntaccordion').remove();
				}
			},
			error: function(xhr, status, error) {
				console.error('Error checking active workflows:', error);
			}
		});
	}

	/**
	 * Fetches branches from the server via an AJAX request.
	 * Populates the dropdown with branches and reinitializes Select2.
	 */
	function fetchBranches() {
		$.ajax( {
			url: e2eDashboard.ajax_url,
			type: 'POST',
			data: {
				action: 'get_branches',
				nonce: e2eDashboardAdmin.nonce
			},
			success( response ) {
				if ( response.success ) {
					populateBranches( response.data );
				} else {
					$( '#branch' ).html( '<option value="">Error fetching branches</option>' );
				}

				// Reinitialize Select2 after the branches are populated
				$( '#branch' ).select2( {
					placeholder: 'Select a branch',
					allowClear: true,
				} );
			},
		} );
	}

	/**
	 * Populates the branch dropdown with the fetched branches.
	 *
	 * @param {Array} branches The array of branch names to populate the dropdown with.
	 */
	function populateBranches( branches ) {
		const branchDropdown = $( '#branch' );
		branchDropdown.empty(); // Clear any existing options
		branchDropdown.append( '<option value="">Select a branch</option>' ); // Default option

		// Append each branch as an option
		branches.forEach( function( branch ) {
			branchDropdown.append( '<option value="' + branch + '">' + branch + '</option>' );
		} );
	}

	/**
	 * Fetches files from the selected branch and displays them using jsTree.
	 *
	 * @param {string} branch The selected branch name.
	 */
	function fetchFilesForBranch( branch ) {
		$.ajax( {
			url: e2eDashboard.ajax_url,
			type: 'POST',
			data: {
				action: 'get_files_for_branch',
				branch,
				nonce: e2eDashboardAdmin.nonce
			},
			success( response ) {
				if ( response.success ) {
					displayFilesUsingJsTree( response.data );
				} else {
					$( '#jstree_demo_div' ).html( '<p>Error fetching files</p>' );
				}
			},
		} );
	}

	/**
	 * Displays the files in jsTree format on the page, removing "tests" and "acceptance" from the hierarchy.
	 *
	 * @param {Array} files The list of file names.
	 */
	function displayFilesUsingJsTree( files ) {
		// If `files` is an object, convert it to an array of values
		if ( ! Array.isArray( files ) ) {
			files = Object.values( files );
		}

		const nodes = [];

		// Function to recursively add nodes to jsTree, skipping "tests" and "acceptance"
		/**
		 *
		 * @param pathParts
		 * @param parentId
		 */
		function addNode( pathParts, parentId ) {
			if ( pathParts.length === 0 ) {
				return;
			}

			const currentPart = pathParts.shift();

			// Skip "tests", "acceptance", and ".codeception" folders
			if ( currentPart === 'tests' || currentPart === 'acceptance' || currentPart === '.codeception' ) {
				addNode( pathParts, parentId );
				return;
			}

			const currentId = parentId ? parentId + '/' + currentPart : currentPart;

			// Check if the node already exists
			const existingNode = nodes.find( ( node ) => node.id === currentId );
			if ( ! existingNode ) {
				// Add the current part as a node
				nodes.push( {
					id: currentId,
					parent: parentId ? parentId : '#',
					text: currentPart,
					icon: pathParts.length === 0 ? 'jstree-file' : 'jstree-folder', // Different icons for files and folders
				} );
			}

			// Recursively add the rest of the path
			addNode( pathParts, currentId );
		}

		// Convert each file path into nodes, skipping the specified folders
		files.forEach( ( filePath ) => {
			const pathParts = filePath.split( '/' );
			addNode( pathParts, '' );
		} );

		// Initialize jsTree with the hierarchical data and enable plugins
		$( '#jstree_demo_div' ).jstree( 'destroy' ).empty(); // Destroy the previous instance if any
		$( '#jstree_demo_div' ).jstree( {
			core: {
				data: nodes,
			},
			plugins: [ 'search', 'checkbox' ], // Enable search and checkbox plugins
			checkbox: {
				keep_selected_style: false, // Keeps default checkbox styling off
				three_state: true, // Allows for tri-state checkboxes (parent-child relation)
				cascade: 'up+down+undetermined', // Controls how cascading is applied
			},
			search: {
				show_only_matches: true,
				close_opened_onclear: true,
			},
		} );

		// Show the search input once the files are loaded
		$( '#jstree_search_input' ).show();

		// Bind event to listen for node selection in jsTree
		$( '#jstree_demo_div' ).on( 'changed.jstree', function( e, data ) {
			// Handle node selection if needed
		} );
	}

	/**
	 * Sends the selected branch and test information to the server via an AJAX POST request.
	 * Runs GitHub Action for each selected test concurrently.
	 */
	function runGitHubAction() {
		const branch = $( '#branch' ).val();
		const selectedNodes = $( '#jstree_demo_div' ).jstree( 'get_checked', true ); // Get all selected nodes (tests)

		if ( ! branch || ! selectedNodes.length ) {
			alert( 'Please select a branch and at least one test to run.' );
			return;
		}

		// Enable "Cancel All" button
		$( '#cancel-all' ).prop( 'disabled', false );

		// Ensure the outermost accordion ul exists
		if ( $( '#cbp-ntaccordion' ).length === 0 ) {
			$( '#result' ).append( '<ul id="cbp-ntaccordion" class="cbp-ntaccordion"></ul>' );
			$( '#cbp-ntaccordion' ).cbpNTAccordion();
		}

		// Loop through each selected test and trigger GitHub action concurrently
		selectedNodes.forEach( function( node ) {
			const selectedPath = node.id;
			const pathParts = selectedPath.split( '/' );

			let command = '';
			let testName = ''; // For the block identifier

			// Determine the command and test name based on the file path
			if ( pathParts[ 0 ] === 'wpforms' ) {
				if ( pathParts.length >= 2 ) {
					command = `wpforms ${pathParts.slice( 1 ).join( '/' )}`.replace( '.php', '' );
					testName = pathParts.slice( 1 ).join( '/' );
				}
			} else {
				const addonName = pathParts[ 0 ].replace( 'wpforms-', '' );
				if ( pathParts.length >= 2 ) {
					command = `${addonName} ${pathParts.slice( 1 ).join( '/' )}`.replace( '.php', '' );
					testName = addonName + ' ' + pathParts.slice( 1 ).join( '/' );
				}
			}

			if ( command ) {
				// Create the test block only once, outside the polling function
				createTestBlockIfNotExists( testName );

				// Trigger the GitHub action for each selected test
				$.post( e2eDashboard.ajax_url, {
					action: 'run_github_action',
					branch,
					test: command,
					nonce: e2eDashboardAdmin.nonce
				}, function( response ) {
					if ( response.success ) {
						// Start polling for job updates and pass the test name
						const workflowRunId = response.data.workflow_run_id;

						// Store the workflowRunId in the test block
						const testContainerId = 'test-' + sanitizeTestName( testName );
						const testBlock = $( '#' + testContainerId );

						testBlock.attr( 'data-workflow-run-id', workflowRunId );

						// Also set the data attribute on the cancel button
						testBlock.find( '.cancel-workflow-run' ).attr( 'data-workflow-run-id', workflowRunId );

						pollJobStatus( workflowRunId, testName );
					} else {
						$( '#result' ).append( '<p>Error: ' + response.data + '</p>' );
					}
				} );
			}
		} );
	}

	/**
	 * Sanitizes the test name to generate a unique identifier for the test block.
	 *
	 * @param  testName
	 * @return {string}
	 */
	function sanitizeTestName( testName ) {
		// Replace any character that is not a letter, digit, hyphen, or underscore with a hyphen
		return testName.replace( /[^a-zA-Z0-9\-_]+/g, '-' ).toLowerCase();
	}

	/**
	 * Creates a test block if it doesn't exist already.
	 * @param testName
	 */
	function createTestBlockIfNotExists( testName ) {
		console.log( 'Attempting to create test block for:', testName );

		const testContainerId = 'test-' + sanitizeTestName( testName );
		console.log( 'Generated testContainerId:', testContainerId );

		const testBlock = $( '#' + testContainerId );

		if ( testBlock.length === 0 ) {
			const newTestBlock = '<li id="' + testContainerId + '" data-workflow-run-id="">' +
				'<h3 class="cbp-nttrigger">' +
				'<span class="workflow-name">Specified e2e Tests (develop)</span>' +
				'<span class="test-name">' + testName + '</span>' +
				'<button class="cancel-workflow-run" data-test-name="' + testName + '">Cancel</button>' +
				'</h3>' +
				'<div class="cbp-ntcontent">' +
				'<ul class="cbp-ntsubaccordion">' +
				/* Job details will be appended here */ '' +
				'</ul>' +
				'</div>' +
				'</li>';
			console.log( 'Appending new test block:', newTestBlock );
			$( '#cbp-ntaccordion' ).prepend( newTestBlock );

			// Bind cancel button click event
			$( '#' + testContainerId + ' .cancel-workflow-run' ).click(function() {
				const workflowRunId = $(this).closest('li').attr('data-workflow-run-id');
				if (workflowRunId) {
					cancelWorkflowRun(workflowRunId);
				}
			});
		} else {
			console.log( 'Test block already exists for:', testName );
		}
	}

	/**
	 * Poll job status for real-time updates, tied to each test.
	 * @param workflowRunId
	 * @param testName
	 */
	function pollJobStatus(workflowRunId, testName) {
		const testContainerId = 'test-' + workflowRunId;
		const testBlock = $('#' + testContainerId);

		if (testBlock.length === 0) {
			return;
		}

		const jobStatusContainer = testBlock.find('ul.cbp-ntsubaccordion');

		if (jobStatusContainer.length === 0) {
			return;
		}

		// Store which items are open before updating
		const openItems = [];
		jobStatusContainer.find('li').each(function() {
			if ($(this).hasClass('cbp-ntopen')) {
				openItems.push($(this).find('h4.cbp-nttrigger').text());
			}
		});

		$.post(
			e2eDashboard.ajax_url,
			{
				action: 'check_workflow_status',
				workflow_run_id: workflowRunId,
				test_name: testName,
				nonce: e2eDashboardAdmin.nonce
			},
			function(response) {
				if (response.success) {
					const jobs = response.data.job_details;
					let jobDetailsHtml = '';

					if (jobs && jobs.length > 0) {
						jobs.forEach(function(job) {
							const status = job.status.toLowerCase();
							const conclusion = job.conclusion ? job.conclusion.toLowerCase() : 'in_progress';
							const steps = job.steps || [];
							const isOpen = openItems.includes(job.name) ? ' cbp-ntopen' : '';

							jobDetailsHtml += '<li class="' + isOpen + '">' +
								'<h4 class="cbp-nttrigger">' + job.name + '</h4>' +
								'<div class="cbp-ntcontent">' +
								'<div class="job-details">' +
								'<div class="job-status-row">' +
								'<span class="status-label">Status:</span>' +
								'<span class="status-badge status-' + status + '">' + 
								(status === 'completed' ? '✓' : status === 'queued' ? '⋯' : '↻') + ' ' + 
								status.charAt(0).toUpperCase() + status.slice(1) + 
								'</span>' +
								'</div>' +
								'<div class="job-steps">';

							if (steps.length > 0) {
								steps.forEach(function(step) {
									const stepStatus = step.status.toLowerCase();
									const stepConclusion = step.conclusion ? step.conclusion.toLowerCase() : 'in_progress';
									
									jobDetailsHtml += '<div class="step-row">' +
										'<span class="step-name">' + step.name + '</span>' +
										'<span class="step-badge step-' + stepStatus + ' conclusion-' + stepConclusion + '">' +
										(stepConclusion === 'success' ? '✓' : 
										 stepConclusion === 'failure' ? '✕' : 
										 stepConclusion === 'cancelled' ? '⊘' : 
										 stepConclusion === 'skipped' ? '⤳' :
										 stepStatus === 'in_progress' ? '↻' : '⋯') + ' ' +
										(stepStatus === 'completed' ? 
											(stepConclusion === 'skipped' ? 'Skipped' : stepConclusion.charAt(0).toUpperCase() + stepConclusion.slice(1)) : 
											stepStatus.charAt(0).toUpperCase() + stepStatus.slice(1)) +
										'</span>' +
										'</div>';
								});
							} else {
								jobDetailsHtml += '<div class="step-row"><span class="step-name">Initializing...</span></div>';
							}

							jobDetailsHtml += '</div>' + // Close job-steps
								'</div>' + // Close job-details
								'</div>' + // Close cbp-ntcontent
								'</li>';
						});

						jobStatusContainer.html(jobDetailsHtml);
					} else {
						jobStatusContainer.html('<li><div class="step-row"><span class="step-name">Waiting for job details...</span></div></li>');
					}

					// Continue polling if any job is not completed
					const hasIncompleteJobs = jobs.some(job => job.status !== 'completed');
					if (hasIncompleteJobs) {
						setTimeout(function() {
							pollJobStatus(workflowRunId, testName);
						}, 5000);
					}
				}
			}
		);
	}

	/**
	 * Cancel a specific workflow run.
	 * @param {string} workflowRunId The ID of the workflow run to cancel.
	 */
	function cancelWorkflowRun(workflowRunId) {
		$.post(e2eDashboard.ajax_url, {
			action: 'cancel_workflow_run',
			workflow_run_id: workflowRunId,
			nonce: e2eDashboardAdmin.nonce
		})
		.done(function(response) {
			if (response.success) {
				alert('Workflow run cancellation requested.');
				// Find and disable the cancel button for this workflow
				$(`li[data-workflow-run-id="${workflowRunId}"] .cancel-workflow-run`)
					.prop('disabled', true)
					.text('Cancellation Requested');
			} else {
				alert('Error: ' + response.data);
			}
		})
		.fail(function(xhr, status, error) {
			alert('Error cancelling workflow: ' + error);
		});
	}

	/**
	 * Event handler for individual cancel button
	 */
	$( document ).on( 'click', '.cancel-workflow-run', function( e ) {
		e.stopPropagation(); // Prevent the click from triggering accordion toggle
		const $button = $( this );
		const testName = $button.data( 'test-name' );
		const workflowRunId = $button.closest('li').data( 'workflow-run-id' );

		if ( ! workflowRunId ) {
			alert( 'Workflow Run ID not found.' );
			return;
		}

		if ( confirm( 'Are you sure you want to cancel the workflow run for "' + testName + '"?' ) ) {
			// Send AJAX request to cancel the workflow run
			$.post( e2eDashboard.ajax_url, {
				action: 'cancel_workflow_run',
				workflow_run_id: workflowRunId,
				nonce: e2eDashboardAdmin.nonce
			}, function( response ) {
				if ( response.success ) {
					alert( 'Workflow run cancellation requested.' );
					// Disable the cancel button
					$button.prop( 'disabled', true ).text( 'Cancellation Requested' );
				} else {
					alert( 'Error: ' + response.data );
				}
			} );
		}
	} );

	/**
	 * Event handler for "Cancel All" button
	 */
	$( '#cancel-all' ).on( 'click', function(e) {
		// Prevent default button behavior and form submission
		e.preventDefault();
		e.stopPropagation();

		if ( confirm( 'Are you sure you want to cancel all running jobs?' ) ) {
			let cancelCount = 0;
			let successCount = 0;
			let failCount = 0;

			// Find all active tests and cancel them
			const activeTests = $( '#cbp-ntaccordion > li' ).filter(function() {
				return $(this).attr('data-workflow-run-id') !== '';
			});

			if (activeTests.length === 0) {
				alert('No active workflows to cancel.');
				return;
			}

			activeTests.each( function() {
				const $test = $(this);
				const workflowRunId = $test.attr('data-workflow-run-id');
				const $cancelButton = $test.find('.cancel-workflow-run');
				const testName = $cancelButton.data('test-name');

				if (workflowRunId) {
					cancelCount++;
					// Send AJAX request to cancel each workflow run
					$.post( e2eDashboard.ajax_url, {
						action: 'cancel_workflow_run',
						workflow_run_id: workflowRunId,
						nonce: e2eDashboardAdmin.nonce
					})
					.done(function(response) {
						if (response.success) {
							successCount++;
							// Disable the cancel button for this test
							$cancelButton.prop('disabled', true).text('Cancellation Requested');
						} else {
							failCount++;
							console.error('Failed to cancel workflow:', testName, response.data);
						}
					})
					.fail(function(xhr, status, error) {
						failCount++;
						console.error('AJAX error:', error);
					})
					.always(function() {
						// When all requests are completed
						if ((successCount + failCount) === cancelCount) {
							if (failCount > 0) {
								alert(`Cancelled ${successCount} workflow(s). Failed to cancel ${failCount} workflow(s).`);
							} else {
								alert(`Successfully requested cancellation for ${successCount} workflow(s).`);
							}
						}
					});
				}
			});
		}
		return false;
	});

	// Initialize the script
	init();
} );
