/**
 * ModSecurity Interface Styles
 * Basic styling for the ModSecurity detection interface
 */

/* Main container */
.wpf-support-helper-content {
    max-width: 800px;
    margin-top: 20px;
}

/* Section styling */
.wpf-support-helper-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);

    h2 {
        margin-top: 0;
        margin-bottom: 15px;
        color: #23282d;
        font-size: 1.3em;
        font-weight: 600;
    }

    h3 {
        margin-top: 20px;
        margin-bottom: 10px;
        color: #0073aa;
        font-size: 1.1em;
        font-weight: 600;
    }

    p {
        margin-bottom: 15px;
        line-height: 1.5;
        color: #555;
    }
}

/* Status indicators */
.wpf-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
	margin-inline-end: 8px;

	&.status-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    &.status-failed {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    &.status-skipped {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    &.status-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
}

/* Detection methods list */
.wpf-detection-methods {
    margin-top: 15px;

    .method-item {
        display: flex;
        align-items: center;
        padding: 10px;
        margin-bottom: 8px;
        background: #fff;
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        transition: border-color 0.2s ease;

        &:hover {
            border-color: #c3c4c7;
        }

        .method-name {
            flex: 1;
            font-weight: 500;
            color: #23282d;
        }

        .method-reason {
            flex: 2;
            color: #666;
            font-size: 0.9em;
			margin-inline-start: 15px;
        }

        // Expandable method item styles
        &.expandable {
            display: block;
            padding: 0;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                border-color: #0073aa;
            }

            // Hide the checkbox
            .method-toggle {
                display: none;
            }

            // Method header (always visible)
            .method-header {
                display: flex;
                align-items: center;
                padding: 10px;
                position: relative;

                .method-name {
                    flex: 1;
                    font-weight: 500;
                    color: #23282d;
                }

                .method-reason {
                    flex: 2;
                    color: #666;
                    font-size: 0.9em;
					margin-inline-start: 15px;
                }

                // Expand arrow
                .expand-arrow {
					margin-inline-start: auto;
                    transition: transform 0.2s ease;
                    color: #666;
                    flex-shrink: 0;
                }
            }

            // Additional info section (hidden by default)
            .method-additional-info {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease;
                border-top: 1px solid #e1e1e1;

                .additional-info-textarea {
                    width: 100%;
                    min-height: 150px;
                    padding: 10px;
                    border: none;
                    background: #f8f9fa;
                    font-family: 'Courier New', Courier, monospace;
                    font-size: 12px;
                    line-height: 1.4;
                    color: #333;
                    resize: vertical;
                    outline: none;
                    white-space: pre;
                    overflow-wrap: break-word;
                }
            }

            // When checkbox is checked (expanded state)
            .method-toggle:checked ~ .method-header .expand-arrow {
                transform: rotate(180deg);
            }

            .method-toggle:checked ~ .method-additional-info {
                max-height: 500px; // Adjust as needed
            }
        }
    }
}

/* Summary section */
.wpf-detection-summary {
    background: #f0f6fc;
    border: 1px solid #c8d3e0;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;

    .summary-title {
        margin: 0 0 10px 0;
        color: #0969da;
        font-size: 1.1em;
        font-weight: 600;
    }

    .summary-stats {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;

        .stat-item {
            .stat-number {
                display: block;
                font-size: 1.5em;
                font-weight: 700;
                color: #23282d;
            }

            .stat-label {
                display: block;
                font-size: 0.9em;
                color: #666;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
        }
    }
}

/* Error message styling */
.wpf-error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;

    .error-title {
        font-weight: 600;
        margin-bottom: 8px;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .wpf-support-helper-content {
        margin: 10px;
        max-width: none;
    }

    .wpf-detection-methods .method-item {
        flex-direction: column;
        align-items: flex-start;

        .method-reason {
			margin-inline-start: 0;
            margin-top: 5px;
        }
    }

    .wpf-detection-summary .summary-stats {
        flex-direction: column;
        gap: 10px;
    }

    .wpf-http-headers .header-item {
        flex-direction: column;

        .header-name {
            flex: none;
            margin-bottom: 5px;
        }
    }
}

