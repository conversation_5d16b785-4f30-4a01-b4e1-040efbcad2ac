// Form Builder styles.
//
// Panel fields.
// Repeater field styles.
//
// @since 1.8.9

// Repeater field images path.
$repeater_images_path: "../images/repeater/";

.wpforms-panel-fields {

	// Field options area (left side).
	.wpforms-field-option-repeater {

		// Display types.
		.wpforms-field-option-row-display {
			margin-top: $spacing_s;
			display: flex;
			justify-content: space-between;
			flex-wrap: wrap;

			input {
				display: none;

				& + label {
					background-color: $color_fields_secondary_text;
					background-size: 100% 100%;
					border: none;
					border-radius: $border_radius_s;
					width: 170px;
					height: 110px;
					padding: 0;
					margin: 0;
					cursor: pointer;

					@include transition( box-shadow, $transition_fast, ease-out );

					&:hover {
						box-shadow: 0 0 0 1px #86919e;
					}

					&.display-rows {
						background-image: url( "#{ $repeater_images_path }display-rows.svg" );

						[dir="rtl"] & {
							background-image: url( "#{ $repeater_images_path }display-rows-rtl.svg" );
						}
					}

					&.display-blocks {
						background-image: url( "#{ $repeater_images_path }display-blocks.svg" );
						margin-inline-end: 0;

						[dir="rtl"] & {
							background-image: url( "#{ $repeater_images_path }display-blocks-rtl.svg" );
						}
					}
				}

				&:checked + label {
					border-color: $color_dark_blue;
					background-color: $color_dark_blue;
					background-size: 180px 120px;
					background-position: -5px -3px;
					box-shadow:
						0 0 0 1px #056aab,
						0 2px 4px rgba( 0, 0, 0, .1 );

					&.display-rows {
						background-image: url( "#{ $repeater_images_path }display-rows-a.svg" );

						[dir="rtl"] & {
							background-image: url( "#{ $repeater_images_path }display-rows-rtl-a.svg" );
						}
					}

					&.display-blocks {
						background-image: url( "#{ $repeater_images_path }display-blocks-a.svg" );

						[dir="rtl"] & {
							background-image: url( "#{ $repeater_images_path }display-blocks-rtl-a.svg" );
						}
					}
				}
			}
		}
	}

	// Form preview area (right side).
	.wpforms-field-repeater {
		& > .label-title {
			font-size: $font_size_ll;
		}

		& > .description {
			margin: 0 0 5px 0;
		}

		// Single column sizes.
		&.size-small {
			.wpforms-layout-display-blocks {
				.wpforms-layout-column {
					&-100 {
						width: calc( #{ $field_size_small } + 20px );
						min-width: 275px;
					}
				}
			}

			.wpforms-layout-display-rows {
				.wpforms-layout-column {
					&-100 {
						width: calc( #{ $field_size_small } + 20px );
						min-width: 275px;

						& + .wpforms-field-repeater-display-rows-buttons {
							inset-inline-start: clamp( 265px, calc( #{ $field_size_small } + 25px ), calc( #{ $field_size_small } + 25px ));
						}
					}
				}
			}
		}

		&,
		&.size-medium {
			.wpforms-layout-display-blocks {
				.wpforms-layout-column {
					&-100 {
						width: calc( #{ $field_size_medium } + 10px );
					}
				}
			}

			.wpforms-layout-display-rows {
				.wpforms-layout-column {
					&-100 {
						width: calc( #{ $field_size_medium } + 10px );

						& + .wpforms-field-repeater-display-rows-buttons {
							inset-inline-start: calc( #{ $field_size_medium } + 15px );
						}
					}
				}
			}
		}

		&.size-large {
			.wpforms-layout-display-blocks {
				.wpforms-layout-column {
					&-100 {
						width: calc( #{ $field_size_large } + 25px );
					}
				}
			}

			.wpforms-layout-display-rows {
				.wpforms-layout-column {
					&-100 {
						width: $field_size_large;
					}

					& + .wpforms-field-repeater-display-rows-buttons {
						inset-inline: auto 15px;
					}
				}
			}
		}

		.wpforms-layout-display-rows {
			.wpforms-layout-column {
				padding-bottom: $spacing_ms;
				min-height: 105px;

				.wpforms-layout-column-placeholder:not(:only-child) {
					display: none;
				}

				&.hide-placeholder {
					.wpforms-layout-column-placeholder {
						display: none;
					}
				}
			}
		}

		// Display Blocks buttons.
		.wpforms-field-repeater-display-blocks-buttons {
			margin-top: $spacing_ms;
			display: flex;
			justify-content: flex-start;
			flex-wrap: nowrap;
			gap: $spacing_s;

			button {
				background: none;
				border: none;
				border-radius: $border_radius_s;
				min-height: 33px;
				max-width: 33%;
				padding: 6px 12px;
				line-height: $font_size_l;
				font-size: $font_size_s;
				font-weight: 400;
				color: $color_lighter_text;
				cursor: pointer;

				@include transition( width, $transition_fast, ease-out );

				i {
					font-size: $font_size_s;
					line-height: $font_size_l;
					margin-inline-end: $spacing_xs;
					height: $font_size_l;
					width: 14px;
				}
			}

			&[data-button-type="buttons_with_icons"] {
				button {
					background: $color_brightest_grey;
				}
			}

			&[data-button-type="buttons"] {
				button {
					background: $color_brightest_grey;
				}

				i {
					display: none;
				}
			}

			&[data-button-type="icons_with_text"] {
				gap: $spacing_m;

				button {
					padding: 0;
					height: auto;
					line-height: $font_size_s;
				}

				i {
					line-height: $font_size_s;
					height: auto;
				}
			}

			&[data-button-type="icons"] {
				button {
					padding: 0;
					height: auto;
					line-height: $font_size_s;
				}

				i {
					line-height: $font_size_m;
					font-size: $font_size_m;
					height: auto;
					margin: 0;
				}

				span {
					display: none;
				}
			}

			&[data-button-type="plain_text"] {
				gap: $spacing_m;

				button {
					padding: 0;
					height: auto;
					line-height: 17px;
				}

				i {
					display: none;
				}
			}
		}

		// Display Rows.
		.wpforms-layout-display-rows {
			position: relative;

			.wpforms-layout-column {
				&:not(.wpforms-layout-column-100) + .wpforms-field-repeater-display-rows-buttons {
					inset-inline: auto 15px;
				}

				&:has(+ .wpforms-field-repeater-display-rows-buttons) {
					margin-inline-end: 60px;
				}
			}

			.wpforms-field-repeater-display-rows-buttons {
				position: absolute;
				display: flex;
				gap: 10px;
				padding: 11px 0 0 0;

				button {
					background: none;
					border: none;
					cursor: pointer;
					color: $color_lighter_text;
					height: 40px;
					margin: 0;
					font-size: $font_size_m;
					width: $font_size_m;
					padding: 0;
				}
			}

			&.hidden-placeholders {
				.wpforms-layout-column {
					padding-bottom: 0;
				}
			}

			.wpforms-field-duplicate {
				display: none;
			}

			.wpforms-alert {
				margin: 15px 10px;
			}
		}
	}
}
