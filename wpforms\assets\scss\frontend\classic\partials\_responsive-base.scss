// Small devices.
@media only screen and (max-width: 600px) {
	.wpforms-container {

		// Avoid horizontal scroll on mobiles.
		// Skip for phone, modern multiple select, radio, layout, repeater, and checkbox field. Otherwise they (dropdowns) will be cropped.
		.wpforms-field:not(.wpforms-field-phone):not(.wpforms-field-select-style-modern):not(.wpforms-field-radio):not(.wpforms-field-checkbox):not(.wpforms-field-layout):not(.wpforms-field-repeater) {
			overflow-x: hidden;
		}

		.wpforms-field {
			padding-right: 1px;
			padding-left: 1px;
		}

		.wpforms-form .wpforms-field > * {
			max-width: 100%;
		}

		// Mobile width override.
		.wpforms-mobile-full {
			width: 100%;
			margin-left: 0;
			float: none;
		}

		// User list column classes.
		.wpforms-checkbox-2-columns,
		.wpforms-multiplechoice-2-columns,
		.wpforms-list-2-columns,
		.wpforms-checkbox-3-columns,
		.wpforms-multiplechoice-3-columns,
		.wpforms-list-3-columns {
			ul li {
				float: none;
				width: 100%;
			}
		}

		// Page indicator.
		.wpforms-page-indicator {
			&.circles {
				.wpforms-page-indicator-page {
					float: none;
					display: block;
					margin: 0 0 10px 0;

					&-number {
						width: 30px;
						height: 30px;
						line-height: 30px;
					}
				}
			}

			&.connector {
				.wpforms-page-indicator-page {
					width: 100% !important;
					padding: 5px 10px;
					&-number {
						display: none;
					}

					&.active {
						font-weight: 700;
					}
				}
			}
		}

		// Preset layout - single line.
		&.inline-fields {
			.wpforms-field-container,
			.wpforms-field {
				display: block;
				width: 100%;
			}

			.wpforms-submit-container {
				width: 100%;
			}
		}
	}
}
