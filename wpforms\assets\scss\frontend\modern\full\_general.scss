// WPForms Modern Full styles.
//
// General styles.
//
// @since 1.8.1

div.wpforms-container-full {
	&:not(:empty) {
		margin: 24px auto;
		padding: var( --wpforms-container-padding );

		background-clip: padding-box;
		background-color: var( --wpforms-background-color );
		background-image: var( --wpforms-background-url );
		background-position: var( --wpforms-background-position );
		background-repeat: var( --wpforms-background-repeat );
		background-size: var( --wpforms-background-size );

		border-style: var( --wpforms-container-border-style );
		border-width: var( --wpforms-container-border-width );
		border-color: var( --wpforms-container-border-color );
		border-radius: var( --wpforms-container-border-radius );

		box-shadow: var( --wpforms-container-shadow-size-box-shadow );
	}

	input,
	label,
	select,
	button,
	textarea {
		margin: 0;
		border: 0;
		padding: 0;
		vertical-align: middle;
		background: none;
		height: auto;
		box-sizing: border-box;
	}

	h1,
	h2,
	h3,
	h4,
	h5,
	h6,
	small,
	sup,
	sub,
	dl,
	dt,
	dd,
	time,
	address,
	pre,
	code,
	blockquote,
	sup,
	sub,
	del {
		font-size: revert;
		font-weight: revert;
		margin: revert;
		padding: revert;
	}

	sup,
	sub {
		position: relative;
	}

	del {
		text-decoration: line-through;
	}

	blockquote {
		padding-left: $spacing_ml;
		border-left: 4px solid;

		p {
			font-size: revert;
			font-weight: revert;
			font-style: italic;
		}
	}

	ul,
	ul li {
		background: none;
		border: 0;
		margin: 0;
		padding: 0;
	}

	ul li {
		margin-bottom: $spacing_m;

		&:last-of-type {
			margin-bottom: 0;
		}
	}

	hr {
		border-top-width: var( --wpforms-field-border-size );
		border-top-style: var( --wpforms-field-border-style );
		border-top-color: var( --wpforms-field-border-color );
		margin: 0.5em auto;
	}

	fieldset {
		min-width: 0;
	}

	.wpforms-form {

		// Form Header area.
		.wpforms-head-container {
			margin: 0;
			padding: 0 0 35px 0;
		}

		.wpforms-title {
			font-weight: 700;
			line-height: 29px;
			color: var( --wpforms-label-color );
		}

		.wpforms-description {
			font-style: normal;
			font-weight: 300;
			font-size: var( --wpforms-label-size-font-size );
			line-height: var( --wpforms-label-size-line-height );
			color: var( --wpforms-label-color );
		}

		// Form Footer area.
		.wpforms-submit-container {
			margin-top: var( --wpforms-button-size-margin-top );
		}

		.wpforms-submit-spinner {
			max-width: 26px;
		}
	}
}

// Test class.
body .wpforms-test {
	outline: 2px solid red !important;
}
