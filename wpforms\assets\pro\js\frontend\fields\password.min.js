let WPFormsPasswordField=window.WPFormsPasswordField||((s,t,o)=>{let i={init(){o(i.ready)},ready(){i.bindEvents()},bindEvents(){o(".wpforms-field-password-input-icon").on("click",i.togglePasswordVisibility),o(s).on("wpformsRepeaterFieldCloneCreated",(s,t)=>{o(t).find(".wpforms-field-password-input-icon").on("click",i.togglePasswordVisibility)}),t.addEventListener("elementor/popup/show",()=>{o(".wpforms-field-password-input-icon").on("click",i.togglePasswordVisibility)})},togglePasswordVisibility(s){s.preventDefault();var s=o(this),t=s.closest(".wpforms-field-password-input"),e=t.find("input"),r="text"===e.attr("type"),i=r?"password":"text",a=t.find(".wpforms-field-password-input-icon-visible"),t=t.find(".wpforms-field-password-input-icon-invisible"),a=(a.toggle(!r),t.toggle(r),e.attr("type",i),s.attr("title")),t=s.attr("data-switch-title");s.attr({title:t,"aria-label":t,"data-switch-title":a})},passwordStrength(s,t){var e,t=o(t);let r=t.closest(".wpforms-field").find(".wpforms-pass-strength-result");return(""!==t.val().trim()||t.hasClass("wpforms-field-required"))&&(r.length||(r=o('<div class="wpforms-pass-strength-result"></div>')).css("max-width",t.css("max-width")),r.removeClass("short bad good strong empty"),s)&&""!==s.trim()?(e=Object.prototype.hasOwnProperty.call(wp.passwordStrength,"userInputDisallowedList")?wp.passwordStrength.userInputDisallowedList():wp.passwordStrength.userInputBlacklist(),e=wp.passwordStrength.meter(s,e,s),(r=i.updateStrengthResultEl(r,e)).insertAfter(t),t.addClass("wpforms-error-pass-strength"),e):(r.remove(),t.removeClass("wpforms-error-pass-strength"),0)},updateStrengthResultEl(s,t){switch(t){case-1:s.addClass("bad").html(pwsL10n.unknown);break;case 2:s.addClass("bad").html(pwsL10n.bad);break;case 3:s.addClass("good").html(pwsL10n.good);break;case 4:s.addClass("strong").html(pwsL10n.strong);break;default:s.addClass("short").html(pwsL10n.short)}return s}};return i})(document,window,jQuery);WPFormsPasswordField.init();