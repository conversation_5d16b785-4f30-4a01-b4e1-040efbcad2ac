/* eslint-disable strict,no-use-before-define */

/**
 * Process the Entries generation.
 *
 * @since 0.5.0
 *
 * @param {Object} event Default form event.
 */
function generateEntries( event ) {
	event.preventDefault();

	// eslint-disable-next-line no-unused-vars
	let [ nonce, form, choicesItem, count, chunkSize ] = event.target;

	nonce = nonce.value;
	form = form.value;
	count = count.value;
	chunkSize = parseInt( chunkSize.value, 10 ) || 100;
	let sentCount = 0;

	/**
	 * Send a request to server.
	 *
	 * @since 0.5.0
	 *
	 * @param {number} size Number of items.
	 *
	 * @return {Promise} Promised AJAX request.
	 */
	function sendToServer( size ) {
		sentCount += size;

		return new Promise(
			( resolve ) => {
				wp.ajax.post( {
					action: 'wpf_generate_entries',
					nonce,
					form,
					count: size,
				} ).then(
					() => {
						generatorUpdateProgressBar( Math.floor( sentCount / ( count / 100 ) ) );
						resolve( true );
					},
				);
			},
		);
	}

	/**
	 * Update the current import progress.
	 *
	 * @since 0.5.0
	 *
	 * @param {number} current Current chunk.
	 *
	 * @return {Promise<number>} Processing promise.
	 */
	function process( current ) {
		if ( current > 0 ) {
			return sendToServer( current >= chunkSize ? chunkSize : current )
				.then( () => process( current - chunkSize ) );
		}

		return Promise.resolve( 1 );
	}

	disableButtons();

	process( count ).then(
		() => {
			enableButtons();
		},
	);
}

/**
 * Process the Forms generation.
 *
 * @since 0.10
 *
 * @param {Object} event Default form event.
 */
function generateForms( event ) {
	event.preventDefault();

	// eslint-disable-next-line no-unused-vars
	let [ nonce, template, choicesItem, count, chunkSize ] = event.target;

	nonce = nonce.value;
	template = template.value;
	count = count.value;
	chunkSize = parseInt( chunkSize.value, 10 ) || 100;
	let sentCount = 0;

	/**
	 * Send a request to server.
	 *
	 * @since 0.10
	 *
	 * @param {number} size Number of items.
	 *
	 * @return {Promise} Promised AJAX request.
	 */
	function sendToServer( size ) {
		sentCount += size;

		return new Promise(
			( resolve ) => {
				wp.ajax.post( {
					action: 'wpf_generate_forms',
					nonce,
					template,
					count,
					chunk: size,
				} ).then(
					() => {
						generatorUpdateProgressBar( Math.floor( sentCount / ( count / 100 ) ) );
						resolve( true );
					},
				);
			},
		);
	}

	/**
	 * Update the current import progress.
	 *
	 * @since 0.5.0
	 *
	 * @param {number} current Currently processed.
	 *
	 * @return {Promise<number>} Processing promise.
	 */
	function process( current ) {
		if ( current > 0 ) {
			return sendToServer( current >= chunkSize ? chunkSize : current )
				.then( () => process( current - chunkSize ) );
		}

		return Promise.resolve( 1 );
	}

	disableButtons();

	process( count ).then(
		() => {
			enableButtons();
		},
	);
}

/**
 * Update the progress bar.
 *
 * @since 0.10
 *
 * @param {number} percent Current progress.
 */
function generatorUpdateProgressBar( percent ) {
	if ( percent === 0 || percent >= 100 ) {
		document.querySelector( '.generator-progress-bar' ).style.display = 'none';
	} else {
		document.querySelector( '.generator-progress-bar' ).style.display = 'block';
		document.querySelector( '.generator-progress-bar' ).style.width = percent + '%';
	}
}

/**
 * Enable buttons when processing is completed.
 *
 * @since 0.5.0
 */
function enableButtons() {
	const generateEntriesForm = document.getElementById( 'generate_entries' );
	if ( generateEntriesForm ) {
		generateEntriesForm.onsubmit = generateEntries;
	}

	const generateFormsForm = document.getElementById( 'generate_forms' );
	if ( generateFormsForm ) {
		generateFormsForm.onsubmit = generateForms;
	}

	const deleteAllButton = document.getElementById( 'delete-all-button' );
	if ( deleteAllButton ) {
		deleteAllButton.onclick = deleteAll;
		deleteAllButton.classList.remove( 'disabled' );
	}

	document.getElementById( 'generate-button' ).classList.remove( 'disabled' );
}

/**
 * Disable buttons when processing is completed.
 *
 * @since 0.5.0
 */
function disableButtons() {
	const generateEntriesForm = document.getElementById( 'generate_entries' );
	if ( generateEntriesForm ) {
		generateEntriesForm.onsubmit = () => {
		};
	}

	const generateFormsForm = document.getElementById( 'generate_forms' );
	if ( generateFormsForm ) {
		generateFormsForm.onsubmit = () => {
		};
	}

	const deleteAllButton = document.getElementById( 'delete-all-button' );
	if ( deleteAllButton ) {
		deleteAllButton.onclick = () => {};
		deleteAllButton.classList.add( 'disabled' );
	}

	document.getElementById( 'generate-button' ).classList.add( 'disabled' );
}

/**
 * Send a request to delete all entries.
 *
 * @since 0.5.0
 */
function deleteAll() {
	disableButtons();

	wp
		.ajax
		.post( {
			action: 'wpf_delete_all_entries',
			nonce: document.querySelector( '[name="nonce"]' ).value,
		} )
		.then(
			() => {
				enableButtons();
			},
		);
}

/**
 * Send a request to reset error handler directories to default.
 *
 * @since 0.22
 */
function resetErrorHandler() {
	// eslint-disable-next-line no-alert
	if ( ! window.confirm( 'Are you sure?' ) ) {
		return;
	}

	wp.ajax.post( {
		action: 'wpf_reset_error_handler',
		nonce: document.querySelector( '[name="nonce"]' ).value,
	} )
		.then(
			() => {
				location.reload();
			},
		);
}

/**
 * Show or hide What's new debug options.
 *
 * @since {VERSION}
 */
function wpfShowSplashScreen() {
	const splashScreenMode = document.getElementById( 'wpforms-splash-screen-mode' ),
		splashVersion = document.getElementById( 'wpforms-splash-screen-version-container' ),
		splashLicense = document.getElementById( 'wpforms-splash-screen-license-container' );

	if ( splashScreenMode.checked ) {
		splashVersion.classList.remove( 'wpforms-hidden' );
		splashLicense.classList.remove( 'wpforms-hidden' );
	} else {
		splashVersion.classList.add( 'wpforms-hidden' );
		splashLicense.classList.add( 'wpforms-hidden' );
	}
}

document.addEventListener(
	'DOMContentLoaded',
	function() {
		const generateEntriesForm = document.getElementById( 'generate_entries' );
		if ( generateEntriesForm ) {
			generateEntriesForm.onsubmit = generateEntries;
			document.getElementById( 'delete-all-button' ).onclick = deleteAll;
		}

		const generateFormsForm = document.getElementById( 'generate_forms' );
		if ( generateFormsForm ) {
			generateFormsForm.onsubmit = generateForms;
		}

		const errorHandlerResetButton = document.getElementById( 'error-handler-reset-button' );
		if ( errorHandlerResetButton ) {
			errorHandlerResetButton.onclick = resetErrorHandler;
		}

		const splashScreenMode = document.getElementById( 'wpforms-splash-screen-mode' );
		if ( splashScreenMode ) {
			splashScreenMode.onchange = wpfShowSplashScreen;
			wpfShowSplashScreen();
		}
	},
);
