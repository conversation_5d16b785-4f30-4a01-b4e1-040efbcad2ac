// Form Builder styles.
//
// MS Windows specific styles.
//
// @since 1.6.8

@import 'partials/common';

textarea,
.wpforms-panel-content-wrap,
.wpforms-panels-toggle,
.smart-tags-list,
.smart-tags-list-display,
#wpforms-panel-fields .wpforms-tab-content,
.wpforms-panel:not(#wpforms-panel-fields) .wpforms-panel-sidebar,
#wpforms-builder-help,
.wpforms-builder-themes-sidebar-content,
.wpforms-scrollbar-compact {
	@include scrollbar( 5px, transparent, $color_scrollbar, 5px );

	&:hover,
	&:focus {
		@include scrollbar( 5px, transparent, $color_scrollbar_hover, 5px );
	}
}

// Fix for the Fields panel.
#wpforms-panel-fields {
	.wpforms-panel-sidebar-content {
		.wpforms-tab-content {
			scrollbar-gutter: stable;

			&.wpforms-add-fields,
			.wpforms-field-option-group-inner,
			.wpforms-field-option-field-title-notice {
				padding-inline-end: 6px;
			}
		}
	}
}

// Fix for the preview content panel.
.wpforms-panel {
	.wpforms-panel-sidebar-content {
		.wpforms-panel-content-wrap {
			scroll-behavior: smooth;
			scrollbar-gutter: stable;
			padding-inline-end: 16px;
		}
	}
}

// Dropdown list thin scrollbar.
.wpforms-builder-dropdown-list ul {
	@include scrollbar( 4px, transparent, $color_scrollbar, 0 );

	&:hover {
		@include scrollbar( 4px, transparent, $color_scrollbar_hover, 0 );
	}
}

// Firefox only.
// We should revert changes for the Fields panel and preview content panel
// since they are not working correctly in Firefox.
@-moz-document url-prefix() {
	// Fix for the fields panel.
	#wpforms-panel-fields {
		.wpforms-panel-sidebar-content {
			.wpforms-tab-content {
				&.wpforms-add-fields,
				.wpforms-field-option-group-inner {
					padding-inline-end: 20px !important;
				}
			}
		}
	}

	// Fix for the preview content panel.
	.wpforms-panel {
		.wpforms-panel-sidebar-content {
			.wpforms-panel-content-wrap {
				padding-inline-end: 30px !important;
			}
		}
	}
}
