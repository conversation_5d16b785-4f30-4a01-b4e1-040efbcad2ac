let WPFormsBuilderPayments=window.WPFormsBuilderPayments||((i,r)=>{let p=r("#wpforms-panel-payments"),l={init(){r("#wpforms-builder").on("wpformsBuilderReady",l.ready)},ready(){l.defaultStates(),l.bind<PERSON>vents(),l.bindHooks()},defaultStates(){r(".wpforms-panel-content-section-payment-toggle input").each(l.toggleContent),r(".wpforms-panel-content-section-payment-plan-name input").each(l.checkPlanName)},bindEvents(){p.on("click",".wpforms-panel-content-section-payment-toggle input",l.toggleContent).on("click",".wpforms-panel-content-section-payment-plan-head-buttons-toggle",l.togglePlan).on("click",".wpforms-panel-content-section-payment-button-add-plan",l.addPlan).on("input",".wpforms-panel-content-section-payment-plan-name input",l.renamePlan).on("focusout",".wpforms-panel-content-section-payment-plan-name input",l.checkPlanName).on("click",".wpforms-panel-content-section-payment-plan-head-buttons-delete",l.deletePlan).on("click",".wpforms-panel-content-section-payment-toggle-recurring input",l.addEmptyPlan).on("change",WPFormsBuilder.getPaymentsTogglesSelector(),l.onPaymentTogglesChange).on("click",".wpforms-panel-content-section-payment-toggle-one-time input",function(){l.noteOneTimePaymentsDisabled(r(this))}),r(i).on("wpformsBeforeSave",l.showNoticesAfterFormSaved).on("wpformsRemoveConditionalLogicRules",function(e,n){l.disableOneTimePayments(n)})},bindHooks(){wp.hooks.addFilter("wpforms.Builder.entryRequirement","wpforms/payments",l.entryRequirementHandler)},toggleContent(){var e=r(this),n=e.closest(".wpforms-panel-content-section-payment"),t=n.find(".wpforms-panel-content-section-payment-toggled-body"),e=e.prop("checked")&&!r("#wpforms-panel-field-settings-disable_entries").prop("checked");t.toggle(e),n.toggleClass("wpforms-panel-content-section-payment-open",e)},addPlan(){if(!r(this).hasClass("education-modal")){let t=l.getProviderSection(r(this));r.confirm({title:!1,content:wpforms_builder.payment_plan_prompt+'<input autofocus="" type="text" id="wpforms-builder-payment-plan-name" placeholder="'+wpforms_builder.payment_plan_prompt_placeholder+'"><p class="error">'+wpforms_builder.payment_error_name+"</p>",backgroundDismiss:!1,closeIcon:!1,icon:"fa fa-info-circle",type:"blue",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){var e=this.$content.find("#wpforms-builder-payment-plan-name").val().trim(),n=this.$content.find(".error");if(!e)return n.show(),!1;l.createNewPlan(e,t)}},cancel:{text:wpforms_builder.cancel}}})}},createNewPlan(e,n){var t=n.find(".wpforms-panel-content-section-payment-recurring"),o=t.find(".wpforms-panel-content-section-payment-plan").last(),o=o.length?o.data("plan-id")+1:0,a=wp.template("wpforms-builder-payments-"+n.data("provider")+"-clone");a&&(t.append(a({index:o}).replaceAll("-dataindex-",`-${o}-`).replaceAll("_dataindex-",`_${o}-`).replaceAll("_dataindex]",`_${o}]`)),(t=(a=t.find(".wpforms-panel-content-section-payment-plan").last()).find(".wpforms-panel-content-section-payment-plan-name input")).val(e||l.getDefaultPlanName(o+1)),t.trigger("input"),r(i).trigger("wpformsFieldUpdate",wpf.getFields()),p.trigger("wpformsPaymentsPlanCreated",a,n.data("provider")),wpf.initTooltips())},addEmptyPlan(){var e=l.getProviderSection(r(this));r(this).prop("checked")&&!e.find(".wpforms-panel-content-section-payment-plan").length&&l.createNewPlan("",e)},togglePlan(){var e=r(this).closest(".wpforms-panel-content-section-payment-plan"),n=e.find(".wpforms-panel-content-section-payment-plan-body"),e=e.find(".wpforms-panel-content-section-payment-plan-head-buttons-toggle");e.toggleClass("fa-chevron-circle-up fa-chevron-circle-down"),n.toggle(e.hasClass("fa-chevron-circle-down"))},renamePlan(){var e,n=r(this),t=n.closest(".wpforms-panel-content-section-payment-plan"),o=t.find(".wpforms-panel-content-section-payment-plan-head-title");n.val()?(e=l.getProviderSection(n),o.html(n.val()),p.trigger("wpformsPaymentsPlanRenamed",n.val(),t,e.data("provider"))):o.html("")},checkPlanName(){var e=r(this),n=e.closest(".wpforms-panel-content-section-payment-plan"),t=n.find(".wpforms-panel-content-section-payment-plan-head-title");e.val()?t.html(e.val()):n.length?(n=l.getDefaultPlanName(n.data("plan-id")+1),t.html(n),e.val(n)):t.html("")},getDefaultPlanName(e){return wpforms_builder.payment_plan_placeholder.replace("{id}",e)},deletePlan(){let n=r(this),t=l.getProviderSection(n);if("stripe"!==t.data("provider")||wpforms_builder_stripe.is_pro){let e=n.closest(".wpforms-panel-content-section-payment-plan");r.alert({title:wpforms_builder.heads_up,content:wpforms_builder.payment_plan_confirm,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){e.remove(),p.trigger("wpformsPaymentsPlanDeleted",e,t.data("provider")),t.find(".wpforms-panel-content-section-payment-plan").length||t.find(".wpforms-panel-content-section-payment-toggle-recurring input").trigger("click")}},cancel:{text:wpforms_builder.cancel}}})}},disableOneTimePayments(e){e.prop("checked")||l.noteOneTimePaymentsDisabled(l.getProviderSection(e).find(".wpforms-panel-content-section-payment-toggle-one-time input"))},isAllowedOneTimePayments(e){var n;return!e.closest(".wpforms-panel-content-section-payment").find(".wpforms-panel-content-section-payment-toggle-recurring input").prop("checked")||!!(n=e.find(".wpforms-panel-content-section-payment-plan")).length&&(e=e.find(".wpforms-conditional-groups"),n.length===e.length)&&l.isRecurringConditionalsValid(n)},isRecurringConditionalsValid(e){let t=!1;return e.find(".wpforms-conditional-block").each(function(){var e=r(this);if(!e.find(".wpforms-conditionals-enable-toggle input").prop("checked"))return!(t=!0);e.find(".wpforms-conditional-row").each(function(){var e=r(this),n=e.find(".wpforms-conditional-value");if(!e.find(".wpforms-conditional-field").val()||!n.prop("disabled")&&!n.val())return!(t=!0)})}),!t},showNoticesAfterFormSaved(){var e=p.find(".wpforms-panel-content-section");e.length&&e.each(function(){l.noteOneTimePaymentsDisabled(r(this).find(".wpforms-panel-content-section-payment-toggle-one-time input"))})},noteOneTimePaymentsDisabled(e){var n=l.getProviderSection(e),t=n.find(".wpforms-panel-content-section-payment-recurring");e.prop("checked")&&!l.isAllowedOneTimePayments(t)&&(n.find(".wpforms-panel-content-section-payment-one-time").hide(),e.prop("checked",!1),l.showPopupDisabledOneTimePayments(n.find(".wpforms-panel-content-section-title").text().trim()))},showPopupDisabledOneTimePayments(e){r.alert({title:wpforms_builder.heads_up,content:wpforms_builder.payment_one_time_payments_disabled.replaceAll("{provider}",e),icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},getProviderSection(e){return e.closest(".wpforms-panel-content-section")},onPaymentTogglesChange(){var e=r(this),n=e.attr("id").replace(/wpforms-panel-field-|-enable|_one_time|_recurring/gi,""),t=r(`.wpforms-panel-content-section-notifications [id*="-${n}-wrap"]`);e.prop("checked")||r(`#wpforms-panel-field-${n}-enable_one_time`).prop("checked")||r(`#wpforms-panel-field-${n}-enable_recurring`).prop("checked")?r("#wpforms-panel-field-settings-disable_entries").prop("checked")?(r.confirm({title:wpforms_builder.heads_up,content:wpforms_builder.payments_entries_off,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}}),e.prop("checked",!1)):t.removeClass("wpforms-hidden"):(t.addClass("wpforms-hidden"),t.find(`input[id*="-${n}"]`).prop("checked",!1))},entryRequirementHandler(o){var e=WPFormsBuilder.isPaymentsEnabled();if(o.required=o?.required||e,e){e=l.getCheckedPaymentToggles();e.forEach(function(e){e=(e=e).closest(".wpforms-panel-content-section"),n=e.data("provider"),e=e.data("provider-name"),t=wpf.updateQueryString("view","payments");var{providerId:n,dependency:t}={providerId:n,dependency:{href:wpf.updateQueryString("section",n,t),text:e}};o.dependencies[n]=t})}return o},getCheckedPaymentToggles(){let n=[];return r(WPFormsBuilder.getPaymentsTogglesSelector()).each(function(){var e=r(this);e.prop("checked")&&n.push(e)}),n}};return l})(document,(window,jQuery));WPFormsBuilderPayments.init();