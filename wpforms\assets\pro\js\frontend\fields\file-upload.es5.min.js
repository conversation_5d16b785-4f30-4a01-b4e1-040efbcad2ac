!function t(n,i,a){function s(o,e){if(!i[o]){if(!n[o]){var r="function"==typeof require&&require;if(!e&&r)return r(o,!0);if(d)return d(o,!0);throw new Error("Cannot find module '"+o+"'")}e=i[o]={exports:{}};n[o][0].call(e.exports,function(e){var r=n[o][1][e];return s(r||e)},e,e.exports,t,n,i,a)}return i[o].exports}for(var d="function"==typeof require&&require,e=0;e<a.length;e++)s(a[e]);return s}({1:[function(e,r,o){function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(r){var e,o;null!==E?setTimeout(r):(e=(()=>{for(var e="",r=0;r<O.payloadSize;++r)e+=String.fromCharCode(Math.round(36*Math.random()+64));return e})(),o=new Date,wp.ajax.post({action:"wpforms_file_upload_speed_test",data:e}).then(function(){var e=new Date-o;E=e>=O.maxTime,r()}).fail(function(){E=!0,r()}))}function i(e){return function(){e.find(".wpforms-uploading-in-progress-alert").length||e.find(".wpforms-submit-container").before('<div class="wpforms-error-alert wpforms-uploading-in-progress-alert">\n\t\t\t\t\t\t'.concat(window.wpforms_file_upload.loading_message,"\n\t\t\t\t\t</div>"))}}function a(e){return 0<e.loading||0<e.getFilesWithStatus("error").length}function s(){var r=!1;return window.wpforms.dropzones.some(function(e){if(a(e))return r=!0}),r}function d(e){var r=e.find(".wpforms-submit"),o=e.find(".wpforms-page-next:visible"),t=i(e);(r=0!==e.find(".wpforms-page-indicator").length&&0!==o.length?o:r).prop("disabled",!0),WPFormsUtils.triggerEvent(e,"wpformsFormSubmitButtonDisable",[e,r]),e.find(".wpforms-submit-overlay").length||"submit"!==r.attr("type")||(r.parent().addClass("wpforms-submit-overlay-container"),r.parent().append('<div class="wpforms-submit-overlay"></div>'),e.find(".wpforms-submit-overlay").css({width:"".concat(r.outerWidth(),"px"),height:"".concat(r.parent().outerHeight(),"px")}),e.find(".wpforms-submit-overlay").on("click",t))}function f(e){var r=jQuery(e.element).closest("form"),o=r.find(".wpforms-submit"),t=r.find(".wpforms-page-next:visible"),n=i(r),e=a(e);0!==r.find(".wpforms-page-indicator").length&&0!==t.length&&(o=t),e!==(Boolean(o.prop("disabled"))||o.hasClass("wpforms-disabled"))&&(e?d(r):s()||(o.prop("disabled",!1),WPFormsUtils.triggerEvent(r,"wpformsFormSubmitButtonRestore",[r,o]),r.find(".wpforms-submit-overlay").off("click",n),r.find(".wpforms-submit-overlay").remove(),o.parent().removeClass("wpforms-submit-overlay-container"),r.find(".wpforms-uploading-in-progress-alert").length&&r.find(".wpforms-uploading-in-progress-alert").remove()))}function p(e){try{return JSON.parse(e)}catch(e){return!1}}function t(e){return 0<e.length}function u(e){return e}function m(e){return e.chunkResponse||e.xhr}function c(e){return"string"==typeof e?e:e.responseText}function w(e){return e.data}function g(e){return jQuery(e.element).parents(".wpforms-field-file-upload").find("input[name="+e.dataTransfer.name+"]")}function h(e){var r,o=g(e);o.val((r=e.files,r=r.map(m).filter(u).map(c).filter(t).map(p).filter(u).map(w),j[(e=e).dataTransfer.formId]&&j[e.dataTransfer.formId][e.dataTransfer.fieldId]?(r.push.apply(r,j[e.dataTransfer.formId][e.dataTransfer.fieldId]),JSON.stringify(r)):r.length?JSON.stringify(r):"")).trigger("input"),void 0!==jQuery.fn.valid&&o.valid()}function y(e,r){var o;e.isErrorNotUploadedDisplayed||((o=document.createElement("span")).innerText=r.toString(),o.setAttribute("data-dz-errormessage",""),e.previewElement.querySelector(".dz-error-message").appendChild(o))}function v(t){return function r(o){o.retries||(o.retries=0),"error"!==o.status&&(wp.ajax.post(jQuery.extend({action:"wpforms_file_chunks_uploaded",form_id:t.dataTransfer.formId,field_id:t.dataTransfer.fieldId,name:o.name},t.options.params.call(t,null,null,{file:o,index:0}))).then(function(e){o.chunkResponse=JSON.stringify({data:e}),t.loading=t.loading||0,t.loading--,t.loading=Math.max(t.loading,0),f(t),h(t)}).fail(function(e){e.responseJSON&&!1===e.responseJSON.success&&e.responseJSON.data?y(o,e.responseJSON.data):(o.retries++,3===o.retries?y(o,window.wpforms_file_upload.errors.file_not_uploaded):setTimeout(function(){r(o)},5e3*o.retries))}),t.processQueue())}}function _(e){setTimeout(function(){e.files.filter(function(e){return e.accepted}).length>=e.options.maxFiles?e.element.querySelector(".dz-message").classList.add("hide"):e.element.querySelector(".dz-message").classList.remove("hide")},0)}function x(t){return function(e){var r,o;e.size>=t.dataTransfer.postMaxSize?(r=e,o=t,setTimeout(function(){var e;r.size>=o.dataTransfer.postMaxSize&&(e=window.wpforms_file_upload.errors.post_max_size,r.isErrorNotUploadedDisplayed||(r.isErrorNotUploadedDisplayed=!0,e=window.wpforms_file_upload.errors.file_not_uploaded+" "+e,y(r,e)))},1)):n(function(){var n,i;n=t,i=e,wp.ajax.post(jQuery.extend({action:"wpforms_upload_chunk_init",form_id:n.dataTransfer.formId,field_id:n.dataTransfer.fieldId,name:i.name,slow:E},n.options.params.call(n,null,null,{file:i,index:0}))).then(function(e){for(var r in e)n.options[r]=e[r];e.dzchunksize&&(n.options.chunkSize=parseInt(e.dzchunksize,10),i.upload.totalChunkCount=Math.ceil(i.size/n.options.chunkSize)),n.processQueue()}).fail(function(e){var r,o,t;i.status="error",i.xhr||(o=(r=n.element.closest(".wpforms-field")).querySelector(".dropzone-input"),t=window.wpforms_file_upload.errors.file_not_uploaded+" "+window.wpforms_file_upload.errors.default_error,i.previewElement.classList.add("dz-processing","dz-error","dz-complete"),o.classList.add("wpforms-error"),r.classList.add("wpforms-has-error"),y(i,t)),n.processQueue()})}),t.loading=t.loading||0,t.loading++,f(t),_(t)}}function z(e,r){wp.ajax.post({action:"wpforms_remove_file",file:e,form_id:r.dataTransfer.formId,field_id:r.dataTransfer.fieldId})}function S(e){var r,o,t,n,i,a,s,d;return e.dropzone||(r=parseInt(e.dataset.formId,10),o=parseInt(e.dataset.fieldId,10)||0,i=parseInt(e.dataset.maxFileNumber,10),t=e.dataset.extensions.split(",").map(function(e){return"."+e}).join(","),(t=new window.Dropzone(e,{url:window.wpforms_file_upload.url,addRemoveLinks:!0,chunking:!0,forceChunking:!0,retryChunks:!0,chunkSize:parseInt(e.dataset.fileChunkSize,10),paramName:e.dataset.inputName,parallelChunkUploads:!!(e.dataset.parallelUploads||"").match(/^true$/i),parallelUploads:parseInt(e.dataset.maxParallelUploads,10),autoProcessQueue:!1,maxFilesize:(parseInt(e.dataset.maxSize,10)/1048576).toFixed(2),maxFiles:i,acceptedFiles:t,dictMaxFilesExceeded:window.wpforms_file_upload.errors.file_limit.replace("{fileLimit}",i),dictInvalidFileType:window.wpforms_file_upload.errors.file_extension,dictFileTooBig:window.wpforms_file_upload.errors.file_size})).dataTransfer={postMaxSize:e.dataset.maxSize,name:e.dataset.inputName,formId:r,fieldId:o},(i=p(g(n=t).val()))&&i.length&&(j[n.dataTransfer.formId]=[],j[n.dataTransfer.formId][n.dataTransfer.fieldId]=JSON.parse(JSON.stringify(i)),i.forEach(function(e,r){e.isDefault=!0,e.index=r,e.type.match(/image.*/)?n.displayExistingFile(e,e.url):(n.emit("addedfile",e),n.emit("complete",e))}),n.options.maxFiles=n.options.maxFiles-i.length),t.on("sending",(a={action:"wpforms_upload_chunk",form_id:r,field_id:o},function(e,r,o){e.size>this.dataTransfer.postMaxSize?(r.send=function(){},e.accepted=!1,e.processing=!1,e.status="rejected",e.previewElement.classList.add("dz-error"),e.previewElement.classList.add("dz-complete")):Object.keys(a).forEach(function(e){o.append(e,a[e])})})),t.on("addedfile",x(t)),t.on("removedfile",(s=t,function(e){_(s);var r=e.chunkResponse||(e.xhr||{}).responseText;r&&(r=p(r))&&r.data&&r.data.file&&z(r.data.file,s),Object.prototype.hasOwnProperty.call(e,"isDefault")&&e.isDefault&&(j[s.dataTransfer.formId][s.dataTransfer.fieldId].splice(e.index,1),s.options.maxFiles++,z(e.file,s)),h(s),s.loading=s.loading||0,s.loading--,s.loading=Math.max(s.loading,0),f(s),0===s.element.querySelectorAll(".dz-preview.dz-error").length&&(s.element.classList.remove("wpforms-error"),s.element.closest(".wpforms-field").classList.remove("wpforms-has-error"))})),t.on("complete",v(t)),t.on("error",(d=t,function(e,r){e.isErrorNotUploadedDisplayed||(r="0"!==(r="object"===l(r)?Object.prototype.hasOwnProperty.call(r,"data")&&"string"==typeof r.data?r.data:"":r)?r:"",e.isErrorNotUploadedDisplayed=!0,e.previewElement.querySelectorAll("[data-dz-errormessage]")[0].textContent=window.wpforms_file_upload.errors.file_not_uploaded+" "+r,d.element.classList.add("wpforms-error"),d.element.closest(".wpforms-field").classList.add("wpforms-has-error"))})),t)}function b(){C(this).prev(".wpforms-uploader").addClass("wpforms-focus")}function T(){C(this).prev(".wpforms-uploader").removeClass("wpforms-focus")}function I(e){e.preventDefault(),13===e.keyCode&&C(this).prev(".wpforms-uploader").trigger("click")}function k(){C(this).next(".dropzone-input").trigger("focus")}function F(e,r){s()&&d(r)}function N(){window.wpforms=window.wpforms||{},window.wpforms.dropzones=[].slice.call(document.querySelectorAll(".wpforms-uploader")).map(S),C(".dropzone-input").on("focus",b).on("blur",T).on("keypress",I),C(".wpforms-uploader").on("click",k),C("form.wpforms-form").on("wpformsCombinedUploadsSizeOk",F)}var C,E,j,O,D;C=jQuery,E=null,j=[],O={maxTime:3e3,payloadSize:102400},(D={init:function(){C(document).on("wpformsReady",N)}}).init(),window.wpformsModernFileUpload=D},{}]},{},[1]);