<?php

namespace WPF\E2EDashboard;

/**
 * Class DBHelpers
 * Collection of database helper methods for various operations.
 *
 * @since 1.0
 *
 * @codingStandardsIgnoreFile
 */
class DBHelpers {

	/**
	 * Resets the current database by dropping all tables.
	 *
	 * @since 1.0
	 *
	 * @param bool $confirm If true, the reset will proceed; otherwise, an exception is thrown.
	 *
	 * @return array An array of messages detailing which tables were dropped.
	 * @throws \Exception If confirmation is not provided or no tables are found.
	 */
	public function resetDatabase( $confirm = false ) {

		if ( ! $confirm ) {

			throw new \Exception( "Database reset requires explicit confirmation." );
		}

		global $wpdb;

		$messages = [];

		// Retrieve a list of all tables in the current database.
		$tables = $wpdb->get_col( 'SHOW TABLES' );

		if ( empty( $tables ) ) {

			throw new \Exception( "No tables found in the database." );
		}

		// Disable foreign key checks.
		$wpdb->query( 'SET FOREIGN_KEY_CHECKS = 0' );

		// Iterate over each table and drop it.
		foreach ( $tables as $table ) {

			$result = $wpdb->query( "DROP TABLE IF EXISTS `$table`" );

			if ( $result === false ) {

				$messages[] = "Could not drop table: $table";
			} else {

				$messages[] = "Dropped table: $table";
			}
		}

		// Re-enable foreign key checks.
		$wpdb->query( 'SET FOREIGN_KEY_CHECKS = 1' );

		$messages[] = "Database reset complete.";

		return $messages;
	}

	/**
	 * Imports a new database from a SQL file.
	 *
	 * This method reads the SQL file, splits it into individual queries,
	 * and executes each query while maintaining transaction safety.
	 *
	 * @since 1.0
	 *
	 * @param string $sqlFile Path to the SQL file to import.
	 * @param bool   $confirm If true, the import will proceed; otherwise, an exception is thrown.
	 *
	 * @return array An array of messages detailing the import process.
	 * @throws \Exception If confirmation is not provided, file cannot be read, or errors occur during execution.
	 */
	public function importDatabase( $sqlFile, $confirm = false ) {

		if ( ! $confirm ) {

			throw new \Exception( "Database import requires explicit confirmation." );
		}

		if ( ! file_exists( $sqlFile ) || ! is_readable( $sqlFile ) ) {

			throw new \Exception( "SQL file does not exist or is not readable: $sqlFile" );
		}

		$sqlContent = file_get_contents( $sqlFile );

		if ( false === $sqlContent ) {

			throw new \Exception( "Failed to read SQL file: $sqlFile" );
		}

		global $wpdb;

		$messages = [];

		// Start transaction
		$wpdb->query( 'START TRANSACTION' );

		try {
			// Split the SQL file into individual queries
			$queries = $this->splitSqlQueries( $sqlContent );

			foreach ( $queries as $query ) {

				if ( ! empty( $query ) ) {

					$result = $wpdb->query( $query );

					if ( false === $result ) {

						throw new \Exception( "Error executing query: " . $wpdb->last_error );
					}

					$messages[] = "Successfully executed query: " . substr( $query, 0, 50 ) . '...';
				}
			}

			// If we got here, commit the transaction
			$wpdb->query( 'COMMIT' );
			$messages[] = "Database import complete.";

		} catch ( \Exception $e ) {

			// If anything went wrong, rollback the transaction
			$wpdb->query( 'ROLLBACK' );
			throw $e;
		}

		return $messages;
	}

	/**
	 * Split SQL content into individual queries.
	 *
	 * This method handles complex SQL files including:
	 * - Delimiter changes
	 * - Comments
	 * - Multi-line queries
	 * - Quoted strings containing semicolons
	 *
	 * @since 1.0
	 *
	 * @param string $sql SQL content to split.
	 *
	 * @return array Array of individual SQL queries.
	 */
	private function splitSqlQueries( $sql ) {

		$queries       = [];
		$current_query = '';
		$delimiter     = ';';
		$in_quote      = false;
		$quote_char    = '';
		$in_comment    = false;
		$lines         = explode( "\n", $sql );

		foreach ( $lines as $line ) {

			$line = trim( $line );

			// Skip empty lines.
			if ( empty( $line ) ) {

				continue;
			}

			// Handle DELIMITER changes
			if ( preg_match( '/^DELIMITER\s+(.+)$/i', $line, $matches ) ) {

				if ( ! empty( $current_query ) ) {

					$queries[]     = $current_query;
					$current_query = '';
				}
				$delimiter = $matches[1];

				continue;
			}

			// Add the line to the current query.
			$current_query .= $line . "\n";

			// Check if this line ends with the delimiter
			if ( substr( $line, -strlen( $delimiter ) ) === $delimiter ) {

				// Remove the delimiter from the end
				$current_query = substr( $current_query, 0, -strlen( $delimiter ) - 1 );

				if ( ! empty( $current_query ) ) {

					$queries[] = $current_query;
				}
				$current_query = '';
			}
		}

		// Add any remaining query
		if ( ! empty( $current_query ) ) {

			$queries[] = $current_query;
		}

		return array_filter( array_map( 'trim', $queries ) );
	}

	/**
	 * Get list of all tables in the database.
	 *
	 * @since 1.0
	 *
	 * @param string $prefix Optional. Filter tables by prefix.
	 *
	 * @return array List of table names.
	 */
	public function getTables( $prefix = '' ) {

		global $wpdb;

		if ( $prefix ) {

			return $wpdb->get_col( $wpdb->prepare( "SHOW TABLES LIKE %s", $prefix . '%' ) );
		}

		return $wpdb->get_col( 'SHOW TABLES' );
	}

	/**
	 * Check if a table exists in the database.
	 *
	 * @since 1.0
	 *
	 * @param string $table_name The name of the table to check.
	 *
	 * @return bool True if table exists, false otherwise.
	 */
	public function tableExists( $table_name ) {

		global $wpdb;

		$result = $wpdb->get_var( $wpdb->prepare(
			"SHOW TABLES LIKE %s",
			$table_name
		) );

		return ! empty( $result );
	}

	/**
	 * Get the size of the database or specific table.
	 *
	 * @since 1.0
	 *
	 * @param string $table_name Optional. Get size of specific table.
	 *
	 * @return array Array containing size in bytes and formatted size.
	 */
	public function getDatabaseSize( $table_name = '' ) {

		global $wpdb;

		if ( $table_name ) {

			$query = $wpdb->prepare(
				"SELECT 
                    data_length + index_length as size,
                    table_rows
                FROM information_schema.TABLES
                WHERE table_schema = %s
                AND table_name = %s",
				DB_NAME,
				$table_name
			);
		} else {

			$query = $wpdb->prepare(
				"SELECT SUM(data_length + index_length) as size
                FROM information_schema.TABLES
                WHERE table_schema = %s",
				DB_NAME
			);
		}

		$result = $wpdb->get_row( $query );
		$size   = ! empty( $result->size ) ? $result->size : 0;

		return [
			'bytes'     => $size,
			'formatted' => $this->formatBytes( $size ),
			'rows'      => isset( $result->table_rows ) ? $result->table_rows : null,
		];
	}

	/**
	 * Format bytes to human readable format.
	 *
	 * @since 1.0
	 *
	 * @param int $bytes     The size in bytes.
	 * @param int $precision Optional. Number of decimal places.
	 *
	 * @return string Formatted size string.
	 */
	private function formatBytes( $bytes, $precision = 2 ) {

		$units = [ 'B', 'KB', 'MB', 'GB', 'TB' ];

		$bytes = max( $bytes, 0 );
		$pow   = floor( ( $bytes ? log( $bytes ) : 0 ) / log( 1024 ) );
		$pow   = min( $pow, count( $units ) - 1 );

		$bytes /= pow( 1024, $pow );

		return round( $bytes, $precision ) . ' ' . $units[ $pow ];
	}

	/**
	 * Replace URLs in the database.
	 *
	 * This method performs a search and replace operation for URLs in:
	 * - Posts (content and guid)
	 * - Options
	 * - Post meta
	 *
	 * @since 1.0
	 *
	 * @param string $old_url The URL to replace.
	 * @param string $new_url The replacement URL.
	 *
	 * @return array Array of messages detailing the replacement process.
	 */
	public function replaceUrls( $old_url, $new_url ) {

		global $wpdb;

		$messages = [];

		if ( $old_url === $new_url ) {
			$messages[] = "Old and new URLs are identical, skipping replacement.";

			return $messages;
		}

		// Start transaction
		$wpdb->query( 'START TRANSACTION' );

		try {

			// Update URLs in posts
			$posts_result = $wpdb->query( $wpdb->prepare(
				"UPDATE {$wpdb->posts} 
				SET post_content = REPLACE(post_content, %s, %s),
					guid = REPLACE(guid, %s, %s)
				WHERE post_content LIKE %s OR guid LIKE %s",
				$old_url,
				$new_url,
				$old_url,
				$new_url,
				'%' . $wpdb->esc_like( $old_url ) . '%',
				'%' . $wpdb->esc_like( $old_url ) . '%'
			) );

			$messages[] = sprintf( "Updated %d posts with new URL.", $posts_result );

			// Update URLs in options
			$options_result = $wpdb->query( $wpdb->prepare(
				"UPDATE {$wpdb->options} 
				SET option_value = REPLACE(option_value, %s, %s)
				WHERE option_value LIKE %s",
				$old_url,
				$new_url,
				'%' . $wpdb->esc_like( $old_url ) . '%'
			) );

			$messages[] = sprintf( "Updated %d options with new URL.", $options_result );

			// Update URLs in postmeta
			$postmeta_result = $wpdb->query( $wpdb->prepare(
				"UPDATE {$wpdb->postmeta} 
				SET meta_value = REPLACE(meta_value, %s, %s)
				WHERE meta_value LIKE %s",
				$old_url,
				$new_url,
				'%' . $wpdb->esc_like( $old_url ) . '%'
			) );

			$messages[] = sprintf( "Updated %d post meta entries with new URL.", $postmeta_result );

			// Commit the transaction
			$wpdb->query( 'COMMIT' );
			$messages[] = "URL replacement completed successfully.";

		} catch ( \Exception $e ) {

			// If anything went wrong, rollback the transaction
			$wpdb->query( 'ROLLBACK' );
			throw new \Exception( "Error replacing URLs: " . $e->getMessage() );
		}

		return $messages;
	}

	/**
	 * Extract URLs from SQL content.
	 *
	 * @since 1.0
	 *
	 * @param string $sql_content The SQL content to search for URLs.
	 *
	 * @return array Array of unique URLs found in the content.
	 */
	public function extractUrls( $sql_content ) {

		preg_match_all( '#https?://[^\s\'"]+#', $sql_content, $matches );

		return array_unique( $matches[0] );
	}
} 