<?php

namespace WPForms\DevTools\FormTemplates;

use WPF<PERSON>_Template;

/**
 * WPForms DevTools template: All Payments with CL.
 *
 * @since 0.24
 */
class AllPaymentsWithCl extends WPForms_Template {

	/**
	 * Primary class constructor.
	 *
	 * @since 0.24
	 */
	public function init() {

		// Template name.
		$this->name = 'All Payments with CL';

		// Template slug.
		$this->slug = 'all_payments_with_cl';

		// Template description.
		$this->description = '';

		// Template field and settings.
		// phpcs:disable WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned
		$this->data = [
			'fields'     => [
				3  => [
					'id'       => '3',
					'type'     => 'name',
					'label'    => 'Name',
					'format'   => 'first-last',
					'required' => '1',
					'size'     => 'medium',
				],
				2  => [
					'id'            => '2',
					'type'          => 'email',
					'label'         => 'Email',
					'required'      => '1',
					'size'          => 'medium',
					'default_value' => false,
				],
				24 => [
					'id'                   => '24',
					'type'                 => 'radio',
					'label'                => 'Payment Method',
					'choices'              => [
						1 => [
							'label'      => 'Stripe',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/all-payments-with-cl/stripe.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'PPC',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/all-payments-with-cl/ppc.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Square',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/all-payments-with-cl/square.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Authorize.Net',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/all-payments-with-cl/authorizenet.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
				],
				21 => [
					'id'                   => '21',
					'type'                 => 'checkbox',
					'label'                => '+ Add a Subscription',
					'choices'              => [
						1 => [
							'label'      => 'Yes',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'conditional_logic'    => '1',
					'conditional_type'     => 'show',
					'conditionals'         => [
						0 => [
							0 => [
								'field'    => '24',
								'operator' => '==',
								'value'    => '1',
							],
						],
					],
				],
				22 => [
					'id'                   => '22',
					'type'                 => 'checkbox',
					'label'                => '+ Add a Subscription',
					'choices'              => [
						1 => [
							'label'      => 'Yes',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'conditional_logic'    => '1',
					'conditional_type'     => 'show',
					'conditionals'         => [
						0 => [
							0 => [
								'field'    => '24',
								'operator' => '==',
								'value'    => '2',
							],
						],
					],
				],
				23 => [
					'id'                   => '23',
					'type'                 => 'checkbox',
					'label'                => '+ Add a Subscription',
					'choices'              => [
						1 => [
							'label'      => 'Yes',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'conditional_logic'    => '1',
					'conditional_type'     => 'show',
					'conditionals'         => [
						0 => [
							0 => [
								'field'    => '24',
								'operator' => '==',
								'value'    => '4',
							],
						],
					],
				],
				13 => [
					'id'              => '13',
					'type'            => 'payment-single',
					'label'           => 'Single Item: With Enabled Quantity',
					'price'           => '99.99',
					'format'          => 'single',
					'min_price'       => '10.00',
					'enable_quantity' => '1',
					'min_quantity'    => '0',
					'max_quantity'    => '20',
					'size'            => 'medium',
					'price_label'     => 'Price: {price}',
				],
				18 => [
					'id'           => '18',
					'type'         => 'payment-single',
					'label'        => 'Single Item: User Defined',
					'price'        => '99.99',
					'format'       => 'user',
					'min_price'    => '10.00',
					'min_quantity' => '0',
					'max_quantity' => '20',
					'size'         => 'medium',
					'price_label'  => 'Price: {price}',
				],
				14 => [
					'id'                      => '14',
					'type'                    => 'payment-select',
					'label'                   => 'Dropdown Items: Modern With Enabled Quantity',
					'choices'                 => [
						1 => [
							'label'      => 'Red',
							'value'      => '111.11',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Yellow',
							'value'      => '250.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Green',
							'value'      => '5,499.99',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'enable_quantity'         => '1',
					'min_quantity'            => '0',
					'max_quantity'            => '10',
					'style'                   => 'modern',
					'size'                    => 'medium',
				],
				16 => [
					'id'                      => '16',
					'type'                    => 'payment-select',
					'label'                   => 'Dropdown Items: Classic',
					'choices'                 => [
						1 => [
							'label'      => 'Red',
							'value'      => '99.99',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Yellow',
							'value'      => '250.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Green',
							'value'      => '5,499.99',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'min_quantity'            => '0',
					'max_quantity'            => '10',
					'style'                   => 'classic',
					'size'                    => 'medium',
				],
				6  => [
					'id'          => '6',
					'type'        => 'payment-coupon',
					'label'       => 'Coupon',
					'button_text' => 'Apply',
				],
				8  => [
					'id'    => '8',
					'type'  => 'payment-total',
					'label' => 'Total',
					'size'  => 'medium',
				],
				4  => [
					'id'                => '4',
					'type'              => 'stripe-credit-card',
					'label'             => 'Stripe Credit Card',
					'required'          => '1',
					'size'              => 'medium',
					'sublabel_position' => 'above',
					'conditional_logic' => '1',
					'conditional_type'  => 'show',
					'conditionals'      => [
						1 => [
							0 => [
								'field'    => '24',
								'operator' => '==',
								'value'    => '1',
							],
						],
						2 => [
							0 => [
								'field'    => '24',
								'operator' => '==',
								'value'    => '1',
							],
							1 => [
								'field'    => '21',
								'operator' => '==',
								'value'    => '1',
							],
						],
					],
				],
				5  => [
					'id'                 => '5',
					'type'               => 'paypal-commerce',
					'label'              => 'PayPal Commerce',
					'paypal_checkout'    => '1',
					'credit_card'        => '1',
					'default_method'     => 'paypal_checkout',
					'amex'               => '1',
					'discover'           => '1',
					'mastercard'         => '1',
					'visa'               => '1',
					'card_holder_enable' => '1',
					'required'           => '1',
					'size'               => 'medium',
					'button_size'        => 'responsive',
					'shape'              => 'pill',
					'color'              => 'blue',
					'conditional_logic'  => '1',
					'conditional_type'   => 'show',
					'conditionals'       => [
						1 => [
							0 => [
								'field'    => '24',
								'operator' => '==',
								'value'    => '2',
							],
						],
						2 => [
							0 => [
								'field'    => '24',
								'operator' => '==',
								'value'    => '2',
							],
							1 => [
								'field'    => '22',
								'operator' => '==',
								'value'    => '1',
							],
						],
					],
				],
				10 => [
					'id'                => '10',
					'type'              => 'square',
					'label'             => 'Square',
					'required'          => '1',
					'size'              => 'medium',
					'conditional_logic' => '1',
					'conditional_type'  => 'show',
					'conditionals'      => [
						0 => [
							0 => [
								'field'    => '24',
								'operator' => '==',
								'value'    => '3',
							],
						],
					],
				],
				11 => [
					'id'                => '11',
					'type'              => 'authorize_net',
					'label'             => 'Authorize.Net',
					'required'          => '1',
					'size'              => 'medium',
					'conditional_logic' => '1',
					'conditional_type'  => 'show',
					'conditionals'      => [
						1 => [
							0 => [
								'field'    => '24',
								'operator' => '==',
								'value'    => '4',
							],
						],
						2 => [
							0 => [
								'field'    => '24',
								'operator' => '==',
								'value'    => '4',
							],
							1 => [
								'field'    => '23',
								'operator' => '==',
								'value'    => '1',
							],
						],
					],
				],
			],
			'field_id'   => 25,
			'settings'   => [
				'form_title'                             => 'All Payments with CL',
				'submit_text'                            => 'Submit',
				'submit_text_processing'                 => 'Sending...',
				'ajax_submit'                            => '1',
				'notification_enable'                    => '1',
				'notifications'                          => [
					1 => [
						'notification_name'                      => 'Default Notification',
						'email'                                  => '{admin_email}',
						'subject'                                => 'New Entry: Blank Form',
						'sender_name'                            => 'ht-buzzard-halu.instawp.xyz',
						'sender_address'                         => '{admin_email}',
						'message'                                => '{all_fields}',
						'file_upload_attachment_fields'          => [],
						'entry_csv_attachment_entry_information' => [],
						'entry_csv_attachment_file_name'         => 'entry-details',
					],
				],
				'confirmations'                          => [
					1 => [
						'name'                        => 'Default Confirmation',
						'type'                        => 'message',
						'message'                     => '<p>Thanks for contacting us! We will be in touch with you shortly.</p>',
						'message_scroll'              => '1',
						'page'                        => '666',
						'message_entry_preview_style' => 'basic',
					],
				],
				'lead_forms'                             => [
					'accent_color'   => '#0299ed',
					'field_borders'  => '#cccccc',
					'primary_text'   => '#444444',
					'secondary_text' => '#777777',
				],
				'antispam_v3'                            => '1',
				'anti_spam'                              => [
					'time_limit'     => [
						'duration' => '3',
					],
					'country_filter' => [
						'action'        => 'allow',
						'country_codes' => [],
						'message'       => 'Sorry, this form does not accept submissions from your country.',
					],
					'keyword_filter' => [
						'message' => 'Sorry, your message can\'t be submitted because it contains prohibited words.',
					],
				],
				'form_pages_footer'                      => 'This content is neither created nor endorsed by WPForms.',
				'form_pages_color_scheme'                => '#448ccb',
				'form_pages_style'                       => 'modern',
				'conversational_forms_color_scheme'      => '#448ccb',
				'conversational_forms_progress_bar'      => 'percentage',
				'save_resume_link_text'                  => 'Save and Resume Later',
				'save_resume_disclaimer_message'         => 'Heads up! Saving your progress now will store a copy of your entry on this server and the site owner may have access to it. For security reasons, sensitive information such as credit cards and mailing addresses, along with file uploads will have to be re-entered when you resume.',
				'save_resume_confirmation_message'       => 'Your form entry has been saved and a unique link has been created which you can access to resume this form.

Enter your email address to receive the link via email. Alternatively, you can copy and save the link below.

Please note, this link should not be shared and will expire in 30 days, afterwards your form entry will be deleted.',
				'save_resume_enable_resume_link'         => '1',
				'save_resume_enable_email_notification'  => '1',
				'save_resume_email_notification_message' => 'Thank you for saving {form_name}. Click the link below to resume the form from any device.

{resume_link}

Remember, the link should not be shared and will expire in 30 days.',
				'save_resume_email_settings_message'     => 'A link to resume this form has been sent to the email address provided.

Please remember, the link should not be shared and will expire in 30 days.',
				'form_tags'                              => [],
			],
			'lead_forms' => [
				'iif_id_ref' => '',
			],
			'payments'   => [
				'stripe'          => [
					'payment_description' => '',
					'receipt_email'       => '',
					'customer_email'      => '',
					'customer_name'       => '',
					'customer_address'    => '',
					'shipping_address'    => '',
					'enable_recurring'    => '1',
					'recurring'           => [
						0 => [
							'name'             => 'Plan Name #1',
							'period'           => 'daily',
							'email'            => '2',
							'customer_name'    => '',
							'customer_address' => '',
						],
					],
				],
				'paypal_commerce' => [
					'enable_one_time'     => '1',
					'name'                => '3',
					'billing_email'       => '2',
					'billing_address'     => '',
					'shipping_address'    => '',
					'payment_description' => '',
					'conditional_logic'   => '1',
					'conditional_type'    => 'go',
					'conditionals'        => [
						0 => [
							0 => [
								'field'    => '24',
								'operator' => '==',
								'value'    => '2',
							],
						],
					],
					'enable_recurring'    => '1',
					'recurring'           => [
						0 => [
							'pp_product_id'     => 'PROD-7H803253D9492051W',
							'pp_plan_id'        => 'P-5TE8777757483962YMTPUNYQ',
							'name'              => 'Plan Name #1',
							'product_type'      => 'digital',
							'recurring_times'   => 'yearly',
							'total_cycles'      => '0',
							'shipping_address'  => '',
							'conditional_logic' => '1',
							'conditional_type'  => 'go',
							'conditionals'      => [
								0 => [
									0 => [
										'field'    => '24',
										'operator' => '==',
										'value'    => '2',
									],
									1 => [
										'field'    => '22',
										'operator' => '==',
										'value'    => '1',
									],
								],
							],
						],
					],
				],
				'authorize_net'   => [
					'enable'                   => '1',
					'payment_description'      => '',
					'receipt_email'            => '2',
					'customer_name'            => '3',
					'customer_billing_address' => '',
					'conditional_logic'        => '1',
					'conditional_type'         => 'go',
					'conditionals'             => [
						0 => [
							0 => [
								'field'    => '24',
								'operator' => '==',
								'value'    => '4',
							],
						],
					],
					'recurring'                => [
						'enable'                   => '1',
						'name'                     => '',
						'period'                   => 'yearly',
						'email'                    => '2',
						'customer_name'            => '3',
						'customer_billing_address' => '',
						'conditional_logic'        => '1',
						'conditional_type'         => 'go',
						'conditionals'             => [
							0 => [
								0 => [
									'field'    => '24',
									'operator' => '==',
									'value'    => '4',
								],
								1 => [
									'field'    => '23',
									'operator' => '==',
									'value'    => '1',
								],
							],
						],
					],
				],
			],
			'meta'       => [
				'entry_columns' => [
					0 => 3,
					1 => 2,
					2 => 'payment',
					3 => 'date',
					4 => 13,
					5 => 9,
				],
				'template'      => 'all_payments_with_cl',
			],
			'providers'  => [
				'google-sheets' => [],
			],
		];
		// phpcs:enable WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned
	}
}
