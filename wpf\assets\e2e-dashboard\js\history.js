/* eslint-disable */
jQuery( document ).ready( function( $ ) {
	/**
	 * Initialize the history page functionalities.
	 */
	function init() {
		// Initial load of recent workflows
		loadRecentWorkflows();

		// Set up periodic check for workflow updates (only basic info)
		setInterval( loadRecentWorkflows, 30000 ); // Check every 30 seconds

		// Initialize click handlers for accordions
		initializeAccordionHandlers();

		// Initialize artifact download handlers
		initializeArtifactHandlers();
	}

	/**
	 * Initialize accordion click handlers
	 */
	function initializeAccordionHandlers() {
		// Handle main accordion clicks
		$( document ).on( 'click', '.cbp-nttrigger', function( e ) {
			e.preventDefault();
			e.stopPropagation();

			const $this = $( this );
			const $parent = $this.parent();
			const $content = $parent.find( '.cbp-ntcontent' ).first();
			const workflowId = $parent.data( 'workflow-run-id' );

			if ( $parent.hasClass( 'cbp-ntopen' ) ) {
				$parent.removeClass( 'cbp-ntopen' );
				$content.slideUp( 200 );
			} else {
				// Close siblings at the same level
				const $siblings = $parent.siblings( '.cbp-ntopen' );
				$siblings.removeClass( 'cbp-ntopen' );
				$siblings.find( '.cbp-ntcontent' ).first().slideUp( 200 );

				// Open this item
				$parent.addClass( 'cbp-ntopen' );
				$content.slideDown( 200 );

				// Load detailed information only when expanding
				if ( workflowId && ! $parent.data( 'details-loaded' ) ) {
					loadWorkflowDetails( workflowId );
				}
			}
		} );
	}

	/**
	 * Initialize artifact download handlers
	 */
	function initializeArtifactHandlers() {
		$( document ).on( 'click', '.download-artifact-btn', function( e ) {
			e.preventDefault();
			e.stopPropagation();

			const $btn = $( this );
			const $container = $btn.closest( '.artifact-download' );
			const runId = $btn.data( 'run-id' );
			const wpVersion = $btn.data( 'wp-version' );
			const phpVersion = $btn.data( 'php-version' );

			// Show loading state
			const originalText = $btn.html();
			$btn.html( '<span class="dashicons dashicons-update rotating"></span> Processing...' ).prop( 'disabled', true );

			// Make the download request
			$.ajax( {
				url: e2eDashboard.ajax_url,
				type: 'POST',
				data: {
					action: 'download_artifact',
					run_id: runId,
					wp_version: wpVersion,
					php_version: phpVersion,
					nonce: e2eDashboardHistory.nonce
				},
				success: function( response ) {
					if ( response.success ) {
						// Remove any existing status message
						$container.find( '.artifact-status' ).remove();

						// Create status message
						updateArtifactStatus( $container, response.data );

						// Update button state
						if ( response.data.file_exists ) {
							$btn.html( '<span class="dashicons dashicons-yes"></span> Already Downloaded' )
								.addClass( 'downloaded' )
								.prop( 'disabled', true );
						} else {
							$btn.html( '<span class="dashicons dashicons-yes-alt"></span> Downloaded' )
								.addClass( 'downloaded' )
								.prop( 'disabled', true );
						}
					} else {
						$btn.html( '<span class="dashicons dashicons-warning"></span> Download Failed' ).addClass( 'error' );
						console.error( 'Download failed:', response.data );
						setTimeout( () => {
							$btn.html( originalText ).removeClass( 'error' ).prop( 'disabled', false );
						}, 3000 );
					}
				},
				error: function( xhr, status, error ) {
					$btn.html( '<span class="dashicons dashicons-warning"></span> Download Failed' ).addClass( 'error' );
					console.error( 'Download error:', error );
					setTimeout( () => {
						$btn.html( originalText ).removeClass( 'error' ).prop( 'disabled', false );
					}, 3000 );
				}
			} );
		} );
	}

	/**
	 * Get status icon based on status and conclusion
	 */
	function getStatusIcon( status, conclusion ) {
		if ( status === 'completed' ) {
			switch ( conclusion ) {
				case 'success':
					return '✓';
				case 'failure':
					return '✕';
				case 'cancelled':
					return '⊘';
				default:
					return '⋯';
			}
		} else if ( status === 'in_progress' ) {
			return '↻';
		}
		return '⋯';
	}

	/**
	 * Get status class based on status and conclusion
	 */
	function getStatusClass( status, conclusion ) {
		if ( status === 'completed' ) {
			return 'status-' + conclusion;
		}
		return 'status-' + status;
	}

	/**
	 * Load workflow details when expanding an accordion item
	 */
	function loadWorkflowDetails( workflowId ) {
		const $workflowItem = $( '#test-' + workflowId );
		const $content = $workflowItem.find( '.cbp-ntsubaccordion' );

		// Show loading state
		$content.html( '<li><div class="loading">Loading details...</div></li>' );

		$.ajax( {
			url: e2eDashboard.ajax_url,
			type: 'POST',
			data: {
				action: 'get_workflow_details',
				workflow_id: workflowId,
				nonce: e2eDashboardHistory.nonce
			},
			success: function( response ) {
				if ( response.success && response.data ) {
					const workflow = response.data;
					let detailsHtml = '';

					if ( workflow.jobs && workflow.jobs.length > 0 ) {
						workflow.jobs.forEach( function( job ) {
							const jobStatus = job.status.toLowerCase();
							const jobConclusion = job.conclusion ? job.conclusion.toLowerCase() : 'in_progress';

							detailsHtml += '<li>' +
								'<h4 class="cbp-nttrigger">' + job.name + '</h4>' +
								'<div class="cbp-ntcontent">' +
								'<div class="job-details">' +
								'<div class="job-status-row">' +
								'<span class="status-label">Status:</span>' +
								'<span class="status-badge status-' + jobStatus + '">' +
								getStatusIcon( jobStatus, jobConclusion ) + ' ' +
								jobStatus.charAt( 0 ).toUpperCase() + jobStatus.slice( 1 ) +
								'</span>' +
								'</div>';

							if ( job.steps && job.steps.length > 0 ) {
								detailsHtml += '<div class="job-steps">';
								job.steps.forEach( function( step ) {
									const stepStatus = step.status.toLowerCase();
									const stepConclusion = step.conclusion ? step.conclusion.toLowerCase() : 'in_progress';

									detailsHtml += '<div class="step-row">' +
										'<span class="step-name">' + step.name + '</span>' +
										'<span class="step-badge step-' + stepStatus + ' conclusion-' + stepConclusion + '">' +
										getStatusIcon( stepStatus, stepConclusion ) + ' ' +
										( stepStatus === 'completed' ?
											stepConclusion.charAt( 0 ).toUpperCase() + stepConclusion.slice( 1 ) :
											stepStatus.charAt( 0 ).toUpperCase() + stepStatus.slice( 1 ) ) +
										'</span>' +
										'</div>';
								} );
								detailsHtml += '</div>';
							}

							// Add download artifacts button if this is an E2E test job
							const jobNameParts = job.name.match( /E2E Tests \((.*?), (\d+)\)/ );
							if ( jobNameParts ) {
								const [ , wpVersion, phpVersion ] = jobNameParts;
								detailsHtml += '<div class="artifact-download">' +
									'<button class="download-artifact-btn" data-wp-version="' + wpVersion + '" ' +
									'data-php-version="' + phpVersion + '" data-run-id="' + workflowId + '">' +
									'<span class="dashicons dashicons-download"></span> Download Artifact' +
									'</button>' +
									'</div>';
							}

							detailsHtml += '</div></div></li>';
						} );
					}

					$content.html( detailsHtml );
					$workflowItem.data( 'details-loaded', true );
				} else {
					$content.html( '<li><div class="error">Failed to load workflow details.</div></li>' );
				}
			},
			error: function( xhr, status, error ) {
				$content.html( '<li><div class="error">Error loading workflow details: ' + error + '</div></li>' );
			}
		} );
	}

	/**
	 * Load recent workflows and display them in the accordion.
	 */
	function loadRecentWorkflows() {
		$.ajax( {
			url: e2eDashboard.ajax_url,
			type: 'POST',
			data: {
				action: 'get_recent_workflows',
				nonce: e2eDashboardHistory.nonce
			},
			success: function( response ) {
				if ( response.success && response.data.length > 0 ) {
					// Ensure the accordion container exists
					if ( $( '#cbp-ntaccordion' ).length === 0 ) {
						$( '#result' ).html( '<ul id="cbp-ntaccordion" class="cbp-ntaccordion"></ul>' );
					}

					// Keep track of workflow IDs we've seen
					const seenWorkflows = new Set();

					// Display each workflow
					response.data.forEach( function( workflow ) {
						// Convert workflow ID to number and store it
						const workflowId = parseInt( workflow.id, 10 );

						// Skip if we've already processed this workflow
						if ( seenWorkflows.has( workflowId ) ) {
							return;
						}
						seenWorkflows.add( workflowId );

						const workflowContainerId = 'test-' + workflowId;

						// Check if this workflow block already exists
						if ( $( '#' + workflowContainerId ).length === 0 ) {
							const status = workflow.status.toLowerCase();
							const conclusion = workflow.conclusion ? workflow.conclusion.toLowerCase() : 'in_progress';

							const statusIcon = getStatusIcon( status, conclusion );
							const statusClass = getStatusClass( status, conclusion );

							let newWorkflowBlock = '<li id="' + workflowContainerId + '" data-workflow-run-id="' + workflowId + '">' +
								'<h3 class="cbp-nttrigger">' +
								'<span class="workflow-name">' + workflow.name + ' (' + workflow.head_branch + ')</span>' +
								'<span class="test-name">' + workflow.name + '</span>' +
								'<span class="workflow-status ' + statusClass + '">' + statusIcon + ' ' +
								( status === 'completed' ? ( conclusion.charAt( 0 ).toUpperCase() + conclusion.slice( 1 ) ) :
									( status.charAt( 0 ).toUpperCase() + status.slice( 1 ) ) ) + '</span>' +
								'</h3>' +
								'<div class="cbp-ntcontent">' +
								'<ul class="cbp-ntsubaccordion">' +
								'<li><div class="loading">Click to load details</div></li>' +
								'</ul></div></li>';

							// Add the new workflow block to the accordion
							$( '#cbp-ntaccordion' ).append( newWorkflowBlock );
						}
					} );

					// Remove any workflow blocks that are no longer in the response
					$( '#cbp-ntaccordion > li' ).each( function() {
						const workflowId = parseInt( $( this ).attr( 'data-workflow-run-id' ), 10 );
						if ( workflowId && ! seenWorkflows.has( workflowId ) ) {
							$( this ).remove();
						}
					} );
				}
			},
			error: function( xhr, status, error ) {
				console.error( 'Error loading recent workflows:', error );
			}
		} );
	}

	function updateArtifactStatus( $container, response ) {
		const statusHtml = `
			<div class="artifact-status ${response.file_exists ? 'exists' : 'success'}">
				<span class="dashicons ${response.file_exists ? 'dashicons-yes' : 'dashicons-yes-alt'}"></span>
				${response.message}
				<span class="filepath">${response.filepath}</span>
			</div>`;

		// Add Test Results section if we have screenshots, HTML report, or SQL file
		if ( response.screenshots.length > 0 || response.html_report || response.sql_file ) {
			let resultsHtml = '<div class="artifact-screenshots">';
			resultsHtml += '<h4>Test Results</h4>';
			resultsHtml += '<div class="screenshot-buttons">';

			// Add screenshot buttons
			if ( response.screenshots && response.screenshots.length > 0 ) {
				response.screenshots.forEach( ( screenshot, index ) => {
					resultsHtml += `
						<a href="${screenshot}" target="_blank" rel="noopener" class="view-screenshot-btn">
							<span class="dashicons dashicons-format-image"></span>
							View Screenshot ${index + 1}
						</a>`;
				} );
			}

			// Add HTML report button if available
			if ( response.html_report ) {
				resultsHtml += `
					<a href="${response.html_report}" target="_blank" rel="noopener" class="view-report-btn">
						<span class="dashicons dashicons-text-page"></span>
						View Test Report
					</a>`;
			}

			// Add SQL import button if available
			if ( response.sql_file ) {
				const importDisabled = ! e2eDashboardHistory.import_allowed;
				const importTooltip = importDisabled ? ' title="Database import is disabled for security reasons"' : '';

				resultsHtml += `
					<button class="view-report-btn import-sql-btn" data-sql-file="${response.sql_file}" ${importDisabled ? 'disabled' : ''}${importTooltip}>
						<span class="dashicons dashicons-database-import"></span>
						Import Database
					</button>
					<button class="view-report-btn download-db-btn" data-sql-file="${response.sql_file}">
						<span class="dashicons dashicons-download"></span>
						Download Database
					</button>`;
			}

			resultsHtml += '</div></div>';
			$container.append( statusHtml + resultsHtml );

			// Add click handler for SQL import
			if ( response.sql_file ) {
				$container.find( '.import-sql-btn' ).on( 'click', function() {
					if ( ! e2eDashboardHistory.import_allowed ) {
						alert( 'Database import functionality is disabled for security reasons.' );
						return;
					}
					const sqlFile = $( this ).data( 'sql-file' );
					importDatabase( sqlFile, $( this ) );
				} );

				// Add click handler for database download
				$container.find( '.download-db-btn' ).on( 'click', function() {
					const sqlFile = $( this ).data( 'sql-file' );
					downloadDatabase( sqlFile, $( this ) );
				} );
			}
		} else {
			$container.append( statusHtml );
		}
	}

	function importDatabase( sqlFile, $button ) {
		// Create a custom confirmation dialog
		const $overlay = $( '<div class="modal-overlay"></div>' ).css( {
			'position': 'fixed',
			'top': 0,
			'left': 0,
			'right': 0,
			'bottom': 0,
			'background': 'rgba(0,0,0,0.7)',
			'z-index': 100000,
			'display': 'flex',
			'align-items': 'center',
			'justify-content': 'center'
		} );

		const $modal = $( '<div class="modal-content"></div>' ).css( {
			'background': '#fff',
			'padding': '20px',
			'border-radius': '5px',
			'max-width': '500px',
			'width': '100%',
			'box-shadow': '0 5px 15px rgba(0,0,0,0.3)'
		} );

		// Create warning content
		$modal.append( `
			<h2 style="color: #d63638; margin-top: 0;">⚠️ CRITICAL WARNING ⚠️</h2>
			<p><strong>This action will completely replace your current WordPress database with test data!</strong></p>
			<p>This is a destructive operation that:</p>
			<ul style="margin-left: 20px; list-style-type: disc;">
				<li>Will erase ALL current site content, users, and settings</li>
				<li>May break your site functionality</li>
				<li>Cannot be undone (except by database restore)</li>
			</ul>
			<p>To confirm, type <strong>IMPORT</strong> in the field below:</p>
			<input type="text" id="confirm-import-text" style="width: 100%; padding: 8px; margin: 10px 0;" placeholder="Type IMPORT to confirm">
			<div style="display: flex; justify-content: flex-end; margin-top: 15px;">
				<button id="cancel-import" class="button" style="margin-right: 10px;">Cancel</button>
				<button id="confirm-import" class="button button-primary" disabled>Import Database</button>
			</div>
		` );

		// Add the modal to the page
		$overlay.append( $modal );
		$( 'body' ).append( $overlay );

		// Handle input validation
		$( '#confirm-import-text' ).on( 'input', function() {
			if ( $( this ).val() === 'IMPORT' ) {
				$( '#confirm-import' ).prop( 'disabled', false );
			} else {
				$( '#confirm-import' ).prop( 'disabled', true );
			}
		} );

		// Handle button clicks
		$( '#cancel-import' ).on( 'click', function() {
			$overlay.remove();
		} );

		$( '#confirm-import' ).on( 'click', function() {
			$overlay.remove();

			// Proceed with import
			const originalText = $button.html();
			$button.html( '<span class="dashicons dashicons-update rotating"></span> Importing...' );
			$button.prop( 'disabled', true );

			// Show a loading message
			const $status = $( '<div class="import-status">' ).insertAfter( $button );
			$status.html( '<span class="dashicons dashicons-update rotating"></span> Importing database...' );

			$.ajax( {
				url: e2eDashboard.ajax_url,
				type: 'POST',
				data: {
					action: 'import_sql_dump',
					sql_file: sqlFile,
					nonce: e2eDashboardHistory.nonce
				},
				success: function( response ) {
					if ( response.success ) {
						$status.html( '<span class="dashicons dashicons-yes"></span> ' + response.data )
							.addClass( 'success' );
						$button.html( '<span class="dashicons dashicons-yes"></span> Import Complete' )
							.addClass( 'imported' );
					} else {
						const errorMsg = response.data || 'Unknown error occurred';
						$status.html( '<span class="dashicons dashicons-warning"></span> ' + errorMsg )
							.addClass( 'error' );
						$button.html( originalText ).prop( 'disabled', false );
					}
				},
				error: function( xhr, status, error ) {
					const errorMsg = xhr.responseJSON?.data || error || 'Unknown error occurred';
					$status.html( '<span class="dashicons dashicons-warning"></span> Error: ' + errorMsg )
						.addClass( 'error' );
					$button.html( originalText ).prop( 'disabled', false );
				},
				timeout: 300000 // 5 minute timeout
			} );
		} );
	}

	/**
	 * Handle database download
	 */
	function downloadDatabase( sqlFile, $button ) {
		const originalText = $button.html();
		$button.html( '<span class="dashicons dashicons-update rotating"></span> Downloading...' ).prop( 'disabled', true );

		$.ajax( {
			url: e2eDashboard.ajax_url,
			type: 'POST',
			data: {
				action: 'download_database',
				sql_file: sqlFile,
				nonce: e2eDashboardHistory.nonce
			},
			success: function( response ) {
				if ( response.success ) {
					// Create a temporary link to download the file
					const link = document.createElement( 'a' );
					link.href = response.data.download_url;
					link.download = response.data.filename;
					document.body.appendChild( link );
					link.click();
					document.body.removeChild( link );

					$button.html( '<span class="dashicons dashicons-yes"></span> Download Complete' )
						.addClass( 'downloaded' );
				} else {
					$button.html( '<span class="dashicons dashicons-warning"></span> Download Failed' ).addClass( 'error' );
					console.error( 'Download failed:', response.data );
					setTimeout( () => {
						$button.html( originalText ).removeClass( 'error' ).prop( 'disabled', false );
					}, 3000 );
				}
			},
			error: function( xhr, status, error ) {
				$button.html( '<span class="dashicons dashicons-warning"></span> Download Failed' ).addClass( 'error' );
				console.error( 'Download error:', error );
				setTimeout( () => {
					$button.html( originalText ).removeClass( 'error' ).prop( 'disabled', false );
				}, 3000 );
			}
		} );
	}

	// Handle cleanup button click
	$( '#cleanup-e2e-folder' ).on( 'click', function() {
		if ( ! confirm( 'Are you sure you want to empty the E2E folder? This action cannot be undone.' ) ) {
			return;
		}

		const $button = $( this );
		const originalText = $button.html();
		$button.prop( 'disabled', true ).html( '<span class="spinner is-active"></span> Cleaning up...' );

		$.ajax( {
			url: e2eDashboard.ajax_url,
			type: 'POST',
			data: {
				action: 'cleanup_e2e_folder',
				nonce: e2eDashboardHistory.nonce
			},
			success: function( response ) {
				if ( response.success ) {
					alert( response.data );
					// Refresh the page to show updated state
					window.location.reload();
				} else {
					alert( 'Error: ' + response.data );
				}
			},
			error: function() {
				alert( 'Error occurred while cleaning up the folder.' );
			},
			complete: function() {
				$button.prop( 'disabled', false ).html( originalText );
			}
		} );
	} );

	// Initialize the script
	init();
} );
