<?php
/**
 * View for the Detector tab.
 *
 * @var string $full_url      Url to determine.
 * @var string $wp_version    WordPress version.
 * @var array  $pages         Found form pages.
 * @var array  $cache_plugins Caching/Optimization plugins.
 * @var string $theme         Site theme.
 * @var bool   $assets        Found the main js-file.
 */

?>
<div class="wpforms-setting-row wpforms-setting-row-text">
	<span class="wpforms-setting-label">
		<label for="detect-url">Site or Page URL</label>
	</span>

	<span class="wpforms-setting-field">
		<form action="" method="POST">
			<?php wp_nonce_field( 'detector', 'detector_nonce' ); ?>
			<input type="url" name="url" id="detect-url" placeholder="https://wpforms.com" value="<?php echo ! empty( $full_url ) ? esc_url( $full_url ) : ''; ?>">
			<button type="submit" class="wpforms-btn wpforms-btn-md wpforms-btn-orange wpf-detect-btn">Detect</button>
		</form>
	</span>

</div>

<?php
if ( empty( $args ) ) {
	return;
}
$unknown = '<span class="wpf-fail">Unknown</span>';
?>

<h2><?php printf( 'Detect: %s', esc_html( $full_url ) ); ?></h2>

<table class="widefat striped wpf-detect-table">
	<tr>
		<th>WPForms</th>
		<td>
			<?php
			if ( empty( $wpforms ) ) {
				echo wp_kses( $unknown, [ 'span' => [ 'class' => true ] ] );
			} else {
				printf(
					'%s %s',
					esc_html( $wpforms['type'] ),
					! empty( $wpforms['version'] )
						? 'v' . esc_html( $wpforms['version'] )
						: wp_kses( $unknown, [ 'span' => [ 'class' => true ] ] )
				);
			}
			?>
		</td>
	</tr>
	<tr>
		<th>Installed Addons</th>
		<td>
            <?php
            if ( empty( $addons ) ) {
                echo wp_kses( $unknown, [ 'span' => [ 'class' => true ] ] );
            } else {
                ?>
                <ul>
                    <?php
                    foreach ( $addons as $slug => $addon ) {
                        printf(
                            '<li>%s %s</li>',
                            esc_html( $addon['title'] ),
                            $addon['version'] ? 'v' . esc_html( $addon['version'] ) : wp_kses( $unknown, [ 'span' => [ 'class' => true ] ] )
                        );
                    }
                    ?>
                </ul>
            <?php } ?>
		</td>
	</tr>
	<tr>
		<th>WordPress Version</th>
		<td><?php echo $wp_version ? esc_html( $wp_version ) : wp_kses( $unknown, [ 'span' => [ 'class' => true ] ] ); ?></td>
	</tr>
	<tr>
		<th>Pages with WPForms</th>
		<td>
			<?php if ( $pages ) { ?>
				<ul>
					<?php
					foreach ( $pages as $url => $title ) { //phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited
						printf(
							'<li><a href="%s" target="_blank">%s</a></li>',
							esc_url( $url ),
							esc_html( $title )
						);
					}
					?>
				</ul>
				<?php
			} else {
				echo wp_kses( $unknown, [ 'span' => [ 'class' => true ] ] );
			}
			?>
		</td>
	</tr>
	<tr>
		<th>Cache/optimize plugins</th>
		<td>
			<?php echo ! empty( $cache_plugins ) ? esc_html( implode( ', ', $cache_plugins ) ) : 'None'; ?>
		</td>
	</tr>
	<tr>
		<th>Theme</th>
		<td><?php echo $theme ? esc_html( ucwords( $theme ) ) : wp_kses( $unknown, [ 'span' => [ 'class' => true ] ] ); ?></td>
	</tr>
	<tr>
		<th>Assets</th>
		<td><?php echo $assets ? esc_html__( 'Found', 'wpf' ) : wp_kses( $unknown, [ 'span' => [ 'class' => true ] ] ); ?></td>
	</tr>
</table>
