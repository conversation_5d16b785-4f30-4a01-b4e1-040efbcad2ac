<?php
/**
 * View for the Lite Connect tab.
 *
 * @var string $query Domain or admin email.
 * @var array  $sites Lite Connect sites.
 */

?>
<div class="wpforms-setting-row wpforms-setting-row-text">
	<span class="wpforms-setting-label">
		<label for="lite-connect-query">Domain or Admin Email</label>
	</span>

	<div class="wpforms-setting-field">
		<form action="" method="POST">
			<?php wp_nonce_field( 'lite_connect', 'lite_connect_nonce' ); ?>
			<input type="text" name="query" id="lite-connect-query" value="<?php echo ! empty( $query ) ? esc_attr( $query ) : ''; ?>">
			<button type="submit" class="wpforms-btn wpforms-btn-md wpforms-btn-orange wpf-lite-connect-btn">Search</button>
		</form>
	</div>

</div>

<?php
if ( empty( $args ) ) {
	return;
}

$error_message = isset( $sites['error'] ) ? $sites['error'] : null;

if ( $error_message ) {
	printf( '<div class="notice notice-error"><p>%s</p></div>', esc_html( $error_message ) );

	return;
}
?>

<table class="widefat striped wpf-detect-table" aria-describedby="List of sites connected to your WPForms account">
	<thead>
		<tr>
			<th>Domain</th>
			<th>Admin Email</th>
			<th>Created</th>
			<th>Total Entries</th>
		</tr>
	</thead>
	<?php foreach ( $sites as $site ) : ?>
		<tr>
			<td>
				<?php echo esc_url( $site['domain'] ); ?>
			</td>
			<td>
				<?php echo esc_html( $site['admin_email'] ); ?>
			</td>
			<td>
				<?php echo esc_html( wpforms_datetime_format( $site['created_at'] ) ); ?>
			</td>
			<td>
				<?php echo esc_html( $site['total_entries'] ); ?>
			</td>
		</tr>
	<?php endforeach; ?>
</table>
