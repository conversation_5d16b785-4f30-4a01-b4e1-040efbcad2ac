// Form Builder styles.
//
// Context menu styles.
//
// @since 1.8.6

.wpforms-context-menu {
	position: absolute;
	z-index: 100000000;
	display: none;

	&-selective-left {
		.wpforms-context-menu-list-item-has-child .wpforms-context-menu-list {
			left: -195px;
		}
	}

	&-list {
		padding: $spacing_ss 0;
		border-radius: $border_radius_m;
		box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.20);
		background-color: $color_black;
		width: 200px;

		&.wpforms-context-menu-list-selective {
			.wpforms-context-menu-list-item {
				&-icon {
					visibility: hidden;
				}

				&.wpforms-context-menu-list-item-active {
					.wpforms-context-menu-list-item-icon {
						visibility: visible;
					}
				}
			}
		}

		&-divider {
			margin: $spacing_ss 0;
			border-top: 1px solid rgba(255, 255, 255, 0.10);
		}

		&-item {
			padding: $spacing_ss $spacing_ms;
			display: flex;
			align-items: center;
			color: $color_white;
			gap: $spacing_s;
			font-size: $font_size_ss;
			font-weight: 400;
			line-height: 17px;
			cursor: pointer;
			position: relative;
			margin: 0;
			@include transition( all, $transition_fast, ease-out );

			&-inactive {
				opacity: .5;
				cursor: default;
			}

			.wpforms-badge {
				margin-inline-start: auto;
				@include transition( all, $transition_fast, ease-out );
			}

			&-icon {
				font-size: $font_size_s;
				line-height: 14px;
				width: 14px;

				i {
					color: rgba(255, 255, 255, 0.75) !important;
				}
			}

			&-has-child {
				.wpforms-context-menu-list {
					display: none;
					position: absolute;
					right: -195px;
					top: -8px;
				}

				&:hover {
					.wpforms-context-menu-list {
						display: block;
					}
				}

				&:after {
					content: '\f105';
					color: rgba(255, 255, 255, 0.50);
					font-family: $font_fa;
					font-size: $font_size_s;
					font-weight: 400;
					position: absolute;
					right: 15px;
				}
			}

			&:not(&-inactive):hover {
				background-color: $color_blue;

				.wpforms-badge {
					color: #30abf0;
					background-color: #e6f4fe;
				}
			}
		}
	}
}

#wpforms-context-menu-container {
	position: relative;

	.wpforms-context-menu-dropdown {
		top: 36px;
		right: -10px;
	}
}
