/* Gutenberg Block */

.edit-post-visual-editor,
.edit-widgets-block-editor,
.editor-styles-wrapper {

	// Rich Text field.
	@include richtext( '../../images' );

	.wpforms-container-full.wpforms-render-modern {
		.wpforms-form {

			div.wpforms-field-richtext .wp-editor-wrap {
				.mce-tinymce.mce-container.mce-panel {
					display: none !important;
				}

				.wp-editor-tabs {
					padding-right: 0;
				}

				.wp-editor-container {
					border: none;
				}

				.quicktags-toolbar {
					border-top-left-radius: var( --wpforms-field-border-radius );
					border-top-width: var( --wpforms-field-border-size );
					border-top-style: var( --wpforms-field-border-style );
					border-top-color: var( --wpforms-field-border-color );
					border-right-width: var( --wpforms-field-border-size );
					border-right-style: var( --wpforms-field-border-style );
					border-right-color: var( --wpforms-field-border-color );
					border-left-width: var( --wpforms-field-border-size );
					border-left-style: var( --wpforms-field-border-style );
					border-left-color: var( --wpforms-field-border-color );
				}

				.wp-editor-area {
					display: block !important;
					border-width: var( --wpforms-field-border-size );
					border-style: var( --wpforms-field-border-style );
					border-color: var( --wpforms-field-border-color );
					border-bottom-left-radius: var( --wpforms-field-border-radius );
					border-bottom-right-radius: var( --wpforms-field-border-radius );
				}
			}

			// Classic File Upload.
			input[type=file] {
				&:disabled {
					background-color: transparent !important;
				}
			}
		}
	}
}

// jQuery Confirm styles.
.jconfirm-modern .jconfirm-box-container {
	.jconfirm-box.jconfirm-type-picture-selector {
		height: 540px;
		border-top-width: 0;

		.jconfirm-title {
			font-style: normal;
			font-weight: 500;
			font-size: 24px;
			line-height: 22px;
			color: $color_primary_text;
			margin: 0 0 10px 0 !important;

			p {
				font-weight: 400;
				font-size: 16px;
				line-height: 22px;
				margin: 15px 0 0 0;
			}
		}

		.jconfirm-closeIcon {
			width: 12px;
			height: 12px;
			background-size: 12px 12px;
			background-repeat: no-repeat;
			background-image: url( '../../images/cross-inverse.svg' );
			opacity: .3;

			&:after {
				display: none;
			}

			&:hover {
				opacity: .5;
			}
		}

		.wpforms-gutenberg-stock-photos-pictures-wrap {
			display: grid;
			grid-template-columns: repeat( 5, 124px );
			grid-row-gap: 20px;
			grid-column-gap: 20px;
			justify-content: center;
			background-color: $color_light_background;
			border-radius: $border_radius_s;
			padding: 20px;
		}

		.wpforms-gutenberg-stock-photos-picture {
			width: 124px;
			height: 124px;
			border-radius: $border_radius_s;
			position: relative;
			cursor: pointer;
			border: none;
			box-shadow: inset 0 0 0 1px rgba( 0, 0, 0, 0.15 );
			background-size: cover;
			background-position: center;
			background-repeat: no-repeat;
			transition: box-shadow $transition_normal ease-in-out;

			&:hover {
				box-shadow: inset 0 0 0 1px $color_black_background,
							0 0 0 1px $color_black_background,
							0 2px 4px rgba(0, 0, 0, 0.15);
			}
		}
	}
}
