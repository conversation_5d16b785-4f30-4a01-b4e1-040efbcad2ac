/* ==========================================================================
   Base - basic bare bones styling
   ========================================================================== */

.wpforms-clear:before {
	content: " ";
	display: table;
}

.wpforms-clear:after {
	clear: both;
	content: " ";
	display: table;
}

.wpforms-container ul,
.wpforms-container ul li {
	background: none;
	border: 0;
	margin: 0;
	list-style: none;
}


/* Basic Field properties
----------------------------------------------------------------------------- */

/* Field sizes - medium */
.wpforms-container input.wpforms-field-medium,
.wpforms-container select.wpforms-field-medium,
.wpforms-container .wpforms-field-row.wpforms-field-medium {
	max-width: 60%;
}

.wpforms-container textarea.wpforms-field-medium {
	height: 120px;
}

/* Field sizes - small */
.wpforms-container input.wpforms-field-small,
.wpforms-container select.wpforms-field-small,
.wpforms-container .wpforms-field-row.wpforms-field-small {
	max-width: 25%;
}

.wpforms-container textarea.wpforms-field-small {
	height: 70px;
}

/* Field sizes - medium */
.wpforms-container input.wpforms-field-large,
.wpforms-container select.wpforms-field-large,
.wpforms-container .wpforms-field-row.wpforms-field-large {
	max-width: 100%;
}

.wpforms-container textarea.wpforms-field-large {
	height: 220px;
}

/* Field container*/
.wpforms-container .wpforms-field {
	padding: 10px 0;
	position: relative;
}

/* Field description */
.wpforms-container .wpforms-field-description,
.wpforms-container .wpforms-field-limit-text {
	font-size: 0.85em;
	margin: 5px 0 0 0;
}

.wpforms-container .wpforms-field-description.wpforms-disclaimer-description {
	background-color: #fff;
	border: 1px solid #ddd;
	padding: 15px 15px 0;
	height: 125px;
	overflow-y: scroll;
	overflow-x: hidden;
}

.wpforms-container .wpforms-field-description.wpforms-disclaimer-description p {
	margin: 0 0 15px 0;
}

.wpforms-container .wpforms-field-description-before,
.wpforms-container .wpforms-field-description.before {
	font-size: 0.85em;
	margin: 0 0 5px 0;
}

/* Labels and sub-labels */
.wpforms-container .wpforms-field-label {
	display: block;
	font-weight: 700;
	float: none;
	word-break: break-word;
	word-wrap: break-word;
}

.wpforms-container .wpforms-field-sublabel {
	display: block;
	font-size: 0.85em;
	float: none;
	word-break: break-word;
    word-wrap: break-word;
}

.wpforms-container .wpforms-field-label-inline {
	display: inline;
	vertical-align: baseline;
	font-weight: 400;
	word-break: break-word;
}

.wpforms-container .wpforms-field-label.wpforms-label-hide,
.wpforms-container .wpforms-field-sublabel.wpforms-sublabel-hide {
	position: absolute;
	clip: rect(0 0 0 0);
	width: 1px;
	height: 1px;
	margin: -1px;
	overflow: hidden;
}

.wpforms-container .wpforms-required-label {
	color: #ff0000;
	font-weight: normal;
}

/* Rows (multi-line fields: address, credit card, etc)
----------------------------------------------------------------------------- */

.wpforms-container .wpforms-field-row {
	margin-bottom: 8px;
	position: relative;
}

.wpforms-container .wpforms-field .wpforms-field-row:last-of-type {
	margin-bottom: 0;
}

.wpforms-container .wpforms-field-row:before {
	content: "";
	display: table;
}

.wpforms-container .wpforms-field-row:after {
	clear: both;
	content: "";
	display: table;
}

.wpforms-container .wpforms-form .wpforms-field-address .wpforms-one-half:only-child {
	margin-left: 0;
}

/* Columns
----------------------------------------------------------------------------- */

/* User column classes (legacy). */
.wpforms-container {

	@import "legacy-columns-base";
}

/* User list column classes  */
.wpforms-container .wpforms-checkbox-2-columns ul,
.wpforms-container .wpforms-multiplechoice-2-columns ul,
.wpforms-container .wpforms-list-2-columns ul,
.wpforms-container .wpforms-checkbox-3-columns ul,
.wpforms-container .wpforms-multiplechoice-3-columns ul,
.wpforms-container .wpforms-list-3-columns ul {
	display: -ms-flex;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-wrap: wrap;
	flex-wrap: wrap;
}

.wpforms-container .wpforms-checkbox-2-columns ul li,
.wpforms-container .wpforms-multiplechoice-2-columns ul li,
.wpforms-container .wpforms-list-2-columns ul li {
	width: 50%;
	display: block;
	padding-right: 26px;
}

.wpforms-container .wpforms-checkbox-3-columns ul li,
.wpforms-container .wpforms-multiplechoice-3-columns ul li,
.wpforms-container .wpforms-list-3-columns ul li {
	width: 33.3333%;
	display: block;
	padding-right: 26px;
}

.wpforms-container .wpforms-list-inline ul li {
	display: inline-block;
	margin-right: 20px;
	vertical-align: top;
}

/* Legacy, for BC */
.wpforms-container .wpforms-first-half {
	float: left;
	width: 48%;
	clear: both;
}
.wpforms-container .wpforms-last-half {
	float: right;
	width: 48%;
	clear: none;
}
.wpforms-container .wpforms-first-third {
	float: left;
	width: 30.666666667%;
	clear: both;
}
.wpforms-container .wpforms-middle-third  {
	float: left;
	width: 30.666666667%;
	margin-left: 4%;
	clear: none;
}
.wpforms-container .wpforms-last-third {
	float: right;
	width: 30.666666667%;
	clear: none;
}
.wpforms-container .wpforms-last {
	float: right !important;
	margin-right: 0 !important;
	clear: none;
}

/* Preset Layouts
----------------------------------------------------------------------------- */

/* Single line */
.wpforms-container.inline-fields {
	overflow: visible;
}

.wpforms-container.inline-fields .wpforms-field-container  {
	display: table;
	width: calc(100% - 160px);
	float: left;
}

.wpforms-container.inline-fields .wpforms-field  {
	display: table-cell;
	padding-right: 2%;
	vertical-align: top;
}

.wpforms-container.inline-fields .wpforms-submit-container {
	float: right;
	width: 160px;
}

.wpforms-container.inline-fields .wpforms-submit {
	display: block;
	width: 100%;
}

.wpforms-container.inline-fields input.wpforms-field-medium,
.wpforms-container.inline-fields select.wpforms-field-medium,
.wpforms-container.inline-fields .wpforms-field-row.wpforms-field-medium {
	max-width: 100%;
}


/* Set Styles
----------------------------------------------------------------------------- */

.wpforms-container input[type=date],
.wpforms-container input[type=datetime],
.wpforms-container input[type=datetime-local],
.wpforms-container input[type=email],
.wpforms-container input[type=month],
.wpforms-container input[type=number],
.wpforms-container input[type=password],
.wpforms-container input[type=range],
.wpforms-container input[type=search],
.wpforms-container input[type=tel],
.wpforms-container input[type=text],
.wpforms-container input[type=time],
.wpforms-container input[type=url],
.wpforms-container input[type=week],
.wpforms-container select,
.wpforms-container textarea {
	display: block;
	width: 100%;
	box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	float: none;
	font-family: inherit;
}

.wpforms-container input[type=checkbox],
.wpforms-container input[type=radio] {
	width: 13px;
	height: 13px;
	margin: 2px 10px 0 3px;
	display: inline-block;
	vertical-align: baseline;
}

.wpforms-container amp-img > img {
	position: absolute; /* Override position:static from previous rule, to prevent breaking AMP layout. */
}

/* reCAPTCHA Area
----------------------------------------------------------------------------- */

.wpforms-container .wpforms-recaptcha-container {
	padding: 10px 0 20px 0;
	clear: both;
}


/* Date/time field
----------------------------------------------------------------------------- */

.wpforms-container  .wpforms-field-date-time-date-sep {
	display:inline-block;
	padding:0 5px;
}

.wpforms-container .wpforms-field-date-time-date-year,
.wpforms-container .wpforms-field-date-time-date-day,
.wpforms-container .wpforms-field-date-time-date-month {
	display:inline-block;
	width:auto;
}


/* Rating field
----------------------------------------------------------------------------- */

.wpforms-container .wpforms-field-rating-item {
	padding: 0 6px 0 0;
	margin: 0;
	display: inline-block;
}

.wpforms-container .wpforms-field-rating svg {
	cursor: pointer;
	-webkit-transform: perspective(1px) translateZ(0);
	transform: perspective(1px) translateZ(0);
	box-shadow: 0 0 1px transparent;
	opacity: 0.60;
}

.wpforms-container .wpforms-field-rating-item.selected svg,
.wpforms-container .wpforms-field-rating-item.hover svg {
	-webkit-transform: scale(1.3);
	transform: scale(1.3);
	opacity: 1;
}


/* Date/time field
----------------------------------------------------------------------------- */

.wpforms-field-container {
	.wpforms-field-date-time {

		.wpforms-field-row {
			display: flex;
			flex-wrap: wrap;
			align-items: start;
			gap: 10px 4%;

			&::before,
			&::after {
				position: absolute;
			}
		}

		.wpforms-date-type-dropdown {
			align-items: center;
			display: flex;
			flex-grow: 1;
			flex-wrap: wrap;

			.wpforms-field-date-dropdown-wrap {
				width: 100%;
			}

			+ .wpforms-field-row-block {
				flex: 1;
				min-width: 30%;
			}

			.wpforms-field-sublabel {
				width: 100%;
			}
		}

		.wpforms-field-date-dropdown-wrap {
			align-items: center;
			display: flex;
			flex-grow: 1;
			flex-wrap: nowrap;
			margin: 0 -6px 0 -6px;

			&.wpforms-field-small {
				width: calc( 25% + 12px );
			}

			&.wpforms-field-medium {
				width: calc( 60% + 12px );
			}

			&.wpforms-field-large {
				width: calc( 100% + 12px );
			}

			select {
				margin: 0 6px 0 6px;
			}
		}

		.wpforms-field-date-time-date-day,
		.wpforms-field-date-time-date-month {
			width: calc( 30% - 12px );
		}

		.wpforms-field-date-time-date-year {
			width: calc( 40% - 12px );
		}

		.wpforms-date-type-datepicker {
			width: clamp( 50%, 100px, 100% );

			+ .wpforms-field-row-block {
				width: clamp( 50%, 100px, 100% );
			}
		}
	}
}

.wpforms-container .wpforms-datepicker-wrap {
	position: relative;
}

.wpforms-container .wpforms-datepicker-wrap .wpforms-datepicker-clear {
	position: absolute;
	background-image: url(../../../pro/images/times-solid-white.svg);
	background-position: 50% 50%;
	background-repeat: no-repeat;
	background-color: #cccccc;
	background-size: 8px;
	width: 16px;
	height: 16px;
	cursor: pointer;
	display: block;
	border-radius: 50%;
	right: 10px;
	top: 50%;
	margin-top: -8px;
	transition: all 0.3s;
}

.wpforms-container .wpforms-datepicker-wrap .wpforms-datepicker-clear:hover {
	background-color: red;
}

.wpforms-container .wpforms-datepicker-wrap .wpforms-field-small + .wpforms-datepicker-clear {
	right: calc( 75% + 10px );
}

.wpforms-container .wpforms-datepicker-wrap .wpforms-field-medium + .wpforms-datepicker-clear {
	right: calc( 40% + 10px );
}


/* Rating field
----------------------------------------------------------------------------- */

.wpforms-container .wpforms-field-rating-item {
	padding: 0 6px 0 0;
	margin: 0;
	display: inline-block;
}

.wpforms-container .wpforms-field-rating svg {
	cursor: pointer;
	-webkit-transform: perspective(1px) translateZ(0);
	transform: perspective(1px) translateZ(0);
	box-shadow: 0 0 1px transparent;
	opacity: 0.60;
}

.wpforms-container .wpforms-field-rating-item.selected svg,
.wpforms-container .wpforms-field-rating-item.hover svg {
	-webkit-transform: scale(1.3);
	transform: scale(1.3);
	opacity: 1;
}

.wpforms-container .wpforms-field-rating-wrapper {
	display: inline-block;
}

.wpforms-container .wpforms-field-rating-labels {
    display: flex;
    justify-content: space-between;
	gap: 10px;
}

/* Image choices
----------------------------------------------------------------------------- */

.wpforms-container ul.wpforms-image-choices label:not(.wpforms-error) {
	cursor: pointer;
	position: relative;
}

.wpforms-container ul.wpforms-image-choices label input {
	top: 50%;
}

/* Modern style */
.wpforms-container .wpforms-list-inline .wpforms-image-choices-modern li {
	margin: 5px 5px 5px 5px ;
}

.wpforms-container .wpforms-image-choices-modern img {
	display: inline-block;
	margin: 0 auto;
	max-width: 100%;
}

.wpforms-container .wpforms-image-choices-modern label:not(.wpforms-error) {
	background-color: #fff;
	display: inline-block;
	margin: 0 auto;
	cursor: pointer;
	border: 1px solid #fff;
	border-radius: 3px;
	padding: 20px 20px 18px 20px;
	transition: all 0.5s;
	text-align: center;
}

.wpforms-container .wpforms-image-choices-modern label:not(.wpforms-error):hover {
	border:1px solid #ddd;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-selected label,
.wpforms-container .wpforms-image-choices-modern li:has( input:checked ) label {
	box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);
}

.wpforms-container .wpforms-image-choices-modern .wpforms-image-choices-image:after {
	content: "\2714";
	font-size: 22px;
	line-height: 32px;
	color: #fff;
	background: green;
	opacity: 0;
	position: absolute;
	top: 50%;
	left: 50%;
	margin: -16px 0 0 -16px;
	width: 32px;
	height: 32px;
	border-radius: 50%;
	transition: all 0.5s;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-selected .wpforms-image-choices-image:after
.wpforms-container .wpforms-image-choices-modern li:has( input:checked ) .wpforms-image-choices-image:after {
	opacity: 1;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-image-choices-image {
	display: block;
	position: relative;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-selected .wpforms-image-choices-label,
.wpforms-container .wpforms-image-choices-modern li:has( input:checked ) .wpforms-image-choices-label {
	font-weight: 700;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-image-choices-label {
	display: block;
	margin-top: 12px;
}

/* Classic */
.wpforms-container .wpforms-list-inline .wpforms-image-choices-classic li {
	margin: 0 10px 10px 0 !important;
}

.wpforms-container .wpforms-image-choices-classic img {
	display: inline-block;
	margin: 0 auto;
	max-width: 100%;
}

.wpforms-container .wpforms-image-choices-classic label:not(.wpforms-error) {
	background-color: #fff;
	display: inline-block;
	margin: 0 auto;
	cursor: pointer;
	border: 2px solid #fff;
	padding: 10px;
	text-align: center;
}

.wpforms-container .wpforms-image-choices-classic label:not(.wpforms-error):hover {
	border-color: #ddd;
}

.wpforms-container .wpforms-image-choices-classic .wpforms-image-choices-image {
	display: block;
}

.wpforms-container .wpforms-image-choices-classic .wpforms-selected label,
.wpforms-container .wpforms-image-choices-classic li:has( input:checked ) label {
	border-color: #666 !important;
}

.wpforms-container .wpforms-image-choices-classic .wpforms-image-choices-label {
	display: block;
	margin-top: 8px;
}

/* Icon choices
----------------------------------------------------------------------------- */

.wpforms-container {

	@import "icon-choices-base";
}

/* Rich Text field
----------------------------------------------------------------------------- */

.wpforms-container {
	.wpforms-form {
		textarea.wpforms-field-small.wp-editor-area {
			height: 100px;
		}

		textarea.wpforms-field-medium.wp-editor-area {
			height: 250px;
		}

		textarea.wpforms-field-large.wp-editor-area {
			height: 400px;
		}

		textarea.wp-editor-area:focus {
			outline: none;
		}
	}
}

/* Layout field
----------------------------------------------------------------------------- */
.wpforms-container {
	.wpforms-field-layout {
		padding: 0;
	}
}

/* Payment fields.
----------------------------------------------------------------------------- */
@import 'payment-quantities-base';
@import '../../../partials/fields/payments/order-summary';

// Payment Total: Order Summary.
.wpforms-container {
	@include order-summary-common;
	@include order-summary-sizes;

	// Color customizations.
	.wpforms-order-summary-container {
		tr {
			&.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
				color: #990000;
			}
		}
	}
}
