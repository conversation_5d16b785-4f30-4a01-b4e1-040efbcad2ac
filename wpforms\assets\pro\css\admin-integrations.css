/* Styles needed for integrations with third-party plugins. */
body div.jconfirm *,
body div.jconfirm *::before,
body div.jconfirm *::after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  justify-items: center;
  animation: none;
  background: #ffffff;
  border-radius: 6px;
  border-top-style: solid;
  border-top-width: 4px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  padding-top: 34px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-title-c,
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane,
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons {
  grid-column: 1 / -1;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-default {
  border-top-width: 0;
  padding-top: 25px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-default .jconfirm-title-c {
  margin-bottom: 20px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-default .jconfirm-title-c .jconfirm-icon-c {
  font-size: 44px;
  margin-bottom: -6px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-default button.btn-confirm {
  background-color: #e27730;
  border-color: #e27730;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-default button.btn-confirm:hover {
  background-color: #cd6622;
  border-color: #cd6622;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-red {
  border-top-color: #d63638 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-red .jconfirm-title-c .jconfirm-icon-c {
  color: #d63638 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-red button.btn-confirm {
  background-color: #d63638;
  border-color: #d63638;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-red button.btn-confirm:hover {
  background-color: #b32d2e;
  border-color: #b32d2e;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-orange {
  border-top-color: #e27730 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-orange .jconfirm-title-c .jconfirm-icon-c {
  color: #e27730 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-orange button.btn-confirm {
  background-color: #e27730;
  border-color: #e27730;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-orange button.btn-confirm:hover {
  background-color: #cd6622;
  border-color: #cd6622;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-yellow {
  border-top-color: #ffb900 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-yellow .jconfirm-title-c .jconfirm-icon-c {
  color: #ffb900 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-yellow button.btn-confirm {
  background-color: #ffb900;
  border-color: #ffb900;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-yellow button.btn-confirm:hover {
  background-color: #ffaa00;
  border-color: #ffaa00;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-blue {
  border-top-color: #0399ed !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-blue .jconfirm-title-c .jconfirm-icon-c {
  color: #0399ed !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-blue button.btn-confirm {
  background-color: #0399ed;
  border-color: #0399ed;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-blue button.btn-confirm:hover {
  background-color: #036aab;
  border-color: #036aab;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-green {
  border-top-color: #00a32a !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-green .jconfirm-title-c .jconfirm-icon-c {
  color: #00a32a !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-green button.btn-confirm {
  background-color: #00a32a;
  border-color: #00a32a;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-green button.btn-confirm:hover {
  background-color: #008a20;
  border-color: #008a20;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-purple {
  border-top-color: #7a30e2 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-purple .jconfirm-title-c .jconfirm-icon-c {
  color: #7a30e2 !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-purple button.btn-confirm {
  background-color: #7a30e2;
  border-color: #7a30e2;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.jconfirm-type-purple button.btn-confirm:hover {
  background-color: #5c24a9;
  border-color: #5c24a9;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-closeIcon {
  color: transparent;
  font-family: FontAwesome;
  height: 14px;
  opacity: 1;
  right: 10px;
  top: 10px;
  width: 14px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-closeIcon:after {
  color: #b0b2b3;
  content: "\f00d";
  font-size: 16px;
  left: 0;
  position: absolute;
  top: 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-closeIcon:hover:after {
  color: #50575e !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-title-c {
  margin: 0 0 20px 0;
  padding: 0;
  font-weight: 600;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-title-c .jconfirm-icon-c {
  font-size: 47px;
  margin: 0;
  -ms-transform: none !important;
  transform: none !important;
  -webkit-transition: none !important;
  transition: none !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-title-c .jconfirm-icon-c + .jconfirm-title {
  margin-top: 20px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-title-c .jconfirm-title {
  color: #3c434a;
  display: block;
  line-height: 30px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane {
  display: block;
  margin-bottom: 20px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content {
  color: #3c434a;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 0;
  overflow: inherit;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content.lite-upgrade p {
  color: #50575e;
  font-size: 18px;
  padding: 0 20px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content p {
  font-size: inherit;
  line-height: inherit;
  margin: 0 0 16px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content p:last-of-type {
  margin: 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content p.large {
  font-size: 18px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content p.small {
  font-size: 14px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=text],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=number],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=email],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=url],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=password],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=search],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content input[type=tel],
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content textarea,
body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-content-pane .jconfirm-content select {
  margin: 10px 2px;
  width: calc(100% - 4px);
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .already-purchased {
  display: block;
  grid-row: 5;
  grid-column: 1 / -1;
  color: #b0b2b3;
  font-size: 14px;
  margin-top: 15px;
  text-decoration: underline;
  text-align: center;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .already-purchased:hover {
  color: #50575e;
  text-decoration: underline;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .discount-note {
  grid-row: 4;
  grid-column: 1 / -1;
  margin: 25px 0 0 0;
  text-align: center;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .discount-note p {
  background-color: #fcf9e8;
  color: #50575e;
  font-size: 16px;
  margin: 0 -30px;
  padding: 22px 52px 12px 52px;
  position: relative;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .discount-note p:after {
  top: -16px;
  background-color: #ffffff;
  border-radius: 50%;
  color: #00a32a;
  content: "\f058";
  display: inline-block;
  font: normal normal normal 14px FontAwesome;
  font-size: 26px;
  margin-right: -18px;
  padding: 5px 6px;
  position: absolute;
  right: 50%;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .discount-note span {
  color: #00a32a;
  font-weight: 700;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .discount-note a {
  color: #50575e;
  display: block;
  margin-top: 12px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .feature-video {
  margin: 30px 0 0 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .pro-feature-video {
  margin: 15px 0 10px 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box input[type=text]:not(.choices__input) {
  display: block;
  width: 99%;
  border: 1px solid #d6d6d6;
  padding: 10px !important;
  box-shadow: none;
  margin: 10px 1px 1px 1px !important;
  line-height: 1 !important;
  outline: 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box input[type=text]:not(.choices__input):focus {
  border-color: #007cba;
  box-shadow: 0 0 0 1px #007cba;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box #wpforms-edu-modal-license-key {
  margin-top: 20px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons {
  margin-top: -10px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button {
  min-width: 83px;
  background: #f6f7f7;
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  color: #50575e;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  outline: none;
  padding: 11px 17px;
  text-transform: none;
  margin: 10px;
  transition-property: all;
  transition-duration: 0.15s;
  transition-timing-function: ease-out;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button:hover {
  background: #e8e9e9;
  border-color: #c3c4c7;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button[disabled] {
  cursor: no-drop;
  pointer-events: none;
  opacity: .25;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button.btn-confirm {
  color: #ffffff;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button.hidden + button {
  margin-left: 0;
  margin-right: 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button.btn-block {
  display: block;
  margin: 0 0 10px 0 !important;
  text-align: center;
  width: 100%;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button.btn-normal-case {
  text-transform: none !important;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-buttons button i {
  margin: 0 10px 0 0;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box .error {
  color: #d63638;
  display: none;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box.wpforms-providers-account-add-modal .jconfirm-content .description {
  font-size: 13px;
  line-height: 1.4;
  margin-top: 15px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box #wpforms-embed-shortcode {
  margin: 20px 0;
  text-align: center;
  font-size: 24px;
  padding: 8px 5px;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box #wpforms-embed-shortcode:disabled {
  color: #333333;
}

body div.jconfirm .jconfirm-box-container .jconfirm-box button[disabled] {
  cursor: no-drop;
}

body div.jconfirm.jconfirm-wpforms-education .jconfirm-content-pane {
  height: auto !important;
  min-height: fit-content;
}

.choices {
  font-size: 16px;
  text-align: start;
}

.choices input[type=text].choices__input:not(.wpforms-hidden) {
  display: inline-block !important;
}

body .jconfirm.has-video div.jconfirm-box-container .jconfirm-box {
  padding-bottom: 0;
  padding-top: 30px;
}

body .jconfirm.has-video div.jconfirm-box-container .already-purchased {
  display: block;
  grid-row: 4;
  grid-column: 1 / 2;
  margin-top: 0;
}

body .jconfirm.has-video div.jconfirm-box-container .already-purchased:hover {
  color: #50575e;
}

body .jconfirm.has-video div.jconfirm-box-container .discount-note {
  grid-row: 5;
  margin: 20px 0 0;
}

body .jconfirm.has-video div.jconfirm-box-container .discount-note p {
  margin: 0 -30px;
  padding: 20px 52px;
  border-radius: 0 0 6px 6px;
}

body .jconfirm.has-video div.jconfirm-box-container .discount-note p:after {
  display: none;
}

body .jconfirm.has-video div.jconfirm-box-container .feature-video, body .jconfirm.has-video div.jconfirm-box-container .pro-feature-video {
  grid-row: 1 / span 4;
  grid-column-start: 2;
  margin-top: 0;
  margin-left: 15px;
}

body .jconfirm.has-video div.jconfirm-box-container .jconfirm-title-c,
body .jconfirm.has-video div.jconfirm-box-container .jconfirm-content-pane,
body .jconfirm.has-video div.jconfirm-box-container .jconfirm-buttons {
  grid-column: 1 / 2;
}

body .jconfirm.upgrade-modal .jconfirm-box-container div.jconfirm-box {
  padding-bottom: 30px;
}

body .jconfirm.upgrade-modal .jconfirm-box-container div.jconfirm-box .pro-feature-video {
  margin-bottom: 0;
}

body .jconfirm.upgrade-modal .jconfirm-box-container div.jconfirm-box .jconfirm-buttons {
  padding-bottom: 0;
}

@media screen and (max-width: 1023px) {
  body .jconfirm.has-video div.jconfirm-box {
    grid-template-columns: repeat(1, 1fr);
  }
  body .jconfirm.has-video .feature-video, body .jconfirm.has-video .pro-feature-video {
    display: none;
  }
}

.elementor-element-edit-mode div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=submit]:disabled, .elementor-element-edit-mode div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=submit]:disabled:hover, .elementor-element-edit-mode div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) input[type=submit].wpforms-disabled,
.elementor-element-edit-mode div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) button[type=submit]:disabled,
.elementor-element-edit-mode div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) button[type=submit]:disabled:hover,
.elementor-element-edit-mode div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) button[type=submit].wpforms-disabled,
.elementor-element-edit-mode div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) .wpforms-page-button:disabled,
.elementor-element-edit-mode div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) .wpforms-page-button:disabled:hover,
.elementor-element-edit-mode div.wpforms-container-full:not(.wpforms-gutenberg-form-selector) .wpforms-page-button.wpforms-disabled {
  opacity: 1 !important;
}

.elementor-editor-active div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .quicktags-toolbar,
.elementor-editor-active .wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar, .elementor-editor-preview div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .quicktags-toolbar,
.elementor-editor-preview .wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar {
  height: 39px;
  display: block;
  background: #f6f7f7 url("../../images/richtext/tinymce-toolbar-full.png") no-repeat left center;
  background-size: auto 34px;
}

.elementor-editor-active div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.elementor-editor-active .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-toolbar-basic .quicktags-toolbar, .elementor-editor-preview div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.elementor-editor-preview .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-toolbar-basic .quicktags-toolbar {
  background-image: url("../../images/richtext/tinymce-toolbar-basic.png");
}

.elementor-editor-active div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.elementor-editor-active .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled.wpforms-field-richtext-toolbar-basic .quicktags-toolbar, .elementor-editor-preview div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.elementor-editor-preview .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled.wpforms-field-richtext-toolbar-basic .quicktags-toolbar {
  background-image: url("../../images/richtext/tinymce-toolbar-basic-mb.png");
}

.elementor-editor-active div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled .quicktags-toolbar,
.elementor-editor-active .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled .quicktags-toolbar, .elementor-editor-preview div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled .quicktags-toolbar,
.elementor-editor-preview .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled .quicktags-toolbar {
  background-image: url("../../images/richtext/tinymce-toolbar-full-mb.png");
}

.elementor-editor-active div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button,
.elementor-editor-active .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button, .elementor-editor-preview div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button,
.elementor-editor-preview .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button {
  pointer-events: none;
  min-height: auto;
}

.elementor-editor-active .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button, .elementor-editor-preview .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button {
  min-height: 29px;
}

.elementor-editor-active .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector, .elementor-editor-preview .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector {
  height: 540px;
  border-top-width: 0;
}

.elementor-editor-active .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-title-c, .elementor-editor-preview .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-title-c {
  text-align: center;
}

.elementor-editor-active .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-title, .elementor-editor-preview .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-title {
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 22px;
  color: #444444;
  margin: 0 0 10px 0 !important;
}

.elementor-editor-active .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-title p, .elementor-editor-preview .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-title p {
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  margin: 15px 0 0 0;
}

.elementor-editor-active .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-closeIcon, .elementor-editor-preview .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-closeIcon {
  width: 12px;
  height: 12px;
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-image: url("../../images/cross-inverse.svg");
  opacity: .3;
}

.elementor-editor-active .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-closeIcon:after, .elementor-editor-preview .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-closeIcon:after {
  display: none;
}

.elementor-editor-active .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-closeIcon:hover, .elementor-editor-preview .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-closeIcon:hover {
  opacity: .5;
}

.elementor-editor-active .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .wpforms-elementor-stock-photos-pictures-wrap, .elementor-editor-preview .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .wpforms-elementor-stock-photos-pictures-wrap {
  display: grid;
  grid-template-columns: repeat(5, 124px);
  grid-row-gap: 20px;
  grid-column-gap: 20px;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 20px;
}

.elementor-editor-active .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .wpforms-elementor-stock-photos-picture, .elementor-editor-preview .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .wpforms-elementor-stock-photos-picture {
  width: 124px;
  height: 124px;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  border: none;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.15);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: box-shadow .15s ease-in-out;
}

.elementor-editor-active .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .wpforms-elementor-stock-photos-picture:hover, .elementor-editor-preview .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .wpforms-elementor-stock-photos-picture:hover {
  box-shadow: inset 0 0 0 1px #2d2d2d, 0 0 0 1px #2d2d2d, 0 2px 4px rgba(0, 0, 0, 0.15);
}

/* Gutenberg Block */
.edit-post-visual-editor div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .quicktags-toolbar,
.edit-post-visual-editor .wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar,
.edit-widgets-block-editor div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .quicktags-toolbar,
.edit-widgets-block-editor .wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar,
.editor-styles-wrapper div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .quicktags-toolbar,
.editor-styles-wrapper .wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar {
  height: 39px;
  display: block;
  background: #f6f7f7 url("../../images/richtext/tinymce-toolbar-full.png") no-repeat left center;
  background-size: auto 34px;
}

.edit-post-visual-editor div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.edit-post-visual-editor .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.edit-widgets-block-editor div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.edit-widgets-block-editor .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.editor-styles-wrapper div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.editor-styles-wrapper .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-toolbar-basic .quicktags-toolbar {
  background-image: url("../../images/richtext/tinymce-toolbar-basic.png");
}

.edit-post-visual-editor div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.edit-post-visual-editor .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.edit-widgets-block-editor div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.edit-widgets-block-editor .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.editor-styles-wrapper div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled.wpforms-field-richtext-toolbar-basic .quicktags-toolbar,
.editor-styles-wrapper .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled.wpforms-field-richtext-toolbar-basic .quicktags-toolbar {
  background-image: url("../../images/richtext/tinymce-toolbar-basic-mb.png");
}

.edit-post-visual-editor div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled .quicktags-toolbar,
.edit-post-visual-editor .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled .quicktags-toolbar,
.edit-widgets-block-editor div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled .quicktags-toolbar,
.edit-widgets-block-editor .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled .quicktags-toolbar,
.editor-styles-wrapper div.wpforms-container-full .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled .quicktags-toolbar,
.editor-styles-wrapper .wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-field-richtext-media-enabled .quicktags-toolbar {
  background-image: url("../../images/richtext/tinymce-toolbar-full-mb.png");
}

.edit-post-visual-editor div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button,
.edit-post-visual-editor .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button,
.edit-widgets-block-editor div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button,
.edit-widgets-block-editor .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button,
.editor-styles-wrapper div.wpforms-container-full .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button,
.editor-styles-wrapper .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button {
  pointer-events: none;
  min-height: auto;
}

.edit-post-visual-editor .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button,
.edit-widgets-block-editor .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button,
.editor-styles-wrapper .wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs button {
  min-height: 29px;
}

.edit-post-visual-editor .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .mce-tinymce.mce-container.mce-panel,
.edit-widgets-block-editor .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .mce-tinymce.mce-container.mce-panel,
.editor-styles-wrapper .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .mce-tinymce.mce-container.mce-panel {
  display: none !important;
}

.edit-post-visual-editor .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .wp-editor-tabs,
.edit-widgets-block-editor .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .wp-editor-tabs,
.editor-styles-wrapper .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .wp-editor-tabs {
  padding-right: 0;
}

.edit-post-visual-editor .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .wp-editor-container,
.edit-widgets-block-editor .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .wp-editor-container,
.editor-styles-wrapper .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .wp-editor-container {
  border: none;
}

.edit-post-visual-editor .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .quicktags-toolbar,
.edit-widgets-block-editor .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .quicktags-toolbar,
.editor-styles-wrapper .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .quicktags-toolbar {
  border-top-left-radius: var(--wpforms-field-border-radius);
  border-top-width: var(--wpforms-field-border-size);
  border-top-style: var(--wpforms-field-border-style);
  border-top-color: var(--wpforms-field-border-color);
  border-right-width: var(--wpforms-field-border-size);
  border-right-style: var(--wpforms-field-border-style);
  border-right-color: var(--wpforms-field-border-color);
  border-left-width: var(--wpforms-field-border-size);
  border-left-style: var(--wpforms-field-border-style);
  border-left-color: var(--wpforms-field-border-color);
}

.edit-post-visual-editor .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .wp-editor-area,
.edit-widgets-block-editor .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .wp-editor-area,
.editor-styles-wrapper .wpforms-container-full.wpforms-render-modern .wpforms-form div.wpforms-field-richtext .wp-editor-wrap .wp-editor-area {
  display: block !important;
  border-width: var(--wpforms-field-border-size);
  border-style: var(--wpforms-field-border-style);
  border-color: var(--wpforms-field-border-color);
  border-bottom-left-radius: var(--wpforms-field-border-radius);
  border-bottom-right-radius: var(--wpforms-field-border-radius);
}

.edit-post-visual-editor .wpforms-container-full.wpforms-render-modern .wpforms-form input[type=file]:disabled,
.edit-widgets-block-editor .wpforms-container-full.wpforms-render-modern .wpforms-form input[type=file]:disabled,
.editor-styles-wrapper .wpforms-container-full.wpforms-render-modern .wpforms-form input[type=file]:disabled {
  background-color: transparent !important;
}

.jconfirm-modern .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector {
  height: 540px;
  border-top-width: 0;
}

.jconfirm-modern .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-title {
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 22px;
  color: #3c434a;
  margin: 0 0 10px 0 !important;
}

.jconfirm-modern .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-title p {
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  margin: 15px 0 0 0;
}

.jconfirm-modern .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-closeIcon {
  width: 12px;
  height: 12px;
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-image: url("../../images/cross-inverse.svg");
  opacity: .3;
}

.jconfirm-modern .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-closeIcon:after {
  display: none;
}

.jconfirm-modern .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .jconfirm-closeIcon:hover {
  opacity: .5;
}

.jconfirm-modern .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .wpforms-gutenberg-stock-photos-pictures-wrap {
  display: grid;
  grid-template-columns: repeat(5, 124px);
  grid-row-gap: 20px;
  grid-column-gap: 20px;
  justify-content: center;
  background-color: #f6f7f7;
  border-radius: 4px;
  padding: 20px;
}

.jconfirm-modern .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .wpforms-gutenberg-stock-photos-picture {
  width: 124px;
  height: 124px;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  border: none;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.15);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: box-shadow 0.15s ease-in-out;
}

.jconfirm-modern .jconfirm-box-container .jconfirm-box.jconfirm-type-picture-selector .wpforms-gutenberg-stock-photos-picture:hover {
  box-shadow: inset 0 0 0 1px #282e32, 0 0 0 1px #282e32, 0 2px 4px rgba(0, 0, 0, 0.15);
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .insert-media.add_media {
  display: none !important;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-container {
  color: initial;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-has-error .quicktags-toolbar {
  border-top-color: #cc0000;
  border-left-color: #cc0000;
  border-right-color: #cc0000;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-has-error .wp-switch-editor {
  border-color: #cc0000;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext.wpforms-has-error .wp-editor-container textarea.wp-editor-area {
  border-color: #cc0000;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .wp-switch-editor {
  float: left;
  box-sizing: border-box;
  position: relative;
  top: var(--wpforms-field-border-size, 1px);
  background: #e6e6e6;
  color: #595959;
  cursor: pointer;
  font-size: 13px;
  font-weight: normal;
  line-height: 1.46153846;
  height: 29px;
  margin: 0 0 0 5px;
  padding: 3px 8px 4px;
  border: 1px solid #cccccc;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-tabs {
  float: right;
  position: relative;
  z-index: 1;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .html-active button.switch-html,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .tmce-active button.switch-tmce,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-widget.mce-btn button {
  border-bottom-color: transparent;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .tmce-active button.switch-tmce,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .html-active button.switch-html {
  background: #f5f5f5;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .tmce-active.wpforms-focused button.switch-tmce,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .html-active.wpforms-focused button.switch-html {
  top: 0;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .html-active .quicktags-toolbar {
  display: flex;
  flex-wrap: wrap;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn.active, div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn:active, div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn.mce-active,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active.active,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active:active,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active.mce-active {
  background-color: transparent;
  color: inherit;
  border-color: #8c8f94;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn.active.mce-btn-has-text, div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn:active.mce-btn-has-text, div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn.mce-active.mce-btn-has-text,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active.active.mce-btn-has-text,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active:active.mce-btn-has-text,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active.mce-active.mce-btn-has-text {
  background-color: #ffffff;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn:focus, div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar .mce-btn-group .mce-btn:hover,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active:focus,
div.wpforms-container .wpforms-form div.wpforms-field-richtext .qt-dfw.active:hover {
  border-color: #8c8f94;
  box-shadow: inset 0 1px 0 #fff, 0 1px 0 rgba(0, 0, 0, 0.08);
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar {
  padding: 3px;
  position: relative;
  border: 1px solid #cccccc;
  border-top-left-radius: 2px;
  background: #f5f5f5;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button {
  height: 26px;
  min-height: 26px;
  line-height: 24px;
  border-width: 1px;
  border-style: solid;
  -webkit-appearance: none;
  border-radius: 3px;
  font-weight: 400;
  color: #2271b1;
  border-color: #2271b1;
  background: #f6f7f7;
  vertical-align: top;
  padding: 0 8px;
  margin-right: 4px;
  text-transform: none;
  text-decoration: none;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button:hover {
  text-decoration: none;
  background: #f6f7f7;
  border-color: #0a4b78;
  color: #0a4b78;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="b"], div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="/b"] {
  font-weight: bold;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="i"], div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="/i"] {
  font-style: italic;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="link"] {
  text-decoration: underline;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="del"], div.wpforms-container .wpforms-form div.wpforms-field-richtext .quicktags-toolbar .button[value="/del"] {
  text-decoration: line-through;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-container textarea.wp-editor-area {
  border-radius: 0 0 2px 2px;
  border-top: 0;
  border-color: #cccccc;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .wp-editor-container textarea.wp-editor-area:focus {
  outline: none;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar-grp .mce-active i {
  color: inherit;
}

div.wpforms-container .wpforms-form div.wpforms-field-richtext .mce-toolbar-grp .mce-active .mce-caret {
  border-top: 0;
  border-bottom: 6px solid #595959;
}

#wpforms-form-page-page div.wpforms-field-richtext button.wp-switch-editor {
  font-size: 13px;
}

.rtl div.wpforms-container .wpforms-form div.wpforms-field-richtext div.wp-editor-tabs {
  float: left;
}

.rtl div.wpforms-container .wpforms-form div.wpforms-field-richtext div.wp-editor-tabs button.switch-tmce {
  margin-left: 0;
}

.rtl div.wpforms-container .wpforms-form div.wpforms-field-richtext div.wp-editor-tabs button.switch-tmce:after {
  left: 0 !important;
}

.rtl .wpforms-form div.wpforms-field-richtext .mce-container-body .mce-resizehandle {
  right: auto;
  left: 0;
  padding-left: 0;
}

.rtl .wpforms-form div.wpforms-field-richtext .mce-container-body .mce-resizehandle .mce-i-resize {
  transform: rotate(90deg);
}

.wpforms-form .wpforms-field-container .wpforms-field.wpforms-field-content {
  padding-top: 20px;
  padding-bottom: 0;
}

.wpforms-field.wpforms-field-content .wpforms-field-row {
  word-break: break-word;
}

.wpforms-field.wpforms-field-content .wpforms-field-row h1, .wpforms-field.wpforms-field-content .wpforms-field-row h2, .wpforms-field.wpforms-field-content .wpforms-field-row h3, .wpforms-field.wpforms-field-content .wpforms-field-row h4, .wpforms-field.wpforms-field-content .wpforms-field-row h5, .wpforms-field.wpforms-field-content .wpforms-field-row h6 {
  margin: 20px 0;
  padding: 0;
  clear: unset;
}

.wpforms-field.wpforms-field-content .wpforms-field-row h1:first-child, .wpforms-field.wpforms-field-content .wpforms-field-row h2:first-child, .wpforms-field.wpforms-field-content .wpforms-field-row h3:first-child, .wpforms-field.wpforms-field-content .wpforms-field-row h4:first-child, .wpforms-field.wpforms-field-content .wpforms-field-row h5:first-child, .wpforms-field.wpforms-field-content .wpforms-field-row h6:first-child {
  margin-top: 0;
}

.wpforms-field.wpforms-field-content .wpforms-field-row h1 {
  font-size: 32px;
  line-height: 40px;
}

.wpforms-field.wpforms-field-content .wpforms-field-row h2 {
  font-size: 28px;
  line-height: 36px;
}

.wpforms-field.wpforms-field-content .wpforms-field-row h3 {
  font-size: 24px;
  line-height: 32px;
}

.wpforms-field.wpforms-field-content .wpforms-field-row h4 {
  font-size: 20px;
  line-height: 28px;
}

.wpforms-field.wpforms-field-content .wpforms-field-row h5 {
  font-size: 18px;
  line-height: 26px;
}

.wpforms-field.wpforms-field-content .wpforms-field-row h6 {
  font-size: 16px;
  line-height: 24px;
  text-transform: uppercase;
}

.wpforms-field.wpforms-field-content .wpforms-field-row p, .wpforms-field.wpforms-field-content .wpforms-field-row blockquote, .wpforms-field.wpforms-field-content .wpforms-field-row pre, .wpforms-field.wpforms-field-content .wpforms-field-row table {
  margin: 0 0 20px 0;
}

.wpforms-field.wpforms-field-content .wpforms-field-row li {
  margin: 0 0 10px 0;
}

.wpforms-field.wpforms-field-content .wpforms-field-row a {
  text-decoration: underline;
}

.wpforms-field.wpforms-field-content .wpforms-field-row a:hover {
  text-decoration: none;
}

.wpforms-field.wpforms-field-content .wpforms-field-row code, .wpforms-field.wpforms-field-content .wpforms-field-row pre {
  font-family: monospace;
  overflow: auto;
}

.wpforms-field.wpforms-field-content .wpforms-field-row del {
  text-decoration: line-through;
}

.wpforms-field.wpforms-field-content .wpforms-field-row ins {
  text-decoration: underline;
}

.wpforms-field.wpforms-field-content .wpforms-field-row small {
  font-size: smaller;
}

.wpforms-field.wpforms-field-content .wpforms-field-row dt {
  margin: 5px 0;
}

.wpforms-field.wpforms-field-content .wpforms-field-row dd {
  margin-left: 25px;
}

.wpforms-field.wpforms-field-content .wpforms-field-row abbr, .wpforms-field.wpforms-field-content .wpforms-field-row acronym {
  text-decoration: underline dotted;
}

.wpforms-field.wpforms-field-content .wpforms-field-row ul {
  list-style: disc outside none !important;
  padding-inline-start: 29px !important;
  margin-bottom: 20px !important;
}

.wpforms-field.wpforms-field-content .wpforms-field-row ul ul {
  list-style-type: circle !important;
  margin-top: 10px !important;
  margin-bottom: 0 !important;
}

.wpforms-field.wpforms-field-content .wpforms-field-row ul ul ul {
  list-style-type: square !important;
}

.wpforms-field.wpforms-field-content .wpforms-field-row ul ol {
  margin-top: 10px;
  margin-bottom: 0;
}

.wpforms-field.wpforms-field-content .wpforms-field-row ul li {
  list-style: inherit !important;
  margin-bottom: 10px !important;
}

.wpforms-field.wpforms-field-content .wpforms-field-row ol {
  list-style: decimal outside none;
  padding-inline-start: 29px;
  margin-bottom: 20px;
}

.wpforms-field.wpforms-field-content .wpforms-field-row ol ol {
  margin-top: 10px;
  margin-bottom: 0;
}

.wpforms-field.wpforms-field-content .wpforms-field-row ol ul {
  margin-top: 10px !important;
  margin-bottom: 0 !important;
}

.wpforms-field.wpforms-field-content .wpforms-field-row ol li {
  list-style: inherit;
}

.wpforms-field.wpforms-field-content .wpforms-field-row blockquote {
  border-left: 4px solid rgba(0, 0, 0, 0.15);
  padding-left: 20px;
}

.wpforms-field.wpforms-field-content .wpforms-field-row blockquote:before, .wpforms-field.wpforms-field-content .wpforms-field-row blockquote:after {
  display: none;
}

.wpforms-field.wpforms-field-content .wpforms-field-row table {
  width: 100%;
  border-collapse: collapse;
  word-break: normal;
}

.wpforms-field.wpforms-field-content .wpforms-field-row table th, .wpforms-field.wpforms-field-content .wpforms-field-row table td {
  padding: 0.5em;
  border: 1px solid;
}

.wpforms-field.wpforms-field-content .wpforms-field-row sup, .wpforms-field.wpforms-field-content .wpforms-field-row sub {
  font-size: smaller;
  line-height: calc( 100% + 11px);
}

.wpforms-field.wpforms-field-content .wpforms-field-row sup {
  vertical-align: super;
}

.wpforms-field.wpforms-field-content .wpforms-field-row sub {
  vertical-align: sub;
}

.wpforms-field.wpforms-field-content .wpforms-field-row img {
  max-width: 100%;
  height: auto;
}

.wpforms-field.wpforms-field-content .wpforms-field-row .alignleft {
  float: left;
  margin: 0 30px 20px 0;
}

.wpforms-field.wpforms-field-content .wpforms-field-row .alignright {
  float: right;
  margin: 0 0 20px 30px;
}

.wpforms-field.wpforms-field-content .wpforms-field-row .aligncenter {
  display: block;
  clear: both;
  text-align: center;
  margin: 0 auto 20px;
}

.wpforms-field.wpforms-field-content .wpforms-field-row .alignnone {
  display: block;
  clear: both;
  margin: 0 0 20px 0;
}

.wpforms-field.wpforms-field-content .wpforms-field-row .wp-caption-dt,
.wpforms-field.wpforms-field-content .wpforms-field-row .wp-caption-dd {
  margin: 0;
}

.wpforms-field.wpforms-field-content .wpforms-field-row .wp-caption {
  position: relative;
  left: auto;
  right: auto;
  transform: none;
  max-width: 100%;
}

.wpforms-field.wpforms-field-content .wpforms-field-row .wp-caption .wp-caption-text,
.wpforms-field.wpforms-field-content .wpforms-field-row .wp-caption .wp-caption-dd {
  text-align: center;
  font-size: 14px;
  margin-top: 0.5em;
}

.wpforms-field.wpforms-field-content .wpforms-field-row .wpforms-field-content-display-frontend-clear {
  clear: both;
}
