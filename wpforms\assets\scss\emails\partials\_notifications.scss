$primary: #333333;
$orange: #e27730;
$blue: #509fe2;
$green: #74ae5e;
$linkColor: #e57722;
$linkColorHover: darken( $linkColor, 20% );
$backgroundColor: #e9eaec;
$backgroundContent: #ffffff;
$fontColor: #333333;
$error: #d63638;

@import 'resets';
@import 'text';

/* Base */
.body,
body {
	background-color: $backgroundColor;
	text-align: center;
	padding: 0 25px 0 25px;
}

.container {
	margin: 0 auto 0 auto;
}

.header {
	line-height: 1;

	.header-image {
		display: inline-block;
		vertical-align: middle;
		width: 80%;
	}

	img {
		display: inline-block !important;
		max-height: 180px; // Default "medium" header image height.
		vertical-align: middle;
	}
}

// Hide the dark variation by default.
.header-wrapper {
	&.dark-mode {
		display: none;
	}
}

.content {

	a, p, pre {
		-ms-word-break: break-word;
		word-break: break-word;
	}

	pre {
		white-space: initial;
	}

	/* Helper class for inline elements. */
	.inline {
		display: inline-block;
	}

	.smart-tag {
		table:not(.wpforms-order-summary-preview) {
			border-collapse: collapse;
			width: 100%;

			td, th {
				border: 1px solid currentColor;
				padding: 5px !important;
			}
		}
	}
}

.content td > *:last-child {
	margin-bottom: 0;
}

.footer {
	color: lighten($fontColor, 40%);

	a {
		color: lighten($fontColor, 40%);
		text-decoration: underline;

		&:hover {
			color: $fontColor;
		}
	}
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
	color: $fontColor;
}

/* Buttons */
.button-link {
	border-radius: 3px;
	padding: 7px 15px;
	text-decoration: none;
}

/* Content */
$fields: signature, rating;

@each $field in $fields {
	.field-#{$field} {
		td.field-value {
			line-height: 1;
		}
	}
}

tr:not(.smart-tag) {
	> .field-value span {
		display: block;
	}
}

/* Repeater & Layout */
.field-repeater-name,
.field-layout-name {
	font-size: 22px;
}

/* File Upload */
.field-file-upload {
	.field-value .file-icon {
		display: inline-block;
		vertical-align: middle;
	}
}

/* Camera */
.field-camera {
	.field-value .file-icon {
		display: inline-block;
		vertical-align: middle;
	}
}

/* RichText, Content */
.field-richtext, .field-content {
	.field-value:only-child {
		display: inline-block;
		width: 100%;
	}

	p .alignleft,
	li .alignleft {
		float: left;
		margin-right: 16px;
		margin-top: 8px;
		margin-bottom: 8px;
	}

	p .aligncenter,
	li .aligncenter {
		display: block;
		margin-left: auto;
		margin-right: auto;
	}

	p .alignright,
	li .alignright {
		float: right;
		margin-left: 16px;
		margin-top: 8px;
		margin-bottom: 8px;
	}

	table {
		border-collapse: collapse;
		width: 100%;

		td, th {
			border: 1px solid currentColor;
			padding: 5px !important;
		}
	}
}

.field-rating {
	.field-value {
		line-height: 1.3 !important;
	}
}

// Order summary table.
@import '../../partials/fields/payments/order-summary';
.field-payment-total,
.smart-tag {
	@include order-summary-common;
	@include order-summary-fancy;

	.wpforms-order-summary-container {
		max-width: 100%;

		table.wpforms-order-summary-preview {
			caption,
			.wpforms-order-summary-placeholder-hidden,
			.wpforms-order-summary-item-quantity-label-short {
				display: none;
			}

			tr {
				&.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
					color: $error !important;
				}
			}
		}
	}
}
