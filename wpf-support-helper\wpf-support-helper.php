<?php
/**
 * Plugin Name: WPForms Support Helper
 * Plugin URI: https://wpforms.com/
 * Description: A WordPress plugin to detect ModSecurity usage and provide diagnostic information for WPForms support.
 * Version: 1.0.0
 * Author: WPForms Team
 * Author URI: https://wpforms.com/
 * Text Domain: wpf-support-helper
 * Domain Path: /languages
 * Requires at least: 5.5
 * Requires PHP: 7.2
 * Network: false
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

// Define plugin constants.

/**
 * Plugin version.
 *
 * @since {VERSION}
 */
define( 'WPF_SUPPORT_HELPER_VERSION', '1.0.0' );

/**
 * Plugin directory path.
 *
 * @since {VERSION}
 */
define( 'WPF_SUPPORT_HELPER_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );

/**
 * Plugin directory URL.
 *
 * @since {VERSION}
 */
define( 'WPF_SUPPORT_HELPER_PLUGIN_URL', plugin_dir_url( __FILE__ ) );

/**
 * Plugin main file path.
 *
 * @since {VERSION}
 */
define( 'WPF_SUPPORT_HELPER_PLUGIN_FILE', __FILE__ );

/**
 * Plugin basename.
 *
 * @since {VERSION}
 */
define( 'WPF_SUPPORT_HELPER_PLUGIN_BASENAME', plugin_basename( __FILE__ ) );

// Load Composer autoloader.
require_once WPF_SUPPORT_HELPER_PLUGIN_DIR . 'vendor/autoload.php';

// Load the Plugin class.
require_once WPF_SUPPORT_HELPER_PLUGIN_DIR . 'src/Plugin.php';

use WPForms\SupportHelper\Plugin;

/**
 * Get plugin instance.
 *
 * @since {VERSION}
 *
 * @return Plugin
 */
function wpf_support_helper(): Plugin {

	return Plugin::instance();
}

// Initialize the plugin.
wpf_support_helper();
