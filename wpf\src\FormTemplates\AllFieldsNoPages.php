<?php

namespace WPForms\DevTools\FormTemplates;

use WPF<PERSON>_Template;

/**
 * WPForms DevTools template: "wpf • All fields (no pages)".
 *
 * @since 0.18
 */
class AllFieldsNoPages extends WPForms_Template {

	const FIRST_CHOICE_IMG  = 'https://img.freepik.com/free-vector/programmers-concept-with-flat-design_23-2147850221.jpg';
	const SECOND_CHOICE_IMG = 'https://img.freepik.com/free-vector/programming-miniature-isometric_1284-25088.jpg';
	const THIRD_CHOICE_IMG  = 'https://img.freepik.com/free-vector/programm-development-flat-illustration_1284-13767.jpg';

	/**
	 * Initialize template.
	 *
	 * @since 0.18
	 *
	 * @noinspection HtmlUnknownTarget
	 */
	public function init() {

		// Template name.
		$this->name = 'wpf • All fields (no pages)';

		// Template slug.
		$this->slug = 'wpf__all_fields_no_pages';

		// Template description.
		$this->description = 'All fields w/o PageBreaks. Added by WPForms DevTools plugin for testing purposes.';

		// Template field and settings.
		$this->data = [
			'fields'   => [
				21 => [
					'id'           => '21',
					'type'         => 'text',
					'label'        => 'Single Line Text',
					'size'         => 'medium',
					'limit_count'  => '1',
					'limit_mode'   => 'characters',
					'map_position' => 'above',
				],
				15 => [
					'id'          => '15',
					'type'        => 'textarea',
					'label'       => 'Paragraph Text',
					'size'        => 'medium',
					'limit_count' => '1',
					'limit_mode'  => 'characters',
				],
				16 => [
					'id'      => '16',
					'type'    => 'select',
					'label'   => 'Dropdown classic',
					'choices' => [
						1 => [
							'label' => 'First Choice',
						],
						2 => [
							'label' => 'Second Choice',
						],
						3 => [
							'label' => 'Third Choice',
						],
					],
					'style'   => 'classic',
					'size'    => 'medium',
				],
				23 => [
					'id'       => '23',
					'type'     => 'select',
					'label'    => 'Dropdown classic multiple',
					'choices'  => [
						1 => [
							'label' => 'First Choice',
						],
						2 => [
							'label' => 'Second Choice',
						],
						3 => [
							'label' => 'Third Choice',
						],
					],
					'multiple' => '1',
					'style'    => 'classic',
					'size'     => 'medium',
				],
				22 => [
					'id'      => '22',
					'type'    => 'select',
					'label'   => 'Dropdown modern',
					'choices' => [
						1 => [
							'label' => 'First Choice',
						],
						2 => [
							'label' => 'Second Choice',
						],
						3 => [
							'label' => 'Third Choice',
						],
					],
					'style'   => 'modern',
					'size'    => 'medium',
				],
				24 => [
					'id'       => '24',
					'type'     => 'select',
					'label'    => 'Dropdown modern multiple',
					'choices'  => [
						1 => [
							'label' => 'First Choice',
						],
						2 => [
							'label' => 'Second Choice',
						],
						3 => [
							'label' => 'Third Choice',
						],
					],
					'multiple' => '1',
					'style'    => 'modern',
					'size'     => 'medium',
				],
				17 => [
					'id'                   => '17',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice',
					'choices'              => [
						1 => [
							'label' => 'First Choice',
						],
						2 => [
							'label' => 'Second Choice',
						],
						3 => [
							'label' => 'Third Choice',
						],
					],
					'choices_images_style' => 'modern',
				],
				18 => [
					'id'                   => '18',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes',
					'choices'              => [
						1 => [
							'label' => 'First Choice',
						],
						2 => [
							'label' => 'Second Choice',
						],
						3 => [
							'label' => 'Third Choice',
						],
					],
					'choices_images_style' => 'modern',
				],
				25 => [
					'id'                   => '25',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes two columns',
					'choices'              => [
						1 => [
							'label' => 'First Choice',
						],
						2 => [
							'label' => 'Second Choice',
						],
						3 => [
							'label' => 'Third Choice',
						],
					],
					'choices_images_style' => 'modern',
					'input_columns'        => '2',
				],
				26 => [
					'id'                   => '26',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes three columns',
					'choices'              => [
						1  => [
							'label' => 'First Choice',
						],
						2  => [
							'label' => 'Second Choice',
						],
						3  => [
							'label' => 'Third Choice',
						],
						4  => [
							'label' => 'Alabama',
						],
						5  => [
							'label' => 'Alaska',
						],
						6  => [
							'label' => 'Arizona',
						],
						7  => [
							'label' => 'Arkansas',
						],
						8  => [
							'label' => 'California',
						],
						9  => [
							'label' => 'Colorado',
						],
						10 => [
							'label' => 'Connecticut',
						],
						11 => [
							'label' => 'Delaware',
						],
						12 => [
							'label' => 'District of Columbia',
						],
						13 => [
							'label' => 'Florida',
						],
						14 => [
							'label' => 'Georgia',
						],
						15 => [
							'label' => 'Hawaii',
						],
						16 => [
							'label' => 'Idaho',
						],
						17 => [
							'label' => 'Illinois',
						],
						18 => [
							'label' => 'Indiana',
						],
						19 => [
							'label' => 'Iowa',
						],
						20 => [
							'label' => 'Kansas',
						],
						21 => [
							'label' => 'Kentucky',
						],
						22 => [
							'label' => 'Louisiana',
						],
						23 => [
							'label' => 'Maine',
						],
						24 => [
							'label' => 'Maryland',
						],
						25 => [
							'label' => 'Massachusetts',
						],
						26 => [
							'label' => 'Michigan',
						],
						27 => [
							'label' => 'Minnesota',
						],
						28 => [
							'label' => 'Mississippi',
						],
						29 => [
							'label' => 'Missouri',
						],
						30 => [
							'label' => 'Montana',
						],
						31 => [
							'label' => 'Nebraska',
						],
						32 => [
							'label' => 'Nevada',
						],
						33 => [
							'label' => 'New Hampshire',
						],
						34 => [
							'label' => 'New Jersey',
						],
						35 => [
							'label' => 'New Mexico',
						],
						36 => [
							'label' => 'New York',
						],
						37 => [
							'label' => 'North Carolina',
						],
						38 => [
							'label' => 'North Dakota',
						],
						39 => [
							'label' => 'Ohio',
						],
						40 => [
							'label' => 'Oklahoma',
						],
						41 => [
							'label' => 'Oregon',
						],
						42 => [
							'label' => 'Pennsylvania',
						],
						43 => [
							'label' => 'Rhode Island',
						],
						44 => [
							'label' => 'South Carolina',
						],
						45 => [
							'label' => 'South Dakota',
						],
						46 => [
							'label' => 'Tennessee',
						],
						47 => [
							'label' => 'Texas',
						],
						48 => [
							'label' => 'Utah',
						],
						49 => [
							'label' => 'Vermont',
						],
						50 => [
							'label' => 'Virginia',
						],
						51 => [
							'label' => 'Washington',
						],
						52 => [
							'label' => 'West Virginia',
						],
						53 => [
							'label' => 'Wisconsin',
						],
						54 => [
							'label' => 'Wyoming',
						],
					],
					'choices_images_style' => 'modern',
					'input_columns'        => '3',
				],
				28 => [
					'id'                   => '28',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes image choices modern',
					'choices'              => [
						1 => [
							'label' => 'First Choice',
							'image' => self::FIRST_CHOICE_IMG,
						],
						2 => [
							'label' => 'Second Choice',
							'image' => self::SECOND_CHOICE_IMG,
						],
						3 => [
							'label' => 'Third Choice',
							'image' => self::THIRD_CHOICE_IMG,
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'modern',
					'input_columns'        => '3',
				],
				53 => [
					'id'                   => '53',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes image choices classic',
					'choices'              => [
						1 => [
							'label' => 'First Choice',
							'image' => self::FIRST_CHOICE_IMG,
						],
						2 => [
							'label' => 'Second Choice',
							'image' => self::SECOND_CHOICE_IMG,
						],
						3 => [
							'label' => 'Third Choice',
							'image' => self::THIRD_CHOICE_IMG,
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'classic',
					'input_columns'        => '3',
				],
				29 => [
					'id'    => '29',
					'type'  => 'number',
					'label' => 'Numbers',
					'size'  => 'medium',
				],
				0  => [
					'id'          => '0',
					'type'        => 'name',
					'label'       => 'Name simple',
					'format'      => 'simple',
					'description' => 'Description test',
					'size'        => 'medium',
				],
				5  => [
					'id'     => '5',
					'type'   => 'name',
					'label'  => 'Name first last',
					'format' => 'first-last',
					'size'   => 'medium',
				],
				6  => [
					'id'     => '6',
					'type'   => 'name',
					'label'  => 'Name first middle last',
					'format' => 'first-middle-last',
					'size'   => 'medium',
				],
				1  => [
					'id'            => '1',
					'type'          => 'email',
					'label'         => 'Email',
					'size'          => 'medium',
					'default_value' => false,
				],
				30 => [
					'id'            => '30',
					'type'          => 'email',
					'label'         => 'Email with confirmation',
					'confirmation'  => '1',
					'size'          => 'medium',
					'default_value' => false,
				],
				31 => [
					'id'            => '31',
					'type'          => 'number-slider',
					'label'         => 'Number Slider',
					'min'           => '0',
					'max'           => '10',
					'size'          => 'medium',
					'default_value' => '0',
					'value_display' => 'Selected Value: {value}',
					'step'          => '1',
				],
				20 => [
					'id'       => '20',
					'type'     => 'gdpr-checkbox',
					'required' => '1',
					'label'    => 'GDPR Agreement',
					'choices'  => [
						1 => [
							'label' => 'I consent to having this website store my submitted information so they can respond to my inquiry.',
						],
					],
				],
				42 => [
					'id'            => '42',
					'type'          => 'divider',
					'label'         => 'Fancy fields',
					'label_disable' => '1',
				],
				32 => [
					'id'     => '32',
					'type'   => 'phone',
					'label'  => 'Phone Smart',
					'format' => 'smart',
					'size'   => 'medium',
				],
				54 => [
					'id'     => '54',
					'type'   => 'phone',
					'label'  => 'Phone International',
					'format' => 'international',
					'size'   => 'medium',
				],
				80 => [
					'id'     => '80',
					'type'   => 'phone',
					'label'  => 'Phone US',
					'format' => 'us',
					'size'   => 'medium',
                ],
				3  => [
					'id'           => '3',
					'type'         => 'address',
					'label'        => 'Address US',
					'scheme'       => 'us',
					'size'         => 'medium',
					'map_position' => 'above',
				],
				55 => [
					'id'           => '55',
					'type'         => 'address',
					'label'        => 'Address international',
					'scheme'       => 'international',
					'size'         => 'medium',
					'map_position' => 'above',
				],
				33 => [
					'id'                          => '33',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time datepicker',
					'format'                      => 'date',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				77 => [
					'id'                          => '77',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time date dropdowns',
					'format'                      => 'date',
					'size'                        => 'medium',
					'date_type'                   => 'dropdown',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				65 => [
					'id'                          => '65',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time timepicker',
					'format'                      => 'time',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				64 => [
					'id'                          => '64',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time datepicker and timepicker',
					'format'                      => 'date-time',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				45 => [
					'id'                          => '45',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time classic dropdowns and timepicker',
					'format'                      => 'date-time',
					'size'                        => 'medium',
					'date_type'                   => 'dropdown',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				34 => [
					'id'    => '34',
					'type'  => 'url',
					'label' => 'Website / URL',
					'size'  => 'medium',
				],
				35 => [
					'id'              => '35',
					'type'            => 'file-upload',
					'label'           => 'File Upload classic',
					'max_file_number' => '1',
					'style'           => 'classic',
				],
				36 => [
					'id'              => '36',
					'type'            => 'file-upload',
					'label'           => 'File Upload modern',
					'max_file_number' => '1',
					'style'           => 'modern',
				],
				4  => [
					'id'                      => '4',
					'type'                    => 'password',
					'label'                   => 'Password',
					'password-strength-level' => '3',
					'size'                    => 'medium',
				],
				56 => [
					'id'                      => '56',
					'type'                    => 'password',
					'label'                   => 'Password with confirmation',
					'confirmation'            => '1',
					'password-strength-level' => '3',
					'size'                    => 'medium',
				],
				37 => [
					'id'    => '37',
					'type'  => 'richtext',
					'label' => 'Rich Text full',
					'style' => 'full',
					'size'  => 'medium',
				],
				73 => [
					'id'    => '73',
					'type'  => 'richtext',
					'label' => 'Rich Text basic',
					'style' => 'basic',
					'size'  => 'medium',
				],
				69 => [
					'label'      => 'Layout field two columns',
					'name'       => 'Layout',
					'label_hide' => '1',
					'size'       => 'large',
					'preset'     => '50-50',
					'columns'    => [
						0 => [
							'width_preset' => '50',
							'fields'       => [
								0 => 70,
							],
						],
						1 => [
							'width_preset' => '50',
							'fields'       => [
								0 => 71,
							],
						],
					],
					'id'         => '69',
					'type'       => 'layout',
				],
				70 => [
					'id'           => '70',
					'type'         => 'text',
					'label'        => 'Single Line Text',
					'size'         => 'medium',
					'limit_count'  => '1',
					'limit_mode'   => 'characters',
					'map_position' => 'above',
				],
				71 => [
					'id'      => '71',
					'type'    => 'select',
					'label'   => 'Dropdown',
					'choices' => [
						1 => [
							'label' => 'First Choice',
						],
						2 => [
							'label' => 'Second Choice',
						],
						3 => [
							'label' => 'Third Choice',
						],
					],
					'style'   => 'classic',
					'size'    => 'medium',
				],
				38 => [
					'label'      => 'Layout field three columns',
					'name'       => 'Layout',
					'label_hide' => '1',
					'size'       => 'large',
					'preset'     => '33-33-33',
					'columns'    => [
						0 => [
							'width_preset' => '33',
							'fields'       => [
								0 => 39,
							],
						],
						1 => [
							'width_preset' => '33',
							'fields'       => [
								0 => 40,
							],
						],
						2 => [
							'width_preset' => '33',
							'fields'       => [
								0 => 41,
							],
						],
					],
					'id'         => '38',
					'type'       => 'layout',
				],
				39 => [
					'id'           => '39',
					'type'         => 'text',
					'label'        => 'Single Line Text',
					'size'         => 'medium',
					'limit_count'  => '1',
					'limit_mode'   => 'characters',
					'map_position' => 'above',
				],
				40 => [
					'id'      => '40',
					'type'    => 'select',
					'label'   => 'Dropdown',
					'choices' => [
						1 => [
							'label' => 'First Choice',
						],
						2 => [
							'label' => 'Second Choice',
						],
						3 => [
							'label' => 'Third Choice',
						],
					],
					'style'   => 'classic',
					'size'    => 'medium',
				],
				41 => [
					'id'            => '41',
					'type'          => 'email',
					'label'         => 'Email',
					'required'      => '1',
					'size'          => 'medium',
					'default_value' => false,
				],
				43 => [
					'id'            => '43',
					'type'          => 'html',
					'code'          => '<p>HTML field content with the <a href=#>link</a></p>',
					'label_disable' => '1',
				],
				81 => [
					'id'      => '81',
					'type'    => 'content',
					'content' => sprintf( '<h4>Add Text and Images to Your Form With Ease</h4><img src="%s" class="alignnone" alt="Fake image"><p>To get started, replace this text with your own.</p>', self::FIRST_CHOICE_IMG ),
					'size'    => 'medium',
					'css'     => '',
				],
				8  => [
					'id'         => '8',
					'type'       => 'rating',
					'label'      => 'Rating',
					'scale'      => '5',
					'icon'       => 'star',
					'icon_size'  => 'medium',
					'icon_color' => '#e27730',
				],
				10 => [
					'id'        => '10',
					'type'      => 'captcha',
					'required'  => '1',
					'label'     => 'Custom Captcha',
					'format'    => 'math',
					'questions' => [
						1 => [
							'question' => 'What is 7+4?',
							'answer'   => '11',
						],
					],
					'size'      => 'medium',
				],
				44 => [
					'id'        => '44',
					'type'      => 'signature',
					'label'     => 'Signature',
					'ink_color' => '#000000',
					'size'      => 'large',
				],
				57 => [
					'id'      => '57',
					'type'    => 'likert_scale',
					'label'   => 'Likert Scale classic',
					'rows'    => [
						1 => 'Item #1',
						2 => 'Item #2',
						3 => 'Item #3',
					],
					'columns' => [
						1 => 'Strongly Disagree',
						2 => 'Disagree',
						3 => 'Neutral',
						4 => 'Agree',
						5 => 'Strongly Agree',
					],
					'survey'  => '1',
					'style'   => 'classic',
					'size'    => 'large',
				],
				68 => [
					'id'      => '68',
					'type'    => 'likert_scale',
					'label'   => 'Likert Scale modern',
					'rows'    => [
						1 => 'Item #1',
						2 => 'Item #2',
						3 => 'Item #3',
					],
					'columns' => [
						1 => 'Strongly Disagree',
						2 => 'Disagree',
						3 => 'Neutral',
						4 => 'Agree',
						5 => 'Strongly Agree',
					],
					'survey'  => '1',
					'style'   => 'modern',
					'size'    => 'large',
				],
				58 => [
					'id'     => '58',
					'type'   => 'net_promoter_score',
					'label'  => 'Net Promoter Score classic',
					'survey' => '1',
					'style'  => 'classic',
					'size'   => 'large',
				],
				67 => [
					'id'     => '67',
					'type'   => 'net_promoter_score',
					'label'  => 'Net Promoter Score modern',
					'survey' => '1',
					'style'  => 'modern',
					'size'   => 'large',
				],
				46 => [
					'id'            => '46',
					'type'          => 'divider',
					'label'         => 'Payment fields',
					'label_disable' => '1',
				],
				47 => [
					'id'     => '47',
					'type'   => 'payment-single',
					'label'  => 'Single Item',
					'price'  => '128.32',
					'format' => 'single',
					'size'   => 'medium',
				],
				48 => [
					'id'     => '48',
					'type'   => 'payment-single',
					'label'  => 'Single Item user defined',
					'price'  => '256.64',
					'format' => 'user',
					'size'   => 'medium',
				],
				49 => [
					'id'                      => '49',
					'type'                    => 'payment-multiple',
					'label'                   => 'Multiple Items',
					'choices'                 => [
						1 => [
							'label' => 'First Item',
							'value' => '10.00',
						],
						2 => [
							'label' => 'Second Item',
							'value' => '25.00',
						],
						3 => [
							'label' => 'Third Item',
							'value' => '50.00',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'modern',
				],
				50 => [
					'id'                      => '50',
					'type'                    => 'payment-checkbox',
					'label'                   => 'Checkbox Items',
					'choices'                 => [
						1 => [
							'label' => 'First Item',
							'value' => '10.00',
						],
						2 => [
							'label' => 'Second Item',
							'value' => '25.00',
						],
						3 => [
							'label' => 'Third Item',
							'value' => '50.00',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'modern',
				],
				75 => [
					'id'                      => '75',
					'type'                    => 'payment-checkbox',
					'label'                   => 'Checkbox Items image choices modern',
					'choices'                 => [
						1 => [
							'label' => 'First Item',
							'value' => '10.00',
							'image' => self::FIRST_CHOICE_IMG,
						],
						2 => [
							'label' => 'Second Item',
							'value' => '25.00',
							'image' => self::SECOND_CHOICE_IMG,
						],
						3 => [
							'label' => 'Third Item',
							'value' => '50.00',
							'image' => self::THIRD_CHOICE_IMG,
						],
					],
					'show_price_after_labels' => '1',
					'choices_images'          => '1',
					'choices_images_style'    => 'modern',
					'input_columns'           => '3',
				],
				76 => [
					'id'                      => '76',
					'type'                    => 'payment-checkbox',
					'label'                   => 'Checkbox Items image choices classic',
					'choices'                 => [
						1 => [
							'label' => 'First Item',
							'value' => '10.00',
							'image' => self::FIRST_CHOICE_IMG,
						],
						2 => [
							'label' => 'Second Item',
							'value' => '25.00',
							'image' => self::SECOND_CHOICE_IMG,
						],
						3 => [
							'label' => 'Third Item',
							'value' => '50.00',
							'image' => self::THIRD_CHOICE_IMG,
						],
					],
					'show_price_after_labels' => '1',
					'choices_images'          => '1',
					'choices_images_style'    => 'classic',
					'input_columns'           => '3',
				],
				51 => [
					'id'                      => '51',
					'type'                    => 'payment-select',
					'label'                   => 'Dropdown Items classic',
					'choices'                 => [
						1 => [
							'label' => 'First Item',
							'value' => '10.00',
						],
						2 => [
							'label' => 'Second Item',
							'value' => '25.00',
						],
						3 => [
							'label' => 'Third Item',
							'value' => '50.00',
						],
					],
					'show_price_after_labels' => '1',
					'style'                   => 'classic',
					'size'                    => 'medium',
				],
				66 => [
					'id'                      => '66',
					'type'                    => 'payment-select',
					'label'                   => 'Dropdown Items modern',
					'choices'                 => [
						1 => [
							'label' => 'First Item',
							'value' => '10.00',
						],
						2 => [
							'label' => 'Second Item',
							'value' => '25.00',
						],
						3 => [
							'label' => 'Third Item',
							'value' => '50.00',
						],
					],
					'show_price_after_labels' => '1',
					'style'                   => 'modern',
					'size'                    => 'medium',
				],
				61 => [
					'id'       => '61',
					'type'     => 'credit-card',
					'label'    => 'Credit Card',
					'required' => '1',
					'size'     => 'medium',
				],
				62 => [
					'id'       => '62',
					'type'     => 'stripe-credit-card',
					'label'    => 'Stripe Credit Card',
					'required' => '1',
					'size'     => 'medium',
				],
				63 => [
					'id'       => '63',
					'type'     => 'authorize_net',
					'label'    => 'Authorize.Net',
					'required' => '1',
					'size'     => 'medium',
				],
				72 => [
					'id'    => '72',
					'type'  => 'payment-coupon',
					'label' => 'Coupon',
				],
				52 => [
					'id'    => '52',
					'type'  => 'payment-total',
					'label' => 'Total',
				],
			],
			'field_id' => 82,
			'settings' => [
				'form_title'             => 'wpf • All fields (no pages)',
				'form_desc'              => 'For testing purposes. Added by WPForms DevTools plugin.',
				'submit_text'            => 'Submit',
				'submit_text_processing' => 'Sending...',
				'ajax_submit'            => '1',
				'notification_enable'    => '1',
				'notifications'          => [
					1 => [
						'notification_name'              => 'Default Notification',
						'email'                          => '{admin_email}',
						'subject'                        => 'New Entry: {form_name} ',
						'sender_name'                    => 'WP.Local',
						'sender_address'                 => '{admin_email}',
						'replyto'                        => '{field_id="1"}',
						'message'                        => '{all_fields}',
						'entry_csv_attachment_file_name' => 'entry-details',
					],
				],
				'confirmations'          => [
					1 => [
						'name'                        => 'Default Confirmation',
						'type'                        => 'message',
						'message'                     => '<p>Thanks for contacting us! We will be in touch with you shortly.</p>',
						'message_scroll'              => '1',
						'page'                        => '',
						'message_entry_preview_style' => 'basic',
					],
				],
				'antispam_v3'            => '1',
				'anti_spam'              => [
					'country_filter' => [
						'action'        => 'allow',
						'country_codes' => [],
						'message'       => 'Sorry, this form does not accept submissions from your country.',
					],
					'keyword_filter' => [
						'message' => 'Sorry, your message can\'t be submitted because it contains prohibited words.',
					],
				],
				'form_tags'              => [],
			],
			'payments' => [
				'stripe'          => [
					'enable'              => '1',
					'payment_description' => '',
					'receipt_email'       => '',
					'recurring'           => [
						'name'   => '',
						'period' => 'yearly',
						'email'  => '',
					],
				],
				'paypal_commerce' => [
					'name'                => '',
					'billing_email'       => '',
					'billing_address'     => '',
					'shipping_address'    => '',
					'payment_description' => '',
					'recurring'           => [
						0 => [
							'pp_product_id'    => '',
							'pp_plan_id'       => '',
							'name'             => 'Plan Name #1',
							'product_type'     => 'digital',
							'recurring_times'  => 'yearly',
							'total_cycles'     => '0',
							'shipping_address' => '',
						],
					],
				],
				'authorize_net'   => [
					'enable'                   => '1',
					'payment_description'      => '',
					'receipt_email'            => '',
					'customer_name'            => '',
					'customer_billing_address' => '',
					'recurring'                => [
						'name'                     => '',
						'period'                   => 'yearly',
						'email'                    => '',
						'customer_name'            => '',
						'customer_billing_address' => '',
					],
				],
			],
			'meta'     => [
				'template' => 'wpf__all_fields_no_pages',
			],
		];
	}
}
