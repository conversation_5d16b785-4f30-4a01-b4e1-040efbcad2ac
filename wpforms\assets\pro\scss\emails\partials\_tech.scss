/* Base */
.body-inner {
	padding-top: 80px;
	padding-bottom: 80px;
}

.wrapper {
	max-width: 700px;
}

.wrapper-inner {
	background-color: $backgroundContent;
	border-radius: 12px;
	box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.05);
}

.header {
	padding: 40px 50px 40px 50px;
	border-bottom: 1px solid lighten($fontColor, 65%);
}

.footer {
	font-size: 14px;
	padding: 30px 50px 30px 50px;
	line-height: 24px;
	border-top: 1px solid lighten($fontColor, 65%);
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
	line-height: 24px;
}

/* Content */
.content {
	padding: 35px 50px 25px 50px;

	.field-name {
		padding-top: 15px;
		padding-bottom: $marginBottom;

		&:not(.field-value) {
			font-size: 18px;
			line-height: 20.7px;
		}

		/* Repeater & Layout */
		&.field-repeater-name,
		&.field-layout-name {
			font-size: 22px;
			padding-top: 25px;
			padding-bottom: 25px;
		}
	}

	.field-value {
		padding-bottom: 25px;
	}

	.field-name.field-value {
		line-height: 24px;
	}
}
