// WPForms Modern Full styles.
//
// Field Pagebreak.
//
// @since 1.8.1

div.wpforms-container-full {
	.wpforms-form {

		.wpforms-page-button {
			min-width: 90px;
		}

		.wpforms-pagebreak-center {
			text-align: center;
		}

		.wpforms-pagebreak-left {
			text-align: start;

			.wpforms-page-button {
				margin: 0 $spacing_s 0 0;
			}
		}

		.wpforms-pagebreak-right {
			text-align: end;

			.wpforms-page-button {
				margin: 0 0 0 $spacing_s;
			}
		}

		.wpforms-pagebreak-split {
			display: flex;
			justify-content: space-between;

			.wpforms-page-prev {
				margin: 0;
			}

			.wpforms-page-next {
				margin: 0 0 0 auto;
			}
		}

		.wpforms-page-indicator {
			color: var( --wpforms-label-color );

			&.progress {
				.wpforms-page-indicator-page-progress-wrap {
					background: linear-gradient( 90deg, var( --wpforms-field-border-color ) -1000%, transparent 500% );
				}
			}

			&.connector {
				.wpforms-page-indicator-page:not(.active) {
					.wpforms-page-indicator-page-number {
						border-width: var( --wpforms-field-border-size );
						border-style: var( --wpforms-field-border-style );
						border-color: var( --wpforms-field-border-color );
						opacity: 0.2;
					}
				}
			}

			&.circles {
				border-top-width: var( --wpforms-field-border-size );
				border-top-style: var( --wpforms-field-border-style );
				border-top-color: var( --wpforms-field-border-color );
				border-bottom-width: var( --wpforms-field-border-size );
				border-bottom-style: var( --wpforms-field-border-style );
				border-bottom-color: var( --wpforms-field-border-color );

				.wpforms-page-indicator-page:not(.active) {
					.wpforms-page-indicator-page-number {
						background: linear-gradient( 90deg, var( --wpforms-field-border-color ) -1000%, transparent 500% );
						color: var( --wpforms-label-color );
						opacity: 1;
					}
				}
			}
		}
	}
}

// RTL related styles.
.rtl div.wpforms-container-full {
	.wpforms-form {
		.wpforms-pagebreak-left,
		.wpforms-pagebreak-right,
		.wpforms-pagebreak-split,
		.wpforms-pagebreak-center {
			.wpforms-page-button.wpforms-page-next,
			.wpforms-page-button.wpforms-page-prev {
				margin: 0 0 0 $spacing_s;
			}
		}

		.wpforms-pagebreak-split {
			&.wpforms-clear {
				&:before {
					content: '';
					display: none;
				}

				&:after {
					content: '';
					display: none;
					clear: both;
				}
			}

			.wpforms-page-next {
				margin: 0 !important;
			}

			.wpforms-page-prev {
				margin: 0 auto 0 0;
			}
		}
	}
}
