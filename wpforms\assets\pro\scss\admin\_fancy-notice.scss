// WPForms admin styles.
//
// Fancy Notice styles.
//
// @since 1.7.4

.notice.wpforms-notice {

	&.notice-fancy-info,
	&.notice-fancy-success {
		background-color: #ffffff;
		border: 1px solid #c3c4c7;
		box-shadow: 0 1px 1px rgba( 0, 0, 0, 0.05 );
		padding: 12px;
		font-size: 14px;
		line-height: 17px;
		opacity: 1;
		transition: all .3s;

		&.is-dismissible {
			.notice-dismiss {
				top: calc( 50% - 19px);

				&:before {
					color: #a0a5aa;
				}

				&:hover {
					&:before {
						color: #d63638;
					}
				}
			}

			.wpforms-fancy-notice-buttons {
				margin-right: 28px;
			}
		}
	}

	&.notice-fancy-info {
		border-left: 4px solid #1073a7;

		.wpforms-fancy-notice-icon {
			background-color: #1073a7;
		}
	}

	&.notice-fancy-success {
		border-left: 4px solid #00a329;

		.wpforms-fancy-notice-icon {
			background-color: #00a329;
		}
	}

	.wpforms-fancy-notice {
		width: 100%;
		align-items: center;
		align-content: stretch;
		justify-content: flex-start;
		display: flex;
	}

	.wpforms-fancy-notice-icon {
		width: 25px;
		height: 25px;
		margin-inline-end: 10px;
		border-radius: 50%;
		padding: 8px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		box-sizing: revert !important;

		svg {
			max-width: 20px;
			max-height: 20px;
			fill: #ffffff;
			vertical-align: middle;
		}

		&.check svg {
			width: 16px;
			height: 16px;
		}
	}

	.wpforms-fancy-notice-title {
		font-weight: 500;
		color: #1d2327;
		margin-bottom: 2px;
	}

	.wpforms-fancy-notice-message {
		color: #50575e;
	}

	.wpforms-fancy-notice-buttons {
		margin-inline-start: auto;
		vertical-align: middle;

		& > a,
		& > button {
			vertical-align: middle;
			margin-inline-start: 10px;
		}
	}
}

@media screen and ( max-width: map-get( $breakpoints, 'ipad' ) ) {
	.wpforms-fancy-notice-icon {
		display: none !important;
	}
}
