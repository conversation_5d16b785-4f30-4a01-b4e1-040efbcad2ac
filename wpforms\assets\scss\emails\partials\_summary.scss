$primary: #333333;
$secondary: #777777;
$orange: #e27730;
$blue: #036aab;
$linkColor: #e27730;
$linkColorHover: #e27730;
$backgroundColor: #f8f8f8;
$backgroundContent: #ffffff;
$fontColor: #444444;
$tableBorder: #dddddd;
$orangeBackground: #f7f0ed;
$blueBackground: #edf3f7;

@import 'resets';
@import 'text';

/* Base */
table.body,
body {
	background-color: $backgroundColor;
	text-align: center;
}

.wrapper {
	max-width: 700px;
}

.body-inner {
	box-sizing: border-box;
	padding-bottom: 40px;
}

.container {
	margin: 0 auto 0 auto;
}

.header {
	line-height: 1;
	padding: 30px;
	text-align: center;

	.header-image {
		display: inline-block;
		margin: 0 auto 0 auto;
		max-width: 260px;
		vertical-align: middle;
	}

	img {
		display: inline-block !important;
		max-height: 180px;
		vertical-align: middle;
	}
}

// Hide the dark variation by default.
.header-wrapper {
	&.dark-mode {
		display: none;
	}
}

/* Typography */
p, td {
	-webkit-hyphens: none;
	-moz-hyphens: none;
	hyphens: none;
}

a, p, pre {
	-ms-word-break: break-word;
	word-break: break-word;
}

.content {
	p {
		font-size: 16px;
		line-height: 24px;
		margin-bottom: 0;

		+ p {
			margin-top: 24px;
		}
	}

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		color: $fontColor;
	}
}

/* Buttons */
.button-container {
	text-align: center;

	.button-wrapper {
		border-spacing: 24px;
		margin: 0 auto;
	}
}

.button-wrapper {
	border-collapse: separate;
}

.button {
	border-collapse: separate !important;
	border-radius: 3px;
	line-height: 16px;
	padding: 11.5px 15px;
	vertical-align: middle;
}

.button-link {
	color: $backgroundContent;
	line-height: 16px;
	font-size: 16px;
	font-weight: 500;
	margin: 0;
	text-decoration: none;
}

.button-orange {
	background: $orange;
	border: 1px solid $orange;
}

.button-blue {
	background: $blue;
	border: 1px solid $blue;
	color: $backgroundContent;
}

.button-blue-outline {
	background: transparent;
	border: 1px solid $blue;

	a {
		color: $blue;
	}
}

/* Content */
.content {
	.summary-container {
		> tbody {
			> tr:last-child {
				.summary-content {
					border-bottom-left-radius: 6px;
					border-bottom-right-radius: 6px;
				}
			}
		}
	}

	.summary-content {
		background-color: $backgroundContent;
		border-top-left-radius: 6px;
		border-top-right-radius: 6px;
		padding: 50px 30px;

		.summary-content-inner {

			div {
				max-width: 600px;
				margin-left: auto;
				margin-right: auto;
			}
		}

		.greeting {
			font-size: 20px;
			line-height: 24px;
			margin-bottom: 24px;
		}
	}

	.summary-trend td {
		white-space: nowrap;
	}

	.email-summaries-wrapper {
		margin-bottom: 1px;

		.summary-trend td {
			line-height: 20px;
		}
	}

	.summary-header,
	.email-summaries-overview-wrapper {
		margin-bottom: 50px;
	}

	.email-summaries-overview {
		background: $backgroundColor;
		border: 1px solid $tableBorder;
		border-collapse: separate;
		border-radius: 6px;

		td {
			padding-bottom: 20px;
			padding-top: 20px;
			vertical-align: middle;
		}

		.overview-icon {
			vertical-align: top;

			img {
				min-width: 52px;
			}
		}

		.overview-stats {
			width: 90%;
			vertical-align: top;

			h5 {
				line-height: 30px;
				margin-bottom: 0;
			}

			p {
				color: $secondary;
				line-height: 22px;
			}
		}

		.summary-trend {
			td {
				font-size: 16px;
				line-height: 16px;

				img {
					min-width: 14px;
				}
			}
		}
	}

	.email-summaries {

		th {
			background-color: $backgroundColor;
			border: 1px solid $tableBorder;
			font-size: 16px;
			font-weight: bold;
			line-height: 16px;
			padding: 15px 20px 15px 20px;
		}

		td {
			border: 1px solid $tableBorder;
			padding: 12px 20px 12px 20px;
			vertical-align: middle;
		}

		.form-name {
			font-size: 16px;
			line-height: 24px;
			width: 90%;
		}

		.entry-count {
			color: $secondary;
			font-size: 16px;
			line-height: 20px;
			text-align: center;
			vertical-align: middle;
			white-space: nowrap;

			a {
				color: $secondary;
				text-decoration: underline;

				&:hover {
					color: $secondary;
				}
			}
		}

		.summary-trend {
			td {
				img {
					min-width: 10px;
				}
			}
		}
	}

	.summary-trend {
		table {
			border-collapse: separate;

			tr td {
				padding: 0;
				border: 0 !important;
			}
		}
	}
}

$trends: ("upward": "#46b450", "downward": "#d63637");

@each $dir, $color in $trends {
	.trend-#{$dir} {
		td {
			color: #{$color} !important;
		}
	}
}

/* Notice */
.summary-notice {
	text-align: center;

	h4 {
		line-height: 36px;
		margin-bottom: 16px;
	}

	p {
		font-size: 16px;
		line-height: 24px;
	}

	> td {
		padding: 50px 50px 26px 50px;
	}

	&:last-child {
		> td {
			border-bottom-left-radius: 6px;
			border-bottom-right-radius: 6px;
		}
	}

	+ .summary-notice {
		margin-top: 1px;
	}
}

tr:last-child > .summary-notice-content,
.summary-notice-icon {
	padding-bottom: 24px;
}

.summary-notice-divider {
	height: 1px;
	font-size: 0;
	line-height: 1px;
}

/* Info Block */
.summary-info-block {
	background-color: $orangeBackground;
}

/* Notification Block */
.summary-notification-block {
	background-color: $blueBackground;

	p a {
		color: $blue;
	}
}

/* Footer */
.footer {
	color: $secondary;
	font-size: 13px;
	line-height: 20px;
	padding: 20px 30px 20px 30px;
	text-align: center;

	a {
		color: $secondary;
		text-decoration: underline;

		&:hover {
			color: $secondary;
		}
	}
}

/* Direction */
$directions: ("rtl": "right", "ltr": "left");

@each $dir, $align in $directions {
	[dir="#{$dir}"] {
		th {
			text-align: #{$align};

			&:first-child {
				@if $dir == 'rtl' {
					border-left: 0 !important;
				} @else {
					border-right: 0 !important;
				}
			}

			&:last-child {
				@if $dir == 'rtl' {
					border-right: 0 !important;
				} @else {
					border-left: 0 !important;
				}
			}
		}

		.summary-content-inner {
			@if $dir == 'rtl' {
				text-align: right;
			} @else {
				text-align: left;
			}
		}

		.summary-trend {
			td:last-child {
				padding-#{$align}: 5px;
			}
		}

		.email-summaries-overview {
			.summary-trend,
			.overview-stats {
				padding-#{$align}: 16px;
			}

			.overview-icon {
				@if $dir == 'rtl' {
					padding-right: 20px;
				} @else {
					padding-left: 20px;
				}
			}

			.summary-trend {
				@if $dir == 'rtl' {
					padding-left: 20px;
				} @else {
					padding-right: 20px;
				}
			}
		}
	}
}
