/* global List */

/**
 * Requirements.
 *
 * @since {VERSION}
 */

const WPFRequirements = window.WPFRequirements || ( function( document, window, $ ) {
	/**
	 * Elements holder.
	 *
	 * @since {VERSION}
	 *
	 * @type {Object}
	 */
	const el = {
		$requirements: $( '.wpforms-admin-devtools-requirements' ),
	};

	/**
	 * Runtime variables.
	 *
	 * @since {VERSION}
	 *
	 * @type {Object}
	 */
	const vars = {};

	/**
	 * Public functions and properties.
	 *
	 * @since {VERSION}
	 *
	 * @type {Object}
	 */
	const app = {

		/**
		 * Start the engine.
		 *
		 * @since {VERSION}
		 */
		init() {
			$( app.ready );
		},

		/**
		 * Document ready.
		 *
		 * @since {VERSION}
		 */
		ready() {
			app.initVars();
			app.events();
		},

		/**
		 * Init variables.
		 *
		 * @since {VERSION}
		 */
		initVars() {
			// Requirements list object.
			vars.requirementsList = new List(
				'wpf-req-container',
				{
					valueNames: [
						'wpf-req-item-name',
					],
				}
			);
		},

		/**
		 * Register JS events.
		 *
		 * @since {VERSION}
		 */
		events() {
			el.$requirements
				.on( 'keyup', '#wpforms-admin-devtools-requirements-search', app.searchRequirements );
		},

		/**
		 * Search requirements.
		 *
		 * @since {VERSION}
		 */
		searchRequirements() {
			vars.requirementsList.search( $( this ).val() );
			// Show/hide the not found message.
			el.$requirements.find( ' .not-found' ).toggle( $( '.list' ).children().length === 0 );
		},
	};

	// Provide access to public functions/properties.
	return app;
}( document, window, jQuery ) );

// Initialize.
WPFRequirements.init();
