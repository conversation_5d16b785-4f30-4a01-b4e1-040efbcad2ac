<?php
/**
 * ModSecurity Configuration Files Detector.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper\Detectors\ModSecurity;

use Exception;
use WPForms\SupportHelper\Detectors\Base\AbstractDetector;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Configuration File Detection for ModSecurity.
 *
 * @since {VERSION}
 */
class ConfigurationFilesDetector extends AbstractDetector {

	/**
	 * Run the detection method.
	 *
	 * @since {VERSION}
	 *
	 * @return array Detection result.
	 *
	 * @throws Exception When file operations fail or security validation fails.
	 */
	public function detect(): array {

		$detected_files = [];

		// Initialize WordPress filesystem API.
		$filesystem = $this->get_wp_filesystem();

		if ( ! $filesystem ) {
			return $this->format_result(
				false,
				__( 'WordPress filesystem API not available', 'wpf-support-helper' ),
				[]
			);
		}

		// Check common ModSecurity configuration file locations.
		$config_paths = [
			'/etc/apache2/mods-enabled/security2.conf',
			'/etc/apache2/mods-available/security2.conf',
			'/etc/httpd/conf.d/mod_security.conf',
			'/etc/httpd/conf.modules.d/10-mod_security.conf',
			'/usr/local/apache2/conf/modsecurity.conf',
			ABSPATH . '.htaccess',
		];

		foreach ( $config_paths as $config_path ) {
			$content = $this->read_config_file( $config_path );

			if ( $content && ( stripos( $content, 'modsecurity' ) !== false || stripos( $content, 'mod_security' ) !== false ) ) {
				$detected_files[] = $config_path;
			}
		}

		// Check for ModSecurity rules in .htaccess.
		$htaccess_path    = ABSPATH . '.htaccess';
		$htaccess_content = $this->read_config_file( $htaccess_path );

		if ( $htaccess_content && ( stripos( $htaccess_content, 'SecRule' ) !== false || stripos( $htaccess_content, 'SecAction' ) !== false ) ) {
			$detected_files[] = $htaccess_path . ' (contains ModSecurity rules)';
		}

		if ( ! empty( $detected_files ) ) {
			return $this->format_result(
				true,
				__( 'ModSecurity configuration files found', 'wpf-support-helper' ),
				$detected_files
			);
		}

		return $this->format_result(
			false,
			__( 'No ModSecurity configuration files found in common locations', 'wpf-support-helper' ),
			[]
		);
	}

	/**
	 * Get the detector name.
	 *
	 * @since {VERSION}
	 *
	 * @return string Detector name.
	 */
	public function get_name(): string {

		return __( 'Configuration File Detection', 'wpf-support-helper' );
	}

	/**
	 * Get the detector description.
	 *
	 * @since {VERSION}
	 *
	 * @return string Detector description.
	 */
	public function get_description(): string {

		return __( 'Look for ModSecurity configuration files', 'wpf-support-helper' );
	}

	/**
	 * Safely read configuration file content.
	 *
	 * @since {VERSION}
	 *
	 * @param string $file_path Path to configuration file.
	 *
	 * @return string File content or empty string on failure.
	 */
	private function read_config_file( string $file_path ): string {

		// Validate input length to prevent buffer overflow attacks.
		if ( strlen( $file_path ) > 4096 ) { // 4KB max path length.
			$this->logger->error( 'Configuration file path too long: ' . strlen( $file_path ) . ' bytes' );

			return '';
		}

		// Validate file path to prevent directory traversal attacks.
		if ( ! $this->is_safe_config_path( $file_path ) ) {
			$this->logger->error( 'Unsafe configuration file path detected: ' . $file_path );

			return '';
		}

		// Initialize WordPress filesystem API.
		$filesystem = $this->get_wp_filesystem();

		if ( ! $filesystem ) {
			$this->logger->error( 'WordPress filesystem API not available for configuration file: ' . $file_path );

			return '';
		}

		// Use WordPress filesystem API to check file accessibility.
		if ( ! $this->is_file_accessible( $filesystem, $file_path ) ) {
			return '';
		}

		// Check file size limit (1MB max for config files) using WordPress filesystem API.
		$max_file_size = 1024 * 1024; // 1MB.
		$file_size     = $filesystem->size( $file_path );

		// Handle case where size() returns false (file doesn't exist or can't be accessed).
		if ( $file_size === false ) {
			$this->logger->error( 'Unable to determine configuration file size: ' . $file_path );

			return '';
		}

		if ( $file_size > $max_file_size ) {
			$this->logger->error( 'Configuration file too large: ' . $file_path . ' (' . $file_size . ' bytes)' );

			return '';
		}

		// Check available memory before reading file.
		// This prevents memory exhaustion when reading configuration files
		// by ensuring we have sufficient memory available (with 25% buffer).
		if ( ! $this->check_memory_limit( $file_size ) ) {
			$this->logger->error( 'Insufficient memory to read configuration file: ' . $file_path );

			return '';
		}

		// Read file content using WordPress filesystem API - this is the WordPress-approved method
		// for file operations instead of using direct PHP file functions like file_get_contents().
		$content = $filesystem->get_contents( $file_path );

		// Handle case where get_contents() returns false.
		if ( $content === false ) {
			$this->logger->error( 'Failed to read configuration file using WordPress filesystem API: ' . $file_path );

			return '';
		}

		return $content;
	}

	/**
	 * Validate configuration file path for security.
	 *
	 * @since {VERSION}
	 *
	 * @param string $file_path File path to validate.
	 *
	 * @return bool True if path is safe, false otherwise.
	 */
	private function is_safe_config_path( string $file_path ): bool {

		// Normalize path separators.
		$file_path = str_replace( '\\', '/', $file_path );

		// Check for directory traversal patterns.
		if ( strpos( $file_path, '../' ) !== false || strpos( $file_path, '..' ) !== false ) {
			return false;
		}

		// Define allowed configuration file locations.
		$allowed_paths = [
			'/etc/apache2/',
			'/etc/httpd/',
			'/usr/local/apache2/',
			ABSPATH,
		];

		// Check if file path starts with any allowed path.
		foreach ( $allowed_paths as $allowed_path ) {
			if ( strpos( $file_path, $allowed_path ) === 0 ) {
				return true;
			}
		}

		return false;
	}
}
