<?php
/**
 * Dependency Injection Container Interface.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper\Container;

use Exception;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Interface for dependency injection container.
 *
 * @since {VERSION}
 */
interface ContainerInterface {

	/**
	 * Register a service in the container.
	 *
	 * @since {VERSION}
	 *
	 * @param string   $id      Service identifier.
	 * @param callable $factory Factory function to create the service.
	 * @param bool     $shared  Whether the service should be shared (singleton).
	 *
	 * @return void
	 */
	public function register( string $id, callable $factory, bool $shared = true ): void;

	/**
	 * Get a service from the container.
	 *
	 * @since {VERSION}
	 *
	 * @param string $id Service identifier.
	 *
	 * @return mixed Service instance.
	 * @throws Exception When service is not found.
	 */
	public function get( string $id );

	/**
	 * Check if a service is registered in the container.
	 *
	 * @since {VERSION}
	 *
	 * @param string $id Service identifier.
	 *
	 * @return bool True if service is registered, false otherwise.
	 */
	public function has( string $id ): bool;

	/**
	 * Remove a service from the container.
	 *
	 * @since {VERSION}
	 *
	 * @param string $id Service identifier.
	 *
	 * @return void
	 */
	public function remove( string $id ): void;

	/**
	 * Get all registered service identifiers.
	 *
	 * @since {VERSION}
	 *
	 * @return array Array of service identifiers.
	 */
	public function get_services(): array;
}
