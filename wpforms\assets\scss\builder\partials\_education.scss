// Form Builder styles.
//
// Education.
//
// @since 1.6.8

@media screen and (max-width: 1023px) {
	.wpforms-dyk-row {
		display: none !important;
	}
}

// "Did You Know?" notices.
.wpforms-dyk {
	.wpforms-dyk-fbox {
		align-content: stretch;
		align-items: center;
		background-color: $color_lightest_blue;
		border-inline-start: 4px solid $color_blue;
		display: flex;
		font-size: $font_size_s;
		gap: $spacing_s;
		justify-content: space-between;
		opacity: 1;
		padding-block: 12px;
		padding-inline-end: 12px;
		padding-inline-start: $spacing_m;

		@include transition( all, $transition_slow, ease-out );

		&.out {
			opacity: 0;
			transform: scaleY( 0 );
		}
	}

	.wpforms-dyk-message {
		b {
			font-weight: 700;
		}
	}

	.wpforms-dyk-buttons {
		align-items: center;
		display: flex;
		gap: $spacing_s;

		.learn-more {
			text-decoration: underline;

			&:hover {
				color: $color_primary_text;
			}
		}

		.wpforms-btn-md {
			font-size: $font_size_s;
			min-height: auto;
		}
	}
}

// SMTP Education notice.
.wpforms-smtp-education-notice {
	background: $color_light_background_notice;
	border-radius: 3px;
	margin: 30px 0;
	padding-block: 18px;
	padding-inline-end: 100px;
	padding-inline-start: 20px;
	position: relative;

	&:after {
		content: '';
		display: block;
		width: 97px;
		height: 78px;
		position: absolute;
		bottom: 0;
		inset-inline-end: 0;
		background-image: url('../../images/smtp/pattie-2.svg');
		background-size: 100%;
		z-index: 1;
	}

	&-title {
		font-weight: 600;
		font-size: 16px;
		line-height: 20px;
	}

	&-description {
		font-weight: 400;
		font-size: 14px;
		line-height: 17px;
		color: $color_secondary_text;
		margin-top: 5px;

		a {
			color: $color_orange;
			font-weight: 600;

			&:hover {
				color: $color_dark_orange;
			}
		}
	}

	&-dismiss-button {
		z-index: 2;

		&:before {
			content: none!important;
		}

		position: absolute;
		inset-inline-end: 0;
		top: 0;
		padding: 5px;
		color: $color_close;

		&:hover {
			color: $color_close_hover;
		}
	}

	@media (max-width: 1024px) {
		padding: 18px 20px;

		&::after {
			margin-top: -18px;
			position: relative;
			bottom: -18px;
			margin-inline-start: auto;
			inset-inline-end: auto;
		}
	}
}

// Calculations Educational notice (alert).
.wpforms-educational-alert {
	&.wpforms-calculations {
		position: relative;
		padding-right: 30px;

		.wpforms-dismiss-button {
			position: absolute;
			inset-inline-end: 1px;
			top: 2px;
		}

		.wpforms-badge-block {
			margin-bottom: 5px;
		}

		h3 {
			font-size: inherit;
			margin: inherit;
			margin-bottom: 1px;
		}
	}

	.wpforms-educational-badge {
		font-size: 8px;
		font-style: normal;
		font-weight: 700;
		line-height: 10px;
		letter-spacing: 0.4px;
		text-transform: uppercase;
		text-align: center;
		padding: 5px 7px;
		border-radius: 3px;
		background-color: #E5F6E9;

		&-green {
			color: #30B450;
		}

		& + h4 {
			margin-top: 10px;
		}
	}
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert {
	&.wpforms-pro-fields-notice {
		margin: 30px 0 10px 0;
		align-items: flex-start;
		padding: 0;
		background: #FDFAF2;
		border: 1px solid rgba( 0, 0, 0, 0.07 );
		box-shadow: 0px 1px 2px rgba( 0, 0, 0, 0.07 );
		border-radius: 6px;
		overflow: hidden;
		max-height: none;

		.wpforms-alert-message {
			padding: 20px 20px 20px 50px;
			background-image: url( "../../images/integrations/ai/bulb-orange.svg" );
			background-size: 14px 21px;
			background-repeat: no-repeat;
			background-position: 20px 22px;

			h3 {
				margin-top: 0;
				margin-bottom: 4px;
				line-height: 21px;
				font-size: 17px;
			}

			p {
				margin: 0;
				line-height: 21px;
			}

			a {
				color: $color_orange;

				&:hover {
					color: $color_dark_orange;
				}
			}
		}

		&.wpforms-alert-error {
			background: $color_lightest_red;

			.wpforms-alert-message {
				background-image: url( "../../images/integrations/ai/bulb-red.svg" );
			}
		}

		.wpforms-alert-buttons {
			padding: 20px;

			button {
				padding: 0;
				opacity: 0.7;

				&:before {
					content: '';
					background-image: url( "../../images/integrations/ai/close.svg" );
					background-size: 12px 12px;
					width: 12px;
					height: 12px;
				}

				&:hover {
					opacity: 1;
				}
			}
		}
	}
}
