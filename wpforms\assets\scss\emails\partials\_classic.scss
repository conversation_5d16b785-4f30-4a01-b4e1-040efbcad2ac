/* Base */
.body-inner {
	padding-top: 50px;
	padding-bottom: 50px;
}

.wrapper {
	max-width: 660px;
}

.wrapper-inner {
	background-color: $backgroundContent;
	border: 1px solid lighten($fontColor, 60%);
	padding: 25px 30px 50px 30px;
}

.header {
	text-align: center;
	padding: 0 0 50px 0;

	.header-image {
		/* This is needed to center the logo in Outlook. */
		margin: 0 auto 0 auto;
	}
}

.footer {
	font-size: 12px;
	line-height: 24px;
	text-align: center;
	padding-top: 25px;
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
	line-height: 22px;
}

/* Content */
.content {
	.field-name {
		padding-top: 25px;
		padding-bottom: $marginBottom;
	}

	.field-value {
		padding-bottom: 25px;
		border-bottom: 1px solid lighten($fontColor, 65%);
	}

	.field-name.field-value {
		line-height: 22px;
	}
}

// Order summary table.
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview {
	border-radius: 0;
}

.wpforms-layout-table {
	> td {
		border-bottom: 1px solid lighten($fontColor, 65%);
	}

	&-display-blocks,
	&-display-columns {
		.wpforms-layout-table-row {
			td {
				&.field-value {
					padding-bottom: 0;
				}
			}
		}
	}
}
