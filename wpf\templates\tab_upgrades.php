<?php
/**
 * View for the Upgrades tab.
 *
 * @var string $nonce    WordPress nonce.
 * @var string $notices  Notices.
 * @var array  $upgrades Upgrades.
 */

?>

<div class="wpforms-admin-content wpforms-admin-settings">
	<?php
		echo $notices; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
	?>

	<form method="post" enctype="multipart/form-data">
		<input type="hidden" name="nonce" value="<?php echo esc_html( $nonce ); ?>">

		<div class="wpforms-setting-row wpforms-setting-row-file">
			<span class="wpforms-setting-label">
				<label for="wpforms-admin-devtools-upgrades-file">File</label>
			</span>

			<span class="wpforms-setting-field">
				<input type="file" id="wpforms-admin-devtools-upgrades-file" name="wpforms_upgrades_file">
			</span>
		</div>

		<p class="submit wpforms-admin-page">
			<button type="submit" class="wpforms-btn wpforms-btn-md wpforms-btn-orange">
				Save
			</button>
		</p>
	</form>

	<?php if ( $upgrades ) : ?>
		<table class="widefat fixed">
			<thead>
				<tr>
					<th class="manage-column column-columnname">Plugin</th>
					<th class="manage-column column-columnname">Slug</th>
					<th class="manage-column column-columnname">Version</th>
					<th class="manage-column column-columnname">WP</th>
					<th class="manage-column column-columnname">PHP</th>
					<th class="manage-column column-columnname">File</th>
					<th class="manage-column column-columnname">Action</th>
				</tr>
			</thead>
			<?php
			foreach ( $upgrades as $upgrade_id => $upgrade ) :
				$upgrade = wp_parse_args(
					$upgrade,
					[
						'name'    => '',
						'slug'    => '',
						'version' => '',
						'wp'      => '',
						'php'     => '',
						'file'    => '',
					]
				);
				?>
				<tr>
					<td class="column-columnname"><?php echo esc_html( $upgrade['name'] ); ?></td>
					<td class="column-columnname"><?php echo esc_html( $upgrade['slug'] ); ?></td>
					<td class="column-columnname"><?php echo esc_html( $upgrade['version'] ); ?></td>
					<td class="column-columnname"><?php echo esc_html( $upgrade['wp'] ); ?></td>
					<td class="column-columnname"><?php echo esc_html( $upgrade['php'] ); ?></td>
					<td class="column-columnname"><?php echo esc_html( $upgrade['file'] ); ?></td>
					<td class="column-columnname">
						<form method="post">
							<input type="hidden" name="nonce" value="<?php echo esc_attr( $nonce ); ?>">
							<input type="hidden" name="wpforms_admin_devtools_delete_upgrade_id" value="<?php echo esc_attr( $upgrade_id ); ?>">

							<button type="submit" class="wpforms-btn wpforms-btn-md wpforms-btn-red">
								Delete
							</button>
						</form>
					</td>
				</tr>
			<?php endforeach; ?>
		</table>
	<?php endif; ?>
</div>
