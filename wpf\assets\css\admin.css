.wpforms-admin-page .wpforms-btn {
	border: 0;
	border-radius: 3px;
	cursor: pointer;
	display: inline-block;
	margin: 0;
	text-decoration: none;
	text-align: center;
	vertical-align: middle;
	white-space: nowrap;
	box-shadow: none;
}

.wpforms-admin-page .wpforms-btn-block {
	display: block;
	width: 100%;
}

.wpforms-admin-page .wpforms-btn-md {
	font-size: 13px;
	font-weight: 600;
	padding: 8px 12px;
	min-height: 35px;
}

.wpforms-admin-page .wpforms-btn-lg {
	font-size: 16px;
	font-weight: 600;
	padding: 16px 28px;
}

.wpforms-admin-page .wpforms-btn-orange {
	background-color: #e27730;
	border-color: #e27730;
	color: #fff;
}

.wpforms-admin-page .wpforms-btn-orange:not(.disabled):hover {
	background-color: #b85a1b;
	border-color: #b85a1b;
}

.wpforms-admin-page .wpforms-btn-grey {
	background-color: #eee;
	border-color: #ccc;
	color: #666;
}

.wpforms-admin-page .wpforms-btn-grey:hover {
	background-color: #d7d7d7;
	border-color: #ccc;
	color: #444;
}

.wpforms-admin-page .wpforms-btn-light-grey {
	background-color: #f5f5f5;
	border: 1px solid #ccc;
	color: #666;
}

.wpforms-admin-page .wpforms-btn-light-grey:hover {
	background-color: #eee;
	color: #444;
}

.wpforms-admin-page .wpforms-btn-trans-green {
	background-color: unset;
	color: #2a9b39;
}

.wpforms-admin-page .wpforms-btn-trans-green:hover {
	background-color: #2a9b39;
	color: #fff;
}

.wpforms-admin-page .wpforms-btn-trans-green .underline {
	position: relative;
}

.wpforms-admin-page .wpforms-btn-trans-green .underline:after {
	content: " ";
	border-bottom: 1px dashed #2a9b39;
	position: absolute;
	bottom: -5px;
	left: 0;
	width: 100%;
}

.wpforms-admin-page .wpforms-btn-trans-green .dashicons {
	height: 18px;
}

.wpforms-admin-page .wpforms-alert {
	padding: 16px;
	margin-bottom: 18px;
	border: 1px solid transparent;
}

.wpforms-admin-page .wpforms-alert h4 {
	margin-top: 0;
	color: inherit;
}

.wpforms-admin-page .wpforms-alert p {
	margin: 0 0 15px 0;
}

.wpforms-admin-page .wpforms-alert p:last-of-type {
	margin: 0;
}

.wpforms-admin-page .wpforms-alert.wpforms-alert-nomargin {
	margin: 0;
}

.wpforms-admin-page .wpforms-alert.wpforms-alert-small {
	font-size: 12px;
}

.wpforms-admin-page .wpforms-alert.wpforms-alert-success {
	background-color: #dff0d8;
	border-color: #d6e9c6;
	color: #3c763d;
}

.wpforms-admin-page .wpforms-alert.wpforms-alert-info {
	background-color: #d9edf7;
	border-color: #bce8f1;
	color: #31708f;
}

.wpforms-admin-page .wpforms-alert.wpforms-alert-warning {
	background-color: #fcf8e3;
	border-color: #faebcc;
	color: #8a6d3b;
}

.wpforms-admin-page .wpforms-alert.wpforms-alert-danger {
	background-color: #f2dede;
	border-color: #ebccd1;
	color: #a94442;
}

#wpforms-header-temp {
	margin: 0;
	position: absolute;
	top: -1px;
	left: 20px;
	right: 20px;
	z-index: 99;
}

#wpforms-header {
	border-top: 3px solid #e27730;
	padding: 22px 20px;
}

#wpforms-header img {
	display: block;
	margin: 0;
	max-width: 235px;
}

.wpforms-admin-wrap .page-title {
	background-color: #fff;
	margin: 0 0 20px 0;
	padding: 15px 20px;
}

.wpforms-admin-wrap .page-title .add-new-h2 {
	margin-left: 28px;
}

.wpforms-admin-tabs {
	background-color: #fff;
	margin: 0 0 20px 0;
	padding: 0 20px;
	list-style: none;
	font-size: 14px;
}

.wpforms-admin-tabs li {
	margin: 0;
	padding: 0;
	float: left;
	position: relative;
}

.wpforms-admin-tabs li:last-of-type {
	margin: 0;
}

.wpforms-admin-tabs li a {
	color: #666;
	display: block;
	padding: 20px 0 18px 0;
	text-decoration: none;
	border-bottom: 2px solid #fff;
	box-shadow: none;
}

.wpforms-admin-tabs li a:hover {
	border-color: #999;
}

.wpforms-admin-tabs li a.active {
	border-color: #e27730;
}

.wpforms-admin-tabs .sub-menu {
	position: absolute;
	top: 100%;
	left: 0;
	width: 180px;
	background-color: #f9f9f9;
	display: none;
	z-index: 1111;
	box-shadow: 0 0 5px #ccc;
}

.wpforms-admin-tabs .sub-menu li {
	width: 100%;
}


.wpforms-admin-tabs li.has-child > a {
	padding-right: 20px;
}

.wpforms-admin-tabs li.has-child:has(.sub-menu li a.active) > a {
	border-color: #e27730;
}

.wpforms-admin-tabs li.has-child > a::after {
	content: "\f078";
	font-family: FontAwesome, sans-serif;
	position: absolute;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
}

.wpforms-admin-tabs li.has-child > a:hover {
	border: none;
}

.wpforms-admin-tabs li.has-child .sub-menu a:hover {
	border-bottom: 2px solid #fff;
}


.wpforms-admin-tabs .sub-menu li a {
	display: block;
	padding: 10px;
}

.wpforms-admin-tabs .sub-menu li a.active, .wpforms-admin-tabs .sub-menu li a:hover {
	border-color: #fff;
	color: #e27730;
}

.wpforms-admin-tabs li.has-child:hover > ul.sub-menu {
	display: block;
}

#wpcontent {
	padding-left: 0 !important;
	position: relative;
}

.wpforms-admin-page #screen-meta-links, .wpforms-admin-page #screen-meta {
	display: none;
}

.wpforms-admin-page .wpforms-hide {
	display: none;
}

.wpforms-admin-page .wpforms-h1-placeholder {
	display: none;
}

.wpforms-admin-page .notice {
	display: none;
}

.wpforms-admin-wrap {
	margin: 0;
}

.wpforms-admin-wrap .notice {
	margin-left: 20px !important;
	margin-right: 20px !important;
}

.wpforms-admin-content {
	padding-left: 20px;
	padding-right: 20px;
}

.wpforms-admin-content:before {
	content: " ";
	display: table;
}

.wpforms-admin-content:after {
	clear: both;
	content: " ";
	display: table;
}

.wpforms-clear:before {
	content: " ";
	display: table;
}

.wpforms-clear:after {
	clear: both;
	content: " ";
	display: table;
}

.wpforms-admin-columns > div[class*="-column-"] {
	float: left;
}

.wpforms-admin-columns .wpforms-admin-column-20 {
	width: 20%;
}

.wpforms-admin-columns .wpforms-admin-column-33 {
	width: 33.33333%;
}

.wpforms-admin-columns .wpforms-admin-column-40 {
	width: 40%;
}

.wpforms-admin-columns .wpforms-admin-column-50 {
	width: 50%;
}

.wpforms-admin-columns .wpforms-admin-column-60 {
	width: 60%;
}

.wpforms-admin-columns .wpforms-admin-column-80 {
	width: 80%;
}

.wpforms-admin-columns .wpforms-admin-column-last {
	float: right !important;
}

.wpforms-admin-columns:after {
	content: "";
	display: table;
	clear: both;
}

#wpforms-overview .tablenav.top {
	margin-top: 0;
}

#wpforms-overview .wp-list-table .column-id {
	width: 30px;
}

#wpforms-overview .wp-list-table .column-entries {
	width: 70px;
}

#wpforms-overview .wp-list-table .column-shortcode {
	width: 150px;
}

#wpforms-overview .wp-list-table .column-created {
	width: 145px;
}

.wpforms-admin-settings *,
.wpforms-admin-settings *::before,
.wpforms-admin-settings *::after {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.wpforms-admin-settings .wpforms-setting-row {
	border-bottom: 1px solid #e4e4e4;
	padding: 30px 0;
	font-size: 14px;
	line-height: 1.3;
}

.wpforms-admin-settings .wpforms-setting-row:first-of-type {
	padding-top: 10px !important;
}

@media (max-width: 781px) {
	.wpforms-admin-settings .wpforms-setting-row {
		padding: 20px 0;
	}
}

.wpforms-admin-settings .wpforms-setting-row.section-heading {
	padding: 20px 0;
}

.wpforms-admin-settings .wpforms-setting-row.section-heading.no-desc h4 {
	margin: 0;
}

.wpforms-admin-settings .wpforms-setting-row.section-heading .wpforms-setting-field {
	margin: 0;
	max-width: 1000px;
}

.wpforms-admin-settings .wpforms-setting-row.tools p, .wpforms-admin-settings .wpforms-setting-row.tools p:first-of-type {
	margin: 0 0 16px 0;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-checkbox input[type=checkbox] {
	float: left;
	margin: 1px 0 0 0;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-checkbox .desc {
	margin: 0 0 0 30px;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-text .wpforms-setting-label {
	padding-top: 8px;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-select .wpforms-setting-label {
	padding-top: 8px;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field input[type=radio] {
	margin: -3px 10px 0 0;
}

.wpforms-admin-settings .wpforms-setting-row.wpforms-setting-row-radio .wpforms-setting-field label {
	margin-right: 30px;
	display: inline-block;
}

.wpforms-admin-settings .wpforms-setting-row h4 {
	font-size: 20px;
	font-weight: 700;
	margin: 0 0 6px 0;
}

.wpforms-admin-settings .wpforms-setting-row h3 {
	font-size: 24px;
	font-weight: 600;
	margin: 0 0 20px 0;
}

.wpforms-admin-settings .wpforms-setting-row p {
	margin: 12px 0 0;
	font-size: 14px;
	line-height: 1.3;
}

.wpforms-admin-settings .wpforms-setting-row p:first-of-type {
	margin: 8px 0 0;
}

.wpforms-admin-settings .wpforms-setting-row p.desc {
	font-style: italic;
	color: #666;
}

.wpforms-admin-settings .wpforms-setting-row p.discount-note {
	font-style: italic;
	color: #666;
}

.wpforms-admin-settings .wpforms-setting-row p.discount-note strong {
	color: green;
}

.wpforms-admin-settings .wpforms-setting-row input[type=text],
.wpforms-admin-settings .wpforms-setting-row input[type=url],
.wpforms-admin-settings .wpforms-setting-row input[type=password] {
	background-color: #fff;
	border: 1px solid #ddd;
	border-radius: 3px;
	box-shadow: none;
	color: #333;
	display: inline-block;
	vertical-align: middle;
	padding: 7px 12px;
	margin: 0 10px 0 0;
	width: 400px;
	min-height: 35px;
	line-height: 1.3;
}

.wpforms-admin-settings .wpforms-setting-row input[type=text]:focus,
.wpforms-admin-settings .wpforms-setting-row input[type=url]:focus,
.wpforms-admin-settings .wpforms-setting-row input[type=password]:focus {
	border-color: #bbb;
}

.wpforms-admin-settings .wpforms-setting-row button {
	margin-right: 10px;
}

.wpforms-admin-settings .wpforms-setting-row .progress-bar {
	background: #fff;
	border: 1px solid #e27730;
	height: 30px;
	width: 100%;
	position: relative;
	border-radius: 3px;
	margin: 0 0 16px 0;
}

.wpforms-admin-settings .wpforms-setting-row .progress-bar.complete {
	border: 1px solid #2a9b39;
}

.wpforms-admin-settings .wpforms-setting-row .progress-bar.complete .bar {
	background-color: #2a9b39;
}

.wpforms-admin-settings .wpforms-setting-row .progress-bar.complete .bar:after {
	content: none;
	display: none;
}

.wpforms-admin-settings .wpforms-setting-row .progress-bar .bar {
	display: block;
	background: #e27730;
	height: 100%;
	left: 0;
	position: absolute;
	-webkit-transition: width 0.5s ease-in-out;
	-moz-transition: width 0.5s ease-in-out;
	-o-transition: width 0.5s ease-in-out;
	transition: width 0.5s ease-in-out;
}

@-webkit-keyframes progress {
	to {
		background-position: 60px 0;
	}
}

@-moz-keyframes progress {
	to {
		background-position: 60px 0;
	}
}

@keyframes progress {
	to {
		background-position: 60px 0;
	}
}

.wpforms-admin-settings .wpforms-setting-row .progress-bar .bar:after {
	content: '';
	display: block;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	-webkit-animation: progress 1s linear infinite;
	-moz-animation: progress 1s linear infinite;
	animation: progress 1s linear infinite;
	background-repeat: repeat-x;
	background-size: 60px 60px;
	background-image: -webkit-linear-gradient(-45deg, transparent 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
	background-image: linear-gradient(-45deg, transparent 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.wpforms-admin-settings .wpforms-setting-label {
	display: block;
	float: left;
	width: 200px;
	padding: 0;
}

.wpforms-admin-settings .wpforms-setting-label label {
	display: block;
	font-weight: 600;
}

.wpforms-admin-settings .wpforms-setting-field {
	display: block;
	margin: 0 0 0 200px;
	max-width: 800px;
}

.wpforms-admin-settings p.submit {
	margin: 0;
	padding: 25px 0;
}

.wpforms-admin-settings .info-area,
.wpforms-admin-settings .pre-error {
	display: block;
	width: 100%;
	max-width: 1000px;
	height: 450px;
	font-family: Menlo, Monaco, monospace;
	font-size: 12px;
	background: #fff;
	box-shadow: none;
	border: 1px solid #ddd;
	white-space: pre;
	overflow: auto;
	padding: 20px;
}

.wpforms-admin-settings .pre-error {
	height: auto;
	max-height: 250px;
	margin-bottom: 20px;
}

#generate_entries button,
#generate_templates button {
	margin-right: 10px;
}

.generator-progress-bar {
	position: fixed;
	top: 0;
	left: 0;
	height: 3px;
	background: #e27730;
	display: none;
	z-index: 99999;
	box-shadow: 0 0 5px #e27730;
	-webkit-transition: width 0.2s, height 4s;
	transition: width 0.2s, height 4s;
}

.disabled {
	opacity: 0.5;
}

.wpforms-admin-devtools-changelog ul {
	list-style-type: disc;
	padding: 0 0 0 24px;
}

.wpforms-admin-devtools-changelog p,
.wpforms-admin-devtools-changelog li {
	font-size: 14px;
}

.wpf-fail {
	color: red;
}

.wpf-fail {
	font-weight: 600;
}

.wpforms-admin-page .wpforms-btn-md.wpf-detect-btn {
	margin-top: -1px;
	padding-top: 7px;
	padding-bottom: 7px;
	min-height: 30px;
}

.wpf-detect-table ul {
	margin: 0;
}

.wpf-detect-table th {
	font-weight: 600;
	vertical-align: top;
	width: 180px;
}

@media screen and (max-width: 782px) {
	.wpf-detect-table th {
		width: auto;
	}
}

/*
 * Error Handler styles.
 */
.wpforms-admin-devtools-error_handler textarea {
	color: #333333;
	background-color: #ffffff;
	border: 1px solid #dddddd;
	border-radius: 3px;
	box-shadow: none;
	vertical-align: middle;
	padding: 7px 12px;
	margin: 0 10px 0 0;
	width: 100%;
	line-height: 1.3;
}

/*
 * End of Error Handler styles.
 */
