.wpforms-layout-table {

	> td {
		padding-bottom: 25px;
	}

	.wpforms-layout-table-row {
		width: 100%;

		.field-value {
			padding-bottom: 25px;
		}

		> tr > td {
			padding-right: 20px;

			&:last-child {
				padding-right: 0;
			}
		}
	}

	&-display-rows {
		.wpforms-layout-table-row {
			&:not(.wpforms-first-row) {
				td.field-name {
					display: none;
				}
			}

			.field-value {
				padding-bottom: 15px;
			}

			&:last-child {
				.field-value {
					padding-bottom: 0;
				}
			}
		}
	}

	&-display-blocks,
	&-display-columns {
		.wpforms-layout-table-row {
			tr:last-child {
				.wpforms-layout-table-cell {
					.field-value {
						padding-bottom: 0;
					}
				}
			}
		}
	}

	.wpforms-layout-table-cell {
		td {
			border: 0 !important;
		}

		&.wpforms-width-25,
		&.wpforms-width-33 {
			.field-payment-total {
				.wpforms-payment-total {
					display: block !important;
				}

				.wpforms-order-summary-preview {
					display: none !important;
				}
			}
		}
	}
}

.field-payment-total {
	.wpforms-payment-total {
		display: none !important;
	}
}
