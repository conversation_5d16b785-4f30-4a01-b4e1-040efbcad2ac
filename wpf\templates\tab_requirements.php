<?php
/**
 * View for the Requirements tab.
 *
 * @var array $addons_req  requirements for the activated add-ons.
 */
?>

<div class="wpf-req-tab-header">
	<h1>Active Addons Requirements</h1>
	<label for="wpforms-admin-devtools-requirements-search"></label>
	<input type="text" id="wpforms-admin-devtools-requirements-search" name="wpforms-admin-devtools-requirements-search" value="" placeholder="Type a keyword">
</div>
<?php if ( $addons_req ) : ?>
	<div id="wpf-req-container">
		<div class="not-found">Nothing found</div>
		<div class="list">
			<?php foreach ( $addons_req as $addon => $requirements ) : ?>
				<div class="wpf-req-item">
					<h2 class="wpf-req-item-name"><?php echo esc_html( $addon ); ?></h2>
					<ul>
						<?php foreach ( $requirements as $req_name => $req_value ) : ?>
							<li class="wpf-wpf-req-item-row">
								<span><?php echo esc_html( $req_name ); ?>: </span>
								<span><?php echo esc_html( $req_value ); ?></span>
							</li>
						<?php endforeach; ?>
					</ul>
				</div>
			<?php endforeach; ?>
		</div>
	</div>
<?php endif; ?>
