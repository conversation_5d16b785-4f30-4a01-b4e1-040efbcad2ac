<?php
/**
 * Multi-Level Cache Implementation.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper\Cache;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Multi-level cache implementation with object cache and transients.
 *
 * @since {VERSION}
 */
class MultiLevelCache implements CacheInterface {

	/**
	 * Primary cache (object cache).
	 *
	 * @since {VERSION}
	 *
	 * @var CacheInterface
	 */
	private $primary_cache;

	/**
	 * Secondary cache (transients).
	 *
	 * @since {VERSION}
	 *
	 * @var CacheInterface
	 */
	private $secondary_cache;

	/**
	 * Constructor.
	 *
	 * @since {VERSION}
	 *
	 * @param CacheInterface $primary_cache   Primary cache (object cache).
	 * @param CacheInterface $secondary_cache Secondary cache (transients).
	 */
	public function __construct( CacheInterface $primary_cache, CacheInterface $secondary_cache ) {

		$this->primary_cache   = $primary_cache;
		$this->secondary_cache = $secondary_cache;
	}

	/**
	 * Get a cached value.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key           Cache key.
	 * @param mixed  $default_value Default value if key doesn't exist.
	 *
	 * @return mixed Cached value or default.
	 */
	public function get( string $key, $default_value = null ) {

		// Try primary cache first.
		$value = $this->primary_cache->get( $key );

		if ( $value !== null ) {
			return $value;
		}

		// Try secondary cache.
		$value = $this->secondary_cache->get( $key );

		if ( $value !== null ) {
			// Warm primary cache.
			$this->primary_cache->set( $key, $value, 300 ); // 5 minutes in primary.

			return $value;
		}

		return $default_value;
	}

	/**
	 * Set a cached value.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key        Cache key.
	 * @param mixed  $value      Value to cache.
	 * @param int    $expiration Expiration time in seconds (0 = no expiration).
	 *
	 * @return bool True on success, false on failure.
	 */
	public function set( string $key, $value, int $expiration = 0 ): bool {

		// Set in both caches with different expiration times.
		$primary_expiration   = min( $expiration, 300 ); // Max 5 minutes in primary.
		$secondary_expiration = $expiration;

		$primary_result   = $this->primary_cache->set( $key, $value, $primary_expiration );
		$secondary_result = $this->secondary_cache->set( $key, $value, $secondary_expiration );

		return $primary_result && $secondary_result;
	}

	/**
	 * Delete a cached value.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key Cache key.
	 *
	 * @return bool True on success, false on failure.
	 */
	public function delete( string $key ): bool {

		$primary_result   = $this->primary_cache->delete( $key );
		$secondary_result = $this->secondary_cache->delete( $key );

		return $primary_result && $secondary_result;
	}

	/**
	 * Check if a key exists in cache.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key Cache key.
	 *
	 * @return bool True if key exists, false otherwise.
	 */
	public function has( string $key ): bool {

		return $this->primary_cache->has( $key ) || $this->secondary_cache->has( $key );
	}

	/**
	 * Clear all cached values.
	 *
	 * @since {VERSION}
	 *
	 * @return bool True on success, false on failure.
	 */
	public function clear(): bool {

		$primary_result   = $this->primary_cache->clear();
		$secondary_result = $this->secondary_cache->clear();

		return $primary_result && $secondary_result;
	}
}
