/* ==========================================================================
   Base - basic bare bones styling
   ========================================================================== */

/* Errors, Warnings, etc
----------------------------------------------------------------------------- */
.wpforms-container .wpforms-error-container,
.wpforms-container noscript.wpforms-error-noscript {
	color: #990000;
}

.wpforms-container label.wpforms-error {
	display: block;
	color: #990000;
	font-size: 0.9em;
	float: none;
	cursor: default;
}

.wpforms-container .wpforms-field input.wpforms-error,
.wpforms-container .wpforms-field input.user-invalid,
.wpforms-container .wpforms-field textarea.wpforms-error,
.wpforms-container .wpforms-field textarea.user-invalid,
.wpforms-container .wpforms-field select.wpforms-error,
.wpforms-container .wpforms-field select.user-invalid,
.wpforms-container .wpforms-field.wpforms-has-error .choices__inner {
	border: 1px solid #cc0000;
}

.wpforms-container .wpforms-field-credit-card-expiration label.wpforms-error,
.wpforms-container .wpforms-field-credit-card-code label.wpforms-error {
	display: none !important;
}

/* Page Indicator themes
----------------------------------------------------------------------------- */

.wpforms-container .wpforms-page-indicator {
	margin: 0 0 20px 0;
	overflow: hidden;
}

/** Circles theme **/
.wpforms-container .wpforms-page-indicator.circles {
	border-top: 1px solid #dfdfdf;
	border-bottom: 1px solid #dfdfdf;
	padding: 15px 10px;
}

.wpforms-container .wpforms-page-indicator.circles .wpforms-page-indicator-page {
	float: left;
	margin: 0 20px 0 0;
}

.wpforms-container .wpforms-page-indicator.circles .wpforms-page-indicator-page:last-of-type {
	margin: 0;
}

.wpforms-container .wpforms-page-indicator.circles .wpforms-page-indicator-page-number {
	height: 40px;
	width: 40px;
	border-radius: 50%;
	display: inline-block;
	margin: 0 10px 0 0;
	line-height: 40px;
	text-align: center;
	background-color: #ddd;
	color: #666;
}

.wpforms-container .wpforms-page-indicator.circles .active .wpforms-page-indicator-page-number {
	color: #fff;
}

/* Connector theme */
.wpforms-container .wpforms-page-indicator.connector .wpforms-page-indicator-page {
	float: left;
	text-align: center;
	line-height: 1.2;
}

.wpforms-container .wpforms-page-indicator.connector .wpforms-page-indicator-page-number {
	display: block;
	text-indent: -9999px;
	height: 6px;
	background-color: #ddd;
	margin: 0 0 16px 0;
	position: relative;
}

.wpforms-container .wpforms-page-indicator.connector .wpforms-page-indicator-page-triangle {
	position: absolute;
	top: 100%;
	left: 50%;
	width: 0;
	height: 0;
	margin-left: -5px;
	border-style: solid;
	border-width: 6px 5px 0 5px;
	border-color: transparent transparent transparent transparent;
}

.wpforms-container .wpforms-page-indicator.connector .wpforms-page-indicator-page-title {
	display: inline-block;
	padding: 0 15px;
	font-size: 16px;
}

/* Progress theme */
.wpforms-container .wpforms-page-indicator.progress {
	font-size: 18px;
}

.wpforms-container .wpforms-page-indicator.progress .wpforms-page-indicator-page-progress-wrap {
	display: block;
	width: 100%;
	background-color: #ddd;
	height: 18px;
	border-radius: 10px;
	overflow: hidden;
	position: relative;
	margin: 5px 0 0;
}

.wpforms-container .wpforms-page-indicator.progress .wpforms-page-indicator-page-progress {
	height: 18px;
	position: absolute;
	left: 0;
	top: 0;
}

/* Notices
----------------------------------------------------------------------------- */

div.wpforms-container .wpforms-notice {
	background-color: #fff;
	border: 1px solid #ddd;
	border-left-width: 12px;
	color: #333;
	font-size: 16px;
	line-height: 1.5;
	margin-bottom: 30px;
	padding: 20px 36px 20px 26px;
	position: relative;
}

div.wpforms-container .wpforms-notice .wpforms-delete {
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-moz-appearance: none;
	-webkit-appearance: none;
	background-color: rgba(10, 10, 10, 0.2);
	border: none;
	border-radius: 290486px;
	cursor: pointer;
	display: inline-block;
	height: 20px;
	margin: 0;
	padding: 0;
	outline: none;
	vertical-align: top;
	width: 20px;
	position: absolute;
	right: 10px;
	top: 10px;
}

div.wpforms-container .wpforms-notice .wpforms-delete:before,
div.wpforms-container .wpforms-notice .wpforms-delete:after {
	background-color: #fff;
	content: "";
	display: block;
	left: 50%;
	position: absolute;
	top: 50%;
	-webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg);
	transform: translateX(-50%) translateY(-50%) rotate(45deg);
	-webkit-transform-origin: center center;
	transform-origin: center center;
}

div.wpforms-container .wpforms-notice .wpforms-delete:before {
	height: 2px;
	width: 50%;
}

div.wpforms-container .wpforms-notice .wpforms-delete:after {
	height: 50%;
	width: 2px;
}

div.wpforms-container .wpforms-notice .wpforms-delete:hover,
div.wpforms-container .wpforms-notice .wpforms-delete:focus  {
	background-color: rgba(10, 10, 10, 0.3);
}

div.wpforms-container .wpforms-notice a {
	text-decoration: underline;
}

div.wpforms-container .wpforms-notice p {
	margin: 0 0 20px 0;
}

div.wpforms-container .wpforms-notice p:last-of-type {
	margin-bottom: 0;
}

div.wpforms-container-full .wpforms-notice.wpforms-info .wpforms-notice-action,
div.wpforms-container-full .wpforms-notice.wpforms-info {
	border-color: #3273dc
}

div.wpforms-container-full .wpforms-notice.wpforms-success .wpforms-notice-action,
div.wpforms-container-full .wpforms-notice.wpforms-success {
	border-color: #23d160
}

div.wpforms-container-full .wpforms-notice.wpforms-warning .wpforms-notice-action,
div.wpforms-container-full .wpforms-notice.wpforms-warning {
	border-color: #ffdd57
}

div.wpforms-container-full .wpforms-notice.wpforms-error .wpforms-notice-action,
div.wpforms-container-full .wpforms-notice.wpforms-error {
	border-color: #ff3860
}

div.wpforms-container-full .wpforms-notice .wpforms-notice-actions {
	margin-top: 20px;
}

div.wpforms-container-full .wpforms-notice .wpforms-notice-action {
	border: 2px solid;
	margin-right: 20px;
	padding: 5px;
	text-decoration: none;
}
div.wpforms-container-full .wpforms-notice .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice .wpforms-notice-action:active {
	color: #fff;
}

div.wpforms-container-full .wpforms-notice.wpforms-info .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice.wpforms-info .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice.wpforms-info .wpforms-notice-action:active {
	background-color: #3273dc;
}

div.wpforms-container-full .wpforms-notice.wpforms-success .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice.wpforms-success .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice.wpforms-success .wpforms-notice-action:active {
	background-color: #23d160;
}

div.wpforms-container-full .wpforms-notice.wpforms-warning .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice.wpforms-warning .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice.wpforms-warning .wpforms-notice-action:active {
	background-color: #ffdd57;
	color: inherit;
}

div.wpforms-container-full .wpforms-notice.wpforms-error .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice.wpforms-error .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice.wpforms-error .wpforms-notice-action:active {
	background-color: #ff3860;
}

div.wpforms-container .wpforms-error-container {
	&.wpforms-error-styled-container {
		padding: 10px 0;
		font-size: 15px;

		p {
			margin: 0;
		}
	}
}

/* Preview notice.
----------------------------------------------------------------------------- */

.wpforms-preview-notice-links {
	line-height: 2.4;
}

/* Form Header area
----------------------------------------------------------------------------- */

.wpforms-container .wpforms-title {
	font-size: 26px;
	margin: 0 0 10px 0;
}

.wpforms-container .wpforms-description {
	margin: 0 0 10px 0;
}


/* Form Footer area
----------------------------------------------------------------------------- */

.wpforms-container .wpforms-submit-container {
	padding: 10px 0 0 0;
	position: relative;
}

.wpforms-container button[type=submit] {
}

.wpforms-container .wpforms-submit-spinner {
	margin-inline-start: 0.5em;
	vertical-align: middle;
}

/* Misc
----------------------------------------------------------------------------- */

.wpforms-container {
	margin-bottom: 26px;
}

/* Honeypot Area */
.wpforms-container .wpforms-field-hp {
	display: none !important;
	position: absolute !important;
	left: -9000px !important;
}

.wpforms-container .wpforms-field.wpforms-field-hidden {
	display: none;
	padding: 0;
}

.wpforms-container .wpforms-screen-reader-element {
	position: absolute !important;
	clip: rect(0, 0, 0, 0);
	height: 1px;
	width: 1px;
	border: 0;
	overflow: hidden;
	word-wrap: normal !important;
}

div.wpforms-container .wpforms-form textarea {
	resize: vertical;
}

/*
 * Hide the form fields upon successful submission. This may not be the best approach.
 * Perhaps more robust: .wpforms-form.amp-form-submit-success > *:not([submit-success]) { display:none }
 */
.amp-form-submit-success .wpforms-field-container,
.amp-form-submit-success .wpforms-submit-container {
	display: none;
}

/* Gutenberg Block
----------------------------------------------------------------------------- */

.edit-post-visual-editor .wpforms-gutenberg-form-selector-wrap h3 {
	width: 100%;
	margin: 10px 0 5px;
	font-weight: 700;
	font-size: 20px;
}

.edit-post-visual-editor .wpforms-gutenberg-form-selector-wrap img {
	margin-right: 25px;
	width: initial;
}

.edit-post-visual-editor .wpforms-gutenberg-form-selector-wrap .components-base-control {
	width: 100%;
}

div.wpforms-gutenberg-form-selector .wpforms-form input:disabled,
div.wpforms-gutenberg-form-selector .wpforms-form textarea:disabled,
div.wpforms-gutenberg-form-selector .wpforms-form select:disabled,
div.wpforms-gutenberg-form-selector .wpforms-form button[type=submit]:disabled {
	cursor: not-allowed;
}

// Override Choices border-radius on the frontend.
div.wpforms-container .wpforms-form .choices.is-open.is-flipped .choices__inner,
div.wpforms-container .wpforms-form .choices.is-open .choices__list--dropdown {
	border-radius: 0 0 2px 2px;
}

div.wpforms-container .wpforms-form .choices.is-open.is-flipped .choices__list--dropdown,
div.wpforms-container .wpforms-form .choices.is-open .choices__inner {
	border-radius: 2px 2px 0 0;
}

div.wpforms-container .wpforms-form .choices {
	.choices__inner {
		border-radius: 2px;
		min-height: 35px;

		.choices__list--single {
			height: auto;
		}

		.choices__list--multiple {
			.choices__item {
				line-height: 1.3;
			}
		}
	}
}

/* RTL support
----------------------------------------------------------------------------- */

/* Phone US format */
body.rtl .wpforms-field-phone input[type=tel] {
	direction: ltr;
	unicode-bidi: embed;
	text-align: right;
}

body.rtl .wpforms-container .wpforms-first {
	float: right;
}

body.rtl .wpforms-container {

	.wpforms-first + .wpforms-one-half {
		margin-right: 4%;
		margin-left: 0;
	}

	&.wpforms-edit-entry-container {

		.wpforms-first + .wpforms-one-half {
			margin-right: 0;
		}
	}
}
