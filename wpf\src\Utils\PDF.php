<?php

namespace WPForms\DevTools\Utils;

use WPForms\Helpers\File;
use WPFormsPDF\Storage as PDFStorage;
use WPFormsPDF\Templates\Templates as PDFTemplates;
use WPFormsPDF\Templates\Themes as PDFThemes;

/**
 * PDF addon trait for Utils.
 *
 * @since 0.45
 */
trait PDF {

	/**
	 * Add PDF addon related menu items.
	 *
	 * @since 0.45
	 *
	 * @param array $nodes Menu nodes.
	 *
	 * @return array Menu nodes.
	 */
	private static function add_pdf_menu_links( array $nodes ): array {

		if ( ! wpforms_is_addon_initialized( 'pdf' ) ) {
			return $nodes;
		}

		$nodes[] = [
			'parent' => 'wpf-utils',
			'title'  => 'PDF addon',
			'id'     => 'wpf-utils-pdf',
		];

		$nodes[] = [
			'parent' => 'wpf-utils-pdf',
			'title'  => 'Wipe Custom Templates',
			'id'     => 'wpf-utils-pdf-wipe-templates',
			'href'   => add_query_arg( 'wpf_utils_action', 'pdf_wipe_templates' ),
		];

		$nodes[] = [
			'parent' => 'wpf-utils-pdf',
			'title'  => 'Wipe Custom Themes',
			'id'     => 'wpf-utils-pdf-wipe-themes',
			'href'   => add_query_arg( 'wpf_utils_action', 'pdf_wipe_themes' ),
		];

		$nodes[] = [
			'parent' => 'wpf-utils-pdf',
			'title'  => 'Use staging API',
			'id'     => 'wpf-utils-pdf-use-staging',
			'href'   => add_query_arg( 'wpf_utils_action', 'pdf_use_staging' ),
		];

		return $nodes;
	}

	/**
	 * Trigger: PDF addon > Wipe Custom Templates.
	 *
	 * @since 0.45
	 *
	 * @noinspection PhpUnusedPrivateMethodInspection
	 */
	private static function trigger_pdf_wipe_templates(): void {

		$file = PDFStorage::get_dir() . '/' . PDFTemplates::CUSTOM_TEMPLATES_FILE;

		if ( ! is_readable( $file ) ) {
			return;
		}

		File::delete( $file );
	}

	/**
	 * Trigger: PDF addon > Wipe Custom Themes.
	 *
	 * @since 0.45
	 *
	 * @noinspection PhpUnusedPrivateMethodInspection
	 */
	private static function trigger_pdf_wipe_themes(): void {

		$file = PDFStorage::get_dir() . '/' . PDFThemes::CUSTOM_THEMES_FILE;

		if ( ! is_readable( $file ) ) {
			return;
		}

		File::delete( $file );
	}

	/**
	 * Trigger: PDF addon > Use staging API.
	 *
	 * @since 0.46
	 *
	 * @noinspection PhpUnusedPrivateMethodInspection
	 */
	private static function trigger_pdf_use_staging(): void {

		$checked = ! empty( self::get_option( 'pdf-use-staging' ) );

		self::update_option( 'pdf-use-staging', $checked ? 0 : 1 );
	}

	/**
	 * Process: PDF addon > Use staging API.
	 *
	 * @since 0.46
	 *
	 * @noinspection PhpUnusedPrivateMethodInspection
	 * @noinspection HtmlUnknownTarget
	 */
	private static function process_pdf_use_staging(): void { // phpcs:ignore WPForms.PHP.HooksMethod.InvalidPlaceForAddingHooks

		add_filter(
			'wpforms_pdfapi_http_request_base_url',
			static function () {

				return 'https://staging.wpformsapi.com/pdf/v1';
			}
		);
	}
}
