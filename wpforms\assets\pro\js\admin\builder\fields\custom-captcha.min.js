var WPFormsCaptcha=window.WPFormsCaptcha||((a,l)=>{let f={removeEmptyQuestionsEvents:"wpformsPanelSwitch wpformsFieldTabToggle wpformsFieldOptionGroupToggle",init(){l(f.ready)},ready(){l("#wpforms-builder").on("change",".wpforms-field-option-captcha .wpforms-field-option-row-format select",f.formatToggle).on("click",".wpforms-field-option-row-questions .add",f.addQuestion).on("click",".wpforms-field-option-row-questions .remove",f.removeQuestion).on("input",".wpforms-field-option-row-questions .question",f.updateQuestion).on("input",".wpforms-field-option-row-questions .answer",f.updateAnswer).on("wpformsBeforeSave",f.removeEmptyQuestions),l(a).on(f.removeEmptyQuestionsEvents,f.removeEmptyQuestions)},formatToggle(){var e=l(this),t=e.val(),e=e.parent().data("field-id"),o=l(`#wpforms-field-option-row-${e}-questions`),e=l(`#wpforms-field-option-row-${e}-size`);"math"===t?(o.hide().addClass("wpforms-hidden"),e.hide()):(o.show().removeClass("wpforms-hidden"),e.show())},addQuestion(e){e.preventDefault();var e=l(this),t=e.closest("li"),e=e.closest(".choices-list"),o=e.data("field-id"),t=t.clone().insertAfter(t),i=e.attr("data-next-id"),o="fields["+o+"][questions]["+i+"]";t.attr("data-key",i),t.find("input.question").val("").attr("name",o+"[question]"),t.find("input.answer").val("").attr("name",o+"[answer]"),e.attr("data-next-id",++i)},removeQuestion(e){e.preventDefault();var e=l(this),t=e.closest("li"),e=e.closest(".choices-list"),o=e.find(".question"),i=e.data("field-id"),o=f.getTotalNotEmptyQuestions(o);1<o||1===o&&0===t.find(".question").val().trim().length?(t.remove(),l("#wpforms-field-"+i).find(".wpforms-question").text(e.find(".question").val())):f.showAlert(null)},removeEmptyQuestions(r){l(".wpforms-field-option-captcha").each(function(e,t){let o=l(t).data("field-id"),i=l(`#wpforms-field-option-row-${o}-questions .choices-list`),s=i.find("li"),n=f.getNotEmptyChoices(s);0===n.length?(f.showAlert(function(){let t=this.$el;l(".jconfirm").map(function(){var e=l(this);return l(t).is(e)||e.find(".btn-default").trigger("click"),null}),l(a).off(f.removeEmptyQuestionsEvents,f.removeEmptyQuestions),"fields"!==l("#wpforms-panels-toggle .active").data("panel")&&WPFormsBuilder.panelSwitch("fields"),WPFormsBuilder.fieldTabToggle(o),l(`#wpforms-field-option-${o} .wpforms-field-option-group`).removeClass("active"),l("#wpforms-field-option-basic-"+o).addClass("active"),l(a).on(f.removeEmptyQuestionsEvents,f.removeEmptyQuestions)}),r.preventDefault()):(s.each(function(e,t){t=l(t);f.isEmptyChoice(t)&&t.remove()}),l("#wpforms-field-"+o).find(".wpforms-question").text(s.find(".question").val()))})},updateQuestion(){var e,t,o=l(this),i=o.val().trim();o.toggleClass("wpforms-error",!i.length),i.length&&(e=(t=o.closest(".choices-list")).find(".question"),t=t.data("field-id"),o.data("prev-value",i),o.is(e[0]))&&l("#wpforms-field-"+t).find(".wpforms-question").text(i)},updateAnswer(){var e=l(this),t=e.val().trim();e.toggleClass("wpforms-error",!t.length)},showAlert(e){l.alert({title:!1,content:wpforms_builder_custom_captcha.error_not_empty_question,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action:e}}})},getTotalNotEmptyQuestions(e){return e.filter(function(){return this.value.trim().length}).length},getNotEmptyChoices(e){return e.filter(function(){return!f.isEmptyChoice(l(this))})},isEmptyChoice(e){return 0===e.find(".question").val().trim().length||0===e.find(".answer").val().trim().length}};return f})(document,(window,jQuery));WPFormsCaptcha.init();