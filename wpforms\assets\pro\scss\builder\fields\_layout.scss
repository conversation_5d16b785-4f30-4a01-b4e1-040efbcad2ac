// Form Builder styles.
//
// Panel fields.
// Layout field styles.
//
// @since 1.7.7

// Layout field images path.
$layout_images_path: "../images/layout/";

.wpforms-panel-fields {

	// Field options area (left side).
	.wpforms-field-option-layout,
	.wpforms-field-option-repeater {

		// Presets.
		.wpforms-field-option-row-preset {
			margin-top: $spacing_s;
			margin-bottom: 0;
			display: flex;
			justify-content: space-between;
			flex-wrap: wrap;

			&:after{
				content: "";
				display: block;
				width: 106px;
			}

			input {
				display: none;

				& + label {
					background-color: $color_fields_secondary_text;
					background-size: 100% 100%;
					border: none;
					border-radius: $border_radius_s;
					width: 106px;
					height: 76px;
					padding: 0;
					margin-bottom: $spacing_m;
					cursor: pointer;

					@include transition( all, $transition_fast, ease-out );

					&:nth-child( 3n ) {
						margin-inline-end: 0;
					}

					&:hover {
						box-shadow: 0 0 0 1px #86919e;
					}

					&.preset-100 {
						background-image: url( "#{ $layout_images_path }100.svg" );
					}

					&.preset-50-50 {
						background-image: url( "#{ $layout_images_path }50-50.svg" );
					}

					&.preset-67-33 {
						background-image: url( "#{ $layout_images_path }67-33.svg" );
					}

					&.preset-33-67 {
						background-image: url( "#{ $layout_images_path }33-67.svg" );
					}

					&.preset-33-33-33 {
						background-image: url( "#{ $layout_images_path }33-33-33.svg" );
					}

					&.preset-50-25-25 {
						background-image: url( "#{ $layout_images_path }50-25-25.svg" );
					}

					&.preset-25-25-50 {
						background-image: url( "#{ $layout_images_path }25-25-50.svg" );
					}

					&.preset-25-50-25 {
						background-image: url( "#{ $layout_images_path }25-50-25.svg" );
					}

					&.preset-25-25-25-25 {
						background-image: url( "#{ $layout_images_path }25-25-25-25.svg" );
					}

					[dir="rtl"] & {
						transform: scaleX(-1);
					}
				}

				&:checked + label {
					border-color: $color_dark_blue;
					background-color: $color_dark_blue;
					box-shadow:
						0 0 0 1px #056aab,
						0 2px 4px rgba( 0, 0, 0, .1 );

					&.preset-100 {
						background-image: url( "#{ $layout_images_path }100-a.svg" );
					}

					&.preset-50-50 {
						background-image: url( "#{ $layout_images_path }50-50-a.svg" );
					}

					&.preset-67-33 {
						background-image: url( "#{ $layout_images_path }67-33-a.svg" );
					}

					&.preset-33-67 {
						background-image: url( "#{ $layout_images_path }33-67-a.svg" );
					}

					&.preset-33-33-33 {
						background-image: url( "#{ $layout_images_path }33-33-33-a.svg" );
					}

					&.preset-50-25-25 {
						background-image: url( "#{ $layout_images_path }50-25-25-a.svg" );
					}

					&.preset-25-25-50 {
						background-image: url( "#{ $layout_images_path }25-25-50-a.svg" );
					}

					&.preset-25-50-25 {
						background-image: url( "#{ $layout_images_path }25-50-25-a.svg" );
					}

					&.preset-25-25-25-25 {
						background-image: url( "#{ $layout_images_path }25-25-25-25-a.svg" );
					}
				}
			}

			&.wpforms-layout-display-rows {
				input {
					& + label {
						&.preset-100 {
							background-image: url("#{ $layout_images_path }100-r.svg");
						}

						&.preset-50-50 {
							background-image: url("#{ $layout_images_path }50-50-r.svg");
						}

						&.preset-67-33 {
							background-image: url("#{ $layout_images_path }67-33-r.svg");
						}

						&.preset-33-67 {
							background-image: url("#{ $layout_images_path }33-67-r.svg");
						}

						&.preset-33-33-33 {
							background-image: url("#{ $layout_images_path }33-33-33-r.svg");
						}

						&.preset-50-25-25 {
							background-image: url("#{ $layout_images_path }50-25-25-r.svg");
						}

						&.preset-25-25-50 {
							background-image: url("#{ $layout_images_path }25-25-50-r.svg");
						}

						&.preset-25-50-25 {
							background-image: url("#{ $layout_images_path }25-50-25-r.svg");
						}

						&.preset-25-25-25-25 {
							background-image: url("#{ $layout_images_path }25-25-25-25-r.svg");
						}

						[dir="rtl"] & {
							transform: scaleX(-1);
						}
					}

					&:checked + label {
						&.preset-100 {
							background-image: url( "#{ $layout_images_path }100-r-a.svg" );
						}

						&.preset-50-50 {
							background-image: url( "#{ $layout_images_path }50-50-r-a.svg" );
						}

						&.preset-67-33 {
							background-image: url( "#{ $layout_images_path }67-33-r-a.svg" );
						}

						&.preset-33-67 {
							background-image: url( "#{ $layout_images_path }33-67-r-a.svg" );
						}

						&.preset-33-33-33 {
							background-image: url( "#{ $layout_images_path }33-33-33-r-a.svg" );
						}

						&.preset-50-25-25 {
							background-image: url( "#{ $layout_images_path }50-25-25-r-a.svg" );
						}

						&.preset-25-25-50 {
							background-image: url( "#{ $layout_images_path }25-25-50-r-a.svg" );
						}

						&.preset-25-50-25 {
							background-image: url( "#{ $layout_images_path }25-50-25-r-a.svg" );
						}

						&.preset-25-25-25-25 {
							background-image: url( "#{ $layout_images_path }25-25-25-25-r-a.svg" );
						}
					}
				}
			}
		}
	}

	// Form preview area (right side).
	.wpforms-field-layout,
	.wpforms-field-repeater {
		& > .label-title {
			font-size: $font_size_ll;
		}

		& > .description {
			margin: 0 0 5px 0;
		}

		.wpforms-field-layout-columns {
			display: flex;
			margin-top: -50px;
			margin-bottom: -15px;
			margin-inline-start: -$spacing_s;
			margin-inline-end: -$spacing_ms;
			align-items: stretch;
		}

		.wpforms-layout-column {
			margin: 0;
			padding-block: 50px 65px;
			padding-inline: 0 5px;
			position: relative;

			.wpforms-field {
				padding: 15px 10px;
				overflow-x: auto;
			}

			&-20 {
				width: 20%;
			}

			&-25 {
				width: 25%;
			}

			&-30 {
				width: 30%;
			}

			&-33 {
				width: 33.33333%;
			}

			&-40 {
				width: 40%;
			}

			&-50 {
				width: 50%;
			}

			&-60 {
				width: 60%;
			}

			&-67 {
				width: 66.66666%;
			}

			&-70 {
				width: 70%;
			}

			&-100 {
				width: 100%;
			}
		}

		.wpforms-field-drag-placeholder {
			min-height: 108px;
		}

		.wpforms-field-drag-pending {
			min-height: 108px;
			padding-top: 40px;
		}

		.wpforms-layout-column-placeholder {
			width: calc( 100% - 25px );
			border-radius: $border_radius_s;
			border: 1px dashed $color_brighter_grey;
			height: 40px;
			padding: $spacing_s;
			position: absolute;
			bottom: $spacing_ms;
			inset-inline-start: $spacing_s;
			display: flex;
			justify-content: center;
			align-items: center;
			gap: $spacing_s;

			.wpforms-plus-path {
				fill: $color_button_icon_light_grey;
			}

			span {
				color: $color_button_icon_grey;
				font-size: $font_size_s;
			}
		}

		.wpforms-fields-sortable-default {
			.wpforms-layout-column-placeholder {
				background-color: $color_white;
				border: 1px solid $color_orange;

				.wpforms-plus-path {
					fill: $color_orange;
				}

				&:hover {
					background-color: $color_white;
				}

				.normal-icon {
					display: none;
				}

				.active-icon {
					display: block;
				}
			}
		}

		// Informational notice (alert) inside the Layout field.
		& > .wpforms-alert {
			margin: $spacing_ms 0;
		}

		.wpforms-layout-column {
			.wpforms-alert-dismissible {
				max-height: fit-content;
				overflow: auto;
			}
		}
	}

	// Override field size in columns - always full width.
	.wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field,
	.wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column-100 .wpforms-field,
	.wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 {
		input[type=text],
		input[type=range],
		input[type=email],
		input[type=url],
		input[type=tel],
		input[type=number],
		input[type=password],
		input[type=file],
		select:not(.quantity-input),
		textarea {
			width: $field_size_medium;
		}

		&.size-large {
			input[type=text],
			input[type=range],
			input[type=email],
			input[type=url],
			input[type=tel],
			input[type=number],
			input[type=password],
			input[type=file],
			select,
			textarea {
				width: $field_size_large;
			}

			&.payment-quantity-enabled {
				.item-price,
				.primary-input,
				.choices {
					width: calc(100% - 85px) ! important;
					min-width: calc(100% - 85px) ! important;
				}
			}
		}

		&.size-medium {
			input[type=text],
			input[type=range],
			input[type=email],
			input[type=url],
			input[type=tel],
			input[type=number],
			input[type=password],
			input[type=file],
			select,
			textarea {
				width: $field_size_medium;
			}
		}

		&.size-small {
			input[type=text],
			input[type=range],
			input[type=email],
			input[type=url],
			input[type=tel],
			input[type=number],
			input[type=password],
			input[type=file],
			select,
			textarea {
				width: $field_size_small;
			}
		}

		&.payment-quantity-enabled {
			select.quantity-input {
				width: 70px ! important;
				min-width: 70px ! important;
			}
		}

		&.wpforms-field-date-time {
			.format-selected {
				flex-wrap: wrap;
			}

			.wpforms-date-dropdown select {
				max-width: calc(100% / 3 - 20px / 3);
				min-width: initial !important;
			}
		}

		&.wpforms-field-date-time:not(.size-small) {
			.format-selected-date-time {
				.wpforms-date-type-datepicker,
				.wpforms-date-type-datepicker + .wpforms-time {
					width: calc(50% - 10px);
				}
			}
		}
	}

	.wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100) .wpforms-field,
	.wpforms-field-repeater .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field,
	.wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) {
		input[type=text],
		input[type=range],
		input[type=email],
		input[type=url],
		input[type=tel],
		input[type=number],
		input[type=password],
		input[type=file],
		select,
		textarea,
		.wpforms-address-scheme,
		.format-selected,
		.choices,
		.wpforms-field-content-preview,
		.wpforms-confirm,
		.wpforms-order-summary-container {
			width: $field_size_large !important;
			min-width: $field_size_large !important;
		}

		&.wpforms-field-date-time {
			.format-selected {
				flex-wrap: wrap;
			}

			.wpforms-date-dropdown select {
				max-width: calc( 100% / 3 - 20px / 3 );
				min-width: initial !important;
			}

			.format-selected-date-time {
				.wpforms-date-type-datepicker,
				.wpforms-date-type-datepicker + .wpforms-time {
					width: calc(50% - 10px);
				}
			}
		}

		&.payment-quantity-enabled {
			select.quantity-input {
				width: 70px ! important;
				min-width: 70px ! important;
			}

			.item-price,
			.primary-input,
			.choices {
				width: calc( 100% - 85px ) ! important;
				min-width: calc( 100% - 85px ) ! important;
			}
		}
	}

	// Adjust styles in slim columns.
	$slim_columns: 20, 25;

	@each $column in $slim_columns {
		.wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-#{$column} .wpforms-field,
		.wpforms-field.wpforms-field-drag-to-column-#{$column} {
			&.payment-quantity-enabled {
				select.quantity-input {
					width: 100% !important;
					min-width: 100% !important;
					margin-top: $spacing_ms;
					margin-inline-start: 0;
				}

				.item-price,
				.primary-input,
				.choices {
					width: 100% !important;
					min-width: 100% !important;
				}
			}
		}
	}

	// Adjust styles in narrow columns.
	$narrow_columns: 20, 25, 30, 33;

	@each $column in $narrow_columns {
		.wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column-#{$column} .wpforms-field,
		.wpforms-field.wpforms-field-drag-to-column-#{$column} {
			&.wpforms-field-date-time {

				.format-selected-date-time {
					.wpforms-date-type-datepicker.wpforms-date,
					.wpforms-date-type-datepicker.wpforms-date + .wpforms-time {
						width: 100%;
					}
				}

				.wpforms-date-type-dropdown + .wpforms-time {
					min-width: 100%;
				}
			}

			&.wpforms-summary-enabled {
				.wpforms-order-summary-container {
					display: none;
				}

				.wpforms-total-amount {
					display: block;
				}
			}
		}
	}

	// Do not overflow the field while dragging over the narrow column.
	.wpforms-field.wpforms-field-drag-to-column {
		overflow-x: hidden;
	}

	// Adjustments for different fields in columns.
	.wpforms-field-layout-columns .wpforms-layout-column {

		&-20,
		&-25,
		&-30,
		&-33 {

			// Authorize.NET.
			.wpforms-field-authorize_net {
				.wpforms-field-row {
					display: flex;
					flex-direction: column;

					& > div {
						position: relative;
						margin-bottom: 10px;
						width: 100%;
					}
				}
			}

			// Choices (checkboxes, radio) fields.
			.wpforms-list-inline,
			.wpforms-list-2-columns,
			.wpforms-list-3-columns {
				ul:not(.wpforms-icon-choices) {
					flex-direction: column;

					li {
						width: 100%;
						max-width: 100%;
						margin: 0 0 $spacing_xs 0;
					}
				}
			}

			// Icon Choices.
			.wpforms-list-inline,
			.wpforms-list-2-columns,
			.wpforms-list-3-columns {
				ul.wpforms-icon-choices {
					flex-direction: column;

					li {
						width: 100%;
						max-width: 100%;
					}
				}
			}
		}

		// For all column width.
		.wpforms-field {
			&.wpforms-field-file-upload {
				.wpforms-file-upload-builder-modern {
					text-align: center;
				}
			}

			&.wpforms-field-captcha {
				.format-selected-math.format-selected {
					input[type=text] {
						width: 70px !important;
						min-width: 70px !important;
					}
				}
			}

			&.wpforms-field-internal-information {
				.internal-information-wrap {
					margin-inline-end: 0;
					padding-inline-end: 20px;
				}
			}

			.wpforms-richtext-wrap {
				min-width: auto;
			}

			// Paragraph Text and Rich Text fields.
			// Small, Medium, Large sizes.
			&.wpforms-field-textarea,
			&.wpforms-field-richtext {
				textarea {
					height: 110px;
				}

				&.size-small {
					textarea {
						height: 60px;
					}
				}

				&.size-medium {
					textarea {
						height: 110px;
					}
				}

				&.size-large {
					textarea {
						height: 300px;
					}
				}
			}

			// Icon Choices list.
			ul.wpforms-icon-choices {
				margin-bottom: -$spacing_ms;
			}
		}
	}
}

#wpforms-builder {
	.wpforms-field-layout-columns .wpforms-layout-column:not(.wpforms-layout-column-100),
	.wpforms-field.wpforms-field-drag-to-column:not(.wpforms-field-drag-to-column-100) {
		// Geolocation map.
		.wpforms-field-address, &.wpforms-field-address,
		.wpforms-field-text, &.wpforms-field-text {
			.wpforms-geolocation-map {
				min-width: 100%;
				max-width: 100%;
			}
		}

		// Address field.
		.wpforms-field-address, &.wpforms-field-address {
			.wpforms-city,
			.wpforms-state,
			.wpforms-postal,
			.wpforms-country {
				float: none;
				width: 100%;
				margin: 0 0 $spacing_s 0;
			}
		}

		// Name field.
		.wpforms-field-name, &.wpforms-field-name {
			.wpforms-simple,
			.wpforms-first-name,
			.wpforms-middle-name,
			.wpforms-last-name {
				float: none;
				width: 100%;
				margin: 0 0 $spacing_s 0;
			}
		}

		// Email field.
		.wpforms-field-email, &.wpforms-field-email {
			.wpforms-confirm-primary,
			.wpforms-confirm-confirmation {
				float: none;
				width: 100%;
				margin: 0 0 $spacing_s 0;
			}
		}

		// Password field.
		.wpforms-field-password, &.wpforms-field-password {
			.wpforms-confirm-primary,
			.wpforms-confirm-confirmation {
				float: none;
				width: 100%;
				margin: 0 0 $spacing_s 0;
			}
		}
	}

	.wpforms-field-layout-columns .wpforms-layout-column.wpforms-layout-column-100 {
		// Name field.
		.wpforms-field-name {
			.wpforms-simple,
			.wpforms-first-name,
			.wpforms-middle-name,
			.wpforms-last-name {
				input {
					width: 100%;
				}
			}
		}
		// Address field.
		.wpforms-field-address {
			input[type=text],
			select {
				width: 100%;
				min-width: initial;
			}
		}
		// Email field.
		.wpforms-field-email {
			.wpforms-confirm-primary,
			.wpforms-confirm-confirmation {
				input {
					width: 100%;
				}
			}
		}

		// Password field.
		.wpforms-field-password {
			.wpforms-confirm-primary,
			.wpforms-confirm-confirmation {
				input {
					width: 100%;
				}
			}
		}
	}
}
