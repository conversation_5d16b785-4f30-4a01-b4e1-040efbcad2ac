<?php
/**
 * <PERSON>rrorHandler class file.
 */

// phpcs:ignore Generic.Commenting.DocComment.MissingShort
/** @noinspection PhpUndefinedClassInspection */

namespace WPForms\DevTools\ErrorHandler;

use WPF<PERSON>\DevTools\Abstracts\MUPlugin;
use WPForms\DevTools\DevTools;
use WPForms\DevTools\Utils;
use QM_Collectors;

/**
 * Class ErrorHandler.
 *
 * @since 0.22
 */
class ErrorHandler extends MUPlugin {

	/**
	 * Error handler mu-plugin dir.
	 *
	 * @since 0.34
	 */
	const MU_DIR = __DIR__;

	/**
	 * Error handler mu-plugin filename.
	 *
	 * @since 0.22
	 */
	const MU_FILENAME = 'wpf-error-handler.php';

	/**
	 * Error handler option key.
	 *
	 * @since 0.22
	 */
	const OPTION_KEY = 'error-handler-dirs';

	/**
	 * Reset action.
	 *
	 * @since 0.22
	 */
	const RESET_ACTION = 'wpf_reset_error_handler';

	/**
	 * Hooks.
	 *
	 * @since 0.34
	 *
	 * @return void
	 */
	protected function hooks() {

		parent::hooks();

		add_action( 'wp_ajax_' . self::RESET_ACTION, [ $this, 'reset' ] );

		// Suppress the _load_textdomain_just_in_time() notices related the WPForms.
		if ( version_compare( $GLOBALS['wp_version'], '6.7', '>=' ) ) {
			add_action( 'doing_it_wrong_run', [ $this, 'action_doing_it_wrong_run' ], 0, 3 );
			add_action( 'doing_it_wrong_run', [ $this, 'action_doing_it_wrong_run' ], 20, 3 );
			add_filter( 'doing_it_wrong_trigger_error', [ $this, 'filter_doing_it_wrong_trigger_error' ], 10, 4 );
		}
	}

	/**
	 * Activation hook.
	 *
	 * @since 0.22
	 *
	 * @return void
	 */
	public function activation_hook() {

		$option = get_option( 'wpf-utils', [] );
		$dirs   = $option[ self::OPTION_KEY ] ?? [];

		if ( ! $dirs ) {
			Utils::update_option( self::OPTION_KEY, $this->init_dirs() );
		}

		parent::activation_hook();
	}

	/**
	 * Reset directories to default.
	 *
	 * @since 0.22
	 *
	 * @return void
	 */
	public function reset() {

		check_ajax_referer( DevTools::NONCE, 'nonce' );
		Utils::update_option( self::OPTION_KEY, $this->init_dirs() );
		wp_send_json_success();
	}

	/**
	 * Init dirs to suppress messages from.
	 *
	 * @since 0.22
	 *
	 * @return array Default dirs.
	 */
	private function init_dirs(): array {

		$dirs = [
			// WPForms.
			// We have suppression for WPForms and addons vendor dirs in WPForms 1.8.5+.
			// Keep it here for testing with lower WPForms versions.
			WP_PLUGIN_DIR . '/wpforms/vendor/',
			WP_PLUGIN_DIR . '/wpforms/vendor_prefixed/',
			WP_PLUGIN_DIR . '/wpforms-lite/vendor/',
			WP_PLUGIN_DIR . '/wpforms-lite/vendor_prefixed/',
			// Addons.
			WP_PLUGIN_DIR . '/wpforms-activecampaign/vendor/',
			WP_PLUGIN_DIR . '/wpforms-authorize-net/vendor/',
			WP_PLUGIN_DIR . '/wpforms-aweber/deprecated/',
			WP_PLUGIN_DIR . '/wpforms-aweber/vendor/',
			WP_PLUGIN_DIR . '/wpforms-calculations/vendor/',
			WP_PLUGIN_DIR . '/wpforms-campaign-monitor/vendor/',
			WP_PLUGIN_DIR . '/wpforms-captcha/vendor/',
			WP_PLUGIN_DIR . '/wpforms-conversational-forms/vendor/',
			WP_PLUGIN_DIR . '/wpforms-convertkit/vendor/',
			WP_PLUGIN_DIR . '/wpforms-convertkit/vendor_prefixed/',
			WP_PLUGIN_DIR . '/wpforms-coupons/vendor/',
			WP_PLUGIN_DIR . '/wpforms-drip/vendor/',
			WP_PLUGIN_DIR . '/wpforms-e2e-helpers/vendor/',
			WP_PLUGIN_DIR . '/wpforms-form-abandonment/vendor/',
			WP_PLUGIN_DIR . '/wpforms-form-locker/vendor/',
			WP_PLUGIN_DIR . '/wpforms-form-pages/vendor/',
			WP_PLUGIN_DIR . '/wpforms-geolocation/vendor/',
			WP_PLUGIN_DIR . '/wpforms-getresponse/vendor/',
			WP_PLUGIN_DIR . '/wpforms-google-sheets/vendor/',
			WP_PLUGIN_DIR . '/wpforms-hubspot/vendor/',
			WP_PLUGIN_DIR . '/wpforms-lead-forms/vendor/',
			WP_PLUGIN_DIR . '/wpforms-mailchimp/vendor/',
			WP_PLUGIN_DIR . '/wpforms-mailerlite/vendor/',
			WP_PLUGIN_DIR . '/wpforms-offline-forms/vendor/',
			WP_PLUGIN_DIR . '/wpforms-paypal-commerce/vendor/',
			WP_PLUGIN_DIR . '/wpforms-paypal-standard/vendor/',
			WP_PLUGIN_DIR . '/wpforms-pdf/vendor/',
			WP_PLUGIN_DIR . '/wpforms-post-submissions/vendor/',
			WP_PLUGIN_DIR . '/wpforms-salesforce/vendor/',
			WP_PLUGIN_DIR . '/wpforms-save-resume/vendor/',
			WP_PLUGIN_DIR . '/wpforms-sendinblue/vendor/',
			WP_PLUGIN_DIR . '/wpforms-signatures/vendor/',
			WP_PLUGIN_DIR . '/wpforms-slack/vendor/',
			WP_PLUGIN_DIR . '/wpforms-square/vendor/',
			WP_PLUGIN_DIR . '/wpforms-stripe/vendor/',
			WP_PLUGIN_DIR . '/wpforms-surveys-polls/vendor/',
			WP_PLUGIN_DIR . '/wpforms-user-journey/vendor/',
			WP_PLUGIN_DIR . '/wpforms-user-registration/vendor/',
			WP_PLUGIN_DIR . '/wpforms-webhooks/vendor/',
			WP_PLUGIN_DIR . '/wpforms-zapier/vendor/',
			// WPF by itself.
			WP_PLUGIN_DIR . '/wpf/vendor/',
			// WP Core.
			ABSPATH . WPINC . '/', // WordPress wp-includes.
			ABSPATH . 'wp-admin/', // WordPress wp-admin.
			// Known libraries in different plugins producing deprecated messages.
			'/action-scheduler/', // Action Scheduler.
			'/mustache/', // Mustache library used in WP-CLI.
			'/rmccue/requests/', // Requests library used in WP-CLI.
			// Plugins producing deprecated messages.
			WP_PLUGIN_DIR . '/advanced-custom-fields-pro/', // Advanced Custom Fields Pro.
			WP_PLUGIN_DIR . '/backwpup/', // BackWPup.
			WP_PLUGIN_DIR . '/business-reviews-bundle/', // Business review bundle.
			WP_PLUGIN_DIR . '/cloudflare/', // Cloudflare.
			WP_PLUGIN_DIR . '/easy-digital-downloads/', // Easy Digital Downloads.
			WP_PLUGIN_DIR . '/elementor/', // Elementor.
			WP_PLUGIN_DIR . '/elementor-pro/', // Elementor Pro.
			WP_PLUGIN_DIR . '/google-site-kit/', // Google Site Kit.
			WP_PLUGIN_DIR . '/gravityforms/', // Gravity Forms.
			WP_PLUGIN_DIR . '/gravityperks/', // Gravity Perks.
			WP_PLUGIN_DIR . '/mailpoet/', // MailPoet.
			WP_PLUGIN_DIR . '/pagelayer/', // Pagelayer.
			WP_PLUGIN_DIR . '/seo-by-rank-math/', // Rank Math SEO.
			WP_PLUGIN_DIR . '/sitepress-multilingual-cms/', // WPML.
			WP_PLUGIN_DIR . '/uncanny-automator/', // Uncanny Automator.
			WP_PLUGIN_DIR . '/woocommerce/', // WooCommerce.
			WP_PLUGIN_DIR . '/wp-google-places-review-slider/', // Google places review slider.
			WP_PLUGIN_DIR . '/wp-job-openings/', // Job openings.
			WP_PLUGIN_DIR . '/wp-seo-multilingual/', // WPML SEO.
			WP_PLUGIN_DIR . '/wp-super-cache/', // WP Super Cache.
			// Themes producing deprecated messages.
			WP_CONTENT_DIR . '/themes/Avada/', // Avada Theme.
			WP_CONTENT_DIR . '/themes/Divi/', // Divi Theme.
			WP_CONTENT_DIR . '/themes/popularfx/', // Popular FX Theme.
		];

		$abspath = str_replace( '\\', '/', realpath( ABSPATH ) );

		return array_map(
			static function ( $dir ) use ( $abspath ) {

				return str_replace( [ '\\', $abspath ], [ '/', '' ], $dir );
			},
			$dirs
		);
	}

	/**
	 * Action for _doing_it_wrong() calls.
	 *
	 * @since 0.35
	 *
	 * @param string $function_name The function that was called.
	 * @param string $message       A message explaining what has been done incorrectly.
	 * @param string $version       The version of WordPress where the message was added.
	 *
	 * @return void
	 * @noinspection PhpMissingParamTypeInspection
	 * @noinspection PhpUnusedParameterInspection
	 */
	public function action_doing_it_wrong_run( $function_name, $message, $version ) { // phpcs:ignore Generic.CodeAnalysis.UnusedFunctionParameter.FoundAfterLastUsed, WPForms.PHP.HooksMethod.InvalidPlaceForAddingHooks

		global $wp_filter;

		$function_name = (string) $function_name;
		$message       = (string) $message;

		if ( ! class_exists( 'QM_Collectors' ) || ( $function_name !== '_load_textdomain_just_in_time' && strpos( $message, '<code>wpforms' ) !== false ) ) {
			return;
		}

		$qm_collector_doing_it_wrong = QM_Collectors::get( 'doing_it_wrong' );
		$current_priority            = $wp_filter['doing_it_wrong_run']->current_priority();

		if ( $qm_collector_doing_it_wrong === null || $current_priority === false ) {
			return;
		}

		switch ( $current_priority ) {
			case 0:
				remove_action( 'doing_it_wrong_run', [ $qm_collector_doing_it_wrong, 'action_doing_it_wrong_run' ] );
				break;

			case 20:
				add_action( 'doing_it_wrong_run', [ $qm_collector_doing_it_wrong, 'action_doing_it_wrong_run' ], 10, 3 );
				break;

			default:
				break;
		}
	}

	/**
	 * Filter for _doing_it_wrong() calls.
	 *
	 * @since 0.37
	 *
	 * @param bool|mixed $trigger       Whether to trigger the error for _doing_it_wrong() calls. Default true.
	 * @param string     $function_name The function that was called.
	 * @param string     $message       A message explaining what has been done incorrectly.
	 * @param string     $version       The version of WordPress where the message was added.
	 *
	 * @return bool
	 * @noinspection PhpMissingParamTypeInspection
	 * @noinspection PhpUnusedParameterInspection
	 */
	public function filter_doing_it_wrong_trigger_error( $trigger, $function_name, $message, $version ): bool { // phpcs:ignore Generic.CodeAnalysis.UnusedFunctionParameter.FoundAfterLastUsed

		$trigger       = (bool) $trigger;
		$function_name = (string) $function_name;
		$message       = (string) $message;

		return $this->is_just_in_time_for_wpforms_domain( $function_name, $message ) ? false : $trigger;
	}

	/**
	 * Whether it is the just_in_time_error for WPForms-related domains.
	 *
	 * @since 0.37
	 *
	 * @param string $function_name Function name.
	 * @param string $message       Message.
	 *
	 * @return bool
	 */
	protected function is_just_in_time_for_wpforms_domain( string $function_name, string $message ): bool {

		return $function_name === '_load_textdomain_just_in_time' && strpos( $message, '<code>wpforms' ) !== false;
	}
}
