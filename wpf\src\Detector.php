<?php

namespace WPForms\DevTools;

use DOMDocument;

/**
 * Detector service.
 *
 * @since {VERSION}
 */
class Detector {

	/**
	 * Full url.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private $full_url;

	/**
	 * Schema plus host.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private $url;

	/**
	 * URL host.
	 *
	 * @since {VERSION}
	 *
	 * @var static
	 */
	private $host;

	/**
	 * HTML content.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private $content;

	/**
	 * List pages.
	 *
	 * @since {VERSION}
	 *
	 * @var array
	 */
	private $pages = [];

	/**
	 * HTML content of first page.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private $first_page_body = '';

	/**
	 * Detector constructor.
	 *
	 * @since {VERSION}
	 *
	 * @param string $url Request URL.
	 */
	public function __construct( $url ) {

		$this->full_url = $url;
		$parse_url      = wp_parse_url( $url );
		$this->host     = $parse_url['host'];
		$this->url      = $parse_url['scheme'] . '://' . $parse_url['host'];
		$this->content  = wp_remote_retrieve_body( wp_remote_get( $this->full_url ) );
		$this->pages    = $this->pages_with_wpforms();
	}

	/**
	 * Detect WPForms.
	 *
	 * @since {VERSION}
	 *
	 * @return array
	 */
	private function wpforms() {

		$changelog_version = $this->get_version_form_changelog( 'wpforms' );

		if ( $changelog_version && $this->is_pro_version_active() ) {
			return [
				'type'    => 'Pro',
				'version' => $changelog_version,
			];
		}

		$response = wp_remote_get( $this->url . '/wp-content/plugins/wpforms-lite/readme.txt' );
		$body     = wp_remote_retrieve_body( $response );

		if ( empty( $body ) || wp_remote_retrieve_response_code( $response ) !== 200 ) {
			return [
				'type'    => false,
				'version' => false,
			];
		}

		preg_match( '^Stable tag:\s+([0-9.]+)^', $body, $version );

		return [
			'type'    => 'Lite',
			'version' => isset( $version[1] ) ? $version[1] : false,
		];
	}


	/**
	 * Detect version from changelog.md.
	 *
	 * @since {VERSION}
	 *
	 * @param string $slug Addon slug.
	 *
	 * @return string|bool
	 */
	private function get_version_form_changelog( $slug ) {

		$response = wp_remote_get( $this->url . '/wp-content/plugins/' . $slug . '/CHANGELOG.md' );
		$body     = wp_remote_retrieve_body( $response );

		if ( ! empty( $body ) && wp_remote_retrieve_response_code( $response ) === 200 ) {
			preg_match( '~\[([0-9.]+)]~', $body, $version );

			return isset( $version[1] ) ? $version[1] : false;
		}

		return false;
	}

	/**
	 * Detect installed addons.
	 *
	 * @since {VERSION}
	 *
	 * @return array
	 */
	private function installed_addons() {

		$addons = Main::wpforms_obj( 'license' )->get_addons();

		if ( ! $addons ) {
			return [];
		}

		$active_addons = [];

		foreach ( $addons as $addon ) {

			$response = wp_remote_head( $this->url . '/wp-content/plugins/' . $addon->slug . '/languages/' . $addon->slug . '.pot' );

			if ( 200 === wp_remote_retrieve_response_code( $response ) ) {
				$active_addons[ $addon->slug ]['title']   = $addon->title;
				$version                                  = $this->get_version_form_changelog( $addon->slug );
				$active_addons[ $addon->slug ]['version'] = $version ? $version : false;
			}
		}

		return $active_addons;
	}

	/**
	 * Detect pages with WPForms.
	 *
	 * @since {VERSION}
	 *
	 * @return array
	 */
	private function pages_with_wpforms() {

		$this->pages = array_merge( $this->pages, $this->pages_in_rest_search_by( 'wp:wpforms' ) );
		$this->pages = array_merge( $this->pages, $this->pages_in_rest_search_by( '[wpforms' ) );

		if ( ! empty( $this->pages ) ) {
			return $this->pages;
		}

		$this->pages = array_merge( $this->pages, $this->pages_in_feed_search_by( 'wp:wpforms' ) );
		$this->pages = array_merge( $this->pages, $this->pages_in_feed_search_by( '[wpforms' ) );

		return $this->pages;
	}

	/**
	 * Search pages using the REST API.
	 *
	 * @since {VERSION}
	 *
	 * @param string $s Search string.
	 *
	 * @return array
	 */
	private function pages_in_rest_search_by( $s ) {

		$pages    = [];
		$response = wp_remote_get(
			sprintf(
				$this->url . '/wp-json/wp/v2/search/?search=%s&per_page=50',
				$s
			)
		);
		$body     = (array) json_decode( wp_remote_retrieve_body( $response ), true );

		if ( ! empty( $body ) && wp_remote_retrieve_response_code( $response ) === 200 ) {
			foreach ( $body as $page ) {
				$page = (array) $page;

				if ( ! isset( $page['title'], $page['url'] ) ) {
					continue;
				}

				$pages[ $page['url'] ] = $page['title'];
			}
		}

		return $pages;
	}

	/**
	 * Search pages using the feed.
	 *
	 * @since {VERSION}
	 *
	 * @param string $s Search string.
	 *
	 * @return array
	 */
	private function pages_in_feed_search_by( $s ) {

		$response = wp_remote_get(
			sprintf(
				$this->url . '/?s=%s&feed=rss2',
				$s
			)
		);
		$body     = wp_remote_retrieve_body( $response );

		if ( empty( $body ) || wp_remote_retrieve_response_code( $response ) !== 200 ) {
			return [];
		}

		$pages = [];

		libxml_use_internal_errors( true );

		$dom = new DOMDocument();

		$dom->loadXML( $body );
		$items = $dom->getElementsByTagName( 'item' );

		foreach ( $items as $item ) {
			$title = $item->getElementsByTagName( 'title' );
			$link  = $item->getElementsByTagName( 'link' );

			if ( ! $title->length || ! $link->length ) {
				continue;
			}
			$pages[ $link->item( 0 )->nodeValue ] = $title->item( 0 )->nodeValue;
		}

		unset( $dom );
		libxml_clear_errors();

		return $pages;
	}

	/**
	 * Detect WordPress version.
	 *
	 * @since {VERSION}
	 *
	 * @return string
	 */
	private function wordpress_version() {

		preg_match( '/<meta name="generator" content="WordPress ([\d.]+)">/', $this->content, $version );

		if ( ! empty( $version[1] ) ) {
			return $version[1];
		}

		$response = wp_remote_get( $this->url . '/feed/' );
		$body     = wp_remote_retrieve_body( $response );

		if ( empty( $body ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
			return '';
		}

		preg_match( '/<generator>https:\/\/wordpress.org\/\?v=([\d.]+)<\/generator>/', $body, $version );

		return ! empty( $version[1] ) ? $version[1] : '';
	}

	/**
	 * Detect Cache/Optimize plugins.
	 *
	 * @since {VERSION}
	 *
	 * @return array
	 */
	private function cache_plugins() {

		$plugins = [
			'WP Rocket'                        => 'wp-rocket/contributors.txt',
			'WP Super Cache'                   => 'wp-super-cache/readme.txt',
			'Autoptimize'                      => 'autoptimize/readme.txt',
			'W3 Total Cache'                   => 'w3-total-cache/readme.txt',
			'WP Fastest Cache'                 => 'wp-fastest-cache/readme.txt',
			'Litespeed Cache'                  => 'litespeed-cache/readme.txt',
			'WP Optimize'                      => 'wp-optimize/readme.txt',
			'Hyper Cache'                      => 'hyper-cache/readme.txt',
			'Comet Cache'                      => 'comet-cache/readme.txt',
			'Cachify'                          => 'cachify/readme.txt',
			'Simple Cache'                     => 'simple-cache/readme.txt',
			'SG SuperCacher (or SG Optimizer)' => 'sg-cachepress/readme.txt',
			'WP Engine Advanced Cache'         => 'wpe-advanced-cache-options/readme.txt',
			'Cache Enabler'                    => 'cache-enabler/readme.txt',
		];

		$active_plugins = [];

		foreach ( $plugins as $plugin => $plugin_url ) {
			$request = wp_remote_head( $this->url . '/wp-content/plugins/' . $plugin_url );

			if ( wp_remote_retrieve_response_code( $request ) === 200 ) {
				$active_plugins[] = $plugin;
			}
		}

		return $active_plugins;
	}

	/**
	 * Detect theme name.
	 *
	 * @since {VERSION}
	 *
	 * @return string
	 */
	private function theme() {

		preg_match( '/wp-content\/themes\/([\w\-%]+)\//', $this->content, $theme );

		if ( empty( $theme[1] ) ) {
			return '';
		}

		$response = wp_remote_get( $this->url . '/wp-content/themes/' . $theme[1] . '/style.css' );
		$body     = wp_remote_retrieve_body( $response );

		if ( empty( $body ) || wp_remote_retrieve_response_code( $response ) !== 200 ) {
			return $theme[1];
		}

		preg_match( '/[Tt]heme [Nn]ame:\s+([\w\- ]+)/', $body, $theme_name );

		return ! empty( $theme_name[1] ) ? trim( $theme_name[1] ) : '';
	}

	/**
	 * Run detector.
	 *
	 * @since {VERSION}
	 *
	 * @return array
	 */
	public function run() {

		return [
			'wpforms'       => $this->wpforms(),
			'addons'        => $this->installed_addons(),
			'pages'         => $this->pages_with_wpforms(),
			'wp_version'    => $this->wordpress_version(),
			'cache_plugins' => $this->cache_plugins(),
			'theme'         => $this->theme(),
			'assets'        => $this->assets(),
		];
	}

	/**
	 * Detect wpforms.js on the page.
	 *
	 * @since {VERSION}
	 *
	 * @return bool
	 */
	private function assets() {

		// Find 'wpforms.js' or 'wpforms.min.js' occurrences in the content.
		if ( preg_match( '/wpforms(?:\.min|)\.js/', $this->content ) ) {
			return true;
		}

		if ( empty( $this->pages ) ) {
			return false;
		}

		$body = $this->get_first_page_body();

		return $body && preg_match( '/wpforms(?:\.min|)\.js/', $body );
	}

	/**
	 * Return HTML content of first page.
	 *
	 * @since {VERSION}
	 *
	 * @return string
	 */
	private function get_first_page_body() {

		if ( empty( $this->pages ) ) {
			return '';
		}

		if ( empty( $this->first_page_body ) ) {

			$page_url = array_key_first( $this->pages );

			$response = wp_remote_get( $page_url );
			$body     = wp_remote_retrieve_body( $response );

			if ( ! empty( $body ) && wp_remote_retrieve_response_code( $response ) === 200 ) {
				$this->first_page_body = $body;
			}
		}

		return $this->first_page_body;
	}

	/**
	 * Check if Pro version active.
	 *
	 * @since {VERSION}
	 *
	 * @return bool
	 */
	private function is_pro_version_active() {

		$body = $this->get_first_page_body();

		// If no pages with forms on the site and both versions installed then Pro version will show as active.
		return empty( $this->pages ) || ( ( $body && preg_match( '^/wpforms/assets/js/wpforms(?:\.min|)\.js^', $body ) ) || ( $body && preg_match( '^/wpforms/assets/images/submit-spin.svg^', $body ) ) );
	}
}
