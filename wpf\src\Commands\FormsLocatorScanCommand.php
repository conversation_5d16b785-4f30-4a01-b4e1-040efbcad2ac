<?php

// phpcs:disable Generic.Commenting.DocComment.MissingShort
/** @noinspection PhpUndefinedNamespaceInspection */
/** @noinspection PhpUndefinedClassInspection */
// phpcs:enable Generic.Commenting.DocComment.MissingShort

namespace WPForms\DevTools\Commands;

use WP_CLI;
use WPForms\Tasks\Actions\FormsLocatorScanTask;

/**
 * Scans or deletes form locations.
 *
 * @since {VERSION}
 */
class FormsLocatorScanCommand {

	/**
	 * Scan form locations.
	 *
	 * : Search pages, posts, custom post types, widgets containing forms.
	 *
	 * ## EXAMPLES
	 *
	 *     wp wpforms locations scan
	 *
	 * @since {VERSION}
	 *
	 * @param array $args       Arguments.
	 * @param array $assoc_args Associative arguments.
	 *
	 * @throws WP_CLI\ExitException Returns error exit code.
	 * @noinspection PhpUnusedParameterInspection
	 */
	public function scan( $args, $assoc_args ) {

		/**
		 * Run Forms Locator rescan action to launch scan immediately.
		 *
		 * @since {VERSION}
		 */
		do_action( FormsLocatorScanTask::RESCAN_ACTION ); // phpcs:ignore WPForms.PHP.ValidateHooks.InvalidHookName

		WP_CLI::success( 'Scan has been started and is performing in the background, please visit your site after a while.' );
	}

	/**
	 * Delete form locations.
	 *
	 * : Delete all form locations data.
	 *
	 * ## EXAMPLES
	 *
	 *     wp wpforms locations delete
	 *
	 * @since {VERSION}
	 *
	 * @param array $args       Arguments.
	 * @param array $assoc_args Associative arguments.
	 *
	 * @throws WP_CLI\ExitException Returns error exit code.
	 * @noinspection PhpUnusedParameterInspection
	 */
	public function delete( $args, $assoc_args ) {

		/**
		 * Run Forms Locator delete action.
		 *
		 * @since {VERSION}
		 */
		do_action( FormsLocatorScanTask::DELETE_ACTION ); // phpcs:ignore WPForms.PHP.ValidateHooks.InvalidHookName

		WP_CLI::success( 'All locations are deleted.' );
	}
}
