<?php

namespace WPForms\DevTools;

use WP_Admin_Bar;
use WPForms\DevTools\FormTemplates\AllDynamicChoices;
use WPForms\DevTools\FormTemplates\AllFieldsNoPages;
use WPForms\DevTools\FormTemplates\AllPaymentsWithCl;
use WPForms\DevTools\FormTemplates\Calculations;
use WPForms\DevTools\FormTemplates\CheckboxesAndMultipleChoices;
use WPForms\DevTools\FormTemplates\FancyFields;
use WPForms\DevTools\FormTemplates\StandardFields;

/**
 * Form Templates.
 *
 * @since 0.18
 */
class FormTemplates {

	/**
	 * Constructor.
	 *
	 * @since 0.18
	 */
	private function __construct() {

		$this->init();
	}

	/**
	 * Instantiate class.
	 *
	 * @since 0.18
	 */
	public static function instance() {

		new self();
	}

	/**
	 * Init class.
	 *
	 * @since 0.18
	 */
	private function init() {

		if (
			! function_exists( 'wpforms' ) ||
			! class_exists( '\WPForms_Template' ) ||
			! wpforms_current_user_can()
		) {
			return;
		}

		/**
		 * Whether to load WPForms\DevTools\FormTemplates class.
		 *
		 * @since 0.18
		 *
		 * @param bool $allow True if allowed. Defaults to true.
		 */
		if ( ! apply_filters( 'wpforms_dev_tools_form_templates', true ) ) {
			return;
		}

		$this->hooks();
		$this->load();
	}

	/**
	 * Hooks.
	 *
	 * @since 0.24
	 */
	public function hooks() {

		add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_assets' ] );
		add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_assets' ] );
		add_action( 'wpforms_admin_adminbarmenu_register_support_menu_after', [ $this, 'admin_bar_menu_links' ], 998 );
	}

	/**
	 * Enqueue assets.
	 *
	 * @since 0.24
	 */
	public function enqueue_assets() {

		wp_enqueue_script(
			'wpf-form-templates',
			WPFORMS_DEV_TOOLS_URL . '/assets/js/form-templates.js',
			[ 'jquery' ],
			false,
			true
		);

		wp_localize_script(
			'wpf-form-templates',
			'wpf_form_templates',
			[
				'nonce'    => wp_create_nonce( 'wpforms-builder' ),
				'ajax_url' => admin_url( 'admin-ajax.php' ),
			]
		);
	}

	/**
	 * Adds positions to WPForms menu in admin bar.
	 *
	 * @since 0.24
	 *
	 * @param WP_Admin_Bar $wp_admin_bar Admin bar.
	 *
	 * @return void
	 */
	public static function admin_bar_menu_links( $wp_admin_bar ) {

		$nodes[] = [
			'parent' => 'wpforms-menu',
			'title'  => esc_html__( 'Create from Template', 'wpf' ),
			'id'     => 'wpforms-form-templates',
			'href'   => '#',
		];

		$templates = [
			'all_dynamic_choices'             => 'All Dynamic Choices',
			'wpf__all_fields_no_pages'        => 'All fields (no pages)',
			'all_payments_with_cl'            => 'All Payments with CL',
			'calculations_test_form'          => 'Calculations Test Form',
			'checkboxes_and_multiple_choices' => 'Checkboxes and Multiple Choices',
			'fancy_fields'                    => 'Fancy Fields',
			'standard_fields'                 => 'Standard Fields',
		];

		foreach ( $templates as $template => $title ) {
			$nodes[] = [
				'parent' => 'wpforms-form-templates',
				'title'  => esc_html( $title ),
				'id'     => $template,
				'href'   => add_query_arg(
					[
						'form-template' => $template,
						'form-title'    => $title,
					],
					admin_url( 'admin.php?page=wpforms-builder' )
				),
				'meta'   => [
					'class' => 'wpforms-form-templates',
				],
			];
		}

		foreach ( $nodes as $node ) {
			if ( ! empty( $node ) ) {
				$wp_admin_bar->add_node( $node );
			}
		}
	}

	/**
	 * Load templates.
	 *
	 * @since 0.18
	 */
	private function load() {

		require_once 'FormTemplates/AllDynamicChoices.php';
		require_once 'FormTemplates/AllFieldsNoPages.php';
		require_once 'FormTemplates/AllPaymentsWithCl.php';
		require_once 'FormTemplates/Calculations.php';
		require_once 'FormTemplates/CheckboxesAndMultipleChoices.php';
		require_once 'FormTemplates/FancyFields.php';
		require_once 'FormTemplates/StandardFields.php';

		new AllDynamicChoices();
		new AllFieldsNoPages();
		new AllPaymentsWithCl();
		new Calculations();
		new CheckboxesAndMultipleChoices();
		new FancyFields();
		new StandardFields();
	}
}
