// WPForms Modern Base styles.
//
// Common fields and form layouts.
//
// @since 1.8.1

.wpforms-container {

	// Legacy columns classes.
	// Import directly from the classic styles.
	@import "../../classic/partials/legacy-columns-base";

	.wpforms-field {
		float: none;
		clear: both;

		// This is needed to fix the classes from Classic styles in Modern markup mode.
		&.wpforms-five-sixths,
		&.wpforms-four-sixths,
		&.wpforms-four-fifths,
		&.wpforms-one-fifth,
		&.wpforms-one-fourth,
		&.wpforms-one-half,
		&.wpforms-one-sixth,
		&.wpforms-one-third,
		&.wpforms-three-fourths,
		&.wpforms-three-fifths,
		&.wpforms-three-sixths,
		&.wpforms-two-fourths,
		&.wpforms-two-fifths,
		&.wpforms-two-sixths,
		&.wpforms-two-thirds {
			float: left;
			margin-left: 20px;
			clear: none;
		}

		// Rows (multi-line fields: address, credit card, etc).
		.wpforms-field-row {
			align-items: start;
			position: relative;
			display: flex;
			justify-content: space-between;
			margin-bottom: $spacing_m;

			.wpforms-field-row-block {
				padding: 0 $spacing_s;

				&:first-child {
					padding-inline-start: 0;
				}

				&:last-child {
					padding-inline-end: 0;
				}

				&:only-child {
					margin-right: auto;
					padding-right: $spacing_s;
				}
			}

			&:before {
				content: "";
				display: table;
			}

			&:after {
				clear: both;
				content: "";
				display: table;
			}

			&:last-of-type {
				margin-bottom: 0;
			}

			& > :only-child {
				width: 100%;
			}

			&.wpforms-no-columns {
				display: block;
			}

			// Field column classes.
			.wpforms-five-sixths,
			.wpforms-four-sixths,
			.wpforms-four-fifths,
			.wpforms-one-fifth,
			.wpforms-one-fourth,
			.wpforms-one-half,
			.wpforms-one-sixth,
			.wpforms-one-third,
			.wpforms-three-fourths,
			.wpforms-three-fifths,
			.wpforms-three-sixths,
			.wpforms-two-fourths,
			.wpforms-two-fifths,
			.wpforms-two-sixths,
			.wpforms-two-thirds {
				float: none;
				margin-left: 0;
				clear: initial;
			}

			.wpforms-one-half,
			.wpforms-three-sixths,
			.wpforms-two-fourths {
				width: 1 / 2 * 100%;
			}

			.wpforms-one-third,
			.wpforms-two-sixths {
				width: 1 / 3 * 100%;
			}

			.wpforms-four-sixths,
			.wpforms-two-thirds {
				width: 2 / 3 * 100%;
			}

			.wpforms-one-fourth {
				width: 1 / 4 * 100%;
			}

			.wpforms-three-fourths {
				width: 3 / 4 * 100%;
			}

			.wpforms-one-fifth {
				width: 1 / 5 * 100%;
			}

			.wpforms-two-fifths {
				width: 2 / 5 * 100%;
			}

			.wpforms-three-fifths {
				width: 3 / 5 * 100%;
			}

			.wpforms-four-fifths {
				width: 4 / 5 * 100%;
			}

			.wpforms-one-sixth {
				width: 1 / 6 * 100%;
			}

			.wpforms-five-sixths {
				width: 5 / 6 * 100%;
			}
		}

		// User list column classes.
		.wpforms-checkbox-2-columns,
		.wpforms-multiplechoice-2-columns,
		.wpforms-list-2-columns,
		.wpforms-checkbox-3-columns,
		.wpforms-multiplechoice-3-columns,
		.wpforms-list-3-columns {
			ul {
				display: grid;
				gap: $spacing_m $spacing_l;
			}
		}

		.wpforms-checkbox-2-columns,
		.wpforms-multiplechoice-2-columns,
		.wpforms-list-2-columns {
			ul {
				grid-template-columns: repeat( 2, 1fr );
			}
		}

		.wpforms-checkbox-3-columns,
		.wpforms-multiplechoice-3-columns,
		.wpforms-list-3-columns {
			ul {
				grid-template-columns: repeat( 3, 1fr );
			}
		}

		.wpforms-list-inline {
			ul li {
				display: inline-block;
				vertical-align: top;
				margin-right: $spacing_ml;
			}
		}
	}

	// Form layout - Single line.
	&.inline-fields {
		overflow: visible;

		.wpforms-form {
			display: flex;
			justify-content: space-between;
		}

		.wpforms-field-container  {
			display: flex;
			justify-content: space-between;
			width: calc( 100% - 175px );

			.wpforms-field {
				padding-right: 7px;
				padding-left: 8px;

				&:first-of-type {
					padding-left: 0;
				}

				&:last-of-type {
					padding-right: 0;
				}
			}
		}

		.wpforms-field-row {
			&:first-of-type {
				.wpforms-field-row-block {
					&:first-child {
						padding-left: 0;
					}
				}
			}
		}

		.wpforms-submit-container {
			width: 160px;
			padding-bottom: 16px;
			align-self: flex-end;
		}

		.wpforms-submit {
			display: block;
			width: 100%;
		}

		input.wpforms-field-medium,
		select.wpforms-field-medium,
		.wpforms-field-row.wpforms-field-medium {
			max-width: 100%;
		}
	}
}
