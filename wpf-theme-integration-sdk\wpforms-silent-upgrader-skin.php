<?php
// phpcs:disable

/**
 * Class WPFormsThemeDevSDK_SilentUpgraderSkin.
 *
 * @since {VERSION}
 */
class WPFormsThemeDevSDK_SilentUpgraderSkin extends \WP_Upgrader_Skin {

	/**
	 * Header.
	 *
	 * @since {VERSION}
	 */
	public function header() {}

	/**
	 * Footer.
	 *
	 * @since {VERSION}
	 */
	public function footer() {}

	/**
	 * Decrement update count.
	 *
	 * @since {VERSION}
	 */
	public function decrement_update_count( $type ) {}

	/**
	 * Feedback.
	 *
	 * @since {VERSION}
	 */
	public function feedback( $string, ...$args ) {}

	/**
	 * Errors.
	 *
	 * @since {VERSION}
	 */
	public function error( $errors ) {
		return $errors;
	}
}
