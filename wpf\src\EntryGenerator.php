<?php

namespace WPForms\DevTools;

use Closure;
use Faker\Factory;
use Faker\Generator;

/**
 * Generator service.
 *
 * @since 0.5
 */
class EntryGenerator {

	/**
	 * Generate fields data.
	 *
	 * @since 0.5
	 *
	 * @param array $fields Input fields from form.
	 *
	 * @return array
	 */
	public static function fields( $fields ) {

		return wpforms_chain( $fields )
			->map( self::run_generate_func( Factory::create() ) )
			->value();
	}

	/**
	 * Decide which function we have to run for each field.
	 *
	 * @since 0.5
	 *
	 * @param Generator $context Instance.
	 *
	 * @return Closure
	 */
	public static function run_generate_func( $context ) {

		return static function ( $field ) use ( $context ) {

			$name = wpforms_list_get(
				[
					'text'               => 'text',
					'textarea'           => 'textarea',
					'select'             => 'select',
					'radio'              => 'select',
					'checkbox'           => 'select',
					'number'             => 'number',
					'name'               => 'name',
					'email'              => 'email',
					'phone'              => 'phone',
					'address'            => 'address',
					'date-time'          => 'date_time',
					'url'                => 'url',
					'password'           => 'password',
					'hidden'             => 'text',
					'html'               => 'html',
					'richtext'           => 'html',
					'rating'             => 'rating',
					'likert_scale'       => 'likert_scale',
					'payment-single'     => 'payment_single',
					'payment-multiple'   => 'payment_multiple',
					'payment-checkbox'   => 'payment_multiple',
					'payment-select'     => 'payment_multiple',
					'stripe-credit-card' => 'credit_card',
					'authorize_net'      => 'credit_card',
					'paypal-commerce'    => 'credit_card',
					'square'             => 'credit_card',
				],
				$field['type'],
				'def'
			);

			return array_merge(
				[
					'id'   => $field['id'],
					'name' => ! empty( $field['label'] ) ? $field['label'] : '',
					'type' => $field['type'],
				],
				call_user_func( [ __CLASS__, $name ], $context, $field )
			);
		};
	}

	/**
	 * Generate for text type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function text( $faker, $field ) {

		return [
			'value' => $faker->sentence( 6, true ),
		];
	}

	/**
	 * Generate for textarea type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function textarea( $faker, $field ) {

		return [
			'value' => $faker->sentence( 12, true ),
		];
	}

	/**
	 * Generate for select type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function select( $faker, $field ) {

		return wpforms_chain( wpforms_list_get( $field, 'choices', [] ) )
			->bind(
				static function ( $choices ) {

					return $choices[ array_rand( $choices ) ];
				}
			)
			->bind(
				static function ( $choice ) {

					return [
						'value'     => $choice['label'],
						'value_raw' => $choice['value'] === '' ? $choice['label'] : $choice['value'],
					];
				}
			)
			->value();
	}

	/**
	 * Generate for likert scale type.
	 *
	 * @since {VERSION}
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function likert_scale( $faker, $field ) {

		$rows    = wpforms_list_get( $field, 'rows', [] );
		$columns = wpforms_list_get( $field, 'columns', [] );

		$values     = [];
		$row_values = [];

		foreach ( $rows as $row_index => $row ) {
			$rand_column = $columns[ array_rand( $columns ) ];

			$rand_column_index = array_search( $rand_column, $columns, true );

			unset( $columns[ $rand_column_index ] );

			$values[]                 = "{$row}\n{$rand_column}";
			$row_values[ $row_index ] = $rand_column_index;
		}

		return [
			'value'     => implode( "\n", $values ),
			'value_raw' => $row_values,
		];
	}

	/**
	 * Generate for number type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function number( $faker, $field ) {

		return [
			'value' => $faker->randomNumber( null, false ),
		];
	}

	/**
	 * Generate for name type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function name( $faker, $field ) {

		$name     = $faker->name;
		$exploded = explode( ' ', $name );
		$first    = array_shift( $exploded );
		$last     = implode( ' ', $exploded );

		return [
			'value' => $name,
			'first' => $first,
			'last'  => $last,
		];
	}

	/**
	 * Generate for email type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function email( $faker, $field ) {

		return [
			'value' => $faker->email,
		];
	}

	/**
	 * Generate for phone type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function phone( $faker, $field ) {

		return [
			'value' => $faker->e164PhoneNumber, // phpcs:ignore WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase
		];
	}

	/**
	 * Generate for address type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function address( $faker, $field ) {

		$address1 = $faker->streetAddress; // phpcs:ignore WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase
		$city     = $faker->city;
		$state    = $faker->state;
		$postal   = $faker->postcode;
		$country  = $faker->country;

		return [
			'value'    => implode(
				' ',
				[
					$postal,
					$country,
					$state,
					$city,
					$address1,
				]
			),
			'address1' => $address1,
			'address2' => $faker->secondaryAddress, // phpcs:ignore WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase
			'city'     => $city,
			'state'    => $state,
			'postal'   => $postal,
			'country'  => $country,
		];
	}

	/**
	 * Generate for date-time type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function date_time( $faker, $field ) {

		$ts = $faker->unixTime();

		return [
			'value' => wpforms_datetime_format( $ts, 'm/d/Y g:i a', true ),
			'date'  => wpforms_date_format( $ts, 'm/d/Y', true ),
			'time'  => wpforms_time_format( $ts, 'g:i a', true ),
			'unix'  => $ts,
		];
	}

	/**
	 * Generate for url type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function url( $faker, $field ) {

		return [
			'value' => $faker->url,
		];
	}

	/**
	 * Generate for password type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function password( $faker, $field ) {

		return [
			'value' => $faker->password,
		];
	}

	/**
	 * Generate for html type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function html( $faker, $field ) {

		return [
			'value' => $faker->randomHtml( 2, 3 ),
		];
	}

	/**
	 * Generate for rating type.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function rating( $faker, $field ) {

		$scale = (int) wpforms_list_get( $field, 'scale', 1 );

		return [
			'value' => $faker->numberBetween( 0, $scale ),
			'icon'  => wpforms_list_get( $field, 'icon', 'start' ),
			'scale' => $scale,
		];
	}

	/**
	 * Generate for credit card type.
	 *
	 * @since 0.18
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function credit_card( $faker, $field ) {

		$details = $faker->creditCardDetails();

		return [
			'value' => implode(
				"\n",
				[
					'name'  => $details['name'],
					'last4' => 'XXXXXXXXXXXX' . substr( $details['number'], -4 ),
					'brand' => $details['type'],
				]
			),
		];
	}

	/**
	 * Generate for single payment type.
	 *
	 * @since 0.18
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function payment_single( $faker, $field ) {

		$amount = $faker->numberBetween( 0, 100 );

		return [
			'value'      => wpforms_format_amount( $amount, true ),
			'amount'     => wpforms_format_amount( $amount ),
			'amount_raw' => $amount,
			'currency'   => wpforms_get_currency(),
		];
	}

	/**
	 * Generate for multiple payment type.
	 *
	 * @since 0.18
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function payment_multiple( $faker, $field ) {

		$data = self::payment_single( $faker, $field );

		$rand_idx = array_rand( $field['choices'] );
		$choice   = $field['choices'][ $rand_idx ];
		$amount   = (int) $choice['value'];

		$data['value']        = $choice['label'] . ' - ' . wpforms_format_amount( $amount, true );
		$data['value_choice'] = $choice['label'];
		$data['value_raw']    = $rand_idx;
		$data['amount']       = wpforms_format_amount( $amount );
		$data['amount_raw']   = $amount;

		return $data;
	}

	/**
	 * Default generate function.
	 *
	 * @since 0.5
	 *
	 * @param Generator $faker Instance.
	 * @param array     $field Input field.
	 *
	 * @return array
	 */
	public static function def( $faker, $field ) {

		return [];
	}
}
