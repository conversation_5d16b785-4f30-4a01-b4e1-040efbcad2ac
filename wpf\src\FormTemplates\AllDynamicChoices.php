<?php

namespace WPForms\DevTools\FormTemplates;

use WPF<PERSON>_Template;

/**
 * WPForms DevTools template: All Dynamic Choices.
 *
 * @since 0.24
 */
class AllDynamicChoices extends WPForms_Template {

	/**
	 * Primary class constructor.
	 *
	 * @since 0.24
	 *
	 * @noinspection HtmlUnknownTarget
	 * @noinspection PackedHashtableOptimizationInspection
	 */
	public function init() {

		// Template name.
		$this->name = 'All Dynamic Choices';

		// Template slug.
		$this->slug = 'all_dynamic_choices';

		// Template description.
		$this->description = '';

		// Template field and settings.
		// phpcs:disable WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned, WordPress.Arrays.MultipleStatementAlignment.LongIndexSpaceBeforeDoubleArrow
		$this->data = [
			'fields'     => [
				15 => [
					'id'            => '15',
					'type'          => 'divider',
					'label'         => 'Landing Pages',
					'label_disable' => '1',
				],
				1  => [
					'id'                   => '1',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - Landing Pages (One Column)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'e-landing-page',
				],
				9  => [
					'id'                   => '9',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - Landing Pages (Two Columns)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'e-landing-page',
				],
				10 => [
					'id'                   => '10',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - Landing Pages (Three Columns)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '3',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'e-landing-page',
				],
				11 => [
					'id'                   => '11',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - Landing Pages (Inline)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'e-landing-page',
				],
				16 => [
					'id'            => '16',
					'type'          => 'divider',
					'label'         => 'My Templates',
					'label_disable' => '1',
				],
				12 => [
					'id'                   => '12',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - My Templates (One Column)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'elementor_library',
				],
				3  => [
					'id'                   => '3',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - My Templates (Two Columns)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'elementor_library',
				],
				13 => [
					'id'                   => '13',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - My Templates (Three Columns)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '3',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'elementor_library',
				],
				14 => [
					'id'                   => '14',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - My Templates (Inline)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'elementor_library',
				],
				17 => [
					'id'            => '17',
					'type'          => 'divider',
					'label'         => 'Pages',
					'label_disable' => '1',
				],
				4  => [
					'id'                   => '4',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - Pages (One Column)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'page',
				],
				19 => [
					'id'                   => '19',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - Pages	(Two Columns)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'page',
				],
				20 => [
					'id'                   => '20',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - Pages	(Three Columns)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '3',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'page',
				],
				21 => [
					'id'                   => '21',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - Pages (Inline)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'page',
				],
				18 => [
					'id'            => '18',
					'type'          => 'divider',
					'label'         => 'Posts',
					'label_disable' => '1',
				],
				5  => [
					'id'                   => '5',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - Posts (One Column)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'post',
				],
				22 => [
					'id'                   => '22',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - Posts (Two Columns)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'post',
				],
				23 => [
					'id'                   => '23',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - Posts (Three Columns)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '3',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'post',
				],
				24 => [
					'id'                   => '24',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Post Type - Posts (Inline)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
					'dynamic_choices'      => 'post_type',
					'dynamic_post_type'    => 'post',
				],
				29 => [
					'id'            => '29',
					'type'          => 'divider',
					'label'         => 'Taxonomy - Categories (copy)',
					'label_disable' => '1',
				],
				6  => [
					'id'                   => '6',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Taxonomy - Categories (One Column)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'dynamic_choices'      => 'taxonomy',
					'dynamic_taxonomy'     => 'category',
				],
				26 => [
					'id'                   => '26',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Taxonomy - Categories (Two Columns)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
					'dynamic_choices'      => 'taxonomy',
					'dynamic_taxonomy'     => 'category',
				],
				27 => [
					'id'                   => '27',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Taxonomy - Categories (Three Columns)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '3',
					'dynamic_choices'      => 'taxonomy',
					'dynamic_taxonomy'     => 'category',
				],
				28 => [
					'id'                   => '28',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Taxonomy - Categories (Inline)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
					'dynamic_choices'      => 'taxonomy',
					'dynamic_taxonomy'     => 'category',
				],
				25 => [
					'id'            => '25',
					'type'          => 'divider',
					'label'         => 'Taxonomy - Tags',
					'label_disable' => '1',
				],
				7  => [
					'id'                   => '7',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Taxonomy - Tags (One Column)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'dynamic_choices'      => 'taxonomy',
					'dynamic_taxonomy'     => 'post_tag',
				],
				32 => [
					'id'                   => '32',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Taxonomy - Tags (Two Columns)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
					'dynamic_choices'      => 'taxonomy',
					'dynamic_taxonomy'     => 'post_tag',
				],
				33 => [
					'id'                   => '33',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Taxonomy - Tags (Three Columns)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '3',
					'dynamic_choices'      => 'taxonomy',
					'dynamic_taxonomy'     => 'post_tag',
				],
				34 => [
					'id'                   => '34',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Dynamic Choices - Taxonomy - Tags (Inline)',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
					'dynamic_choices'      => 'taxonomy',
					'dynamic_taxonomy'     => 'post_tag',
				],
			],
			'field_id'   => 35,
			'settings'   => [
				'form_title'                                       => 'All Dynamic Choices',
				'submit_text'                                      => 'Submit',
				'submit_text_processing'                           => 'Sending...',
				'ajax_submit'                                      => '1',
				'notification_enable'                              => '1',
				'notifications'                                    => [
					1 => [
						'notification_name'                      => 'Default Notification',
						'email'                                  => '{admin_email}',
						'subject'                                => 'New Entry: Blank Form (ID #3659)',
						'sender_name'                            => '10TEST',
						'sender_address'                         => '{admin_email}',
						'message'                                => '{all_fields}',
						'file_upload_attachment_fields'          => [],
						'entry_csv_attachment_entry_information' => [],
						'entry_csv_attachment_file_name'         => 'entry-details',
					],
				],
				'confirmations'                                    => [
					1 => [
						'name'                        => 'Default Confirmation',
						'type'                        => 'message',
						'message'                     => '<p>Thanks for contacting us! We will be in touch with you shortly.</p>',
						'message_scroll'              => '1',
						'page'                        => '666',
						'message_entry_preview_style' => 'basic',
					],
				],
				'lead_forms'                                       => [
					'accent_color'   => '#0299ed',
					'field_borders'  => '#cccccc',
					'primary_text'   => '#444444',
					'secondary_text' => '#777777',
				],
				'antispam_v3'                                      => '1',
				'anti_spam'                                        => [
					'time_limit'     => [
						'duration' => '3',
					],
					'country_filter' => [
						'action'        => 'allow',
						'country_codes' => [],
						'message'       => 'Sorry, this form does not accept submissions from your country.',
					],
					'keyword_filter' => [
						'message' => 'Sorry, your message can\'t be submitted because it contains prohibited words.',
					],
				],
				'post_submissions_type'                            => 'post',
				'post_submissions_status'                          => 'pending',
				'registration_role'                                => 'subscriber',
				'registration_activation_method'                   => 'user',
				'registration_email_user_activation_subject'       => '{site_name} Activation Required',
				'registration_email_user_activation_message'       => 'IMPORTANT: You must activate your account before you can log in.
Please visit the link below.

{url_user_activation}',
				'registration_hide_message'                        => 'Hi {user_first_name}, you’re already logged in. <a href="{url_logout}">Log out</a>.',
				'registration_email_admin_subject'                 => '{site_name} New User Registration',
				'registration_email_admin_message'                 => 'New user registration on your site {site_name}:

Username: {user_registration_login}
Email: {user_registration_email}',
				'registration_email_user_subject'                  => '{site_name} Your username and password info',
				'registration_email_user_message'                  => 'Username: {user_registration_login}
Password: {user_registration_password}
{url_login}

',
				'registration_email_user_after_activation_subject' => '{site_name} Your account was successfully activated',
				'registration_email_user_after_activation_message' => 'You can log in with your credentials now.

{url_login}',
				'form_pages_title'                                 => 'Blank Form',
				'form_pages_footer'                                => 'This content is neither created nor endorsed by WPForms.',
				'form_pages_color_scheme'                          => '#448ccb',
				'form_pages_style'                                 => 'modern',
				'conversational_forms_title'                       => 'Blank Form',
				'conversational_forms_color_scheme'                => '#448ccb',
				'conversational_forms_progress_bar'                => 'percentage',
				'form_locker_verification_type'                    => 'password',
				'form_locker_age'                                  => '18',
				'form_locker_age_criteria'                         => '>=',
				'form_locker_user_entry_email_duration'            => 'day_start',
				'save_resume_link_text'                            => 'Save and Resume Later',
				'save_resume_disclaimer_message'                   => 'Heads up! Saving your progress now will store a copy of your entry on this server and the site owner may have access to it. For security reasons, sensitive information such as credit cards and mailing addresses, along with file uploads will have to be re-entered when you resume.',
				'save_resume_confirmation_message'                 => 'Your form entry has been saved and a unique link has been created which you can access to resume this form.

Enter your email address to receive the link via email. Alternatively, you can copy and save the link below.

Please note, this link should not be shared and will expire in 30 days, afterwards your form entry will be deleted.',
				'save_resume_enable_resume_link'                   => '1',
				'save_resume_enable_email_notification'            => '1',
				'save_resume_email_notification_message'           => 'Thank you for saving {form_name}. Click the link below to resume the form from any device.

{resume_link}

Remember, the link should not be shared and will expire in 30 days.',
				'save_resume_email_settings_message'               => 'A link to resume this form has been sent to the email address provided.

Please remember, the link should not be shared and will expire in 30 days.',
				'form_tags'                                        => [],
			],
			'lead_forms' => [
				'iif_id_ref' => '',
			],
			'payments'   => [
				'stripe'          => [
					'payment_description' => '',
					'receipt_email'       => '',
					'customer_email'      => '',
					'customer_name'       => '',
					'recurring'           => [
						0 => [
							'name'          => 'Plan Name #1',
							'period'        => 'yearly',
							'email'         => '',
							'customer_name' => '',
						],
					],
				],
				'paypal_commerce' => [
					'name'                => '',
					'billing_email'       => '',
					'billing_address'     => '',
					'shipping_address'    => '',
					'payment_description' => '',
					'recurring'           => [
						0 => [
							'pp_product_id'    => '',
							'pp_plan_id'       => '',
							'name'             => 'Plan Name #1',
							'product_type'     => 'digital',
							'recurring_times'  => 'yearly',
							'total_cycles'     => '0',
							'shipping_address' => '',
						],
					],
				],
				'paypal_standard' => [
					'production_email' => '',
					'sandbox_email'    => '',
					'mode'             => 'production',
					'transaction'      => 'product',
					'cancel_url'       => '',
					'shipping'         => '0',
				],
				'square'          => [
					'payment_description' => '',
					'buyer_email'         => '',
					'billing_name'        => '',
					'billing_address'     => '',
				],
				'authorize_net'   => [
					'payment_description'      => '',
					'receipt_email'            => '',
					'customer_name'            => '',
					'customer_billing_address' => '',
					'recurring'                => [
						'name'                     => '',
						'period'                   => 'yearly',
						'email'                    => '',
						'customer_name'            => '',
						'customer_billing_address' => '',
					],
				],
			],
			'meta'       => [
				'template' => 'all_dynamic_choices',
			],
			'providers'  => [
				'google-sheets' => [],
			],
		];
		// phpcs:disable WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned, WordPress.Arrays.MultipleStatementAlignment.LongIndexSpaceBeforeDoubleArrow
	}
}
