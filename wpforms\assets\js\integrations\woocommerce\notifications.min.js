let WPFormsWoocommerceNotifications=window.WPFormsWoocommerceNotifications||(e=>{let o={init(){e(o.ready)},ready(){o.events()},events(){e("#wpforms-woocommerce-close").on("click",o.dismiss)},dismiss(){e(this).closest(".wpforms-woocommerce-notification").remove();var o={action:"wpforms_woocommerce_dismiss",nonce:wpforms_woocommerce_notifications.nonce};e.post(wpforms_woocommerce_notifications.ajax_url,o,function(o){o.success||console.log(o)}).fail(function(o){console.log(o.responseText)})}};return o})((document,window,jQuery));WPFormsWoocommerceNotifications.init();