var WPFormsStripeCardField=window.WPFormsStripeCardField||((e,s)=>{let t={init:function(){t.bindUIActions()},bindUIActions:function(){s(e).on("wpformsSaved",t.ajaxRequiredCheck),s(e).on("wpformsSaved",t.paymentsEnabledCheck),s(e).on("click","#wpforms-add-fields-"+wpforms_builder_stripe.field_slug,t.stripeKeysCheck),s(e).on("change",".wpforms-field-option-stripe-credit-card .wpforms-field-option-row-sublabel_position select",t.sublabelPositionChange),s(e).on("change",".wpforms-field-option-stripe-credit-card .wpforms-field-option-row-link_email select",t.linkEmailChange),s(e).on("wpformsFieldAdd",t.disableAddCardButton),s(e).on("wpformsFieldDelete",t.enableAddCardButton),s(e).on("wpformsFieldDelete",t.maybeResetLinkEmailField)},ajaxRequiredCheck:function(){s(".wpforms-field.wpforms-field-"+wpforms_builder_stripe.field_slug).length&&!s("#wpforms-panel-field-settings-ajax_submit").is(":checked")&&s.alert({title:wpforms_builder.heads_up,content:wpforms_builder.stripe_ajax_required,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},paymentsEnabledCheck:function(){!s(`.wpforms-field.wpforms-field-${wpforms_builder_stripe.field_slug}:visible`).length||s("#wpforms-panel-field-stripe-enable").is(":checked")||s("#wpforms-panel-field-stripe-enable_one_time").is(":checked")||s("#wpforms-panel-field-stripe-enable_recurring").is(":checked")||s.alert({title:wpforms_builder.heads_up,content:wpforms_builder.payments_enabled_required,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},stripeKeysCheck:function(){s(this).hasClass("stripe-keys-required")&&s.alert({title:wpforms_builder.heads_up,content:wpforms_builder.stripe_keys_required,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},disableAddCardButton:function(e,i,r){wpforms_builder_stripe.field_slug===r&&(s("#wpforms-add-fields-"+wpforms_builder_stripe.field_slug).prop("disabled",!0),t.paymentsEnabledCheck())},enableAddCardButton:function(e,i,r){wpforms_builder_stripe.field_slug===r&&s("#wpforms-add-fields-"+wpforms_builder_stripe.field_slug).prop("disabled",!1)},sublabelPositionChange:function(){var e=s(this).parent().data("field-id"),e=s("#wpforms-field-"+e).find(".wpforms-stripe-payment-element");e.toggleClass("above"),e.toggleClass("floating"),e.find("select").val(e.hasClass("above")?"empty":"country")},linkEmailChange:function(){var e=s(this).parent().data("field-id");s("#wpforms-field-"+e).find(".wpforms-stripe-link-email").toggleClass("wpforms-hidden",""!==s(this).val())},maybeResetLinkEmailField:function(e,i,r){"email"===r&&s(".wpforms-field-option-stripe-credit-card .wpforms-field-option-row-link_email select").trigger("change")}};return t})(document,(window,jQuery));WPFormsStripeCardField.init();