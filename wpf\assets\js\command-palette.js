/* global wpf_command_palette, wpforms_settings */

/**
 * Command Palette.
 *
 * @see {@link Docs} for Ninja Keys usage information.
 * @see {@link https://github.com/ssleptsov/ninja-keys#usage}
 *
 * @since 0.19
 */

'use strict';

const WPFCommandPalette = window.WPFCommandPalette || ( function( document, window, $ ) {

	/**
	 * Ninja Keys instance.
	 *
	 * @since 0.19
	 *
	 * @type {object}
	 */
	let ninja;

	/**
	 * Public functions and properties.
	 *
	 * @since 0.19
	 *
	 * @type {object}
	 */
	const app = {

		/**
		 * Start the engine.
		 *
		 * @since 0.19
		 */
		init: function() {

			app.bindEvents();
			app.initNinjaKeys();
		},

		/**
		 * Bind events.
		 *
		 * @since 0.19
		 */
		bindEvents: function() {

			$( '.wpforms-form' ).on( 'wpformsAjaxSubmitSuccess', app.updateLastEntities );
		},

		/**
		 * Init Ninja Keys.
		 *
		 * @since 0.19
		 */
		initNinjaKeys: function() {

			// Create and cache the instance.
			ninja = document.querySelector( 'ninja-keys' );

			ninja.data = [];

			ninja.data.push(
				...app.getFormsCommands(),
				...app.getEntriesCommands(),
				...app.getPaymentsCommands(),
				...app.getCouponsCommands(),
				...app.getTemplatesCommands(),
				...app.getSettingsCommands(),
				...app.getToolsCommands(),
				...app.getAddonsCommands(),
				...app.getGeneratorsCommands(),
				...app.getHooksCommands(),
				...app.getLicenseCommands(),
				...app.getDebugCommands(),
				...app.getCacheCommands(),
			);

			if ( wpf_command_palette.last_payment !== '0'  ) {
				ninja.data.push(
					{
						id: 'Last Payment',
						title: 'Last Payment',
						parent: 'Payments',
						handler: () => {
							window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-payments&view=single&payment_id=${wpf_command_palette.last_payment}`;
						},
					},
				);
			}

			if ( wpf_command_palette.last_entry !== '0'  ) {
				ninja.data.push(
					{
						id: 'Last Entry',
						title: 'Last Entry',
						parent: 'Entries',
						handler: () => {
							window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-entries&view=details&entry_id=${wpf_command_palette.last_entry}`;
						},
					},
				);
			}
		},

		/**
		 * Define parent command and subcommands for Forms.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Forms commands.
		 */
		getFormsCommands: function() {
			const platform = navigator.userAgent || navigator?.userAgentData?.platform; // eslint-disable-line compat/compat
			const isWinOrLinux = platform.toLowerCase().includes( 'linux' ) || platform.toLowerCase().includes( 'windows' );

			const forms = [
				{
					id: 'Forms',
					title: 'Forms...',
					children: [ 'Overview', 'Create', 'All' ],
					hotkey: isWinOrLinux ? 'ctrl+K' : 'cmd+K',
					handler: () => {
						ninja.open( { parent: 'Forms' } );
						return { keepOpen: true };
					},
				},
				{
					id: 'Overview',
					title: 'Overview',
					parent: 'Forms',
					handler: () => {
						window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-overview`;
					},
				},
				{
					id: 'Create',
					title: 'Create New Form',
					parent: 'Forms',
					handler: () => {
						window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-builder`;
					},
				},
			];

			forms.push( ...app.getForms() );

			return forms;
		},

		/**
		 * Define parent command and subcommands for Entries.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Entries commands.
		 */
		getEntriesCommands: function() {

			return [
				{
					id: 'Entries',
					title: 'Entries',
					handler: () => {
						window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-entries`;
					},
				},
			];
		},

		/**
		 * Define parent command and subcommands for Payments.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Payments commands.
		 */
		getPaymentsCommands: function() {

			return [
				{
					id: 'Payments',
					title: 'Payments',
					handler: () => {
						window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-payments`;
					},
				},
			];
		},

		/**
		 * Define parent command and subcommands for Coupons.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Payments commands.
		 */
		getCouponsCommands: function() {

			if ( ! wpf_command_palette.is_active_coupons_addon ) {
				return [];
			}

			const coupons = [
				{
					id: 'Coupons',
					title: 'Coupons',
					handler: () => {
						window.location.href = '/wp-admin/admin.php?page=wpforms-payments&view=coupons';
					},
				},
				{
					id: 'Create Coupon',
					title: 'Create New Coupon',
					handler: () => {
						window.location.href = '/wp-admin/admin.php?page=wpforms-payments&view=coupon';
					},
				},
			];

			coupons.push( ...app.getCoupons() );

			return coupons;
		},

		/**
		 * Get all coupons.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Coupons.
		 */
		getCoupons: function() {

			if ( ! wpf_command_palette.coupons.length ) {
				return [
					{
						id: 'No Coupons',
						title: 'There are no Coupons yet.',
					},
				];
			}

			const coupons = wpf_command_palette.coupons.map( ( coupon ) => {

				return {
					id: coupon.id,
					title: `${coupon.id} – ${coupon.title}`,
					parent: 'Coupons',
					section: 'All Coupons',
					handler: () => {
						window.location.href = `/wp-admin/admin.php?page=wpforms-payments&view=coupon&coupon_id=${coupon.id}`;
					},
				};
			} );

			const lastCouponId = Math.max( ...wpf_command_palette.coupons.map( coupon => coupon.id ) );

			coupons.unshift( {
				id: 'Last Coupon',
				title: 'Last Coupon',
				parent: 'Coupons',
				section: 'All Coupons',
				handler: () => {
					window.location.href = `/wp-admin/admin.php?page=wpforms-payments&view=coupon&coupon_id=${lastCouponId}`;
				},
			} );

			return coupons;
		},

		/**
		 * Define parent command and subcommands for Templates.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Templates commands.
		 */
		getTemplatesCommands: function() {

			return [
				{
					id: 'Templates',
					title: 'Form Templates',
					handler: () => {
						window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-templates`;
					},
				},
			];
		},

		/**
		 * Define parent command and subcommands to open all Settings pages.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Settings commands.
		 */
		getSettingsCommands: function() {

			const pageUrl = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-settings&view=`;

			return [
				{
					id: 'Settings',
					title: 'Settings...',
					children: [ 'General', 'Email', 'Captcha', 'Validation', 'Payments', 'Integrations', 'Geolocation', 'Access', 'Misc' ],
					handler: () => {
						ninja.open( { parent: 'Settings' } );
						return { keepOpen: true };
					},
				},
				{
					id: 'General',
					title: 'General',
					parent: 'Settings',
					handler: () => {
						window.location.href = pageUrl + 'general';
					},
				},
				{
					id: 'Email',
					title: 'Email',
					parent: 'Settings',
					handler: () => {
						window.location.href = pageUrl + 'email';
					},
				},
				{
					id: 'Captcha',
					title: 'Captcha',
					parent: 'Settings',
					handler: () => {
						window.location.href = pageUrl + 'captcha';
					},
				},
				{
					id: 'Validation',
					title: 'Validation',
					parent: 'Settings',
					handler: () => {
						window.location.href = pageUrl + 'validation';
					},
				},
				{
					id: 'Payments',
					title: 'Payments',
					parent: 'Settings',
					handler: () => {
						window.location.href = pageUrl + 'payments';
					},
				},
				{
					id: 'Integrations',
					title: 'Integrations',
					parent: 'Settings',
					handler: () => {
						window.location.href = pageUrl + 'integrations';
					},
				},
				{
					id: 'Geolocation',
					title: 'Geolocation',
					parent: 'Settings',
					handler: () => {
						window.location.href = pageUrl + 'geolocation';
					},
				},
				{
					id: 'Access',
					title: 'Access',
					parent: 'Settings',
					handler: () => {
						window.location.href = pageUrl + 'access';
					},
				},
				{
					id: 'Misc',
					title: 'Misc',
					parent: 'Settings',
					handler: () => {
						window.location.href = pageUrl + 'misc';
					},
				},
			];
		},

		/**
		 * Define parent command and subcommands to open all Tools pages.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Tools commands.
		 */
		getToolsCommands: function() {

			const pageUrl = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-tools&view=`;

			return [
				{
					id: 'Tools',
					title: 'Tools...',
					children: [ 'Import', 'Export', 'System info', 'Scheduled actions', 'Logs' ],
					handler: () => {
						ninja.open( { parent: 'Tools' } );
						return { keepOpen: true };
					},
				},
				{
					id: 'Import',
					title: 'Import',
					parent: 'Tools',
					handler: () => {
						window.location.href = pageUrl + 'import';
					},
				},
				{
					id: 'Export',
					title: 'Export',
					parent: 'Tools',
					handler: () => {
						window.location.href = pageUrl + 'export';
					},
				},
				{
					id: 'System info',
					title: 'System info',
					parent: 'Tools',
					handler: () => {
						window.location.href = pageUrl + 'system';
					},
				},
				{
					id: 'Scheduled actions',
					title: 'Scheduled actions',
					parent: 'Tools',
					handler: () => {
						window.location.href = pageUrl + 'action-scheduler&s=wpforms';
					},
				},
				{
					id: 'Logs',
					title: 'Logs',
					parent: 'Tools',
					handler: () => {
						window.location.href = pageUrl + 'logs';
					},
				},
			];
		},

		/**
		 * Define parent command and subcommands for Addons.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Addons commands.
		 */
		getAddonsCommands: function() {

			return [
				{
					id: 'Addons',
					title: 'Addons',
					handler: () => {
						window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-addons`;
					},
				},
			];
		},

		/**
		 * Define parent command and subcommands for Generators (DevTools).
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Generators commands.
		 */
		getGeneratorsCommands: function() {

			return [
				{
					id: 'Generate',
					title: 'Generate...',
					children: [
						{
							id: 'Forms',
							title: 'Forms',
							parent: 'Generate',
							handler: () => {
								window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms_dev_tools&view=forms`;
							},
						},
						{
							id: 'Entries',
							title: 'Entries',
							parent: 'Generate',
							handler: () => {
								window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms_dev_tools&view=entries`;
							},
						},
						{
							id: 'Modal',
							title: 'Modal',
							parent: 'Generate',
							handler: () => {
								window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms_dev_tools&view=modals`;
							},
						},
					],
					handler: () => {
						ninja.open( { parent: 'Generate' } );
						return { keepOpen: true };
					},
				},
			];
		},

		/**
		 * Define parent command and subcommands for Hooks (DevTools).
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Hooks commands.
		 */
		getHooksCommands: function() {

			// @todo Allow selecting core or addons, use URL: /wp-admin/admin.php?page=wpforms_dev_tools&view=hooks&plugin=wpforms-aweber

			return [
				{
					id: 'Hooks',
					title: 'Hooks Documentation',
					handler: () => {
						window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms_dev_tools&view=hooks`;
					},
				},
			];
		},

		/**
		 * Define parent command and subcommands for working with Cache.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Cache commands.
		 */
		getCacheCommands: function() {

			return [
				{
					id: 'Cache',
					title: 'Cache...',
					section: 'Utilities',
					children: [ 'Cache: Object and Transients', 'Cache: JSON Files', 'Cache: All' ],
					handler: () => {
						ninja.open( { parent: 'Cache' } );
						return { keepOpen: true };
					},
				},
				{
					id: 'Cache: Object and Transients',
					title: 'Wipe Object Cache and Transients',
					parent: 'Cache',
					handler: () => {
						window.location.href += '&wpf_utils_action=wipe_object_cache_and_transients';
					},
				},
				{
					id: 'Cache: JSON Files',
					title: 'Wipe *.json Files Cache',
					parent: 'Cache',
					handler: () => {
						window.location.href += '&wpf_utils_action=wipe_file_cache';
					},
				},
				{
					id: 'Cache: All',
					title: 'Wipe All Cache',
					parent: 'Cache',
					handler: () => {
						window.location.href += '&wpf_utils_action=wipe_all_cache';
					},
				},
			];
		},

		/**
		 * Define parent command and subcommands for working with WPForms Lite/Pro version and License.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} License commands.
		 */
		getLicenseCommands: function() {

			const switchToUrl = $( '#wp-admin-bar-wpf-pro-lite-switch a' );

			if ( switchToUrl.length === 0 ) {
				return [];
			}

			const switchTo = wpf_command_palette.is_pro ? 'Lite' : 'Pro';

			return [
				{
					id: 'License',
					title: 'Switch to ' + switchTo,
					section: 'Utilities',
					handler: () => {
						window.location.href = switchToUrl.attr( 'href' );
					},
				},
			];
		},

		/**
		 * Define parent command and subcommands for working with WPForms debug output.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Debug commands.
		 */
		getDebugCommands: function() {

			const toggleDebugUrl = $( '#wp-admin-bar-wpf-utils-hide-debug-data a' );

			if ( toggleDebugUrl.length === 0 ) {
				return [];
			}

			const toggleDebug = wpf_command_palette.hide_debug ? 'Show' : 'Hide';

			return [
				{
					id: 'Debug Data',
					title: toggleDebug + ' Debug Data',
					section: 'Utilities',
					handler: () => {
						window.location.href = toggleDebugUrl.attr( 'href' );
					},
				},
			];
		},

		/**
		 * Update last entities.
		 *
		 * @since 0.19
		 */
		updateLastEntities: function() {

			$.get(
				wpforms_settings.ajaxurl,
				{
					action: 'wpf_get_last_entities',
				},
				function( response ) {

					if ( ! response.success ) {
						return;
					}

					if ( ! response.data ) {
						return;
					}

					if ( response.data.last_entry ) {
						wpf_command_palette.last_entry = response.data.last_entry;
					}

					if ( response.data.last_payment ) {
						wpf_command_palette.last_payment = response.data.last_payment;
					}
				} );
		},

		/**
		 * Get all forms.
		 *
		 * @since 0.19
		 *
		 * @returns {Array} Forms.
		 */
		getForms: function() {

			if ( ! wpf_command_palette.forms.length ) {
				return [
					{
						id: 'No Forms',
						title: 'There are no Forms yet.',
					},
				];
			}

			const forms = wpf_command_palette.forms.map( ( form ) => {

				const formCommands =  {
					id: form.id,
					title: `${form.id} – ${form.title}`,
					parent: 'Forms',
					section: 'All Forms',
					children: [
						{
							id: 'Edit',
							title: 'Edit',
							parent: form.id,
							handler: () => {
								window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-builder&view=fields&form_id=${form.id}`;
							},
						},
						{
							id: 'Preview',
							title: 'Preview',
							parent: form.id,
							handler: () => {
								window.location.href = `${wpf_command_palette.site_url}/?wpforms_form_preview=${form.id}`;
							},
						},
					],
					handler: () => {
						ninja.open( { parent: form.id } );
						return { keepOpen: true };
					},
				};

				if ( wpf_command_palette.is_pro === '1' ) {
					formCommands.children.push(
						{
							id: 'Entries',
							title: 'Entries',
							parent: form.id,
							handler: () => {
								window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-entries&form_id=${form.id}`;
							},
						},
					);
				}

				return formCommands;
			} );

			const lastFormId = Math.max( ...wpf_command_palette.forms.map( form => form.id ) );

			forms.unshift( {
				id: 'Last Form',
				title: 'Last Form',
				parent: 'Forms',
				section: 'All Forms',
				children: [
					{
						id: 'Edit',
						title: 'Edit',
						parent: lastFormId,
						handler: () => {
							window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-builder&view=fields&form_id=${lastFormId}`;
						},
					},
					{
						id: 'Preview',
						title: 'Preview',
						parent: lastFormId,
						handler: () => {
							window.location.href = `${wpf_command_palette.site_url}/?wpforms_form_preview=${lastFormId}`;
						},
					},
					{
						id: 'Entries',
						title: 'Entries',
						parent: lastFormId,
						handler: () => {
							window.location.href = `${wpf_command_palette.admin_url}/admin.php?page=wpforms-entries&form_id=${lastFormId}`;
						},
					},
				],
				handler: () => {
					ninja.open( { parent: lastFormId } );
					return { keepOpen: true };
				},
			} );

			return forms;
		},
	};

	return app;

}( document, window, jQuery ) );

// Initialize.
WPFCommandPalette.init();
