{"name": "wpforms/devtools", "description": "WPForms internal development tools.", "type": "wordpress-plugin", "license": "GPL-2.0-or-later", "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"composer/installers": true}}, "require": {"php": ">=7.2", "ext-dom": "*", "ext-libxml": "*", "erusev/parsedown": "^1.7.4", "fakerphp/faker": "^1.9.2|^1.11.0", "guzzlehttp/guzzle": "^7.0", "symfony/polyfill-php73": "^1.8", "wp-hooks/generator": "^0.9.0"}, "require-dev": {"roave/security-advisories": "dev-latest"}, "autoload": {"psr-4": {"WPForms\\DevTools\\": "src/", "WPF\\E2EDashboard\\": "src/E2EDashboard/"}, "exclude-from-classmap": ["src/ErrorHandler/wpf-error-handler.php", "src/PCP/wpf-pcp.php"]}, "scripts": {"hooks": "bash ../.bash/hooks"}, "extra": {"wp-hooks": {"ignore-files": ["assets", "build", "languages", "libs", "tests", "vendor"]}}}