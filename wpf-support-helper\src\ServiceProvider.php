<?php
/**
 * Service Provider for Dependency Injection.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper;

use WP<PERSON>orms\SupportHelper\Admin\AdminNotices;
use WPForms\SupportHelper\Cache\CacheInterface;
use WP<PERSON><PERSON>\SupportHelper\Cache\TransientCache;
use WPF<PERSON>\SupportHelper\Cache\ObjectCache;
use WPF<PERSON>\SupportHelper\Cache\MultiLevelCache;
use WPF<PERSON>\SupportHelper\Container\Container;
use WPForms\SupportHelper\Container\ContainerInterface;
use WPForms\SupportHelper\Logger\LoggerInterface;
use WPForms\SupportHelper\Logger\WordPressLogger;
use WPF<PERSON>\SupportHelper\Detectors\ModSecurity\DetectionEngine;
use WPForms\SupportHelper\Admin\SettingsPage;
use WPForms\SupportHelper\Admin\Tools;
use WPForms\SupportHelper\Admin\ModSecurityDataHandler;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Service provider for configuring dependencies.
 *
 * @since {VERSION}
 */
class ServiceProvider {

	/**
	 * Dependency injection container.
	 *
	 * @since {VERSION}
	 *
	 * @var ContainerInterface
	 */
	private $container;

	/**
	 * Constructor.
	 *
	 * @since {VERSION}
	 */
	public function __construct() {

		$this->container = new Container();

		$this->register_services();
	}

	/**
	 * Get a service from the container.
	 *
	 * @since {VERSION}
	 *
	 * @param string $service_id Service identifier.
	 *
	 * @return mixed Service instance.
	 */
	public function get( string $service_id ) {

		return $this->container->get( $service_id );
	}

	/**
	 * Register all services in the container.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	private function register_services(): void {

		// Register cache service.
		$this->container->singleton(
			CacheInterface::class,
			static function () {
				$object_cache    = new ObjectCache();
				$transient_cache = new TransientCache();

				return new MultiLevelCache( $object_cache, $transient_cache );
			}
		);

		// Register logger service.
		$this->container->singleton(
			LoggerInterface::class,
			static function () {
				$log_level = defined( 'WP_DEBUG' ) && WP_DEBUG ? LoggerInterface::DEBUG : LoggerInterface::ERROR;

				return new WordPressLogger( $log_level, '[WPForms Support Helper]' );
			}
		);

		// Register DetectionEngine service.
		$this->container->singleton(
			DetectionEngine::class,
			static function ( ContainerInterface $container ) {

				return new DetectionEngine(
					$container->get( CacheInterface::class ),
					$container->get( LoggerInterface::class )
				);
			}
		);

		// Register settings page service.
		$this->container->singleton(
			SettingsPage::class,
			static function ( ContainerInterface $container ) {

				return new SettingsPage(
					$container->get( ModSecurityDataHandler::class )
				);
			}
		);

		// Register site health service.
		$this->container->singleton(
			SiteHealth::class,
			static function ( ContainerInterface $container ) {

				return new SiteHealth(
					$container->get( DetectionEngine::class ),
					$container->get( SettingsPage::class )
				);
			}
		);

		// Register admin notices service.
		$this->container->singleton(
			AdminNotices::class,
			static function ( ContainerInterface $container ) {

				return new AdminNotices( $container->get( SettingsPage::class ) );
			}
		);

		// Register ModSecurity data handler service.
		$this->container->singleton(
			ModSecurityDataHandler::class,
			static function ( ContainerInterface $container ) {

				return new ModSecurityDataHandler(
					$container->get( DetectionEngine::class ),
					$container->get( LoggerInterface::class )
				);
			}
		);

		// Register tools service.
		$this->container->singleton(
			Tools::class,
			static function ( ContainerInterface $container ) {

				return new Tools(
					$container->get( SettingsPage::class )
				);
			}
		);

		// Register convenience aliases.
		$this->register_aliases();
	}

	/**
	 * Register service aliases for easier access.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	private function register_aliases(): void {

		$aliases = [
			'cache'         => CacheInterface::class,
			'logger'        => LoggerInterface::class,
			'modsecurity'   => DetectionEngine::class,
			'site_health'   => SiteHealth::class,
			'admin_notices' => AdminNotices::class,
			'settings_page' => SettingsPage::class,
			'tools'         => Tools::class,
			'data_handler'  => ModSecurityDataHandler::class,
		];

		foreach ( $aliases as $alias => $service_class ) {
			$this->container->register(
				$alias,
				static function ( ContainerInterface $container ) use ( $service_class ) {

					return $container->get( $service_class );
				}
			);
		}
	}

	/**
	 * Registers all services in the container.
	 *
	 * Loops through all services fetched from the container and initializes them.
	 *
	 * @since {VERSION}
	 */
	public function register_all(): void {

		$services = $this->container->get_services();

		foreach ( $services as $service ) {
			$this->get( $service );
		}
	}

	/**
	 * Configure services for testing environment.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	public function configure_for_testing(): void {

		// Override logger for testing.
		$this->container->register(
			LoggerInterface::class,
			static function () {

				return WordPressLogger::debug_logger( '[WPForms Support Helper - Test]' );
			}
		);

		// Override cache for testing (shorter expiration).
		$this->container->register(
			CacheInterface::class,
			static function () {

				return new TransientCache( 60 );
			}
		);
	}

	/**
	 * Configure services for production environment.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	public function configure_for_production(): void {

		// Override logger for production (error level only).
		$this->container->register(
			LoggerInterface::class,
			static function () {

				return WordPressLogger::error_logger();
			}
		);

		// Override cache for production (longer expiration).
		$this->container->register(
			CacheInterface::class,
			static function () {

				return new TransientCache( 7200 ); // 2 hours.
			}
		);
	}
}
