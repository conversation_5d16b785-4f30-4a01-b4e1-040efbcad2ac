let WPFormsSettingsStripe=window.WPFormsSettingsStripe||(o=>{let t={},e={alertTitle:wpforms_admin.heads_up,alertContent:wpforms_admin_settings_stripe.mode_update,ok:wpforms_admin.ok,hideClassName:"wpforms-hide"},n={init(){o(n.ready)},ready(){n.setup(),n.bindEvents()},setup(){t.$wrapper=o(".wpforms-admin-content-payments"),t.$liveConnectionBlock=o(".wpforms-stripe-connection-status-live"),t.$testConnectionBlock=o(".wpforms-stripe-connection-status-test"),t.$testModeCheckbox=o("#wpforms-setting-stripe-test-mode"),t.copyButton=o("#wpforms-setting-row-stripe-webhooks-endpoint-set .wpforms-copy-to-clipboard"),t.webhookEndpointUrl=o("input#wpforms-stripe-webhook-endpoint-url"),t.webhookMethod=o('input[name="stripe-webhooks-communication"]')},bindEvents(){t.$wrapper.on("change","#wpforms-setting-stripe-test-mode",n.triggerModeSwitchAlert),t.copyButton.on("click",function(e){wpf.copyValueToClipboard(e,o(this),t.webhookEndpointUrl)}),t.webhookMethod.on("change",n.onMethodChange)},triggerModeSwitchAlert(){(t.$testModeCheckbox.is(":checked")?(t.$liveConnectionBlock.addClass(e.hideClassName),t.$testConnectionBlock):(t.$testConnectionBlock.addClass(e.hideClassName),t.$liveConnectionBlock)).removeClass(e.hideClassName),o("#wpforms-setting-row-stripe-connection-status .wpforms-connected").is(":visible")||o.alert({title:e.alertTitle,content:e.alertContent,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:e.ok,btnClass:"btn-confirm",keys:["enter"]}}})},copyWebhooksEndpoint(e){console.warn('WARNING! Function "WPFormsSettingsStripe.copyWebhooksEndpoint()" has been deprecated! Use wpf.copyWebhooksEndpoint() instead.'),wpf.copyValueToClipboard(e,o(this),t.webhookEndpointUrl)},onMethodChange(){var e=t.webhookMethod.filter(":checked").val(),e=wpforms_admin_settings_stripe.webhook_urls[e];t.webhookEndpointUrl.val(e)}};return n})((document,window,jQuery));WPFormsSettingsStripe.init();