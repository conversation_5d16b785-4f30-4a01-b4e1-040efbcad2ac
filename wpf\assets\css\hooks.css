.wpforms-hooks-tabs {
    margin: 40px 0 20px;
    display: flex;
    border-bottom: 1px solid #e4e4e4;
}
.wpforms-hooks-tabs li {
    margin: 0 20px -1px 0;
    padding: 6px 0;
    border-bottom: 2px solid transparent;
}
.wpforms-hooks-tabs li a {
    text-decoration: none;
    color: #3c434a;
    font-weight: 600;
    font-size: 14px;
}
.wpforms-hooks-tabs li:hover {
    border-color: #999;
}
.wpforms-hooks-tabs li.wpforms-hooks-tab-active {
    border-color: #e27730;
}

#wpforms-admin-devtools-hooks-list .list {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(2, 1fr);
}

.wpforms-hook {
    background: #fff;
    border: 1px solid #CCCCCC;
    border-radius: 3px;
    padding: 20px;
}
.wpforms-hook:hover {
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.15);
}

.wpforms-hook-not-documented {
    color: #888;
}

.wpforms-hook-file {
    margin: 6px 0;
    font-family: Menlo,Monaco,monospace;
    font-size: 12px;
    color: #aaa;
}
.wpforms-hook-name {
    margin: 6px 0 26px;
    overflow-wrap: break-word;
    word-break: break-all;
    line-height: 1.5;
}
.wpforms-hook-description {
    font-size: 14px;
}
.wpforms-hook-long-description {
    font-size: 14px;
}

.wpforms-hook-tag-title {
    margin-bottom: 9px;
}
.wpforms-hook-tag-data {
    width: 100%;
    border-spacing: 0;
}
.wpforms-hook-tag-data tr:first-child td {
    border-top-width: 2px;
}
.wpforms-hook-tag-data td {
    border-top: 1px solid #ededed;
    padding: 4px 0;
}
.wpforms-hook-tag-code {
    font-family: Menlo,Monaco,monospace;
    font-size: 12px;
}

