var WPFormsEntryPreview=window.WPFormsEntryPreview||((s,e,f)=>{var a={init:function(){f(s).on("wpformsBeforePageChange",a.pageChange).on("wpformsEntryPreviewUpdated",function(e,r,n){n.find(".wpforms-iframe").each(a.updateIframe)}).on("wpformsAjaxSubmitSuccessConfirmation",function(){f(".wpforms-container > .wpforms-entry-preview .wpforms-iframe").each(a.updateIframe)}),f(e).on("load",function(){f(".wpforms-iframe").each(a.updateIframe)})},pageChange:function(e,r,n){f(e.target).hasClass("wpforms-page-next")&&(wpforms.saveTinyMCE(),a.update(r,n))},update:function(e,r){var n,a,i,t,o=r.find(".wpforms-page-"+e+" .wpforms-field-entry-preview");o.length&&(e=o.data("field-id"),n=o.find(".wpforms-entry-preview-updating-message"),a=o.find(".wpforms-entry-preview-notice"),i=o.find(".wpforms-entry-preview-wrapper"),(t=new FormData(r.get(0))).append("action","wpforms_get_entry_preview"),t.append("current_entry_preview_id",e),f.ajax({data:t,type:"post",url:wpforms_settings.ajaxurl,dataType:"json",processData:!1,contentType:!1,beforeSend:function(){o.addClass("wpforms-field-entry-preview-updating"),a.hide(),i.hide(),n.show()},success:function(e){e.data?(i.html(e.data),o.show()):o.hide()},complete:function(){o.removeClass("wpforms-field-entry-preview-updating"),n.hide(),a.show(),i.show(),f(s).trigger("wpformsEntryPreviewUpdated",[r,o])}}))},updateIframe:function(){var e=f(".wpforms-entry-preview-wrapper").is(":visible")?f(".wpforms-entry-preview-value").css("color"):"inherit";WPFormsIframe.update.call(this,{color:e})},iframeStyles:function(e){console.warn('WARNING! Function "WPFormsEntryPreview.iframeStyles( iframe )" has been deprecated, please use the new "WPFormsIframe.iframeStyles( iframe, options )" function instead!'),WPFormsIframe.iframeStyles(e)},iframeBody:function(e,r){console.warn('WARNING! Function "WPFormsEntryPreview.iframeBody( iframe, html )" has been deprecated, please use the new "WPFormsIframe.iframeBody( iframe, html )" function instead!'),WPFormsIframe.iframeBody(e,r)},iframeFullHeight:function(e){console.warn('WARNING! Function "WPFormsEntryPreview.iframeFullHeight( iframe )" has been deprecated, please use the new "WPFormsIframe.iframeFullHeight( iframe )" function instead!'),WPFormsIframe.iframeFullHeight(e)}};return a})(document,window,jQuery);WPFormsEntryPreview.init();