<?php
/**
 * ModSecurity Apache Modules Detector.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper\Detectors\ModSecurity;

use Exception;
use WPForms\SupportHelper\Detectors\Base\AbstractDetector;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Apache Module Detection for ModSecurity.
 *
 * @since {VERSION}
 */
class ApacheModulesDetector extends AbstractDetector {

	/**
	 * Run the detection method.
	 *
	 * @since {VERSION}
	 *
	 * @return array Detection result.
	 */
	public function detect(): array {

		$detected_modules   = [];
		$disabled_functions = [];

		// Try different detection methods.
		$detected_modules = array_merge( $detected_modules, $this->detect_via_apache_get_modules( $disabled_functions ) );

		if ( empty( $detected_modules ) ) {
			$detected_modules = array_merge( $detected_modules, $this->detect_via_phpinfo( $disabled_functions ) );
		}

		$detected_modules = array_merge( $detected_modules, $this->detect_via_loaded_extensions( $disabled_functions ) );

		return $this->format_detection_result( $detected_modules, $disabled_functions );
	}

	/**
	 * Detect ModSecurity via apache_get_modules().
	 *
	 * @since {VERSION}
	 *
	 * @param array $disabled_functions Reference to disabled functions array.
	 *
	 * @return array Detected modules.
	 */
	private function detect_via_apache_get_modules( array &$disabled_functions ): array {

		$detected_modules = [];

		if ( ! $this->is_function_available( 'apache_get_modules' ) ) {
			$disabled_functions[] = 'apache_get_modules()';

			return $detected_modules;
		}

		try {
			$modules = apache_get_modules();

			foreach ( $modules as $module ) {
				if ( $this->is_modsecurity_module( $module ) ) {
					$detected_modules[] = $module;
				}
			}
		} catch ( Exception $e ) {
			$this->log_error( 'apache_get_modules() failed: ' . $e->getMessage() );
			$disabled_functions[] = 'apache_get_modules() - ' . $e->getMessage();
		}

		return $detected_modules;
	}

	/**
	 * Detect ModSecurity via phpinfo().
	 *
	 * @since {VERSION}
	 *
	 * @param array $disabled_functions Reference to disabled functions array.
	 *
	 * @return array Detected modules.
	 */
	private function detect_via_phpinfo( array &$disabled_functions ): array {

		$detected_modules = [];

		if ( ! $this->is_function_available( 'phpinfo' ) ) {
			$disabled_functions[] = 'phpinfo()';

			return $detected_modules;
		}

		try {
			ob_start();
			phpinfo( INFO_MODULES ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.prevent_path_disclosure_phpinfo
			$phpinfo = ob_get_clean();

			if ( stripos( $phpinfo, 'mod_security' ) !== false || stripos( $phpinfo, 'modsecurity' ) !== false ) {
				$detected_modules[] = 'Found in phpinfo() output';
			}
		} catch ( Exception $e ) {
			$this->log_error( 'phpinfo() failed: ' . $e->getMessage() );
			$disabled_functions[] = 'phpinfo() - ' . $e->getMessage();
		}

		return $detected_modules;
	}

	/**
	 * Detect ModSecurity via loaded extensions.
	 *
	 * @since {VERSION}
	 *
	 * @param array $disabled_functions Reference to disabled functions array.
	 *
	 * @return array Detected modules.
	 */
	private function detect_via_loaded_extensions( array &$disabled_functions ): array {

		$detected_modules = [];

		if ( ! function_exists( 'get_loaded_extensions' ) ) {
			$disabled_functions[] = 'get_loaded_extensions()';

			return $detected_modules;
		}

		try {
			$extensions = get_loaded_extensions();

			foreach ( $extensions as $extension ) {
				if ( $this->is_modsecurity_extension( $extension ) ) {
					$detected_modules[] = $extension;
				}
			}
		} catch ( Exception $e ) {
			$this->log_error( 'get_loaded_extensions() failed: ' . $e->getMessage() );
			$disabled_functions[] = 'get_loaded_extensions() - ' . $e->getMessage();
		}

		return $detected_modules;
	}

	/**
	 * Check if module name indicates ModSecurity.
	 *
	 * @since {VERSION}
	 *
	 * @param string $module Module name.
	 *
	 * @return bool True if module is ModSecurity-related.
	 */
	private function is_modsecurity_module( string $module ): bool {

		return stripos( $module, 'mod_security' ) !== false || stripos( $module, 'security2' ) !== false;
	}

	/**
	 * Check if extension name indicates ModSecurity.
	 *
	 * @since {VERSION}
	 *
	 * @param string $extension Extension name.
	 *
	 * @return bool True if extension is ModSecurity-related.
	 */
	private function is_modsecurity_extension( string $extension ): bool {

		return stripos( $extension, 'modsecurity' ) !== false || stripos( $extension, 'mod_security' ) !== false;
	}

	/**
	 * Format the final detection result.
	 *
	 * @since {VERSION}
	 *
	 * @param array $detected_modules   Detected modules.
	 * @param array $disabled_functions Disabled functions.
	 *
	 * @return array Formatted detection result.
	 */
	private function format_detection_result( array $detected_modules, array $disabled_functions ): array {

		if ( ! empty( $detected_modules ) ) {
			return $this->format_result(
				true,
				__( 'ModSecurity detected in Apache modules', 'wpf-support-helper' ),
				[
					'modules'            => $detected_modules,
					'disabled_functions' => $disabled_functions,
				]
			);
		}

		$reason = __( 'No ModSecurity modules found', 'wpf-support-helper' );

		if ( ! empty( $disabled_functions ) ) {
			$reason .= ' (' . sprintf(
				/* translators: %s - List of disabled functions. */
				__( 'Limited detection due to disabled functions: %s', 'wpf-support-helper' ),
				implode( ', ', $disabled_functions )
			) . ')';
		}

		return $this->format_result(
			false,
			$reason,
			[
				'disabled_functions' => $disabled_functions,
			]
		);
	}

	/**
	 * Get the detector name.
	 *
	 * @since {VERSION}
	 *
	 * @return string Detector name.
	 */
	public function get_name(): string {

		return __( 'Apache Module Detection', 'wpf-support-helper' );
	}

	/**
	 * Get the detector description.
	 *
	 * @since {VERSION}
	 *
	 * @return string Detector description.
	 */
	public function get_description(): string {

		return __( 'Check loaded Apache modules for ModSecurity', 'wpf-support-helper' );
	}
}
