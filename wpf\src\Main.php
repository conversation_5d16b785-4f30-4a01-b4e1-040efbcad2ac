<?php

namespace WPForms\DevTools;

use WP<PERSON><PERSON>\DevTools\ErrorHandler\ErrorHandler;
use WPForms\DevTools\PCP\PCP;
use WPForms\Helpers\Transient;
use WPForms\Admin\Builder\TemplatesCache;
use WPF<PERSON>\Admin\Builder\TemplateSingleCache;

/**
 * Main plugin class.
 *
 * @since 0.5
 */
class Main {

	/**
	 * Registry of classes.
	 *
	 * @since 0.22
	 *
	 * @var array
	 */
	private $registry = [];

	/**
	 * Get a single instance of the class.
	 *
	 * @since 0.22
	 *
	 * @return Main
	 */
	public static function get_instance() {

		static $instance = null;

		if ( ! $instance instanceof self ) {
			$instance = new self();

			$instance->init();
		}

		return $instance;
	}

	/**
	 * Get a class instance from a registry.
	 *
	 * @since 0.22
	 *
	 * @param string $name Class name or an alias.
	 *
	 * @return mixed|null
	 */
	public function get( string $name ) {

		if ( ! empty( $this->registry[ $name ] ) ) {
			return $this->registry[ $name ];
		}

		return null;
	}

	/**
	 * Init class.
	 *
	 * @since 0.22
	 *
	 * @return void
	 */
	public function init() {

		$this->registry['error_handler'] = new ErrorHandler();
		$this->registry['pcp']           = new PCP();

		$this->hooks();
	}

	/**
	 * Add hooks.
	 *
	 * @since 0.22
	 *
	 * @return void
	 */
	private function hooks() {

		register_deactivation_hook( WPFORMS_DEV_PLUGIN_FILE, [ $this, 'deactivation_hook' ] );

		add_action( 'plugins_loaded', [ $this, 'load_plugin_textdomain' ] );
	}

	/**
	 * Get WPForms object.
	 *
	 * @since 0.29
	 *
	 * @param string $obj_name Object name.
	 *
	 * @return mixed
	 */
	public static function wpforms_obj( string $obj_name ) {

		$wpforms = wpforms();

		return method_exists( $wpforms, 'obj' ) ? $wpforms->obj( $obj_name ) : $wpforms->get( $obj_name );
	}

	/**
	 * Deactivation hook.
	 *
	 * @since 0.22
	 *
	 * @return void
	 */
	public function deactivation_hook() {

		delete_site_option( 'wpf-force-lite-version' );
	}

	/**
	 * Load plugin text domain.
	 *
	 * @since 0.22
	 *
	 * @return void
	 */
	public function load_plugin_textdomain() {

		global $l10n;

		$domain = 'wpf';

		if ( isset( $l10n[ $domain ] ) ) {
			return;
		}

		load_plugin_textdomain(
			$domain,
			false,
			dirname( plugin_basename( WPFORMS_DEV_PLUGIN_FILE ) ) . '/languages/'
		);
	}

	/**
	 * Add link to edit form after it is displayed.
	 *
	 * @since 0.1
	 *
	 * @param array $form_data Form data and settings.
	 */
	public static function after_form_edit( $form_data ) {

		echo wpforms_render( // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
			'after_form_edit',
			[
				'url' => admin_url( 'admin.php?page=wpforms-builder&view=fields&form_id=' . $form_data['id'] ),
			],
			true
		);
	}

	/**
	 * Admin bar menu customizations for WPFs.
	 *
	 * @since 0.1
	 *
	 * @param object $wp_admin_bar WP Admin bar.
	 */
	public static function admin_bar_menu_links( $wp_admin_bar ) {

		if ( ! function_exists( 'wpforms' ) ) {
			return;
		}

		$switch_to = get_site_option( 'wpf-force-lite-version' ) ? 'pro' : 'lite';

		$nodes = [
			[
				'parent' => 'wpforms-menu',
				'title'  => 'Switch to ' . ucfirst( $switch_to ),
				'id'     => 'wpf-pro-lite-switch',
				'href'   => wp_nonce_url( add_query_arg( 'wpf_switch_to', $switch_to ), 'wpf_pro_lite_version_switch', 'wpf_nonce' ),
			],
		];

		if ( $switch_to === 'lite' ) {
			$nodes = self::license_type_links( $nodes );
			$nodes = self::license_status_links( $nodes );
		}

		array_map(
			static function ( $node ) use ( $wp_admin_bar ) {

				$wp_admin_bar->add_node( $node );
			},
			$nodes
		);
	}

	/**
	 * Add license type links to the admin bar menu.
	 *
	 * @since {VERSION}
	 *
	 * @param array $nodes Menu nodes.
	 *
	 * @return array
	 */
	private static function license_type_links( $nodes ) {

		$force_type      = get_site_option( 'wpf-force-license-type' );
		$current_license = wpforms_get_license_type() ?? 'ultimate';
		$current_title   = $force_type === 'original' ? $current_license . ' (original) ' : $current_license;
		$license_types   = [ 'original', 'basic', 'plus', 'pro', 'elite', 'agency', 'ultimate' ];
		$nodes[]         = [
			'parent' => 'wpforms-menu',
			'title'  => 'License: ' . ucfirst( $current_title ),
			'id'     => 'wpf-pro-license-switch',
			'href'   => '#',
		];

		foreach ( $license_types as $license_type ) {
			if ( $license_type === $force_type ) {
				continue;
			}

			$nodes[] = [
				'parent' => 'wpf-pro-license-switch',
				'title'  => ucfirst( $license_type ),
				'id'     => 'wpf-pro-license-switch-' . $license_type,
				'href'   => wp_nonce_url( add_query_arg( 'wpf_pro_license_type_switch', $license_type ), 'wpf_pro_license_type_switch', 'wpf_nonce' ),
			];
		}

		return $nodes;
	}

	/**
	 * Add license status links to the admin bar menu.
	 *
	 * @since {VERSION}
	 *
	 * @param array $nodes Menu nodes.
	 *
	 * @return array
	 */
	private static function license_status_links( $nodes ) {

		$license          = self::wpforms_obj( 'license' );
		$force_status     = get_site_option( 'wpf-force-license-status' );
		$license_statuses = [ 'original', 'deactivated', 'expired', 'disabled', 'invalid', 'active' ];
		$current_status   = 'active';

		foreach ( $license_statuses as $license_status ) {

			if ( $license_status === 'original' ) {
				continue;
			}

			if ( $license_status === 'deactivated' ) {
				if ( $force_status === 'deactivated' ) {
					$current_status = $force_status;

					break;
				}

				continue;
			}

			$is_license_status = "is_$license_status";

			if ( $license && $license->$is_license_status() ) {
				$current_status = $license_status;

				break;
			}
		}

		$current_title = $force_status === 'original' ? $current_status . ' (original) ' : $current_status;
		$nodes[]       = [
			'parent' => 'wpforms-menu',
			'title'  => 'Status: ' . ucfirst( $current_title ),
			'id'     => 'wpf-pro-license-status',
			'href'   => '#',
		];

		foreach ( $license_statuses as $license_status ) {
			if ( $license_status === $force_status ) {
				continue;
			}

			$nodes[] = [
				'parent' => 'wpf-pro-license-status',
				'title'  => ucfirst( $license_status ),
				'id'     => 'wpf-pro-license-status-' . $license_status,
				'href'   => wp_nonce_url( add_query_arg( 'wpf_pro_license_status_switch', $license_status ), 'wpf_pro_license_status_switch', 'wpf_nonce' ),
			];
		}

		return $nodes;
	}

	/**
	 * Conditionally update "Force Lite" option.
	 *
	 * @since 0.2
	 */
	public static function pro_lite_switch_update_option() {

		$nonce  = ! empty( $_GET['wpf_nonce'] ) ? sanitize_key( $_GET['wpf_nonce'] ) : '';
		$switch = ! empty( $_REQUEST['wpf_switch_to'] ) ? sanitize_key( $_REQUEST['wpf_switch_to'] ) : '';

		if ( empty( $nonce ) || ! wp_verify_nonce( $nonce, 'wpf_pro_lite_version_switch' ) ) {
			return;
		}

		if ( $switch === 'lite' ) {
			update_site_option( 'wpf-force-lite-version', true );
		}

		if ( $switch === 'pro' ) {
			delete_site_option( 'wpf-force-lite-version' );
		}

		self::lite_connect_restart();
		self::wipe_templates_cache();
		self::clear_widgets_cache();

		// Remove notifications data from a database.
		// It will cause to force new data.
		delete_option( 'wpforms_notifications' );

		wp_safe_redirect( remove_query_arg( [ 'wpf_switch_to', 'wpf_nonce' ] ) );
		exit;
	}

	/**
	 * Restart the import flags for Lite Connect.
	 *
	 * @since 0.2
	 */
	private static function lite_connect_restart() {

		if ( class_exists( '\WPForms\Pro\Integrations\LiteConnect\Integration' ) ) {
			// phpcs:ignore WPForms.PHP.BackSlash.UseShortSyntax
			\WPForms\Pro\Integrations\LiteConnect\Integration::maybe_restart_import_flag();
		}
	}

	/**
	 * Wipe templates cache.
	 *
	 * @since 0.6
	 */
	private static function wipe_templates_cache() {

		// Wipe templates content cache.
		if ( class_exists( TemplatesCache::class ) ) {
			( new TemplatesCache() )->wipe_content_cache();
		}

		// Wipe cache of an empty templates.
		if ( class_exists( TemplateSingleCache::class ) ) {
			// phpcs:ignore WPForms.PHP.BackSlash.UseShortSyntax
			( new TemplateSingleCache() )->wipe_empty_templates_cache();
		}
	}

	/**
	 * Clear widgets cache.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	private static function clear_widgets_cache() {

		if ( class_exists( ProDashboardWidget::class ) ) {
			// phpcs:ignore WPForms.PHP.BackSlash.UseShortSyntax
			ProDashboardWidget::clear_widget_cache();
		}

		if ( class_exists( LiteDashboardWidget::class ) ) {
			// phpcs:ignore WPForms.PHP.BackSlash.UseShortSyntax
			LiteDashboardWidget::clear_widget_cache();
		}
	}

	/**
	 * Force Lite version via a hook.
	 *
	 * @since 0.2
	 *
	 * @param bool $is_pro Is pro.
	 *
	 * @return bool
	 */
	public static function pro_lite_switch_force_lite( $is_pro ) {

		if ( $is_pro && get_site_option( 'wpf-force-lite-version' ) ) {
			return false;
		}

		return $is_pro;
	}

	/**
	 * Switch plugin to custom license.
	 *
	 * @since {VERSION}
	 */
	public static function pro_update_license() {

		$actions = [
			'wpf_pro_license_type_switch'   => 'pro_update_license_type',
			'wpf_pro_license_status_switch' => 'pro_update_license_status',
		];

		foreach ( $actions as $action => $method ) {
			$nonce = filter_input( INPUT_GET, 'wpf_nonce', FILTER_SANITIZE_FULL_SPECIAL_CHARS );

			if ( empty( $nonce ) || ! wp_verify_nonce( $nonce, $action ) ) {
				continue;
			}

			$arg = filter_input( INPUT_GET, $action, FILTER_SANITIZE_FULL_SPECIAL_CHARS );

			self::$method( $arg );

			Transient::delete( 'addons_urls' );

			wp_safe_redirect( remove_query_arg( [ $action, 'wpf_nonce' ] ) );
			exit;
		}
	}

	/**
	 * Update license type.
	 *
	 * @since {VERSION}
	 *
	 * @param string $license_type License type.
	 */
	private static function pro_update_license_type( $license_type ) {

		update_site_option( 'wpf-force-license-type', $license_type );

		// Remove saved data from a database.
		// It will cause to force new data.
		Transient::delete( 'addons_urls' );
		delete_option( 'wpforms_notifications' );
	}

	/**
	 * Update license status.
	 *
	 * @since {VERSION}
	 *
	 * @param string $license_status License status.
	 */
	private static function pro_update_license_status( $license_status ) {

		update_site_option( 'wpf-force-license-status', $license_status );
	}

	/**
	 * Force license type and mute license for Lite version.
	 *
	 * @since 0.7
	 *
	 * @param mixed  $value  Value of wpforms_license option.
	 * @param string $option Option name.
	 *
	 * @return mixed
	 */
	public static function pro_force_license( $value, $option ) {

		if ( $option !== 'wpforms_license' ) {
			return $value;
		}

		if ( empty( $value['key'] ) || empty( $value['type'] ) ) {
			return $value;
		}

		if ( get_site_option( 'wpf-force-lite-version' ) ) {
			return '';
		}

		$force_type = get_site_option( 'wpf-force-license-type' );

		if ( $force_type && $force_type !== 'original' && $value['type'] !== $force_type ) {
			$value['type'] = $force_type;
		}

		$force_status = get_site_option( 'wpf-force-license-status', 'active' );

		if ( $force_status === 'deactivated' ) {
			$value['key'] = '';

		} elseif ( $force_status !== 'active' && $force_status !== 'original' ) {
			$value['is_expired']  = false;
			$value['is_disabled'] = false;
			$value['is_invalid']  = false;

			$value[ "is_$force_status" ] = true;
		}

		return $value;
	}
}
