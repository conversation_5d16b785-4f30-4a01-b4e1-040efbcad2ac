<?php

namespace WPForms\DevTools\WhatsNew;

/**
 * Registration and functionality related to taxonomies.
 *
 * @since 0.39
 */
class Taxonomies {

	/**
	 * Core constructor.
	 *
	 * @since 0.39
	 */
	public function __construct() {

		$this->hooks();
	}

	/**
	 * All the actions and filters are registered here.
	 *
	 * @since 0.39
	 */
	public function hooks(): void {

		add_action( 'init', [ $this, 'register' ], 0 );
	}

	/**
	 * Register taxonomies.
	 *
	 * @since 0.39
	 */
	public function register(): void {

		register_taxonomy(
			'wpf_license',
			[ 'wpf_whats_new' ],
			[
				'hierarchical'       => true,
				'labels'             => [
					'name'              => 'Licenses',
					'singular_name'     => 'License',
					'search_items'      => 'Search Licenses',
					'all_items'         => 'All Licenses',
					'parent_item'       => 'Parent Licenses',
					'parent_item_colon' => 'Parent Licenses:',
					'edit_item'         => 'Edit License',
					'update_item'       => 'Update License',
					'add_new_item'      => 'Add New License',
					'new_item_name'     => 'New License Name',
					'menu_name'         => 'Licenses',
				],
				'show_ui'            => true,
				'publicly_queryable' => false,
				'rewrite'            => false,
				'show_admin_column'  => true,
			]
		);

		register_taxonomy(
			'wpf_version',
			[ 'wpf_whats_new' ],
			[
				'hierarchical'       => true,
				'labels'             => [
					'name'              => 'WPForms Versions',
					'singular_name'     => 'WPForms Version',
					'search_items'      => 'Search WPForms Versions',
					'all_items'         => 'All WPForms Versions',
					'parent_item'       => 'Parent WPForms Versions',
					'parent_item_colon' => 'Parent WPForms Versions:',
					'edit_item'         => 'Edit WPForms Version',
					'update_item'       => 'Update WPForms Version',
					'add_new_item'      => 'Add New WPForms Version',
					'new_item_name'     => 'New WPForms Version',
					'menu_name'         => 'WPForms Versions',
				],
				'show_ui'            => true,
				'publicly_queryable' => false,
				'rewrite'            => false,
				'show_admin_column'  => false,
				'meta_box_cb'        => false,
			]
		);
	}
}
