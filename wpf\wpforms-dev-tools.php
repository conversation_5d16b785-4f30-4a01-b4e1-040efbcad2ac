<?php
/**
 * Plugin Name: WPForms Developer Tools
 * Plugin URI:  https://wpforms.com
 * Description: Custom internal developer tools for WPForms.
 * Author:      WPForms
 * Author URI:  https://wpforms.com
 * Version:     0.47
 */

// phpcs:disable Generic.Commenting.DocComment.MissingShort
/** @noinspection PhpUndefinedClassInspection */
/** @noinspection PhpUndefinedConstantInspection */
// phpcs:enable Generic.Commenting.DocComment.MissingShort

namespace WPForms\DevTools;

use WP_CLI;
use WPForms\DevTools\Commands\EntriesGeneratorCommand;
use WPForms\DevTools\Commands\FormsLocatorScanCommand;
use WPForms\DevTools\Commands\UpgradeCommand;
use WPForms\DevTools\FormEmbeddings\Utils as EmbeddingsUtils;
use WPForms\DevTools\WhatsNew\Loader as WhatsNewLoader;
use WPForms\DevTools\ChromeExt\Loader as ChromeExtLoader;
use WPF\E2EDashboard\E2EDashboard;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

// ==============================================================
// Constants
// ==============================================================

/**
 * Plugin version.
 *
 * @since 0.1
 */
const WPFORMS_DEV_TOOLS_VERSION = '0.47';

/**
 * Plugin Folder Path.
 *
 * @since 0.1
 */
define( 'WPFORMS_DEV_TOOLS_DIR', __DIR__ . '/' );

/**
 * Plugin Folder URL.
 *
 * @since 0.1
 */
define( 'WPFORMS_DEV_TOOLS_URL', untrailingslashit( plugin_dir_url( __FILE__ ) ) );

/**
 * Main plugin file.
 *
 * @since 0.22
 */
define( 'WPFORMS_DEV_PLUGIN_FILE', __FILE__ );

require_once __DIR__ . '/vendor/autoload.php';

// ==============================================================
// Filters
// ==============================================================

add_filter(
	'wpforms_helpers_templates_get_theme_template_paths',
	static function ( $paths ) {

		$paths[101] = __DIR__ . '/templates';

		return $paths;
	}
);

/**
 * Enable Templates code loading on the Form Generator page.
 *
 * @since 0.10
 *
 * @param bool|mixed $allow Allow loading.
 *
 * @return bool
 */
function wpf_forms_allow_load( $allow ): bool {

	if (
		// phpcs:ignore WordPress.Security
		! empty( $_REQUEST['page'] ) && $_REQUEST['page'] === DevTools::PAGE_SLUG &&
		// phpcs:ignore WordPress.Security
		! empty( $_REQUEST['view'] ) && $_REQUEST['view'] === 'forms'
	) {
		return true;
	}

	return (bool) $allow;
}

// Dashboard widget default state.
add_action( 'upgrader_process_complete', [ DashboardWidget::class, 'set_default_widget_state' ] );
register_activation_hook( WPFORMS_DEV_PLUGIN_FILE, [ DashboardWidget::class, 'set_default_widget_state' ] );

// Allow form templates to be loaded on the Forms page.
add_filter( 'wpforms_admin_builder_templates_allow_load', __NAMESPACE__ . '\wpf_forms_allow_load' );
add_filter( 'wpforms_admin_builder_templatescache_allow_load', __NAMESPACE__ . '\wpf_forms_allow_load' );
add_filter( 'wpforms_admin_builder_templatesinglecache_allow_load', __NAMESPACE__ . '\wpf_forms_allow_load' );

// ==============================================================
// Actions
// ==============================================================

add_action( 'wpforms_frontend_output_after', [ Main::class, 'after_form_edit' ] );
add_action( 'wpforms_admin_adminbarmenu_register_support_menu_after', [ RecentForms::class, 'admin_bar_menu_links' ], 998 );
add_action( 'wpforms_admin_adminbarmenu_register_support_menu_after', [ Main::class, 'admin_bar_menu_links' ], 999 );
add_action( 'wpforms_admin_adminbarmenu_register_support_menu_after', [ AdminBar::class, 'init' ], 1001 );
add_action( 'init', [ RTL::class, 'instance' ] );
add_action( 'init', [ WhatsNewLoader::class, 'get_instance' ], -100 );
add_action( 'init', [ Main::class, 'pro_lite_switch_update_option' ] );
add_action( 'init', [ Main::class, 'pro_update_license' ] );
add_action( 'init', [ SplashScreen::class, 'instance' ] );

add_action( 'plugins_loaded', [ Utils::class, 'init' ], 0 );
add_filter( 'wpforms_allow_pro_version', [ Main::class, 'pro_lite_switch_force_lite' ] );
add_filter( 'option_wpforms_license', [ Main::class, 'pro_force_license' ], 10, 2 );
add_action( 'wp_ajax_wpf_generate_entries', [ GenerateEntries::class, 'ajax_generate_entries' ] );
add_action( 'wp_ajax_wpf_delete_all_entries', [ GenerateEntries::class, 'ajax_delete_all' ] );
add_action( 'wp_ajax_wpf_generate_forms', [ GenerateForms::class, 'ajax_generate_forms' ] );
add_action( 'admin_print_scripts-toplevel_page_wpforms_dev_tools', [ Assets::class, 'dev_tools_scripts' ] );
add_action( 'admin_enqueue_scripts', [ Assets::class, 'scripts_styles' ] );
add_action( 'wp_enqueue_scripts', [ Assets::class, 'scripts_styles' ] );
add_action( 'wpforms_loaded', [ Assets::class, 'prevent_assets_cache' ] );
add_action( 'wpforms_loaded', [ DashboardWidget::class, 'instance' ] );
add_action( 'wpforms_loaded', [ DevTools::class, 'instance' ] );
add_action( 'wpforms_loaded', [ FormTemplates::class, 'instance' ] );
add_action( 'wpforms_loaded', [ ChromeExtLoader::class, 'instance' ] );
add_action( 'wpforms_loaded', [ E2EDashboard::class, 'instance' ] );
add_action( 'wp_print_scripts', [ Assets::class, 'dev_tools_inline_scripts' ] );
add_action( 'wpforms_loaded', [ CommandPalette::class, 'instance' ] );
add_action( 'wpforms_builder_enqueues', [ Assets::class, 'scripts_styles_builder' ], 20 );
add_action( 'wp_ajax_wpf_builder_bulk_embed', [ EmbeddingsUtils::class, 'ajax_bulk_embed' ] );

// ==============================================================
// WP CLI Commands
// ==============================================================

if ( defined( 'WP_CLI' ) && WP_CLI ) {
	WP_CLI::add_command( 'wpforms', EntriesGeneratorCommand::class );
	WP_CLI::add_command( 'wpforms locations', FormsLocatorScanCommand::class );
	WP_CLI::add_command( 'wpforms upgrade', UpgradeCommand::class );
}

/**
 * Get the instance of the plugin Main class.
 *
 * @since 0.22
 *
 * @return Main
 */
function wpf(): Main {

	return Main::get_instance();
}

// phpcs:ignore Generic.Commenting.DocComment.MissingShort
/** @noinspection UnusedFunctionResultInspection */
wpf();
