// Entries Single (Details) - admin.php?page=wpforms-entries&view=details

$payment_icons: total cart, gateway card;
$statuses:
	processed $color_dark_green,
	completed $color_dark_green,
	active $color_dark_green,
	cancelled $color_table_secondary_text,
	not-synced #50575e,
	failed $color_red,
	pending #bd8600,
	refunded $color_table_secondary_text,
	partrefund $color_table_secondary_text;

// Mixin with styles for the bar in education panels for addons (User Journey, Geolocation, etc).
//
// @since 1.8.5
@mixin addons-education-bar {
	h2.hndle {
		justify-content: space-between;
	}

	.wpforms-education-hide .dashicons {
		font-size: $font_size_ll;
		height: $font_size_ll;
		width: $font_size_ll;
		margin: 0;
		color: $color_light_text;
	}

	.wpforms-education-hide:hover .dashicons {
		color: $neutral-90;
	}
}

#wpforms-entries-single {
	.postbox {
		.inside {
			p {
				&:only-child {
					padding: 10px;
				}
			}
		}
	}

	&.wpforms-admin-wrap {
		.page-title {
			padding: 14px 20px;
			@include media("<=phone") {
				a.page-title-action {
					display: none!important;
				}
			}
		}

		&.wpforms-entries-single-edit {
			.page-title {
				padding: 15px 20px;
			}
		}
	}

	// Override the single navigation styles to show the settings button.
	.wpforms-admin-single-navigation {
		@include media("<=phone") {
			display: flex;

			.wpforms-admin-single-navigation-text {
				display: none;
			}
		}
	}

	.wpforms-entries-settings-container {
		.button {
			background: #ffffff;
			border-color: #8c8f94;
			color: #50575e;
			padding: 5px;
			height: auto;
			width: 34px;
			min-height: 32px;
			box-shadow: none;
			line-height: 17px;
			margin-bottom: 0;

			&:focus {
				box-shadow: 0 0 0 1px #056aab;
			}

			&:focus,
			&:hover {
				color: #056aab;
				background: #ffffff;
				border: 1px solid #056aab;
			}
		}

		.dashicons {
			font-size: 19px;
			line-height: 17px;
			height: 17px;
			width: 17px;
		}

		.wpforms-entries-settings-menu {
			background: #ffffff;
			border-radius: 6px;
			box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
			display: none;
			width: 240px;
			position: absolute;
			z-index: 30;
			right: 10px;
			top: 55px;

			.wpforms-entries-settings-menu-wrap {
				.wpforms-settings-title {
					display: block;
					width: 100%;
					font-size: 11px;
					line-height: 13px;
					text-transform: uppercase;
					padding: 14px 15px;
					border-top: 1px solid #dcdcde;
					border-bottom: 1px solid #dcdcde;
					margin: 7.5px 0;
					font-weight: 600;
					color: #50575e;
					box-sizing: border-box;

					&:first-child {
						border-top: 0;
						margin-top: 0;
					}
				}

				.wpforms-toggle-control {
					padding: 8px 14px;
					font-weight: 400;

					&:last-child {
						padding: 8px 14px 16px 14px;
					}
				}

			}
		}
	}

	// Entry fields metabox.
	#wpforms-entry-fields {
		overflow: visible;

		h2.hndle {
			display: flex;

			.dashicons {
				font-size: $font_size_m;
				height: $font_size_m;
				width: $font_size_m;
				margin-top: 0;
				color: $color_yellow;
				vertical-align: text-bottom;
				line-height: $font_size_m;
			}
		}

		.inside {
			padding: 0;
			margin: 0;

			p {
				padding: 0;
				margin: 0;
			}

			.no-fields {
				padding: 12px;
				margin: 0;
			}

			.wpforms-entry-field-name {
				font-weight: 600;
				background: #f6f6f6;
				padding: 8px 12px;
			}

			.wpforms-entry-field-value {
				padding: 8px 12px;
				border-radius: 4px;

				span:not( .file-icon ) {
					display: block;
				}

				img {
					max-width: 100%;
				}

				&:after {
					content: '';
					display: block;
					clear: both;
				}

			}

			.empty {
				&.wpforms-entry-field-value,
				.wpforms-entry-field-value {
					font-style: italic;
					color: #999;
				}
			}

			.wpforms-field-file-upload,
			.wpforms-field-pdf,
			.wpforms-field-camera {
				.file-icon {
					padding-right: 10px;

					img {
						vertical-align: bottom;
					}
				}

				a {
					cursor: pointer;
					word-break: break-all;

					&.disabled {
						opacity: 0.5;
					}
				}
			}

			.wpforms-field-file-upload,
			.wpforms-field-camera {
				p.file-entry{
					padding-left: 12px;
				}

				.wpforms-help-tooltip {
					line-height: 16px;
					font-size: 14px;
					margin-left: 10px;
					color: #a6a6a6;

					&:hover {
						color: #444444;
					}
				}

				.button-link-delete {
					text-decoration: none;
				}

				.wpforms-trash-icon {
					width: 16px;
					height: 16px;
					font-size: 16px;
					color: #a00;
					margin-left: 8px;

					&:hover {
						color: #dc3232;
					}
				}
			}

			// Entry Edit Layout and Repeater Fields.
			.wpforms-entry-edit-repeater,
			.wpforms-entry-edit-layout {
				.wpforms-entry-edit-row {
					display: flex;
					flex-wrap: nowrap;
					justify-content: space-between;

					.wpforms-entry-edit-column {
						width: var(--field-layout-column-width, auto);
					}
				}
			}
		}

		.wpforms-empty-field-toggle {
			float: right;
			text-decoration: none;
			padding: 2px 0 0;
			font-size: 12px;
		}

		.wpforms-field-richtext {
			.wpforms-entry-field-value {
				max-width: 100%;
				overflow: auto;
			}

			.wpforms-entry-field-value-richtext {
				width: calc( 100% + 24px );
				height: 0;
				margin: -8px -12px -11px -12px;
			}

			img {
				max-width: 100%;
			}
		}

		.wpforms-edit-entry-field-richtext {
			.wpforms-field-richtext {
				padding: 10px;
			}
		}

		.wpforms-field-layout-column.wpforms-field-layout-column-empty {
			.wpforms-entry-field-item.wpforms-hide {
				.wpforms-entry-field-name, .wpforms-entry-field-value-is-choice {
					display: none;
				}
			}
		}

		.wpforms-entry-field-item,
		.wpforms-field-repeater-block,
		.wpforms-field-repeater-row,
		.wpforms-field-layout-row,
		.wpforms-field-layout-column {
			.wpforms-entry-field-name {
				.wpforms-entry-field-description {
					display: block;
					color: #a7aaad;
					font-size: 13px;
					font-weight: 400;

					&.wpforms-hide {
						display: none;
					}
				}
			}
		}

		.wpforms-entry-field-item,
		.wpforms-field-repeater-block,
		.wpforms-field-repeater-row {
			.wpforms-entry-field-value-is-choice {
				padding: 8px 10px;

				.field-value-choice-image-wrapper {
					max-width: 200px;
					position: relative;

					img {
						width: 100%;
						height: auto;
					}
				}

				.field-value-choice {
					@include media("<=tablet") {
						padding: 5px 0;
					}
				}

				.field-value-choice-checked {
					.field-value-choice-image-wrapper {
						&:before,
						&:after {
							content: '';
							position: absolute;
							left: 50%;
							top: 50%;
							display: block;
						}

						&:before {
							width: 40px;
							height: 40px;
							background: #066aab;
							border-radius: 50%;
							transform: translate(-50%, -50%);
							border: 0;
							box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
						}

						&:after {
							content: "\2714";
							color: #fff;
							font-size: 22px;
							transform: translate(-50%, -50%);
						}
					}
				}

			}

			&.wpforms-field-entry-checkbox,
			&.wpforms-field-entry-radio,
			&.wpforms-field-entry-payment-multiple,
			&.wpforms-field-entry-payment-checkbox {
				.wpforms-entry-field-value-is-choice {
					.wpforms-entry-choice-wrapper {
						display: grid;
						gap: 10px;

						&.wpforms-entry-choice-column-3 {
							grid-template-columns: repeat(3, auto);

						}

						&.wpforms-entry-choice-column-2 {
							grid-template-columns: repeat(2, auto);
						}

						&.wpforms-entry-choice-column-1 {
							grid-template-columns: repeat(1, auto);
						}

						&.wpforms-entry-choice-column-inline {
							display: inline-flex;
							flex-wrap: wrap;
						}

						.field-value-choice-image {
							padding: 20px;
							text-align: center;

							.field-value-choice-image-wrapper {
								margin: auto;
							}

							&.field-value-choice-checked {
								box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);

								.field-value-choice-image-wrapper {
									min-height: 80px;
								}
							}
						}
					}

					&.wpforms-hide {
						display: none;
					}
				}
			}

			&.wpforms-field-pagebreak {
				.wpforms-entry-field-name {
					font-size: 14px;
					color: #32373c;
					line-height: 20px;
					background: #dcdcde;
					width: 100%;
					box-sizing: border-box;
				}
			}

			&.wpforms-field-entry-divider {
				.wpforms-entry-field-name {
					background: #f1f1f1;
					border-bottom: 1px solid #dcdcde;
					border-top: 1px solid #dcdcde;
					width: 100%;
					box-sizing: border-box;
				}
			}
		}

		.wpforms-field-repeater-row,
		.wpforms-field-repeater-block,
		.wpforms-field-layout-row,
		.wpforms-field-layout-column {
			// Hide Layout or Repeater field Name hidden by CL.
			&.wpforms-conditional-hidden:has( .wpforms-entry-field-item.empty.wpforms-hide ) {
				& > .wpforms-entry-field-name {
					display: none;
				}
			}

			// Show Layout or Repeater field Name if any field is not empty.
			&.wpforms-conditional-hidden:has( .wpforms-entry-field-item:not( .empty.wpforms-hide ) ) {
				& > .wpforms-entry-field-name {
					display: revert;
				}
			}

			> .wpforms-entry-field-name {
				background: #f1f1f1;
				border-bottom: 1px solid #dcdcde;
				border-top: 1px solid #dcdcde;
				width: 100%;
				box-sizing: border-box;

				&:has( .wpforms-entry-field-description.wpforms-hide ) {
					padding: 0;
					border-bottom: none;

					&:has( .wpforms-entry-field-name-wrapper ) {
						padding: 8px 12px;
						border-bottom: 1px solid #dcdcde;
					}
				}
			}

			&:first-child {
				> .wpforms-entry-field-name {
					border-top: 0;
				}
			}
		}

		.wpforms-edit-entry-field {
			.wpforms-field-layout-block,
			.wpforms-field-repeater-block {
				>.wpforms-entry-field-name {
					background: #f1f1f1;
					border-bottom: 1px solid #dcdcde;
					border-top: 1px solid #dcdcde;
				}
			}
		}

		.inside > div.wpforms-edit-entry-field:first-of-type .wpforms-entry-field-name {
			border-top: 0;
		}

		.wpforms-entries-fields-wrapper {
			&.wpforms-entry-maintain-layout {
				.wpforms-layout-row, .wpforms-entry-field-layout {
					display: flex;
					flex-wrap: nowrap;
					justify-content: space-between;
				}

				.wpforms-entry-field-layout {
					width: 100%;
					word-wrap: break-word;

					.wpforms-entry-field-layout-inner:empty {
						background: #ffffff;
					}

					.wpforms-entry-field-item {
						background: #f6f6f6;

						.wpforms-entry-field-value {
							background: #ffffff;
						}
					}
				}

				.wpforms-field-layout-column {
					width: var(--field-layout-column-width, auto);
				}

				.wpforms-field-repeater-row {
					.wpforms-layout-row {
						&:not(:first-child) {
							.wpforms-entry-field-name {
								display: none;
							}
						}
					}
				}
			}

			.wpforms-field-repeater-rows:has(.wpforms-field-layout-column:not(.empty.wpforms-hide)) {
				padding-bottom: 4px;
			}

			.wpforms-field-repeater-row {
				.wpforms-entry-field-value {
					padding: 4px 12px;
				}
			}

			&.wpforms-entry-compact-layout {
				> .wpforms-entry-field-item,
				> .wpforms-field-layout-row .wpforms-entry-field-item,
				> .wpforms-field-layout-column .wpforms-entry-field-item {
					display: flex;
					padding: 0;
					background: #f6f6f6;

					&.wpforms-entry-field-row-alt {
						background: #ffffff;
					}

					&.wpforms-hide {
						display: none;
					}

					.wpforms-entry-field-name {
						width: 200px;
						min-width: 200px;
						word-break: break-word;
						background: none;
					}

					.wpforms-entry-field-value {
						margin: 0;
						width: 100%;
						word-break: break-word;
						padding: 10px;
					}

					&.wpforms-field-divider {
						.wpforms-entry-field-name {
							background: #f1f1f1;
							width: 100%;
						}
					}

					&.wpforms-field-pagebreak{
						.wpforms-entry-field-name {
							background: #dcdcde;
							width: 100%;
						}
					}

				}
			}
		}
	}

	// Entry Notes metabox.
	#wpforms-entry-notes {
		.inside {
			margin: 0;
			padding: 0;
		}

		.wpforms-entry-notes-new {
			padding: 10px;
		}

		form {
			display: none;
		}

		.btns {
			overflow: auto;
			padding: 8px 0;
		}

		.no-notes {
			margin: 0;
			padding: 4px 10px 8px;
		}

		.wpforms-entry-notes-single {
			padding: 10px;

			p:last-of-type {
				margin-bottom: 0;
			}

			&.odd {
				background-color: $surface-background-light;
			}

			ul {
				list-style-type: disc;
				padding: 0 0 0 26px;
			}
		}

		.wpforms-entry-notes-byline {
			color: #888;

			.sep {
				color: #ddd;
			}

			.notes-user {
				text-decoration: none;
				font-weight: 600;
			}

			.note-delete {
				color: #a00;
				text-decoration: none;

				&:hover {
					color: red;
					text-decoration: none;
				}
			}
		}
	}

	// Entry Logs metabox.
	#wpforms-entry-logs {
		.inside {
			margin: 0;
			padding: 0;
		}

		form {
			display: none;
		}

		.btns {
			overflow: auto;
			padding: 8px 0;
		}

		.no-logs {
			margin: 0;
			padding: 4px 10px 8px;
		}

		.wpforms-entry-logs-single {
			padding: 10px;

			&:first-of-type {
				border-top: 0;
			}

			p:last-of-type {
				margin-bottom: 0;
			}

			&.odd {
				background-color: $surface-background-light;
			}
		}

		.wpforms-entry-logs-byline {
			color: #888;

			.notes-user {
				text-decoration: none;
				font-weight: 600;
			}
		}
	}


	// Entry Details, Actions, and Payments.
	#wpforms-entry-details .inside,
	#wpforms-entry-payment .inside,
	#wpforms-entry-actions .inside {
		margin: 0;
		padding: 0;

		#major-publishing-actions {
			background: #f6f6f6;
		}

		.wpforms-entry-ip {
			strong {
				word-break: break-all;
			}
		}
	}

	.wpforms-education-hide {
		cursor: pointer;
		color: $color_light_text;
		display: block;
		float: right;

		&:hover {
			color: $neutral-90;
		}
	}

	.wpforms-entry-payment-meta,
	.wpforms-entry-details-meta,
	.wpforms-entry-actions-meta {
		padding: $spacing_xs 0;

		p {
			color: #32373c;
			display: flex;
			flex-wrap: wrap;
			gap: $spacing_xs;
			margin: 0;
			padding: 6px 12px 6.5px 42px;
			position: relative;
		}
	}

	.wpforms-entry-payment-meta {

		@each $status, $color in $statuses {

			.status-#{$status} {
				&.wpforms-entry-payment-meta-status {
					strong {
						color: $color;
					}
				}

				&::before {
					-webkit-mask-image: url(../../images/payments/status/icon-#{$status}.svg);
					mask-image: url(../../images/payments/status/icon-#{$status}.svg);
				}
			}
		}
	}

	@each $name, $icon in $payment_icons {

		.wpforms-entry-payment-meta-#{$name} {
			&::before {
				background-image: url(../images/entries/payments/#{$icon}.svg);
			}
		}
	}

	[class*="wpforms-entry-payment-meta-"] {
		&::before {
			background-size: contain;
			content: '';
			color: $color_toggle_icon_hover;
			height: 22px;
			position: absolute;
			text-decoration: none;
			top: $spacing_xs;
			inset-inline-start: 12px;
			width: 22px;
		}
	}

	.wpforms-entry-payment-meta-status,
	.wpforms-entry-payment-meta-type {
		&::before {
			background-color: $color_toggle_icon_hover;
			height: 16px;
			left: $spacing_ms;
			top: $spacing_ss;
			width: 16px;
			border-radius: 50%;
		}

		strong {
			text-transform: capitalize;
		}
	}

	.wpforms-entry-payment-publishing-actions {
		padding: $spacing_s;
		background: #f6f7f7;
		display: flex;
		justify-content: flex-end;
		border-top: 1px solid #c3c4c7;
		border-radius: 0 0 4px 4px;
	}

	.wpforms-entry-details-meta strong,
	.wpforms-entry-actions-meta strong {
		font-weight: 600;
	}

	.wpforms-entry-details-meta .dashicons,
	.wpforms-entry-actions-meta .dashicons {
		color: $color_toggle_icon_hover;
		font-size: 22px;
		height: 22px;
		inset-inline-start: 12px;
		position: absolute;
		text-decoration: none;
		top: $spacing_xs;
		width: 22px;
	}

	.wpforms-entry-actions-meta {
		.dashicons {
			padding-right: 3px;
		}
	}

	#wpforms-entry-details .trash {
		color: #a00;
		text-decoration: none;
		padding: 1px 2px;
	}

	#wpforms-entry-details .trash:hover {
		color: red;
	}

	.wpforms-entry-actions-meta a {
		text-decoration: none;
	}

	.wpforms-entry-actions-meta a:hover {
		text-decoration: underline;
	}

	// Entry Location.
	#wpforms-entry-geolocation {
		@include addons-education-bar;

		.inside {
			padding: 0;
			margin: 0;

			p:not(:only-child) {
				margin: 1em 0;
			}
		}

		ul {
			margin: 0;

			li {
				border-bottom: 1px solid #eee;
				margin: 0;
				overflow: auto;

				&:last-of-type {
					border-bottom: 0;
				}
			}
		}

		.wpforms-geolocation-meta {
			display: inline-block;
			width: 85px;
			border-inline-end: 1px solid #eee;
			padding: 10px 20px 10px 12px;
		}

		.wpforms-geolocation-value {
			display: inline-block;
			padding: 10px 12px 10px 20px;
		}

		.wpforms-flag {
			vertical-align: text-top;
			margin-right: 4px;
		}

		.wpforms-geolocation-preview {
			position: relative;

			.wpforms-geolocation-map {
				width: 100%;
				height: 320px;
				background-image: url('../images/education-geolocation.jpg');
				background-size: cover;
				background-position: center center;
			}

			.overlay {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background-color: #ffffff;
				opacity: .75;
				z-index: 1;
			}

			.wpforms-geolocation-form {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translateX(-50%) translateY(-50%);
				padding: 30px;
				box-sizing: border-box;
				text-align: center;
				background-color: #fff;
				max-width: 380px;
				width: 100%;
				box-shadow: 0 5px 30px #00000026;
				border-radius: 5px;
				color: #444444;
				z-index: 2;

				h2 {
					padding: 0;
					font-size: 20px;
					color: $neutral-90;
					font-weight: bold;
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 10px;
				}

				p {
					font-size: 15px;
					line-height: 20px;
				}

				a {
					margin-top: 5px;
					box-sizing: border-box;
				}

				.msg {
					text-align: left;
					padding: 7px 10px;
					margin: 20px 0 0;
				}
			}
		}
	}

	// User Journey.
	#wpforms-entry-user-journey {
		@include addons-education-bar;

		.inside {
			padding: 0;
			margin: 0;

			p:not(:only-child) {
				margin: 1em 0;
			}
		}

		table {
			width: 100%;
			border-spacing: 0;

			tr {

				&:last-of-type {
					td {
						border-bottom: 0;
					}
				}

				td {
					vertical-align: top;
					border-bottom: 1px solid #eee;
					padding: 10px;

					&:first-of-type {
						padding-left: 12px;
					}

					&:last-of-type {
						padding-right: 12px;
					}

					&.date {
						background: #f5f5f5;
						font-weight: 600;
					}

					&.title-area {
						a {
							text-decoration: none;
						}

						.go {
							color: #a6a6a6;
							margin: 0 4px;
							font-size: 12px;

							&:hover {
								color: #444;
							}
						}

						.path {
							font-weight: 400;
							color: #a6a6a6;
						}
					}

					&.time {
						width: 65px;
					}

					&.duration {
						width: 130px;
					}

					.fa-circle {
						font-size: 4px;
						vertical-align: middle;
						margin: 0 4px;
						color: #ccc;
					}

					.fa-check {
						color: #009933;
					}
				}
			}
		}

		.wpforms-user-journey-preview {
			position: relative;

			.overlay {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background-color: #ffffff;
				opacity: .75;
				z-index: 1;
			}

			.wpforms-user-journey-form {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translateX(-50%) translateY(-50%);
				padding: 30px;
				box-sizing: border-box;
				text-align: center;
				background-color: #fff;
				max-width: 380px;
				width: 100%;
				box-shadow: 0 5px 30px #00000026;
				border-radius: 5px;
				color: #444444;
				z-index: 2;

				h2 {
					padding: 0;
					font-size: 20px;
					color: $neutral-90;
					font-weight: bold;
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 10px;
				}

				p {
					font-size: 15px;
					line-height: 20px;
				}

				a {
					margin-top: 5px;
					box-sizing: border-box;
				}

				.msg {
					text-align: left;
					padding: 7px 10px;
					margin: 20px 0 0;
				}
			}
		}
	}
}

// RTL related styles.
.rtl {

	#wpforms-entries-single {

		.wpforms-entry-details-meta, .wpforms-entry-actions-meta, .wpforms-entry-payment-meta {

			p {
				padding: $border_radius_m 42px 6.5px $font_size_sss;
			}
		}

		.wpforms-entries-settings-container .wpforms-entries-settings-menu {
			right: auto;
			left: $spacing_s;
		}
	}
}
