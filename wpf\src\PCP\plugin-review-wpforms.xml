<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="WPPluginCheck" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/squizlabs/PHP_CodeSniffer/master/phpcs.xsd">

	<!-- For more information: https://make.wordpress.org/plugins/handbook/review/ -->
	<description>Standards any plugin to be published on wp.org should comply with.</description>

	<!--
	Prevent errors caused by WordPress Coding Standards not supporting PHP 8.0+.
	See https://github.com/WordPress/WordPress-Coding-Standards/issues/2035
	-->
	<ini name="error_reporting" value="E_ALL &#38; ~E_DEPRECATED" />

	<arg name="report" value="code"/>
	<arg value="s"/>

	<!-- Plugins should be compatible with PHP 5.2 and higher. -->
	<config name="testVersion" value="5.2-"/>

	<exclude-pattern>*/tgm-plugin-activation/*</exclude-pattern>
	<exclude-pattern>*/freemius/*</exclude-pattern>
	<exclude-pattern>*/dompdf/*</exclude-pattern>
	<exclude-pattern>*/cmb2/*</exclude-pattern>
	<exclude-pattern>*/redux-framework/*</exclude-pattern>
	<exclude-pattern>*/cherry-framework/*</exclude-pattern>
	<exclude-pattern>*/titan-framework/*</exclude-pattern>
	<exclude-pattern>*/vendor/*</exclude-pattern>
	<exclude-pattern>*/guzzlehttp/*</exclude-pattern>
	<exclude-pattern>*/vendors/*</exclude-pattern>
	<exclude-pattern>*/plugin-update-checker/*</exclude-pattern>
	<exclude-pattern>*/composer_directory/*</exclude-pattern>
	<exclude-pattern>*/node_modules/*</exclude-pattern>
	<exclude-pattern>*/build/*</exclude-pattern>
	<exclude-pattern>*/pro/libs/*</exclude-pattern>
	<exclude-pattern>*/vendor_prefixed/*</exclude-pattern>

	<!-- All SQL queries should be prepared as close to the time of querying the database as possible. -->
	<rule ref="WordPress.DB.PreparedSQL"/>
	<rule ref="WordPress.DB.PreparedSQL.InterpolatedNotPrepared">
		<!-- Ideally this wouldn't trigger on "safe" items, but it's triggered on any variable in the SQL. -->
		<type>warning</type>
	</rule>

	<!-- Verify that placeholders in prepared queries are used correctly. -->
	<rule ref="WordPress.DB.PreparedSQLPlaceholders"/>

	<!-- Nonce verification. -->
	<rule ref="WordPress.Security.NonceVerification">
		<!-- This is triggered on all GET/POST access, it can't be an error. -->
		<type>warning</type>
	</rule>

	<!-- Sanitized Input rules -->
	<rule ref="WordPress.Security.ValidatedSanitizedInput">
		<type>error</type>
		<severity>7</severity>
	</rule>

	<!-- Prohibit the use of the backtick operator. -->
	<rule ref="Generic.PHP.BacktickOperator"/>

	<!-- Prohibit the use of the `goto` PHP language construct. -->
	<rule ref="Generic.PHP.DiscourageGoto.Found">
		<type>error</type>
		<message>The "goto" language construct should not be used.</message>
	</rule>

	<!-- No PHP short open tags allowed. -->
	<rule ref="Generic.PHP.DisallowShortOpenTag"/>

	<!-- Alternative PHP open tags are not allowed. -->
	<rule ref="Generic.PHP.DisallowAlternativePHPTags"/>

	<!-- Prevent path disclosure when using add_theme_page(). -->
	<rule ref="WordPress.Security.PluginMenuSlug"/>

	<!-- While most plugins shouldn't query the database directly, if they do, it should be done correctly. -->
	<!-- Don't use the PHP database functions and classes, use the WP abstraction layer instead. -->
	<rule ref="WordPress.DB.RestrictedClasses"/>
	<rule ref="WordPress.DB.RestrictedFunctions"/>

	<!-- Check for code WP does better -->
	<rule ref="WordPress.WP.AlternativeFunctions">
		<type>error</type>
		<exclude name="WordPress.WP.AlternativeFunctions.json_encode"/>
	</rule>

	<rule ref="Generic.PHP.ForbiddenFunctions">
		<properties>
			<property name="forbiddenFunctions" type="array">
				<element key="move_uploaded_file" value="null"/>
				<element key="passthru" value="null"/>
				<element key="proc_open" value="null"/>
				<element key="create_function" value="null"/>
				<element key="eval" value="null"/>
				<element key="str_rot13" value="null"/>
			</property>
		</properties>
	</rule>

	<!-- Check for use of deprecated WordPress classes, functions, and function parameters. -->
	<rule ref="WordPress.WP.DeprecatedClasses"/>
	<rule ref="WordPress.WP.DeprecatedFunctions"/>
	<rule ref="WordPress.WP.DeprecatedParameters"/>
	<rule ref="WordPress.DateTime.RestrictedFunctions"/>

	<!-- Check for deprecated WordPress constants. -->
	<rule ref="WordPress.WP.DiscouragedConstants">
		<type>error</type>
	</rule>

	<!-- Check for usage of deprecated parameter values in WP functions and provide alternative based on the parameter passed. -->
	<rule ref="WordPress.WP.DeprecatedParameterValues"/>

	<!-- Introduce WPForms escaping functions -->
	<rule ref="WordPress.Security.EscapeOutput">
		<properties>
			<property name="customEscapingFunctions" type="array">
				<element value="wpforms_esc_richtext_field"/>
				<element value="wpforms_sanitize_amount"/>
				<element value="wpforms_sanitize_array_combine"/>
				<element value="wpforms_sanitize_classes"/>
				<element value="wpforms_sanitize_error"/>
				<element value="wpforms_sanitize_hex_color"/>
				<element value="wpforms_sanitize_key"/>
				<element value="wpforms_sanitize_textarea_field"/>
				<element value="wpforms_sanitize_text_deeply"/>
				<element value="wpforms_esc_unselected_choices"/>
				<element value="wpforms_validate_field_id"/>
				<!-- Safe functions -->
				<element value="wpforms_datetime_format"/>
				<element value="wpforms_format_amount"/>
				<element value="wpforms_html_attributes"/>
				<element value="wpforms_panel_field"/>
				<element value="wpforms_date_format"/>
			</property>
		</properties>
	</rule>

</ruleset>
