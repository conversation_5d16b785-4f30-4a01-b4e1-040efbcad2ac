<?php

use Codeception\Actor;
use Codeception\Exception\ModuleException;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\WebDriverBy;

/**
 * The acceptance tests actor.
 *
 * Inherited Methods
 *
 * @method void wantToTest( $text )
 * @method void wantTo( $text )
 * @method void execute( $callable )
 * @method void expectTo( $prediction )
 * @method void expect( $prediction )
 * @method void amGoingTo( $argumentation )
 * @method void am( $role )
 * @method void lookForwardTo( $achieveValue )
 * @method void comment( $description )
 * @method void pause()
 *
 * @SuppressWarnings(PHPMD)
 */
class AcceptanceTester extends Actor {

	use _generated\AcceptanceTesterActions;

	/**
	 * Switches to next tab.
	 * Overrides default method in order to resize window after switching to tab.
	 *
	 * @param int $offset Number of tabs to switch, next one is 1.
	 *
	 * @throws Exception
	 */
	public function switchToNextTab( $offset = 1 ) {

		$this->getScenario()->runStep( new \Codeception\Step\Action( 'switchToNextTab', func_get_args() ) );
		$this->resizeWindow( 1980, 1050 );
	}

	/**
	 * Opens new tab.
	 * Overrides default method in order to resize window after switching to tab.
	 *
	 * @throws Exception
	 */
	public function openNewTab() {

		$this->getScenario()->runStep( new \Codeception\Step\Action( 'openNewTab', func_get_args() ) );
		$this->resizeWindow( 1980, 1050 );
	}

	/**
	 * Scrolls to the bottom of the page.
	 */
	public function scrollToBottom() {

		$this->executeJS( 'window.scrollTo( 0, document.body.scrollHeight )' );
	}

	/**
	 * Assert that URL contains expected part of URL.
	 *
	 * @param string $partURL Expected part of URL.
	 */
	public function assertUrlContains( $partURL ) {

		$this->assertStringContainsString( $partURL, $this->getCurrentUrl() );
	}

	/**
	 * Assert that URL equals to expected URL.
	 *
	 * @param string $url Expected URL.
	 */
	public function assertUrlEquals( $url ) {

		$this->assertEquals( $url, $this->getCurrentUrl() );
	}

	/**
	 * Check a confirmation message after a form submitting.
	 *
	 * @param string $message Expected confirmation message.
	 *
	 * @throws Exception
	 */
	public function checkConfirmationMessage( $message = 'Thanks for contacting us! We will be in touch with you shortly.' ) {

		$this->waitForText( $message, 30, '.wpforms-confirmation-container-full' );
	}

	/**
	 * Output in error.log content of the $var.
	 *
	 * @param mixed $var Variable.
	 */
	public function log( $var ) {

		$printFunction = static function ( $var ) {

			ob_start();

			if ( is_bool( $var ) || empty( $var ) ) {
				var_dump( $var ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_var_dump
			} else {
				print_r( $var ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r
			}

			return ob_get_clean();
		};

		error_log( $printFunction( $var ) ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_error_log
	}

	/**
	 * Scrolls and clicks to the item.
	 * That's needed in order to avoid the absolutely positioned WordPress admin bar.
	 *
	 * @param string $element XPath of the element you want to scroll and click to.
	 */
	public function scrollAndClick( $element ) {

		$this->scrollTo( $element, 0, - 40 );
		$this->click( $element );
	}

	/**
	 * Click an element using JS.
	 *
	 * @param string $xpathElement      XPath Element.
	 * @param bool   $triggerMouseEvent Whether to trigger a mouse event (default is false).
	 *
	 * @return void
	 */
	public function clickWithJS( $xpathElement, $triggerMouseEvent = false ) {
		$this->scrollTo( $xpathElement, 0, -100 );

		$element = addslashes( $xpathElement );

		$jsCode = "
		var element = document.evaluate('$element', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
		if (element) {
			element.click();
		}
		";

		if ( $triggerMouseEvent ) {
			$jsCode .= "
			var mouseEvent = new MouseEvent('mousedown', {
				bubbles: true,
				cancelable: true,
				view: window
			});
			element.dispatchEvent(mouseEvent);
			";
		}

		$this->executeJS( $jsCode );
	}

	/**
	 * Returns Site URL as found in database.
	 *
	 * @return string
	 */
	public function getSiteUrl() {

		return (string) getenv( 'WP_URL' );
	}

	/**
	 * Asserts given response code for given URL.
	 *
	 * @param string $url                  URL to check.
	 * @param int    $expectedResponseCode Expected response code, defaults to 200.
	 */
	public function assertResponseCodeForUrl( $url, $expectedResponseCode = 200 ) {

		$headers            = get_headers( $url );
		$actualResponseCode = substr( $headers[0], 9, 3 );

		$this->assertEquals( $expectedResponseCode, $actualResponseCode );
	}

	/**
	 * Asserts response code for given URL isn't equal to the one provided.
	 *
	 * @param string $url                    URL to check.
	 * @param int    $unexpectedResponseCode Not-expected response code, defaults to 404.
	 */
	public function dontSeeResponseCodeForUrl( $url, $unexpectedResponseCode = 404 ) {

		$headers            = get_headers( $url );
		$actualResponseCode = substr( $headers[0], 9, 3 );

		$this->assertNotEquals( $unexpectedResponseCode, $actualResponseCode );
	}

	/**
	 * Returns email of admin.
	 *
	 * @return string
	 */
	public function getAdminEmail() {

		return $this->grabOptionFromDatabase( 'admin_email' );
	}

	/**
	 * Waits for a certain value to appear in the database.
	 *
	 * @param string $expectedValue      Expected value to be used in assertion.
	 * @param string $table              DB Table.
	 * @param string $field              Field of db table.
	 * @param array  $criteria           Criteria for db query.
	 * @param int    $maxNumberOfRetries Maximum number of retries.
	 *
	 * @throws Exception
	 */
	public function waitForValueInDb( $expectedValue, $table, $field, $criteria, $maxNumberOfRetries = 10 ) {

		$actualValue = null;

		for ( $i = 0; $i <= $maxNumberOfRetries; $i++ ) {
			$actualValue = $this->grabFromDatabase( $table, $field, $criteria );

			if ( $actualValue !== false ) {
				continue;
			}
			$this->wait( 0.5 );
		}
		$this->assertEquals( $expectedValue, $actualValue, "Expected value wasn't matched with the actual one fetched from db." );
	}

	/**
	 * Asserts whether a specific setting or field value in the database matches an expected value.
	 *
	 * @param string $settingsName   The name/key of the setting within the serialized data, or the field name if not serialized.
	 * @param mixed  $expectedValue  The expected value to compare against.
	 * @param string $table          The name of the database table.
	 * @param string $field          The name of the database column that might hold the serialized data or simple value.
	 * @param array  $criteria       The criteria to match against in the database (usually an associative array).
	 *
	 * @return bool  Returns true if the setting's or field's value matches the expected value, false otherwise.
	 *
	 * @throws \Exception  Throws an exception if the specified setting is not found in the serialized data.
	 */
	public function assertSettingsInDb( $settingsName, $expectedValue, $table, $field, $criteria ) {

		$dbValue = $this->grabFromDatabase( $table, $field, $criteria );

		if ( $this->isSerialized( $dbValue ) ) {

			$data = unserialize( $dbValue );

			if ( isset( $data[ $settingsName ] ) ) {
				return $data[ $settingsName ] == $expectedValue;
			}
		} elseif ( $dbValue == $settingsName ) {
			return $dbValue == $expectedValue;
		}

		return false;
	}

	/**
	 * Helper function to determine if a string represents serialized data.
	 *
	 * This function uses a combination of checks to determine if the input string is serialized:
	 *
	 * 1. It checks if the string represents a serialized `false`. This is a special edge case
	 *    because the only serialized representation of a boolean `false` in PHP is `b:0;`.
	 *
	 * 2. It then attempts to unserialize the string. The `@` symbol before `unserialize`
	 *    suppresses any PHP notices or warnings that might arise from trying to unserialize
	 *    a non-serialized string.
	 *    - If unserialization is successful (meaning the string was serialized), it returns
	 *      the unserialized value.
	 *    - If it fails (meaning the string wasn't serialized), it returns `false`.
	 *
	 * By using both checks, the function can more reliably determine if a string is serialized,
	 * even accounting for edge cases like serialized `false`.
	 *
	 * @param string $data The string to check for serialization.
	 *
	 * @return bool Returns true if the string represents serialized data, and false otherwise.
	 */
	protected function isSerialized( $data ) {
		return ( $data == serialize( false ) || @unserialize( $data ) !== false );
	}

	/**
	 * Generates random string.
	 *
	 * @param int  $length       The amount of chars the random string should have.
	 * @param bool $allowNumbers Allow numbers in random string.
	 *
	 * @return string The random string
	 */
	public function generateRandomString( $length, $allowNumbers = true ) {

		$randString = '';
		$char       = array_merge( range( 'A', 'Z' ), range( 'a', 'z' ), $allowNumbers ? range( '0', '9' ) : [] );
		$max        = count( $char ) - 1;

		for ( $i = 0; $i < $length; $i ++ ) {
			$rand        = mt_rand( 0, $max ); //phpcs:ignore
			$randString .= $char[ $rand ];
		}

		return $randString;
	}

	/**
	 * Fetches a CSS value for an element.
	 *
	 * @param string $element   The element XPath.
	 * @param string $attribute The CSS attribute.
	 *
	 * @return string The CSS value.
	 */
	public function getCSSValue( $element, $attribute ) {

		return $this->executeInSelenium(
            function ( RemoteWebDriver $webdriver ) use ( $element, $attribute ) {
                    return $webdriver->findElement( WebDriverBy::xpath( $element ) )->getCSSValue( $attribute );
            }
        );
	}

	/**
	 * Fetches the full path of the functions file of the active theme.
	 *
	 * @return string The full path of the functions.php file.
	 */
	private function getActiveThemeFunctionsFile() {

		$this->runShellCommand( 'wp theme list --status=active --field=name' );
		$activeTheme = $this->grabShellOutput();
		$this->runShellCommand( 'wp theme path' );
		$themesPath = $this->grabShellOutput();

		return $themesPath . '/' . $activeTheme . '/functions.php';
	}

	/**
	 * Backup the functions.php file of the active theme to restore later.
	 */
	public function backupActiveThemeFunctionsFile() {

		$functionsFile = $this->getActiveThemeFunctionsFile();

		$this->runShellCommand( sprintf( 'cp %s %s_temp', $functionsFile, $functionsFile ) );
	}

	/**
	 * Restores the functions.php file from a backup.
	 */
	public function restoreActiveThemeFunctionsFile() {

		$functionsFile = $this->getActiveThemeFunctionsFile();

		$this->runShellCommand( sprintf( 'mv %s_temp %s', $functionsFile, $functionsFile ), false );
	}

	/**
	 * Appends some content to the functions.php file of the active theme.
	 *
	 * @param string $content The content to append.
	 */
	public function appendToActiveThemeFunctionsFile( $content ) {

		$functionsFile = $this->getActiveThemeFunctionsFile();

		$this->runShellCommand( sprintf( 'echo "%s" >> %s', $content, $functionsFile ) );
	}

	/**
	 * Gets the last file in the given directory, directory defaults to the root of the WordPress installation.
	 *
	 * @param string $dir        Directory to search into.
	 * @param string $searchTerm Term to search for.
	 *
	 * @return string The absolute path of the exported file.
	 */
	public function getLastDownloadedFile( $dir, $searchTerm ) {

		$scannedDir = array_diff( scandir( $dir ), [ '..', '.' ] );
		$matches    = array_values(
			array_filter(
				$scannedDir,
				function ( $var ) use ( $searchTerm ) {
					return preg_match( "/\b$searchTerm\b/i", $var );
				}
			)
		);

		return $dir  . '/' .  end( $matches );
	}

	/**
	 * Takes the path of a .csv file and converts it to an array.
	 *
	 * @param string $filePath Path of the .csv file.
	 *
	 * @return array
	 */
	public function CSVToArray( $filePath ) {

		$file    = fopen( $filePath, 'r' );
		$headers = fgetcsv( $file );
		$data    = [];

		while ( ( $row = fgetcsv( $file ) ) !== false ) {
			$item = [];

			foreach ( $row as $key => $value ) {
				$item[ $headers[ $key ] ] = $value ?: null;
			}
			$data[] = $item;
		}
		fclose( $file );

		return $data;
	}

	/**
	 * Converts a single-site to multi-site with subdomains.
	 */
	public function convertToMultisiteWithSubdomains() {

		$this->runShellCommand( 'wp core multisite-convert --subdomains' );
	}

	/**
	 * Uses wp-cli to convert a multi-site to a normal single one by deleting the related entries in wp-config.php and resetting db.
	 */
	public function cleanupMultisite() {

		$this->runShellCommand( 'wp db reset --yes' );
		$this->runShellCommand( 'wp config delete WP_ALLOW_MULTISITE' );
		$this->runShellCommand( 'wp config delete MULTISITE' );
		$this->runShellCommand( 'wp config delete SUBDOMAIN_INSTALL' );
		$this->runShellCommand( 'wp config delete DOMAIN_CURRENT_SITE' );
		$this->runShellCommand( 'wp config delete PATH_CURRENT_SITE' );
		$this->runShellCommand( 'wp config delete SITE_ID_CURRENT_SITE' );
		$this->runShellCommand( 'wp config delete BLOG_ID_CURRENT_SITE' );
	}

	/**
	 * Hide element by ID.
	 *
	 * @param string $elementId ID of the element.
	 */
	public function hideElementById( $elementId ) {

		if ( $this->tryToSeeElement( $elementId ) ) {
			$elementId = str_replace( '#', '', $elementId );

			$this->executeJS( "document.getElementById('$elementId').style.display = 'none';" );
		}
	}

	/**
	 * Hide all elements with CSS Class provided.
	 *
	 * @param string $elementClass Class of the elements.
	 *
	 * @return void
	 */
	public function hideElementsByClass( $elementClass ) {
		$this->executeJS( sprintf( '
			const elements = document.getElementsByClassName("%s");
			const array    = Array.from(elements);
			array.forEach(hide);

			function hide( item ) {
				if (typeof item !== "undefined") {
					item.style.display = "none";
				}
			}', $elementClass )
		);
	}

	/**
	 * Hides the WordPress admin bar as it's sometimes conflicting with clicking.
	 */
	public function hideWPAdminBar() {

		$this->hideElementById( '#wpadminbar' );
	}

	/**
	 * Tries to fill in field and returns false in case it fails.
	 *
	 * @param string $field Field.
	 * @param string $value Value.
	 *
	 * @return bool
	 */
	public function tryToFillField( $field, $value ) {

		try {
			$this->fillField( $field, $value );
			return true;
		} catch ( Exception $e ) {
			return false;
		}
	}

	/**
	 * Tries to see text provided, returns false if it fails.
	 *
	 * @param string $text
	 * @param string $selector Selector - optional.
	 *
	 * @return bool
	 */
	public function tryToSee( $text, $selector = null  ) {

		try {
			$this->see( $text, $selector );
			return true;
		} catch ( Exception $e ) {
			return false;
		}
	}

	/**
	 * Tries to assert text provided isn't visible, returns false if it fails.
	 *
	 * @param string $text
	 * @param string $selector Selector - optional.
	 *
	 * @return bool
	 */
	public function tryToDontSee( $text, $selector = null  ) {

		try {
			$this->dontSee( $text, $selector );
			return true;
		} catch ( Exception $e ) {
			return false;
		}
	}

	/**
	 * Tries to clear field.
	 *
	 * @param string $field Field.
	 *
	 * @return bool
	 */
	public function tryToClearField( $field ) {
		try {
			$this->clearField( $field );
			return true;
		} catch ( Exception $e ) {
			return false;
		}
	}

	/**
	 * Tries to see if a checkbox is checked, returns false if it isn't.
	 *
	 * @param string $selector Selector for the checkbox.
	 *
	 * @return bool
	 */
	public function tryToSeeChecked( $selector ) {
		try {
			$this->seeCheckboxIsChecked( $selector );
			return true;
		} catch ( \Exception $e ) {
			return false;
		}
	}

	/**
	 * Emulates network conditions in browser.
	 *
	 * @param string $offline            Offline status, set to true to set browser offline.
	 * @param int    $latency            Latency.
	 * @param int    $downloadThroughput Download throughput.
	 * @param int    $uploadThroughput   Upload throughput.
	 * @param string $connectionType     Connection type.
	 */
	public function emulateNetworkConditions( $offline = false, $latency = 0, $downloadThroughput = -1, $uploadThroughput = -1, $connectionType = 'none' ) {

		$this->executeInSelenium(
			function ( RemoteWebDriver $webdriver ) use ( $offline, $latency, $downloadThroughput, $uploadThroughput, $connectionType ){
				$webdriver->executeCustomCommand(
					'/session/:sessionId/goog/cdp/execute',
					'POST',
					[
						'cmd'    => 'Network.emulateNetworkConditions',
						'params' => [
							'offline'            => $offline,
							'latency'            => $latency,
							'downloadThroughput' => $downloadThroughput,
							'uploadThroughput'   => $uploadThroughput,
							'connectionType'     => $connectionType,
						],
					]
				);
			}
		);
	}

	/**
	 * This method is used to just press the TAB key of the keyboard.
	 *
	 * @param string $element The element inside you wish to press the Tab. Default is body.
	 *
	 * @return void
	 */
	public function pressTabKey( $element = 'body' ) {

		$this->pressKey( $element, \Facebook\WebDriver\WebDriverKeys::TAB );
	}

	/**
	 * This method is used to just press the SPACE key of the keyboard.
	 *
	 * @param string $element The element inside you wish to press the Tab. Default is body.
	 *
	 * @return void
	 */
	public function pressSpaceKey( $element = 'body') {

		$this->pressKey( $element, \Facebook\WebDriver\WebDriverKeys::SPACE );
	}

	/**
	 * This method is used to just press the SPACE key of the keyboard.
	 *
	 * @param string $element The element inside you wish to press the Tab. Default is body.
	 *
	 * @return void
	 */
	public function pressEnterKey( $element = 'body') {

		$this->pressKey( $element, \Facebook\WebDriver\WebDriverKeys::ENTER );
	}

	/**
	 * Merge user defined arguments into defaults array.
	 *
	 * @param array|object $args Value to merge with $defaults.
	 * @param array $defaults Optional. Array that serves as the defaults. Default empty.
	 *
	 * @return array Merged user defined values with defaults.
	 */
	public function parseArgs( $args, $defaults = '' ) {
		if ( is_object( $args ) ) {
			$r = get_object_vars( $args );
		} elseif ( is_array( $args ) ) {
			$r =& $args;
		} else {
			return;
		}

		if ( is_array( $defaults ) ) {
			return array_merge( $defaults, $r );
		}

		return $r;
	}

	/**
	 * Calculate the discounted price after applying either a flat or percentage discount.
	 *
	 * @param string $price The initial Price.
	 * @param string $currency The currency symbol (like $).
	 * @param string $calculationType The type of calculation ('flat' or 'percentage').
	 * @param string $amount The amount to subtract in a flat calculation or the percentage to subtract in a percentage calculation.
	 *
	 * @return string
	 */
	public function flatOrPercentageCalculations( $price, $currency, $calculationType = 'flat', $amount ) {
		$price  = str_replace( $currency, '', $price );
		$amount = str_replace( $currency, '', $amount );

		if ( $calculationType == 'flat' ) {
			$result = $price - $amount;
		} elseif ( $calculationType == 'percentage' ) {
			$result = round( $price - ( $price * ( $amount / 100 ) ), 2 );
		}

		return $result = $currency . number_format( $result, 2 );
	}

	/*
	 * In the plugin administration screen deactivate a plugin clicking the "Deactivate" link.
	 *
	 * The method will **not** handle authentication and navigation to the plugins administration page.
	 *
	 * This function overrides the ones provided from wp-browser.
	 *
	 * @param string|array<string> $pluginSlug The plugin slug, like "hello-dolly", or a list of plugin slugs.
	 *
	 * @return void
	 * @example
	 * ```php
	 * // Deactivate one plugin.
	 * $I->loginAsAdmin();
	 * $I->amOnPluginsPage();
	 * $I->deactivatePlugin('hello-dolly');
	 * // Deactivate a list of plugins.
	 * $I->loginAsAdmin();
	 * $I->amOnPluginsPage();
	 * $I->deactivatePlugin(['hello-dolly', 'my-plugin']);
	 * ```
	 *
	 */
	public function deactivatePlugin( $pluginSlug ) {

		foreach ( (array) $pluginSlug as $plugin ) {
			$this->scrollAndClick( '//*[@data-slug="' . $plugin . '"]//span[@class="deactivate"]/a' );
		}
	}

	/**
	 * In the plugin administration screen activates one or more plugins clicking the "Activate" link.
	 *
	 * The method will **not** handle authentication and navigation to the plugins administration page.
	 *
	 * This function overrides the ones provided from wp-browser.
	 *
	 * @param string|array<string> $pluginSlug The plugin slug, like "hello-dolly" or a list of plugin slugs.
	 *
	 * @return void
	 * @example
	 * ```php
	 * // Activate a plugin.
	 * $I->loginAsAdmin();
	 * $I->amOnPluginsPage();
	 * $I->activatePlugin('hello-dolly');
	 * // Activate a list of plugins.
	 * $I->loginAsAdmin();
	 * $I->amOnPluginsPage();
	 * $I->activatePlugin(['hello-dolly','another-plugin']);
	 * ```
	 *
	 */
	public function activatePlugin( $pluginSlug ) {

		$plugins = (array) $pluginSlug;
		foreach ( $plugins as $plugin ) {
			$this->scrollAndClick( '//*[@data-slug="' . $plugin . '"]//span[@class="activate"]/a' );
		}
	}

	/**
	 * Retrieves and returns the current branch name from a stored file.
	 *
	 * @return string The name of the current branch.
	 */
	public function returnCurrentBranchName() {

		$filePath = __DIR__ . '/branchName.txt';

		$fileContent = file_get_contents( $filePath );

		$branchName = str_replace( 'BRANCH_NAME: ', '', $fileContent );
		$branchName = trim( $branchName );

		return $branchName;
	}

	/**
	 * Disable the unsaved changes alert on the page.
	 *
	 * This method injects JavaScript to stop the 'beforeunload' event's
	 * immediate propagation, thereby preventing any unsaved changes alert
	 * from being shown when attempting to leave the page.
	 */
	public function disableUnsavedChangesAlert() {

		$this->executeJS( "window.addEventListener('beforeunload', function(event) { event.stopImmediatePropagation(); }, true);" );
	}

	/**
	 * Removes the span from a currency element.
	 *
	 * @see https://github.com/awesomemotive/wpforms-plugin/issues/12036
	 *
	 * @return void
	 */
	public function maybeRemoveSpanFromCurrency() {
		$this->executeJS( "
        let spans = document.querySelectorAll('span.wpforms-currency-symbol');
        spans.forEach(span => {
            let currencyContent = span.textContent;

            // Append the currency content to the previous sibling (text node)
            if (span.previousSibling && span.previousSibling.nodeType === Node.TEXT_NODE) {
                span.previousSibling.textContent += currencyContent;
            } else {
                // If there is no previous text node, create one. In case there is no price
                let textNode = document.createTextNode(currencyContent);
                span.parentNode.insertBefore(textNode, span);
            }
            span.remove();
        });
    " );
	}

	/**
	 * Clears the debug log.
	 * This is useful to ensure that the log does not contain any entries from previous tests.
	 * @return void
	 */
	public function clearDebugLog() {

		$debugLogPath = '../debug.log';
		if ( file_exists( $debugLogPath ) ) {
			file_put_contents( $debugLogPath, '' );
		}

	}

	/**
	 * Loads WordPress using the path from the .env.local file.
	 *
	 * Checks for WP_ABSOLUTE_PATH environment variables.
	 * Throws an exception if wp-load.php cannot be found.
	 *
	 * @throws Exception
	 */
	public function loadWordPress() {
		if ( ! defined( 'ABSPATH' ) ) {
			$wp_root      = getenv( 'WP_ABSOLUTE_PATH' );
			$wp_load_path = $wp_root ? rtrim( $wp_root, '/' ) . '/wp-load.php' : null;
			if ( ! $wp_load_path || ! file_exists( $wp_load_path ) ) {
				throw new Exception( 'wp-load.php not found. Set WP_ABSOLUTE_PATH in your .env.local file.' );
			}
			require_once $wp_load_path;
		}
	}
}


