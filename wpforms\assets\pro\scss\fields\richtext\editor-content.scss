// Additional CSS for *inside* TinyMCE iframe.
// This file is enqueued in class-richtext.php.

.mce-content-body[class*='wpforms-'] {
	li {
		list-style-position: outside;

		&[style='text-align: right;'],
		&[style='text-align: center;'],
		&[style='text-align: justify;'] {
			list-style-position: inside;
		}
	}

	p {
		img {
			display: block;
		}
	}

	.aligncenter,
	.alignnone {
		clear: both;
		// Make the same margins as alingleft/alignright.
		margin-top: 0.5em;
		margin-bottom: 0.5em;
	}
}
