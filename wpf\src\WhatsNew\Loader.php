<?php

namespace WPForms\DevTools\WhatsNew;

use WPForms\DevTools\ACF\ACF;

/**
 * What's New tool loader class.
 *
 * @since 0.39
 */
final class Loader {

	/**
	 * Have the only available instance of the class.
	 *
	 * @since 0.39
	 *
	 * @var Loader
	 */
	private static $instance;

	/**
	 * Get instance.
	 *
	 * @since 0.39
	 *
	 * @return Loader
	 */
	public static function get_instance(): Loader {

		if ( ! isset( self::$instance ) && ! ( self::$instance instanceof self ) ) {
			self::$instance = new Loader();
		}

		return self::$instance;
	}

	/**
	 * Constructor.
	 *
	 * @since 0.39
	 */
	public function __construct() {

		new ACF();

		/**
		 * Enable the What's New tool.
		 *
		 * @since 0.39
		 *
		 * @param bool $enable Whether to enable the What's New tool. Default is false.
		 */
		if ( ! apply_filters( 'wpf_whats_new_enable', false ) ) { // phpcs:ignore WPForms.PHP.ValidateHooks.InvalidHookName
			return;
		}

		new Taxonomies();
		new WhatsNew();
	}
}
