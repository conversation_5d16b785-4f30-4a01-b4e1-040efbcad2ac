body,
.body {
	height: 100% !important;
	margin: 0;
	padding: 0;
	width: 100% !important;
	min-width: 100%;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-font-smoothing: antialiased !important;
	-moz-osx-font-smoothing: grayscale !important;
}

img {
	outline: none;
	text-decoration: none;
	-ms-interpolation-mode: bicubic;
	width: auto;
	max-width: 100%;
	clear: both;
	display: block;
}

a img {
	border: none;
}

p {
	margin: 0 0 10px 0;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

td {
	word-wrap: break-word;
	-webkit-hyphens: auto;
	-moz-hyphens: auto;
	hyphens: auto;
	border-collapse: collapse !important;
}

table,
tr,
td {
	padding: 0;
	vertical-align: top;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	margin: 0;
	padding: 0;
}

.ReadMsgBody,
.ExternalClass {
	width: 100%;
}

.ExternalClass {
	width: 100%;
}

.ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
	line-height: 100%;
}

table,
td {
	mso-table-lspace: 0pt;
	mso-table-rspace: 0pt;
}

#outlook a {
	padding: 0;
}

img {
	-ms-interpolation-mode: bicubic;
}

body, table, td, p, a, li, blockquote {
	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%;
}
