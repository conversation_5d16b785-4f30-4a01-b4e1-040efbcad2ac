<?php
/**
 * Plugin Name: WPForms Branch Switcher
 * Plugin URI:  https://wpforms.com
 * Description: Custom internal developer tool for WPForms. If you are on a non-local host, you can <a href="https://github.com/settings/tokens">generate a token</a> that has the <code>read</code> scope, and add <code>define( 'WPF_BRANCH_SWITCHER_TOKEN', "your-key" ); define( 'WPF_BRANCH_SWITCHER_USER', "your-github-username" );</code> in wp-config.php.
 * Author:      WPForms
 * Version:     0.2
 */

/**
 * Plugin version.
 *
 * @since 0.1
 */
const WPFORMS_BRANCH_SWITCHER_VERSION = '0.2';

/**
 * Plugin Folder Path.
 *
 * @since 0.1
 */
define( 'WPFORMS_BRANCH_SWITCHER_DIR', plugin_dir_path( __FILE__ ) );

/**
 * Plugin Folder URL.
 *
 * @since 0.1
 */
define( 'WPFORMS_BRANCH_SWITCHER_URL', plugin_dir_url( __FILE__ ) );

/**
 * Git Branch Switcher service.
 *
 * @since 0.1
 */
class WPFormsBranchSwitcher {

	/**
	 * Configuration.
	 *
	 * @since 0.1
	 *
	 * @var array
	 */
	protected $cfg = [
		'dir'          => WP_PLUGIN_DIR,
		'repo_url_git' => 'https://github.com/awesomemotive/wpforms-plugin.git',
		'repo_url_dir' => 'https://github.com/awesomemotive/wpforms-plugin/',
	];

	/**
	 * Latest Git command.
	 *
	 * @since 0.1
	 *
	 * @var string
	 */
	public $git_cmd = '';

	/**
	 * Git commands log.
	 *
	 * @since 0.1
	 *
	 * @var array
	 */
	public $git_cmd_log = [];

	/**
	 * Result of the latest Git command.
	 *
	 * @since 0.1
	 *
	 * @var array
	 */
	public $git_result = [];

	/**
	 * Git results log.
	 *
	 * @since 0.1
	 *
	 * @var array
	 */
	public $git_result_log = [];

	/**
	 * Latest Git command error.
	 *
	 * @since 0.1
	 *
	 * @var int
	 */
	public $git_err = 0;

	/**
	 * Composer errors log.
	 *
	 * @since 0.1
	 *
	 * @var array
	 */
	public $composer_errors_log = [];

	/**
	 * `disable_functions` from php.ini.
	 *
	 * @since 0.1
	 *
	 * @var array
	 */
	public $php_disable_functions = [];

	/**
	 * Constructor.
	 *
	 * @since 0.1
	 */
	public function __construct() {

		$this->php_disable_functions = explode( ',', ini_get( 'disable_functions' ) );

		add_action( 'admin_bar_menu', [ $this, 'admin_bar_menu' ], 1000 );

		$footer_style_hook  = is_admin() ? 'admin_footer' : 'wp_footer';
		$footer_script_hook = is_admin() ? 'admin_enqueue_scripts' : 'wp_enqueue_scripts';


		add_action( $footer_script_hook, [ $this, 'script' ] );
		add_action( $footer_style_hook, [ $this, 'styles' ] );

		add_action( 'wp_ajax_wpf_brsw_update_local', [ $this, 'ajax_update_local' ] );
		add_action( 'wp_ajax_wpf_brsw_checkout_branch', [ $this, 'ajax_checkout_branch' ] );
	}

	/**
	 * Admin Bar Menu item.
	 *
	 * @since 0.1
	 */
	public function admin_bar_menu() {

		global $wp_admin_bar;

		$latest_commit_id = $this->git_get_latest_commit_id();
		$current_branch   = $this->git_get_current_branch();

		$nodes = [
			[
				'id'    => 'wpf-brsw',
				'title' => $current_branch,
				'href'  => '#',
			],
			[
				'id'     => 'wpf-brsw-latest-commit',
				'parent' => 'wpf-brsw',
				'title'  => 'Latest commit: <a href="' . $this->cfg['repo_url_dir'] . 'commit/' . $latest_commit_id . '" class="inline mono" title="Open on GitHub" target="_blank">' . substr( $latest_commit_id, 0, 7 ) . '</a>',
			],
			[
				'id'     => 'wpf-brsw-update-local',
				'parent' => 'wpf-brsw',
				'title'  => 'Update local branch',
				'href'   => '#',
			],
			[
				'id'     => 'wpf-brsw-branch',
				'parent' => 'wpf-brsw',
				'title'  => 'Switch branch:',
				'href'   => '#',
			],
		];

		$branches = $this->git_get_branches();

		if ( ! empty( $branches ) ) {
			foreach ( $branches as $branch ) {
				if ( $current_branch === $branch ) {
					continue;
				}
				$nodes[] = [
					'id'     => 'wpf-brsw-branch-' . sanitize_key( $branch ),
					'parent' => 'wpf-brsw-branch',
					'title'  => sanitize_text_field( $branch ),
					'href'   => '#',
				];
			}
		}

		foreach ( $nodes as $node ) {
			$wp_admin_bar->add_node( $node );
		}
	}

	/**
	 * Write something to the log file.
	 * Used for logging results of the `exec()`.
	 *
	 * @since 0.1
	 *
	 * @param string $string Content that should be written to log file.
	 */
	public function log( $string ) {

		if ( ! defined( 'WPFORMS_BRANCH_SWITCHER_LOG' ) || ! WPFORMS_BRANCH_SWITCHER_LOG || empty( $string ) ) {
			return;
		}

		$file = 'wpforms-branch-switcher-log-' . wp_hash( 'log' ) . '.log';

		file_put_contents( WPFORMS_BRANCH_SWITCHER_DIR . $file, current_time( 'mysql' ) . ' ' . (string) $string . "\n", FILE_APPEND ); // phpcs:ignore
	}

	/**
	 * Ajax endpoint. Update local branch from remote origin.
	 *
	 * @since 0.1
	 */
	public function ajax_update_local() {

		// Run a security check.
		check_ajax_referer( 'wpf_brsw', 'nonce' );

		$update_result = $this->git_update_local_branch();

		if ( empty( $update_result ) ) {
			wp_send_json_error(
				[
					'msg' => implode( "\n", $this->git_result ),
				]
			);
		}

		$this->send_composer_json_result( $this->run_composer_install(), 'Finished pulling down the latest updates from Github.' );
	}

	/**
	 * Ajax endpoint. Checkout branch.
	 *
	 * @since 0.1
	 */
	public function ajax_checkout_branch() {

		// Run a security check.
		check_ajax_referer( 'wpf_brsw', 'nonce' );

		$branch = ! empty( $_POST['branch'] ) ? sanitize_text_field( wp_unslash( $_POST['branch'] ) ) : '';

		$checkout_result = $this->git_checkout_branch( $branch );

		if ( empty( $checkout_result ) ) {
			wp_send_json_error(
				[
					'msg' => implode( "\n", $this->git_result ),
				]
			);
		}

		$this->send_composer_json_result( $this->run_composer_install(), 'The branch `' . $branch . '` switched correctly.' );
	}

	/**
	 * Execute Git command.
	 *
	 * @since 0.1
	 *
	 * @param string $cmd Git arguments string, for example
	 *                    $this->git( 'rev-parse --abbrev-ref HEAD' ).
	 *
	 * @return bool|array Command execution result as array or false if `exec()` is disabled.
	 */
	public function git( $cmd ) {

		if ( in_array( 'exec', $this->php_disable_functions, true ) ) {
			$this->git_result = [ "The `exec()` function is disabled.\n`git` can't be executed." ];
			$this->git_err    = 1;

			return false;
		}

		if ( empty( $cmd ) ) {
			return [];
		}

		$cmd = preg_replace( '/^git /', '', (string) $cmd );

		// Switch to the proper directory.
		chdir( $this->cfg['dir'] );

		$this->git_cmd       = 'git ' . $cmd . ' 2>&1';
		$this->git_cmd_log[] = $this->git_cmd;

		if ( defined( 'WPF_SSH_KEY' ) ) {
			putenv( 'GIT_SSH_COMMAND=ssh -i ' . WPF_SSH_KEY ); // phpcs:ignore
		}

		exec( $this->git_cmd, $out, $err ); // phpcs:ignore

		$this->git_result = $out;
		$this->git_err    = $err;

		$this->log( implode( "\n", [ $this->git_cmd ] + $out ) );
		$this->git_result_log[ current_time( 'mysql' ) . ' ' . $this->git_cmd ] = $out;

		return $out;
	}

	/**
	 * Get current branch.
	 *
	 * @since 0.1
	 *
	 * @return string Current branch name.
	 */
	public function git_get_current_branch() {

		$git_result = $this->git( 'rev-parse --abbrev-ref HEAD' );

		if ( $this->git_err > 0 ) {
			return '';
		}

		return sanitize_text_field( end( $git_result ) );
	}

	/**
	 * Get latest commit id.
	 *
	 * @since 0.1
	 *
	 * @return string Latest commit id.
	 */
	public function git_get_latest_commit_id() {

		$git_result = $this->git( 'rev-parse HEAD' );

		if ( $this->git_err > 0 ) {
			return '';
		}

		return end( $git_result );
	}

	/**
	 * Get branches.
	 *
	 * @since 0.1
	 *
	 * @return false|array Array of branches or false on error.
	 */
	public function git_get_branches() {

		// If we're using the access token, we'll need to set the upstream to have it.
		if ( defined( 'WPF_BRANCH_SWITCHER_TOKEN' ) && defined( 'WPF_BRANCH_SWITCHER_USER' ) ) {
			$this->git_set_remote_url_for_access_token_user();
		}

		if ( ! (bool) get_transient( 'wpf_dev_branch_swither_fetch_branches' ) ) {
			$this->git( 'fetch origin' );
			if ( $this->git_err > 0 ) {
				return false;
			}
			set_transient( 'wpf_dev_branch_swither_fetch_branches', 'true', HOUR_IN_SECONDS / 4 );
		}

		$git_result = $this->git( 'branch -a' );

		if ( $this->git_err > 0 || ! is_array( $git_result ) ) {
			return false;
		}

		$branches = [];

		foreach ( $git_result as $line ) {
			if ( strpos( $line, 'HEAD' ) !== false || strpos( $line, '* ' ) !== false ) {
				continue;
			}
			$branches[] = str_replace( 'remotes/origin/', '', sanitize_text_field( $line ) );
		}

		sort( $branches );

		return array_unique( $branches );
	}

	/**
	 * Update local branch.
	 *
	 * @since 0.1
	 *
	 * @return false|array Git result lines as array or false on error.
	 */
	public function git_update_local_branch() {

		$branch = $this->git_get_current_branch();

		$this->git( 'fetch origin ' . $branch );

		if ( $this->git_err > 0 ) {
			return false;
		}

		$this->git( 'reset --hard origin/' . $branch );

		if ( $this->git_err > 0 ) {
			return false;
		}

		return $this->git_result;
	}

	/**
	 * Re-sets the remote url for users that have the access token constants set.
	 *
	 * @since 0.2
	 *
	 * @return bool False if credentials are not set, true if we attempted to run the git commands.
	 */
	public function git_set_remote_url_for_access_token_user() {

		// Verify the access token constants are set. If not, we just fail silently.
		if ( ! defined( 'WPF_BRANCH_SWITCHER_TOKEN' ) || ! defined( 'WPF_BRANCH_SWITCHER_USER' ) ) {
			return false;
		}

		// Disable terminal prompts.
		putenv( 'GIT_TERMINAL_PROMPT=0' ); // phpcs:ignore

		// Build up our user/token string.
		$git_user_token = 'https://' . WPF_BRANCH_SWITCHER_USER . ':' . WPF_BRANCH_SWITCHER_TOKEN . '@';
		$remote         = str_replace( 'https://', $git_user_token, $this->cfg['repo_url_dir'] );

		// Re-set the origin to use our token.
		$this->git( 'remote set-url origin ' . $remote );

		return true;
	}

	/**
	 * Checkout branch.
	 *
	 * @since 0.1
	 *
	 * @param string $branch Branch.
	 *
	 * @return false|array Git result lines as array or false on error.
	 */
	public function git_checkout_branch( $branch ) {

		// Discard any local changes before checkout.
		$this->git( 'reset --hard HEAD' );
		$this->git( 'clean -fd' );

		$this->git( 'checkout ' . $branch );

		if ( $this->git_err > 0 ) {
			return false;
		}

		return $this->git_result;
	}

	/**
	 * Run composer install foreach composer dir.
	 *
	 * @since 0.1
	 *
	 * @return bool|array Result of composer install execution..
	 */
	public function run_composer_install() {

		if ( ! in_array( 'set_time_limit', $this->php_disable_functions, true ) ) {
			set_time_limit( 0 );
		}

		if ( in_array( 'exec', $this->php_disable_functions, true ) ) {
			return false;
		}

		$composer_cmd  = 'composer install';
		$composer_dirs = $this->get_composer_dirs();

		$result                    = [];
		$this->composer_errors_log = [];

		foreach ( $composer_dirs as $dir ) {

			// Switch to the proper directory.
			chdir( $dir );

			exec( $composer_cmd. ' 2>&1', $out, $err ); // phpcs:ignore

			$result[] = [
				'dir'        => $dir,
				'output'     => $out,
				'return_var' => $err,
			];

			$this->log( implode( "\n", [ $dir . ' ' . $composer_cmd ] + $out ) );

			if ( $err > 0 ) {
				$this->composer_errors_log = array_merge( $this->composer_errors_log, $out );
			}
		}

		return $result;
	}


	/**
	 * Get all `composer` dirs.
	 *
	 * @since 0.1
	 *
	 * @return array Directories that conttains `composer.lock` file.
	 */
	public function get_composer_dirs() {

		$composer_lock_files = glob( wp_normalize_path( $this->cfg['dir'] ) . '/{composer.lock,wpf*/composer.lock}', GLOB_BRACE );

		return array_map(
			static function( $file ) {

				return str_replace( 'composer.lock', '', $file );
			},
			$composer_lock_files
		);
	}

	/**
	 * Send json result after composer install execution.
	 *
	 * @param array  $composer_result Result of $this->run_composer_install().
	 * @param string $success_msg     Success message.
	 *
	 * @since 0.1
	 */
	public function send_composer_json_result( $composer_result, $success_msg ) {

		if ( empty( $composer_result ) ) {
			wp_send_json_error(
				[
					'msg' => "The `exec()` function is disabled.\n`composer install` can't be executed.",
				]
			);
		}

		if ( ! empty( $this->composer_errors_log ) ) {
			wp_send_json_success(
				[
					'msg'                 => $success_msg . "\nError(s) happened with `composer install`:\n" . implode( "\n", $this->composer_errors_log ) . "\n",
					'composer_errors_log' => $this->composer_errors_log,
					'composer_result'     => $composer_result,
				]
			);
		}

		wp_send_json_success(
			[
				'msg'             => $success_msg . "\n`composer install` has been executed without errors.\n",
				'composer_result' => $composer_result,
			]
		);
	}

	/**
	 * Include JavaScript file.
	 *
	 * @since 0.1
	 */
	public function script() {

		wp_enqueue_script( 'wpf-branch-switcher', WPFORMS_BRANCH_SWITCHER_URL . 'wpforms-branch-switcher.js', [ 'jquery' ], WPFORMS_BRANCH_SWITCHER_VERSION, true );

		wp_localize_script(
			'wpf-branch-switcher',
			'wpfBrsw',
			[
				'nonce'        => wp_create_nonce( 'wpf_brsw' ),
				'ajaxurl'      => admin_url( 'admin-ajax.php' ),
				'ok'           => 'Ok',
				'cancel'       => 'Cancel',
				'updating'     => 'Updating local branch. Please wait...',
				'switching'    => 'Switching branch. Please wait...',
				'clickrefresh' => 'Click `Ok` button to refresh the page',
				'erroroccured' => 'Unfortunately, an error occurred:',
			]
		);
	}

	/**
	 * Inline CSS.
	 *
	 * @since 0.1
	 */
	public function styles() {
		?>
		<style>

			#wpadminbar #wp-admin-bar-wpf-brsw .inline {
				display: inline;
			}

			#wpadminbar #wp-admin-bar-wpf-brsw .mono {
				font-family: monospace;
				color: #f1f1f1;
			}

			#wpadminbar #wp-admin-bar-wpf-brsw a.mono:hover {
				color: #F18F2D;
			}

			#wpadminbar #wp-admin-bar-wpf-brsw .wpf-brsw-branch-wrapper {
				vertical-align: top;
				padding-bottom: 0;
			}

			#wpadminbar #wp-admin-bar-wpf-brsw .wpf-brsw-branch-checkout {
				display: inline-block;
				width: 20% !important;
				float: none !important;
				vertical-align: top;
				padding: 2px 5px !important;
				height: auto !important;
				margin-left: 4px;
			}

			#wp-admin-bar-wpf-brsw-branch {
				padding-bottom: 0;
			}

			#wp-admin-bar-wpf-brsw-branch .ab-sub-wrapper {
				max-height: calc( 100vh - 100px );
				overflow-y: auto;
			}

			#wpf-branch-switcher-loading {
				top: 0;
				left: 0;
				position: fixed;
				width: 100%;
				height: 100%;
				background-color: rgba(255,255,255,0.5);
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				z-index: +999999;
			}

			#wpf-branch-switcher-loading span {
				display: block;
				font-size: 15px;
				margin-top: 30px;
			}

			.lds-ring {
				display: block;
				position: relative;
				width: 64px;
				height: 64px;
			}
			.lds-ring div {
				box-sizing: border-box;
				display: block;
				position: absolute;
				width: 51px;
				height: 51px;
				margin: 6px;
				border: 4px solid #808080;
				border-radius: 50%;
				animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
				border-color: #808080 transparent transparent transparent;
			}
			.lds-ring div:nth-child(1) {
				animation-delay: -0.45s;
			}
			.lds-ring div:nth-child(2) {
				animation-delay: -0.3s;
			}
			.lds-ring div:nth-child(3) {
				animation-delay: -0.15s;
			}
			@keyframes lds-ring {
				0% {
					transform: rotate(0deg);
				}
				100% {
					transform: rotate(360deg);
				}
			}
		</style>
		<?php
	}

}

new WPFormsBranchSwitcher();
