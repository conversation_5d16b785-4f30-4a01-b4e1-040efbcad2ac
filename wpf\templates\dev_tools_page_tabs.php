<?php
/**
 * Base template for the tab bar.
 *
 * @var array  $parent_tabs A list of registered parent tabs.
 * @var array  $child_tabs  A list of registered child tabs.
 * @var string $active      Active tab key/slug.
 */

foreach ( $parent_tabs as $tab_key => $tab ) { // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited

	$class     = $tab_key === $active ? 'active' : '';
	$has_child = isset( $tab['has_child'] ) ? 'has-child' : '';
	?>

	<li class="<?php echo esc_attr( $has_child ); ?>">
		<a href="<?php echo esc_url( $tab['url'] ); ?>" class="<?php echo esc_attr( $class ); ?>">
			<?php echo esc_html( $tab['title'] ); ?>
		</a>

		<?php if ( $has_child ) : ?>
			<ul class="sub-menu">
				<?php foreach ( $child_tabs as $key => $child_tab ) : ?>
					<?php
					if ( $child_tab['parent'] === $tab_key ) :
						$class = $key === $active ? 'active' : '';
						?>
						<li>
							<a href="<?php echo esc_url( $child_tab['url'] ); ?>" class="<?php echo esc_attr( $class ); ?>">
								<?php echo esc_html( $child_tab['title'] ); ?>
							</a>
						</li>
					<?php endif; ?>
				<?php endforeach; ?>
			</ul>
		<?php endif; ?>
	</li>

	<?php
}
