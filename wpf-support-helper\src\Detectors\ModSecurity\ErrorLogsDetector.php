<?php
/**
 * ModSecurity Error Logs Detector.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper\Detectors\ModSecurity;

use Exception;
use WP_Filesystem_Base; // phpcs:ignore WPForms.PHP.UseStatement.UnusedUseStatement
use WPForms\SupportHelper\Detectors\Base\AbstractDetector;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Error Log Analysis for ModSecurity.
 *
 * @since {VERSION}
 */
class ErrorLogsDetector extends AbstractDetector {


	/**
	 * Run the detection method.
	 *
	 * @since {VERSION}
	 *
	 * @return array Detection result.
	 *
	 * @throws Exception When file operations fail or security validation fails.
	 */
	public function detect(): array {

		$detected_entries = [];

		// Initialize WordPress filesystem API.
		$filesystem = $this->get_wp_filesystem();

		if ( ! $filesystem ) {
			return [
				'detected'        => false,
				'reason'          => __( 'WordPress filesystem API not available', 'wpf-support-helper' ),
				'additional_info' => [],
			];
		}

		// Check for modsec_audit.log in common ModSecurity directories.
		$modsec_audit_directories = [
			'/var/log/apache2/',
			'/var/log/httpd/',
			'/var/log/nginx/',
			'/var/log/modsec/',
			'/var/log/modsecurity/',
			'/usr/local/apache2/logs/',
			'/etc/httpd/logs/',
			'/var/log/',
			'/tmp/',
			'/opt/modsecurity/var/log/',
			'/opt/homebrew/var/log/nginx/',
		];

		foreach ( $modsec_audit_directories as $directory ) {
			$log_path = rtrim( $directory, '/' ) . '/modsec_audit.log';

			// Use WordPress filesystem API to check file existence and readability.
			if ( $this->is_file_accessible( $filesystem, $log_path ) ) {
				$log_content          = $this->read_log_file( $log_path, 1000 ); // Read last 1000 lines.
				$detected_entries_log = $this->find_detection_patterns_in_log( $log_content );

				if ( ! empty( $detected_entries_log ) ) {
					$detected_entries[ $log_path ] = $detected_entries_log;
				}
			}
		}

		if ( ! empty( $detected_entries ) ) {
			return [
				'detected'        => true,
				'reason'          => __( 'ModSecurity detected in modsec_audit.log files', 'wpf-support-helper' ),
				'additional_info' => $detected_entries,
			];
		}

		return [
			'detected'        => false,
			'reason'          => __( 'No modsec_audit.log files found or no ModSecurity entries detected', 'wpf-support-helper' ),
			'additional_info' => [],
		];
	}

	/**
	 * Get the detector name.
	 *
	 * @since {VERSION}
	 *
	 * @return string Detector name.
	 */
	public function get_name(): string {

		return __( 'Error Log Analysis', 'wpf-support-helper' );
	}

	/**
	 * Get the detector description.
	 *
	 * @since {VERSION}
	 *
	 * @return string Detector description.
	 */
	public function get_description(): string {

		return __( 'Check modsec_audit.log files for ModSecurity entries', 'wpf-support-helper' );
	}

	/**
	 * Read log file content (last N lines).
	 *
	 * @since {VERSION}
	 *
	 * @param string $file_path Path to log file.
	 * @param int    $lines     Number of lines to read from end.
	 *
	 * @return string Log content.
	 *
	 * @throws Exception When file operations fail or security validation fails.
	 */
	private function read_log_file( string $file_path, int $lines = 100 ): string {

		// Validate input length to prevent buffer overflow attacks.
		if ( strlen( $file_path ) > 4096 ) { // 4KB max path length.
			$this->log_error( 'File path too long: ' . strlen( $file_path ) . ' bytes' );

			return '';
		}

		// Initialize WordPress filesystem API.
		$filesystem = $this->get_wp_filesystem();

		if ( ! $filesystem ) {
			$this->log_error( 'WordPress filesystem API not available for file: ' . $file_path );

			return '';
		}

		// Use WordPress filesystem API to check file accessibility.
		if ( ! $this->is_file_accessible( $filesystem, $file_path ) ) {
			return '';
		}

		// Validate input parameters.
		$lines = max( 1, min( $lines, 10000 ) ); // Limit between 1 and 10000 lines.

		// Check file size limit (10MB max) using WordPress filesystem API.
		$max_file_size = 10 * 1024 * 1024; // 10MB.
		$file_size     = $filesystem->size( $file_path );

		// Handle case where size() returns false (file doesn't exist or can't be accessed).
		if ( $file_size === false ) {
			$this->log_error( 'Unable to determine file size: ' . $file_path );

			return '';
		}

		if ( $file_size > $max_file_size ) {
			$this->log_error( 'File too large: ' . $file_path . ' (' . $file_size . ' bytes)' );

			return '';
		}

		// Check available memory before reading large files.
		// This prevents memory exhaustion when reading very large log files.
		if ( ! $this->check_memory_limit( $file_size ) ) {
			$this->log_error( 'Insufficient memory to read file: ' . $file_path );

			return '';
		}

		// For security reasons, avoid using exec() and use WordPress-approved methods
		// Instead of using tail command, we'll read the file using WordPress filesystem API
		// and extract the last N lines programmatically to maintain WordPress standards.

		// Read file content using WordPress filesystem API - this is the WordPress-approved method
		// for file operations instead of using direct PHP file functions like file_get_contents().
		$file_content = $filesystem->get_contents( $file_path );

		// Handle case where get_contents() returns false.
		if ( $file_content === false ) {
			$this->log_error( 'Failed to read file using WordPress filesystem API: ' . $file_path );

			return '';
		}

		// If file is empty, return empty string.
		if ( empty( $file_content ) ) {
			return '';
		}

		// Split content into lines and get the last N lines.
		$file_lines = explode( "\n", $file_content );
		$last_lines = array_slice( $file_lines, - $lines );

		return implode( "\n", $last_lines );
	}

	/**
	 * Find ModSecurity entries in log content.
	 *
	 * @since {VERSION}
	 *
	 * @param string $log_content Log content to search.
	 *
	 * @return array Found ModSecurity entries.
	 */
	private function find_detection_patterns_in_log( string $log_content ): array {

		if ( empty( $log_content ) ) {
			return [];
		}

		$modsec_patterns = [
			'modsecurity',
			'mod_security',
			'ModSecurity',
			'SecRule',
			'SecAction',
			'Access denied with code 403',
			'ModSecurity: Warning',
			'ModSecurity: Access denied',
		];

		$found_entries = [];
		$lines         = explode( "\n", $log_content );

		foreach ( $lines as $line_number => $line ) {
			foreach ( $modsec_patterns as $pattern ) {
				if ( stripos( $line, $pattern ) === false ) {
					continue;
				}

				$found_entries[] = [
					'line'    => $line_number + 1,
					'content' => trim( $line ),
					'pattern' => $pattern,
				];

				break; // Only record one pattern match per line.
			}
		}

		return $found_entries;
	}

	/**
	 * Check if detector can run in current environment.
	 *
	 * Error logs detector requires basic file system functions.
	 *
	 * @since {VERSION}
	 *
	 * @return bool True if detector can run, false otherwise.
	 */
	public function supports(): bool {

		return function_exists( 'file_exists' ) &&
			function_exists( 'is_readable' ) &&
			function_exists( 'file_get_contents' );
	}

	/**
	 * Get detector execution priority.
	 *
	 * Error logs have lower priority as they involve file I/O operations.
	 *
	 * @since {VERSION}
	 *
	 * @return int Priority level (1-100, default: 50).
	 */
	public function get_priority(): int {

		return 60;
	}

	/**
	 * Get detector requirements.
	 *
	 * Error logs detector requires file system functions and optionally exec.
	 *
	 * @since {VERSION}
	 *
	 * @return array Array of requirements with keys: functions, extensions, files, etc.
	 */
	public function get_requirements(): array {

		return [
			'functions'  => [ 'file_exists', 'is_readable', 'file_get_contents', 'filesize' ],
			'extensions' => [],
			'files'      => [
				'/var/log/apache2/error.log',
				'/var/log/httpd/error_log',
				'/var/log/nginx/error.log',
				WP_CONTENT_DIR . '/debug.log',
			],
			'constants'  => [ 'WP_CONTENT_DIR' ],
		];
	}
}
