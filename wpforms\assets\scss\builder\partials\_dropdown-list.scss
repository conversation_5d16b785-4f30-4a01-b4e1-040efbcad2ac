// Form Builder styles.
//
// Dropdown list.
//
// @since 1.8.4

.wpforms-builder-dropdown-list {
	position: absolute;
	display: flex;
	flex-direction: column;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	z-index: 100;
	width: 370px;
	background: $color_white;
	border-radius: $border_radius_s;
	border: 1px solid $color_secondary_text;
	box-shadow: 0 4px 10px 0 rgba( 0, 0, 0, 0.15 );
	visibility: visible;
	overflow: hidden;
	transition: visibility $transition_fast,
		opacity $transition_fast ease-in;

	&.closed {
		opacity: 0;
		visibility: hidden;
	}

	.title {
		color: $color_primary_text;
		background: $color_light_background;
		border-bottom: 1px solid $color_divider;
		padding: $spacing_s $spacing_s + 2px;
		font-weight: 500;
		text-decoration: none;
		cursor: default;
	}

	ul {
		display: flex;
		flex-direction: column;
		max-height: 170px;
		overflow-x: hidden;
		overflow-y: auto;

		li {
			display: flex;
			flex-direction: row;
			flex-shrink: 0;
			gap: 10px;
			justify-content: space-between;
			padding: $spacing_s $spacing_s + 2px;
			background: $color_white;
			margin: 0;
			border-bottom: 1px solid $color_light_background_hover;
			cursor: pointer;
			color: $color_secondary_text;

			&:hover {
				color: $color_white;
				background: $color_blue;

				.grey {
					color: $color_white;
				}
			}

			.grey {
				color: $color_secondary_text;
			}
		}
	}

	& .wpforms-builder-dropdown-list-search-container {
		position: relative;
		padding: $spacing_s;
		border-bottom: 1px solid $color_divider;

		& input {
			width: 100%;

			&::-webkit-search-cancel-button {
				-webkit-appearance: none;
				appearance: none;
			}
		}

		& .wpforms-builder-dropdown-list-search-close {
			display: none;
			position: absolute;
			inset-inline-end: $spacing_m;
			top: 50%;
			transform: translateY(-50%);
			cursor: pointer;
			color: $color_hint;
			font-size: 16px;

			&.active {
				display: block;
			}

			&:hover {
				color: $color_fields_secondary_text;
			}
		}
	}

	.wpforms-no-results {
		display: none;
		padding: $spacing_s;
		color: $color_secondary_text;
	}
}

// Dropdown list for the field options.
.wpforms-field-option-row {
	.wpforms-builder-dropdown-list {
		left: -5px;
		width: auto;
		right: -5px;
	}
}
