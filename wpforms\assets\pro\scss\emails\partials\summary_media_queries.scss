@import '../../../../scss/emails/partials/summary_media_queries';
@import '../../../../scss/emails/partials/layout_media_queries';

@media only screen and (max-width: 700px) {
	.license-banner {
		padding: 0 30px 30px 30px !important;
	}

	.license-banner-content {
		padding: 30px !important;
	}
}

// Dark mode.
@mixin darkModeStyles() {
	.license-banner-content {
		background-color: #373031 !important;

		.button-link,
		h5 {
			color: $darkprimary !important;
		}

		p,
		a {
			color: #d5d5d6 !important;
		}
	}

	table.license-expire_soon {
		background-color: #37312f !important;
	}
}

// Add support for dark mode.
@media (prefers-color-scheme: dark) {
	@include darkModeStyles();
}

// Add support for legacy Outlook dark mode.
[data-ogsc] {
	@include darkModeStyles();
}
