.tooltipster-base.tooltipster-sidetip .tooltipster-content {
  font-size: 14px;
  padding: 8px 16px;
}

.tooltipster-base.tooltipster-sidetip .tooltipster-arrow-border {
  display: none;
}

.tooltipster-base.tooltipster-sidetip .tooltipster-arrow-background {
  border-width: 6px;
  border-color: transparent;
}

.tooltipster-base.tooltipster-sidetip .tooltipster-box {
  background: rgba(34, 34, 34, 0.95);
  border: none;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-top .tooltipster-arrow-background, .tooltipster-base.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow-background {
  top: 0;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-top .tooltipster-content, .tooltipster-base.tooltipster-sidetip.tooltipster-bottom .tooltipster-content {
  text-align: center;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-top .tooltipster-arrow, .tooltipster-base.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow {
  height: 6px;
  margin-left: -6px;
  width: 12px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-left .tooltipster-arrow-background, .tooltipster-base.tooltipster-sidetip.tooltipster-right .tooltipster-arrow-background {
  left: 0;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-left .tooltipster-arrow, .tooltipster-base.tooltipster-sidetip.tooltipster-right .tooltipster-arrow {
  height: 12px;
  margin-top: -6px;
  width: 6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-top .tooltipster-box {
  margin-bottom: 6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-top .tooltipster-arrow-background {
  border-top-color: rgba(34, 34, 34, 0.95);
}

.tooltipster-base.tooltipster-sidetip.tooltipster-bottom .tooltipster-box {
  margin-top: 6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow-background {
  border-bottom-color: rgba(34, 34, 34, 0.95);
}

.tooltipster-base.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow-uncropped {
  top: -6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-right .tooltipster-box {
  margin-left: 6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-right .tooltipster-arrow-background {
  border-right-color: rgba(34, 34, 34, 0.95);
}

.tooltipster-base.tooltipster-sidetip.tooltipster-right .tooltipster-arrow-uncropped {
  left: -6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-left .tooltipster-box {
  margin-right: 6px;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-left .tooltipster-arrow-background {
  border-left-color: rgba(34, 34, 34, 0.95);
}

.wpforms-admin-popup-container {
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  height: 100vh;
  left: 0;
  overflow: hidden;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 100100;
}

.wpforms-admin-popup {
  background-color: white;
  border-radius: 6px;
  -moz-box-shadow: 0 0 40px 0 rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0 0 40px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 40px 0 rgba(0, 0, 0, 0.2);
  display: block !important;
  left: 50%;
  max-width: 550px;
  min-width: 550px;
  position: absolute;
  text-align: center;
  top: 50%;
  transform: translate(-50%, -50%);
  overflow: visible;
}

.wpforms-admin-popup-content {
  padding: 40px 50px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wpforms-admin-popup-content h3 {
  color: #3c434a;
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 0;
  margin: 0 0 20px 0;
  text-align: center;
}

.wpforms-admin-popup-content p {
  color: #6a6f76;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 22px;
  margin: 0 0 15px 0;
  text-align: center;
}

.wpforms-admin-popup-content p.no-gap {
  margin-bottom: 0;
}

.wpforms-admin-popup-content p.secondary {
  font-size: 14px;
  line-height: 17px;
}

.wpforms-admin-popup-content p:last-of-type {
  margin-bottom: 0;
}

.wpforms-admin-popup-content b {
  font-weight: 600;
}

.wpforms-admin-popup-content select, .wpforms-admin-popup-content input[type=text] {
  border: 1px solid #dcdcde;
  border-radius: 4px;
  color: #6a6f76;
  font-size: 14px;
  height: auto;
  line-height: 21px;
  padding: 8px 12px 8px 12px;
  vertical-align: middle;
  width: 320px;
}

.wpforms-admin-popup-content select:focus, .wpforms-admin-popup-content input[type=text]:focus {
  border-color: #036aab;
}

.wpforms-admin-popup-content .choices__inner {
  padding: 0;
  width: 320px;
}

.wpforms-admin-popup-content input[type=text].wpforms-admin-popup-shortcode {
  background: #f6f7f7;
  color: #3c434a;
  font-family: monospace;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 15px;
  margin: 20px 0 0 0;
  padding: 12px 22px;
  text-align: center;
  width: 100%;
}

.wpforms-admin-popup-content a.wpforms-admin-popup-toggle {
  color: #6a6f76;
  text-decoration-line: underline;
}

.wpforms-admin-popup-content a.wpforms-admin-popup-toggle:hover, .wpforms-admin-popup-content a.wpforms-admin-popup-toggle:focus {
  box-shadow: none;
  color: #3c434a;
}

.wpforms-admin-popup-content iframe {
  height: 253px;
  margin: 20px 0 0 0;
  width: 449px;
}

.wpforms-admin-popup-close {
  color: #b0b2b3;
  cursor: pointer;
  display: block;
  font-size: 16px !important;
  line-height: 12px;
  position: absolute;
  inset-inline-end: 10px;
  top: 10px;
}

.wpforms-admin-popup-close:hover {
  color: #6a6f76;
}

.wpforms-admin-popup-btn {
  background-color: #e27730;
  border: none;
  border-radius: 4px;
  box-shadow: none;
  color: #ffffff;
  cursor: pointer;
  display: inline-block;
  font-size: 16px;
  font-weight: 600;
  line-height: 19px;
  max-width: calc( 50% - 15px);
  width: calc( 50% - 15px);
  overflow: hidden;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  text-overflow: ellipsis;
  vertical-align: middle;
  white-space: nowrap;
}

.wpforms-admin-popup-btn:hover {
  background-color: #cd6622;
}

.wpforms-admin-popup-btn .dashicons-external {
  margin-inline-start: 6px;
}

.wpforms-admin-popup-btn[data-action="go"] {
  width: 120px;
}

.wpforms-admin-popup-btn:first-child {
  margin-inline-end: 10px;
}

.wpforms-admin-popup-btn:last-child {
  margin-inline-start: 10px;
}

.wpforms-admin-popup-btn:only-child {
  margin-left: 0;
  margin-right: 0;
}

.wpforms-admin-popup-btn:disabled {
  cursor: default;
  opacity: .5;
}

.wpforms-admin-popup-btn:disabled:hover {
  background-color: #e27730;
}

.wpforms-admin-popup-bottom {
  margin: 20px 0 0 0;
}

.wpforms-admin-popup-flex {
  align-items: stretch;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}

#wpforms-admin-form-embed-wizard .is-flipped .choices__list--dropdown {
  top: 100%;
  bottom: auto;
  margin-top: -1px;
  margin-bottom: 0;
}

#wpforms-admin-form-embed-wizard .choices__list--single {
  color: #6a6f76;
  line-height: 21px;
  padding: 8px 12px 8px 12px;
  vertical-align: middle;
}

#wpforms-admin-form-embed-wizard .choices__item {
  color: #6a6f76;
}

#wpforms-admin-form-embed-wizard-shortcode-wrap {
  display: table;
  margin: 0 auto;
}

#wpforms-admin-form-embed-wizard-shortcode {
  inset-inline-start: 15px;
  position: relative;
  width: 400px;
}

#wpforms-admin-form-embed-wizard-shortcode-copy {
  position: relative;
  inset-inline-end: 25px;
  top: 10px;
}

#wpforms-admin-form-embed-wizard-shortcode-copy i {
  background: #ffffff;
  border: 1px solid #c3c4c7;
  border-radius: 3px;
  color: #a7aaad;
  cursor: pointer;
  padding: 8px;
  vertical-align: middle;
}

#wpforms-admin-form-embed-wizard-shortcode-copy:hover i {
  border-color: #8c8f94;
  color: #3c434a;
}

#wpforms-builder-help {
  background-color: #ffffff;
  display: block;
  height: 100%;
  max-height: 100vh;
  opacity: 1;
  overflow-y: auto;
  position: fixed;
  width: 100vw;
  z-index: 100100;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

#wpforms-builder-help * {
  opacity: 1;
}

#wpforms-builder-help .wpforms-builder-help-docs {
  margin: 0 0 25px 0;
}

#wpforms-builder-help .wpforms-builder-help-docs li {
  margin: 0;
  padding-bottom: 14px;
  padding-inline-start: 4px;
}

#wpforms-builder-help .wpforms-builder-help-docs li i {
  color: #b0b2b3;
  font-size: 16px;
  margin-inline-end: 14px;
}

#wpforms-builder-help .wpforms-builder-help-docs li a {
  border-bottom: 1px solid transparent;
  color: #6a6f76;
  font-size: 15px;
  text-decoration: none;
}

#wpforms-builder-help .wpforms-builder-help-docs li a:hover {
  border-bottom: 1px solid #79c2f4;
  color: #036aab;
}

#wpforms-builder-help .wpforms-builder-help-docs .viewall {
  margin: 10px 0 0 0;
}

#wpforms-builder-help .wpforms-btn.wpforms-btn-md {
  font-size: 15px;
  font-weight: 600;
  min-height: auto;
  padding: 11px 17px 11px 17px;
}

#wpforms-builder-help-logo {
  height: 50px;
  inset-inline-start: 20px;
  position: fixed;
  top: 20px;
  width: 64px;
}

#wpforms-builder-help-close {
  cursor: pointer;
  font-size: 32px;
  height: 28px;
  position: fixed;
  inset-inline-end: 20px;
  top: 20px;
  width: 28px;
  transition-property: all;
  transition-duration: 0.05s;
  transition-timing-function: ease-out;
}

#wpforms-builder-help-close svg {
  height: 100% !important;
  width: 100% !important;
}

#wpforms-builder-help-close svg path {
  fill: #a7aaad !important;
}

#wpforms-builder-help-close:hover svg path {
  fill: #6a6f76 !important;
}

#wpforms-builder-help-content {
  background-color: #ffffff;
  margin: 0 auto 50px auto;
  width: 700px;
}

#wpforms-builder-help-content #wpforms-builder-help-search {
  background-color: #ffffff;
  padding: 74px 0 50px 0;
  position: relative;
  text-align: center;
  top: 0;
}

#wpforms-builder-help-content #wpforms-builder-help-search input {
  background-image: none;
  background-position: 22px center;
  background-repeat: no-repeat;
  background-size: 20px 20px;
  border: 1px solid #a7aaad;
  border-radius: 25px;
  color: #3c434a;
  font-size: 20px;
  letter-spacing: 0;
  line-height: 20px;
  min-height: 48px;
  padding-block: 10px;
  padding-inline-end: 10px;
  padding-inline-start: 54px;
  text-align: start;
  width: 700px;
}

#wpforms-builder-help-content #wpforms-builder-help-search input:focus {
  border: 1px solid #036aab;
}

#wpforms-builder-help-content #wpforms-builder-help-search input::placeholder {
  color: #b0b2b3;
}

.rtl #wpforms-builder-help-content #wpforms-builder-help-search input {
  background-position: calc(100% - 22px) center;
}

#wpforms-builder-help-content #wpforms-builder-help-search #wpforms-builder-help-search-clear {
  cursor: pointer;
  height: 20px;
  inset-inline-start: 22px;
  opacity: .7;
  position: absolute;
  top: 89px;
  width: 20px;
}

#wpforms-builder-help-content #wpforms-builder-help-search #wpforms-builder-help-search-clear svg {
  height: 100% !important;
  width: 100% !important;
}

#wpforms-builder-help-content #wpforms-builder-help-search #wpforms-builder-help-search-clear svg path {
  fill: #a7aaad !important;
}

#wpforms-builder-help-content #wpforms-builder-help-search #wpforms-builder-help-search-clear:hover {
  opacity: 1;
}

#wpforms-builder-help-content #wpforms-builder-help-search.wpforms-empty #wpforms-builder-help-search-clear {
  display: none;
}

#wpforms-builder-help-content #wpforms-builder-help-search.wpforms-empty input {
  background-image: url("../../images/search.svg");
}

#wpforms-builder-help-content .wpforms-builder-help-error {
  font-size: 16px;
  font-weight: 600;
  margin: 220px 0 100px 0;
  text-align: center;
}

#wpforms-builder-help-result .wpforms-builder-help-docs, #wpforms-builder-help-no-result .wpforms-builder-help-docs {
  margin: 0 0 36px 0;
}

#wpforms-builder-help-result span, #wpforms-builder-help-no-result span {
  color: #6a6f76;
  font-size: 15px;
}

#wpforms-builder-help-categories .wpforms-builder-help-categories-toggle .wpforms-builder-help-category {
  border-top: 1px solid #dcdcde;
  margin: 0;
}

#wpforms-builder-help-categories .wpforms-builder-help-categories-toggle .wpforms-builder-help-category:last-child {
  border-bottom: 1px solid #dcdcde;
}

#wpforms-builder-help-categories .wpforms-builder-help-categories-toggle .wpforms-builder-help-category header {
  align-items: center;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

#wpforms-builder-help-categories .wpforms-builder-help-categories-toggle .wpforms-builder-help-category header:hover span {
  color: #036aab;
}

#wpforms-builder-help-categories .wpforms-builder-help-categories-toggle .wpforms-builder-help-category header i.wpforms-folder {
  color: #a7aaad;
  font-size: 21px;
  margin-block: 23px;
  margin-inline-end: 11px;
}

#wpforms-builder-help-categories .wpforms-builder-help-categories-toggle .wpforms-builder-help-category header i.wpforms-arrow {
  color: #c3c4c7;
  font-size: 24px;
  margin-inline-start: auto;
  transition-property: transform;
  transition-duration: 0.25s;
  transition-timing-function: ease-out;
}

.rtl #wpforms-builder-help-categories .wpforms-builder-help-categories-toggle .wpforms-builder-help-category header i.wpforms-arrow {
  transform: scale(-1, 1);
}

#wpforms-builder-help-categories .wpforms-builder-help-categories-toggle .wpforms-builder-help-category header span {
  color: #3c434a;
  font-size: 16px;
  font-weight: 600;
}

#wpforms-builder-help-categories .wpforms-builder-help-categories-toggle .wpforms-builder-help-category.opened i.wpforms-arrow {
  transform: rotate(90deg);
}

#wpforms-builder-help-footer {
  align-items: center;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  margin: 50px 0 0 0;
}

#wpforms-builder-help-footer .wpforms-builder-help-footer-block {
  border: 1px solid #dcdcde;
  border-radius: 6px;
  box-sizing: border-box;
  max-width: 325px;
  padding: 25px;
  text-align: center;
}

#wpforms-builder-help-footer .wpforms-builder-help-footer-block i {
  color: #a7aaad;
  font-size: 48px;
  margin: 0 0 20px 0;
}

#wpforms-builder-help-footer .wpforms-builder-help-footer-block h3 {
  color: #3c434a;
  font-size: 16px;
  margin: 0 0 10px 0;
}

#wpforms-builder-help-footer .wpforms-builder-help-footer-block p {
  color: #6a6f76;
  font-size: 14px;
  margin: 0 0 20px 0;
}

@-webkit-keyframes rotation {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes rotation {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@media screen and (max-width: 750px) {
  #wpforms-builder-help {
    display: none;
  }
}

@media screen and (max-width: 1023px) {
  .wpforms-dyk-row {
    display: none !important;
  }
}

.wpforms-dyk .wpforms-dyk-fbox {
  align-content: stretch;
  align-items: center;
  background-color: #f1f6fa;
  border-inline-start: 4px solid #036aab;
  display: flex;
  font-size: 14px;
  gap: 10px;
  justify-content: space-between;
  opacity: 1;
  padding-block: 12px;
  padding-inline-end: 12px;
  padding-inline-start: 20px;
  transition-property: all;
  transition-duration: 0.25s;
  transition-timing-function: ease-out;
}

.wpforms-dyk .wpforms-dyk-fbox.out {
  opacity: 0;
  transform: scaleY(0);
}

.wpforms-dyk .wpforms-dyk-message b {
  font-weight: 700;
}

.wpforms-dyk .wpforms-dyk-buttons {
  align-items: center;
  display: flex;
  gap: 10px;
}

.wpforms-dyk .wpforms-dyk-buttons .learn-more {
  text-decoration: underline;
}

.wpforms-dyk .wpforms-dyk-buttons .learn-more:hover {
  color: #3c434a;
}

.wpforms-dyk .wpforms-dyk-buttons .wpforms-btn-md {
  font-size: 14px;
  min-height: auto;
}

.wpforms-smtp-education-notice {
  background: #f0f0f1;
  border-radius: 3px;
  margin: 30px 0;
  padding-block: 18px;
  padding-inline-end: 100px;
  padding-inline-start: 20px;
  position: relative;
}

.wpforms-smtp-education-notice:after {
  content: '';
  display: block;
  width: 97px;
  height: 78px;
  position: absolute;
  bottom: 0;
  inset-inline-end: 0;
  background-image: url("../../images/smtp/pattie-2.svg");
  background-size: 100%;
  z-index: 1;
}

.wpforms-smtp-education-notice-title {
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
}

.wpforms-smtp-education-notice-description {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #6a6f76;
  margin-top: 5px;
}

.wpforms-smtp-education-notice-description a {
  color: #e27730;
  font-weight: 600;
}

.wpforms-smtp-education-notice-description a:hover {
  color: #cd6622;
}

.wpforms-smtp-education-notice-dismiss-button {
  z-index: 2;
  position: absolute;
  inset-inline-end: 0;
  top: 0;
  padding: 5px;
  color: #b0b2b3;
}

.wpforms-smtp-education-notice-dismiss-button:before {
  content: none !important;
}

.wpforms-smtp-education-notice-dismiss-button:hover {
  color: #6a6f76;
}

@media (max-width: 1024px) {
  .wpforms-smtp-education-notice {
    padding: 18px 20px;
  }
  .wpforms-smtp-education-notice::after {
    margin-top: -18px;
    position: relative;
    bottom: -18px;
    margin-inline-start: auto;
    inset-inline-end: auto;
  }
}

.wpforms-educational-alert.wpforms-calculations {
  position: relative;
  padding-right: 30px;
}

.wpforms-educational-alert.wpforms-calculations .wpforms-dismiss-button {
  position: absolute;
  inset-inline-end: 1px;
  top: 2px;
}

.wpforms-educational-alert.wpforms-calculations .wpforms-badge-block {
  margin-bottom: 5px;
}

.wpforms-educational-alert.wpforms-calculations h3 {
  font-size: inherit;
  margin: inherit;
  margin-bottom: 1px;
}

.wpforms-educational-alert .wpforms-educational-badge {
  font-size: 8px;
  font-style: normal;
  font-weight: 700;
  line-height: 10px;
  letter-spacing: 0.4px;
  text-transform: uppercase;
  text-align: center;
  padding: 5px 7px;
  border-radius: 3px;
  background-color: #E5F6E9;
}

.wpforms-educational-alert .wpforms-educational-badge-green {
  color: #30B450;
}

.wpforms-educational-alert .wpforms-educational-badge + h4 {
  margin-top: 10px;
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert.wpforms-pro-fields-notice {
  margin: 30px 0 10px 0;
  align-items: flex-start;
  padding: 0;
  background: #FDFAF2;
  border: 1px solid rgba(0, 0, 0, 0.07);
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.07);
  border-radius: 6px;
  overflow: hidden;
  max-height: none;
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert.wpforms-pro-fields-notice .wpforms-alert-message {
  padding: 20px 20px 20px 50px;
  background-image: url("../../images/integrations/ai/bulb-orange.svg");
  background-size: 14px 21px;
  background-repeat: no-repeat;
  background-position: 20px 22px;
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert.wpforms-pro-fields-notice .wpforms-alert-message h3 {
  margin-top: 0;
  margin-bottom: 4px;
  line-height: 21px;
  font-size: 17px;
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert.wpforms-pro-fields-notice .wpforms-alert-message p {
  margin: 0;
  line-height: 21px;
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert.wpforms-pro-fields-notice .wpforms-alert-message a {
  color: #e27730;
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert.wpforms-pro-fields-notice .wpforms-alert-message a:hover {
  color: #cd6622;
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert.wpforms-pro-fields-notice.wpforms-alert-error {
  background: #fcf0f1;
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert.wpforms-pro-fields-notice.wpforms-alert-error .wpforms-alert-message {
  background-image: url("../../images/integrations/ai/bulb-red.svg");
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert.wpforms-pro-fields-notice .wpforms-alert-buttons {
  padding: 20px;
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert.wpforms-pro-fields-notice .wpforms-alert-buttons button {
  padding: 0;
  opacity: 0.7;
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert.wpforms-pro-fields-notice .wpforms-alert-buttons button:before {
  content: '';
  background-image: url("../../images/integrations/ai/close.svg");
  background-size: 12px 12px;
  width: 12px;
  height: 12px;
}

.wpforms-admin-page .wpforms-panel-content .wpforms-alert.wpforms-pro-fields-notice .wpforms-alert-buttons button:hover {
  opacity: 1;
}

.wpforms-pdf-popup {
  position: fixed;
  bottom: 16px;
  inset-inline-end: 16px;
  width: 280px;
  height: 258px;
  padding: 63px 30px 30px 30px;
  background-color: white;
  z-index: 100109;
  text-align: center;
  box-shadow: 5px 10px 30px -5px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
}

.wpforms-pdf-popup .wpforms-pdf-popup-content .icon {
  position: absolute;
  top: -26px;
  left: 50%;
  transform: translateX(-50%);
}

.wpforms-pdf-popup .wpforms-pdf-popup-content .icon img {
  width: 64px;
  height: 69px;
}

.wpforms-pdf-popup .wpforms-pdf-popup-content .close-popup {
  position: absolute;
  top: 10px;
  inset-inline-end: 11px;
  cursor: pointer;
  transform: scale(1.4);
  color: #bbbbbb;
}

.wpforms-pdf-popup .wpforms-pdf-popup-content .badge {
  color: #30b450;
  text-transform: uppercase;
  font-weight: 700;
  font-size: 10px;
  line-height: 100%;
}

.wpforms-pdf-popup .wpforms-pdf-popup-content h2 {
  font-weight: 700;
  font-size: 20px;
  line-height: 100%;
  margin: 8px;
}

.wpforms-pdf-popup .wpforms-pdf-popup-content p {
  line-height: 20px;
  margin-top: 15px;
}

.wpforms-pdf-popup .wpforms-pdf-popup-content button {
  margin-top: 10px;
}
