<?php
/**
 * Admin Notices Class.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper\Admin;

// Exit if accessed directly.
use WPForms\SupportHelper\ActivationInterface;
use WPForms\SupportHelper\Cache\CacheInterface;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Admin notices functionality.
 *
 * @since {VERSION}
 */
class AdminNotices implements ActivationInterface {

	/**
	 * Notice dismissal option key.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private const NOTICE_DISMISSED_KEY = CacheInterface::CACHE_PREFIX . 'notice_dismissed';

	/**
	 * Plugin activation option key.
	 *
	 * @since {VERSION}
	 */
	private const ACTIVATION_KEY = CacheInterface::CACHE_PREFIX . 'activated';

	/**
	 * Represents the settings page of the application.
	 *
	 * @since {VERSION}
	 *
	 * @var SettingsPage
	 */
	private $settings_page;

	/**
	 * Constructor method to initialize the class with dependencies.
	 *
	 * @since {VERSION}
	 *
	 * @param SettingsPage $settings_page Instance of the SettingsPage class.
	 */
	public function __construct( SettingsPage $settings_page ) {

		$this->settings_page = $settings_page;

		$this->hooks();
	}

	/**
	 * Initialize admin notices.
	 *
	 * @since {VERSION}
	 */
	private function hooks(): void {

		add_action( 'admin_notices', [ $this, 'display_notices' ] );
		add_action( 'wp_ajax_wpf_support_helper_dismiss_notice', [ $this, 'dismiss_notice' ] );
		add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_scripts' ] );
		add_action( 'admin_notices', [ $this, 'display_activation_notice' ] );
	}

	/**
	 * Display admin notices.
	 *
	 * @since {VERSION}
	 */
	public function display_notices(): void {

		// Only show notices on admin pages.
		if ( ! is_admin() ) {
			return;
		}

		// Check if WPForms is active.
		if ( $this->is_wpforms_active() ) {
			return;
		}

		// Check if notice was dismissed.
		if ( get_option( self::NOTICE_DISMISSED_KEY, false ) ) {
			return;
		}

		// Display WPForms inactive notice.
		$this->display_wpforms_inactive_notice();
	}

	/**
	 * Display WPForms inactive notice.
	 *
	 * @since {VERSION}
	 */
	private function display_wpforms_inactive_notice(): void {

		$message = sprintf(
			'<p><strong>%s</strong></p>
			<p>%s</p>
			<p>%s</p>
			<ul>
				<li>• %s</li>
				<li>• %s</li>
				<li>• %s</li>
			</ul>
			<p><a href="%s" class="button button-primary">%s</a> <a href="%s" class="button">%s</a></p>',
			esc_html__( 'WPForms Support Helper is Active', 'wpf-support-helper' ),
			esc_html__( 'WPForms is not currently active on this site, but the Support Helper plugin is still functional and will continue to detect ModSecurity.', 'wpf-support-helper' ),
			esc_html__( 'This plugin provides the following functionality:', 'wpf-support-helper' ),
			esc_html__( 'ModSecurity detection using multiple methods', 'wpf-support-helper' ),
			esc_html__( 'Detailed diagnostic information in Site Health', 'wpf-support-helper' ),
			esc_html__( 'Comprehensive reporting for support purposes', 'wpf-support-helper' ),
			admin_url( 'site-health.php?tab=debug' ),
			esc_html__( 'View Site Health Info', 'wpf-support-helper' ),
			'https://wpforms.com/',
			esc_html__( 'Get WPForms', 'wpf-support-helper' )
		);

		printf(
			'<div class="notice notice-info is-dismissible" data-dismissible="wpf-support-helper-wpforms-notice">
				%s
			</div>',
			$message // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
		);
	}

	/**
	 * Enqueue admin scripts.
	 *
	 * @since {VERSION}
	 */
	public function enqueue_scripts() {

		// Only enqueue on admin pages where we might show notices.
		if ( ! is_admin() || $this->is_wpforms_active() || get_option( self::NOTICE_DISMISSED_KEY, false ) ) {
			return;
		}

		// Enqueue WordPress dismiss script.
		wp_enqueue_script( 'jquery' );

		// Add inline script for notice dismissal.
		$script = "
		jQuery(document).ready(function($) {
			$(document).on('click', '.notice[data-dismissible=\"wpf-support-helper-wpforms-notice\"] .notice-dismiss', function() {
				$.ajax({
					url: ajaxurl,
					type: 'POST',
					data: {
						action: 'wpf_support_helper_dismiss_notice',
						nonce: '" . wp_create_nonce( self::NOTICE_DISMISSED_KEY ) . "'
					}
				});
			});
		});
		";

		wp_add_inline_script( 'jquery', $script );
	}

	/**
	 * Handle notice dismissal via AJAX.
	 *
	 * @since {VERSION}
	 */
	public function dismiss_notice(): void {

		// Verify nonce.
		// phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized
		if ( ! wp_verify_nonce( wp_unslash( $_POST['nonce'] ?? '' ), self::NOTICE_DISMISSED_KEY ) ) {
			wp_die( esc_html__( 'Security check failed.', 'wpf-support-helper' ) );
		}

		// Check user capabilities.
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_die( esc_html__( 'Insufficient permissions.', 'wpf-support-helper' ) );
		}

		// Set dismissal flag.
		update_option( self::NOTICE_DISMISSED_KEY, true );

		wp_die(); // This is required to terminate immediately and return a proper response.
	}

	/**
	 * Check if WPForms is active.
	 *
	 * @since {VERSION}
	 *
	 * @return bool True if WPForms is active.
	 */
	private function is_wpforms_active(): bool {

		return function_exists( 'wpforms' ) || class_exists( 'WPForms' );
	}

	/**
	 * Reset notice dismissal (for testing purposes).
	 *
	 * @since {VERSION}
	 */
	public function reset_options(): void {

		delete_option( self::NOTICE_DISMISSED_KEY );
	}

	/**
	 * Display activation notice.
	 *
	 * @since {VERSION}
	 */
	public function display_activation_notice(): void {

		// Only show on plugin activation.
		if ( ! get_option( self::ACTIVATION_KEY, false ) ) {
			return;
		}

		// Clear the activation flag.
		delete_option( self::ACTIVATION_KEY );

		$message = sprintf(
			'<p><strong>%s</strong></p>
			<p>%s</p>
			<p><a href="%s" class="button button-primary">%s</a></p>',
			esc_html__( 'WPForms Support Helper Activated', 'wpf-support-helper' ),
			esc_html__( 'The plugin is now detecting ModSecurity on your server. You can view the results in WPForms -> Tools -> Support Helper page.', 'wpf-support-helper' ),
			$this->settings_page->get_link(),
			esc_html__( 'View Detection Results', 'wpf-support-helper' )
		);

		printf(
			'<div class="notice notice-success is-dismissible">
				%s
			</div>',
			$message // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
		);
	}

	/**
	 * Handle plugin activation.
	 *
	 * Clears existing notice dismissal options and sets activation flag.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	public function activate_plugin(): void {

		// Clear existing notice dismissal options.
		delete_option( self::NOTICE_DISMISSED_KEY );

		// Set activation flag.
		update_option( self::ACTIVATION_KEY, true );
	}

	/**
	 * Handle plugin deactivation.
	 *
	 * Clears all AdminNotices related options and transients.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	public function deactivate_plugin(): void {

		// Delete notice dismissal option.
		delete_option( self::NOTICE_DISMISSED_KEY );

		// Delete activation option.
		delete_option( self::ACTIVATION_KEY );
	}
}
