// Form Builder styles.
//
// Colors.
//
// @since 1.6.8

// Colors by name:
$color_white:                          #ffffff;
$color_black:                          #1d2327;

$color_dark_red:                       #b32d2e;
$color_red:                            #d63638;
$color_bright_red:                     #ee5c5c;
$color_lighter_red:                    #f9e1e1;
$color_lightest_red:                   #fcf0f1;

$color_dark_green:                     #008a20;
$color_green:                          #00a32a;
$color_lightest_green:                 #edfaef;
$color_darker_blue:                    #005387;
$color_dark_blue:                      #215d8f;
$color_blue:                           #036aab;
$color_bright_blue:                    #0399ed;
$color_bright_blue_alt:                #4285f4;
$color_brighter_blue:                  #00c6bf;
$color_light_blue:                     #79c2f4;
$color_lighter_blue:                   #cce0ed;
$color_lightest_blue:                  #f1f6fa;

$color_dark_orange:                    #cd6622;
$color_orange:                         #e27730;
$color_light_orange:                   #f48120;

$color_dark_yellow:                    #ffaa00;
$color_yellow:                         #ffb900;
$color_lightest_yellow:                #fcf9e8;

$color_darker_grey:                    #50575e;
$color_dark_grey:                      #6a6f76;
$color_bright_grey:                    #a7aaad;
$color_bright_grey_alt:                #b0b2b3;
$color_brighter_grey:                  #c3c4c7;
$color_brighter_grey_alt:              #dcdcde;
$color_brightest_grey:                 #e8e9e9;
$color_light_grey:                     #f0f0f1;
$color_lighter_grey:                   #f6f7f7;
$color_lightest_grey:                  #f6f7f7;

// Colors by usage:
$color_black_background:               #282e32;
$color_black_background_hover:         #3c434a;
$color_dark_grey_background:           #6a6f76;
$color_grey_background:                #e8e9e9;
$color_grey_background_hover:          #c3c4c7;
$color_light_background:               #f6f7f7;
$color_light_background_hover:         #e8e9e9;
$color_light_background_notice:        #f0f0f1;

$color_divider:                        #dcdcde;
$color_border:                         #c3c4c7;
$color_border_hover:                   #8c8f94;
$color_hint:                           #b0b2b3;
$color_primary_text:                   #3c434a;
$color_secondary_text:                 #6a6f76;
$color_light_text:                     #8c8f94;
$color_lighter_text:                   #a7aaad;

$color_fields_tabs:                    #dfe7ef;
$color_fields_divider:                 #ced7e0;
$color_fields_background:              #ebf3fc;
$color_fields_background_alt:          #dfe7ef;
$color_fields_background_hover:        #e0e8f0;
$color_fields_border:                  #b0b6bd;
$color_fields_hint:                    #b0b6bd;
$color_fields_secondary_text:          #86919e;
$color_preview_button_background:      #999c9e;

$color_close:                          #b0b2b3;
$color_close_hover:                    #6a6f76;

$color_button_icon_light_grey:         #a7aaad;
$color_button_icon_grey:               #8c8f94;

$color_purple:                         #7a30e2;
$color_purple_text:                    #9b64e8;
$color_purple_background:              #faf5fe;
$color_purple_background_hover:        #f5e9ff;
$color_purple_hover:                   #5c24a9;

// Semitransparent colors:
$color_white_trans_35:                 rgba( 255, 255, 255, .35 );
$color_white_trans_65:                 rgba( 255, 255, 255, .65 );
$color_white_trans_60:                 rgba( 255, 255, 255, .6 );
$color_white_trans_50:                 rgba( 255, 255, 255, .5 );
$color_black_trans_35:                 rgba( 0, 0, 0, .35 );
$color_black_trans_15:                 rgba( 0, 0, 0, .15 );

$color_scrollbar:                      rgba( 0, 0, 0, 0 );
$color_scrollbar_hover:                rgba( 0, 0, 0, .5 );

$color_box_shadow:                     rgba( 0, 0, 0, .1 );

// We need this var since $blue used in `_choices.scss`,
// which is used in the `admin.scss` as well.
$blue: $color_blue;
