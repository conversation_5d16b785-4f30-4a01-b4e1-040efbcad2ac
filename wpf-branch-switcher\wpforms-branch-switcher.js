/* global wpfBrsw */

/**
 * Branch Switcher.
 * WPForms Developer Tools.
 *
 * @since 0.1
 */

'use strict';

var wpfBranchSwitcher = window.wpfBranchSwitcher || ( function( document, window, $ ) {

	/**
	 * Elements reference.
	 *
	 * @since 0.1
	 *
	 * @type {object}
	 */
	var el = {};

	/**
	 * Public functions and properties.
	 *
	 * @since 0.1
	 *
	 * @type {object}
	 */
	var app = {

		/**
		 * Start the engine.
		 *
		 * @since 0.1
		 */
		init: function() {

			$( app.ready );
		},

		/**
		 * Document ready.
		 *
		 * @since 0.1
		 */
		ready: function() {

			app.elements();
			app.events();
		},

		/**
		 * Elements.
		 *
		 * @since 0.1
		 */
		elements: function() {

			el = {
				$adminBarMenuItem:  $( '#wp-admin-bar-wpf-brsw > .ab-item' ),
				$switchBranchItem:  $( '#wp-admin-bar-wpf-brsw-branch > .ab-item' ),
				$updateLocal:       $( '#wp-admin-bar-wpf-brsw-update-local a' ),
				$branchesSubmenu:   $( '#wp-admin-bar-wpf-brsw-branch-default' ),
			};
		},

		/**
		 * Register JS events.
		 *
		 * @since 0.1
		 */
		events: function() {

			// Admin Bar Menu Item.
			el.$adminBarMenuItem.on( 'click', function( e ) {
				e.preventDefault();
			} );

			el.$switchBranchItem.on( 'click', function( e ) {
				e.preventDefault();
			} );

			// Checkout selected branch.
			app.checkoutBranch();

			// Update local branch.
			app.updateBranch();
		},

		/**
		 * Update local branch event.
		 *
		 * @since 0.1
		 */
		updateBranch: function() {

			el.$updateLocal.on( 'click', function( e ) {

				e.preventDefault();

				var data = {
					action: 'wpf_brsw_update_local',
					nonce: wpfBrsw.nonce,
				};

				app.modalLoading( wpfBrsw.updating );

				$.post( wpfBrsw.ajaxurl, data ).done( function( response ) {
					app.modalLoadingClose();

					if ( response.success ) {
						app.modalSuccessRefresh( response.data.msg );
					} else {
						app.modalError( response.data.msg );
					}
				} );
			} );
		},

		/**
		 * Update local branch event.
		 *
		 * @since 0.1
		 */
		checkoutBranch: function() {

			el.$branchesSubmenu.on( 'click', 'a.ab-item', function( e ) {

				e.preventDefault();

				var branch = $( this ).text(),
					data = {
						action: 'wpf_brsw_checkout_branch',
						nonce: wpfBrsw.nonce,
						branch: branch,
					};

				$.post( wpfBrsw.ajaxurl, data ).done( function( response ) {

					app.modalLoadingClose();

					if ( response.success ) {
						app.modalSuccessRefresh( response.data.msg );
					} else {
						app.modalError( response.data.msg );
					}
				} );
			} );
		},

		/**
		 * Display loading message.
		 *
		 * @since 0.1
		 *
		 * @param {string} msg Loading message.
		 */
		modalLoading: function( msg ) {

			$( 'body' )
				.append( '<div id="wpf-branch-switcher-loading"><div class="lds-ring"><div></div><div></div><div></div><div></div></div><span>' + msg + '</span></div>' )
				.css( { overflow: 'hidden' } );
		},

		/**
		 * Close loading modal.
		 *
		 * @since 0.1
		 */
		modalLoadingClose: function() {

			$( '#wpf-branch-switcher-loading' ).remove();
			$( 'body' ).css( { overflow: 'auto' } );
		},

		/**
		 * Display success message.
		 *
		 * @since 0.1
		 *
		 * @param {string} msg Success message.
		 *
		 * @returns {void} jQuery confirm object.
		 */
		modalSuccessRefresh: function( msg ) {

			setTimeout( function() {
				var content = ( msg || '' ) + '\n' + wpfBrsw.clickrefresh;
				if ( confirm( content ) ) {
					window.location.reload();
				}
			}, 100 );
		},

		/**
		 * Display error message in modal alert box.
		 *
		 * @since 0.1
		 *
		 * @param {string} msg Error message.
		 *
		 * @returns {void} jQuery confirm object.
		 */
		modalError: function( msg ) {

			setTimeout( function() {
				var content = wpfBrsw.erroroccured + '\n' + ( msg || '' );
				alert( content );
			}, 100 );
		},
	};

	// Provide access to public functions/properties.
	return app;

}( document, window, jQuery ) );

// Initialize.
wpfBranchSwitcher.init();
