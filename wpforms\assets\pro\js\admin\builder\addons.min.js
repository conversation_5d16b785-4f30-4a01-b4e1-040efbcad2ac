var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.Addons=WPForms.Admin.Builder.Addons||(s=>{let e={},d={isGoogleSheetsVisited:!1,init(){s(d.ready)},ready(){d.setup(),d.events()},setup(){e.$builder=s("#wpforms-builder")},events(){e.$builder.on("click","#wpforms-panel-field-settings-conversational_forms_enable",d.optionCFEnableModal),e.$builder.on("click","#wpforms-panel-field-settings-save_resume_enable",d.optionSaveAndResumeEnableModal),e.$builder.on("click","#wpforms-panel-field-settings-form_abandonment",d.optionFormAbandonmentEnableModal),e.$builder.on("click",".wpforms-panel-sidebar-section-google-sheets",d.optionGoogleSheetsVisitedModal),e.$builder.on("click","#wpforms-panel-field-lead_forms-enable",d.optionLeadFormsEnableModal),e.$builder.on("wpformsFieldAddDragStart wpformsBeforeFieldAddOnClick",d.layoutFieldsCantAddModal)},layoutFieldsCantAddModal(o,r){if(d.isLayoutBasedField(r)){let e="";d.isAddonEnabled("wpforms-conversational-forms")&&(e=wpforms_builder[r].field_add_cf_alert_text,o.preventDefault()),d.isAddonEnabled("wpforms-save-resume")&&"repeater"===r&&!d.isInsideRepeaterAddonAllowed("wpforms-save-resume")&&(e=wpforms_builder.repeater.addons_requirements_alert_text["wpforms-save-resume"]),d.isAddonEnabled("wpforms-form-abandonment")&&"repeater"===r&&!d.isInsideRepeaterAddonAllowed("wpforms-form-abandonment")&&(e=wpforms_builder.repeater.addons_requirements_alert_text["wpforms-form-abandonment"]),s(".wpforms-panel-sidebar-section-google-sheets").hasClass("education-modal")||"repeater"!==r||d.isInsideRepeaterAddonAllowed("wpforms-google-sheets")||!d.isGoogleSheetsConnectionsExist()||(e=wpforms_builder.repeater.addons_requirements_alert_text["wpforms-google-sheets"]),d.isAddonEnabled("wpforms-lead-forms")&&"repeater"===r&&!d.isInsideRepeaterAddonAllowed("wpforms-lead-forms")&&(e=wpforms_builder.repeater.addons_requirements_alert["wpforms-lead-forms"],o.preventDefault()),""!==e&&d.openModal({content:e})}},isAddonEnabled(e){var o={"wpforms-conversational-forms":"#wpforms-panel-field-settings-conversational_forms_enable","wpforms-save-resume":"#wpforms-panel-field-settings-save_resume_enable","wpforms-form-abandonment":"#wpforms-panel-field-settings-form_abandonment","wpforms-lead-forms":"#wpforms-panel-field-lead_forms-enable"};return!!o[e]&&s(o[e]).is(":checked")},isInsideRepeaterAddonAllowed(e){return wpforms_builder.repeater.addons_requirements[e]},isRepeaterAdded(){return 0<s("#wpforms-field-options .wpforms-field-option-repeater").length},optionFormAbandonmentEnableModal(){d.isAddonEnabled("wpforms-form-abandonment")&&!d.isInsideRepeaterAddonAllowed("wpforms-form-abandonment")&&d.isRepeaterAdded()&&d.openModal({content:wpforms_builder.repeater.addons_requirements_alert_text["wpforms-form-abandonment"]})},optionGoogleSheetsVisitedModal(){d.isGoogleSheetsVisited||s(this).hasClass("education-modal")||d.isInsideRepeaterAddonAllowed("wpforms-google-sheets")||d.isRepeaterAdded()&&d.isGoogleSheetsConnectionsExist()&&(d.isGoogleSheetsVisited=!0,d.openModal({content:wpforms_builder.repeater.addons_requirements_alert_text["wpforms-google-sheets"]}))},isGoogleSheetsConnectionsExist(){return WPForms.Admin.Builder.Providers?.GoogleSheets?.isReady?Boolean(s(".wpforms-builder-provider-connection","#google-sheets-provider").length):wpforms_builder.repeater.is_google_sheets_has_connection},optionSaveAndResumeEnableModal(){d.isAddonEnabled("wpforms-save-resume")&&!d.isInsideRepeaterAddonAllowed("wpforms-save-resume")&&d.isRepeaterAdded()&&d.openModal({content:wpforms_builder.repeater.addons_requirements_alert_text["wpforms-save-resume"]})},optionCFEnableModal(e){d.isAddonEnabled("wpforms-conversational-forms")&&d.isRepeaterAdded()&&(e.preventDefault(),d.openModal({content:wpforms_builder.repeater.enabled_cf_alert_text}))},optionLeadFormsEnableModal(e){d.isAddonEnabled("wpforms-lead-forms")&&d.isRepeaterAdded()&&(e.preventDefault(),d.openModal({content:wpforms_builder.repeater.addons_requirements_alert_text["wpforms-lead-forms"]}))},openModal(e){e&&e.content&&s.confirm({title:e.title??wpforms_builder.heads_up,content:e.content,icon:e.icon??"fa fa-exclamation-circle",type:e.type??"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},isLayoutBasedField(e){return!!WPForms.Admin.Builder.FieldLayout&&WPForms.Admin.Builder.FieldLayout.isLayoutBasedField(e)}};return d})((document,window,jQuery)),WPForms.Admin.Builder.Addons.init();