// WPForms Classic styles.
//
// Legacy user columns.
//
// @since 1.8.1

.wpforms-five-sixths,
.wpforms-four-sixths,
.wpforms-four-fifths,
.wpforms-one-fifth,
.wpforms-one-fourth,
.wpforms-one-half,
.wpforms-one-sixth,
.wpforms-one-third,
.wpforms-three-fourths,
.wpforms-three-fifths,
.wpforms-three-sixths,
.wpforms-two-fourths,
.wpforms-two-fifths,
.wpforms-two-sixths,
.wpforms-two-thirds {
	float: left;
	margin-left: 20px;
	clear: none;
}

.wpforms-one-half,
.wpforms-three-sixths,
.wpforms-two-fourths {
	width: calc( 50% - 10px );
}

.wpforms-one-third,
.wpforms-two-sixths {
	width: calc( 100% / 3 - 20px );

	&.wpforms-first {
		width: calc( 100% / 3 );
	}
}

.wpforms-four-sixths,
.wpforms-two-thirds {
	width: calc( 2 * 100% / 3 - 20px );

	&.wpforms-first {
		width: calc( 2 * 100% / 3 );
	}
}

.wpforms-one-fourth {
	width: calc( 25% - 20px );

	&.wpforms-first {
		width: 25%;
	}
}

.wpforms-three-fourths {
	width: calc( 75% - 20px );

	&.wpforms-first {
		width: 75%;
	}
}

.wpforms-one-fifth {
	width: calc( 100% / 5 - 20px );

	&.wpforms-first {
		width: calc( 100% / 5 );
	}
}

.wpforms-two-fifths {
	width: calc( 2 * 100% / 5 - 20px );

	&.wpforms-first {
		width: calc( 2 * 100% / 5 );
	}
}

.wpforms-three-fifths {
	width: calc( 3 * 100% / 5 - 20px );

	&.wpforms-first {
		width: calc( 3 * 100% / 5 );
	}
}

.wpforms-four-fifths {
	width: calc( 4 * 100% / 5 - 20px );

	&.wpforms-first {
		width: calc( 4 * 100% / 5 );
	}
}

.wpforms-one-sixth {
	width: calc( 100% / 6 - 20px );

	&.wpforms-first {
		width: calc( 100% / 6 );
	}
}

.wpforms-five-sixths {
	width: calc( 5 * 100% / 6 - 20px );

	&.wpforms-first {
		width: calc( 5 * 100% / 6 );
	}
}

.wpforms-first {
	clear: both !important;
	margin-left: 0 !important;
}
