WPForms.Admin.Builder.Providers.ConstantContactV3=WPForms.Admin.Builder.Providers.ConstantContactV3||((n,l)=>{let p={selectors:{accountField:".js-wpforms-builder-constant-contact-v3-provider-connection-account",actionData:".wpforms-builder-constant-contact-v3-provider-actions-data",actionField:".js-wpforms-builder-constant-contact-v3-provider-connection-action",connection:".wpforms-panel-content-section-constant-contact-v3 .wpforms-builder-provider-connection"},$elements:{$connections:l(".wpforms-panel-content-section-constant-contact-v3 .wpforms-builder-provider-connections"),$holder:l("#wpforms-panel-providers"),$panel:l("#constant-contact-v3-provider")},provider:"constant-contact-v3",Providers:{},Templates:{},Cache:{},isReady:!1,init(){"providers"===wpf.getQueryString("view")&&p.$elements.$holder.on("WPForms.Admin.Builder.Providers.ready",p.ready),l(n).on("wpformsPanelSwitched",function(n,e){"providers"===e&&p.ready()})},ready(){p.isReady||(p.Providers=WPForms.Admin.Builder.Providers,p.Templates=WPForms.Admin.Builder.Templates,p.Cache=p.Providers.cache,p.Templates.add(["wpforms-constant-contact-v3-builder-content-connection","wpforms-constant-contact-v3-builder-content-connection-error","wpforms-constant-contact-v3-builder-content-connection-select-field","wpforms-constant-contact-v3-builder-content-connection-conditionals"]),p.bindUIActions(),p.bindTriggers(),p.processInitial(),p.isReady=!0)},bindUIActions(){p.$elements.$panel.on("connectionCreate",p.connection.create).on("connectionDelete",p.connection.delete).on("change",p.selectors.accountField,p.ui.accountField.change).on("change",p.selectors.actionField,p.ui.actionField.change)},bindTriggers(){p.$elements.$connections.on("connectionsDataLoaded",function(n,e){if(!_.isEmpty(e.connections))for(var t in e.connections)p.connection.generate({connection:e.connections[t],conditional:e.conditionals[t]})}),p.$elements.$connections.on("connectionGenerated",function(n,e){var t=p.connection.getById(e.connection.id);_.has(e.connection,"isNew")&&e.connection.isNew?p.connection.replaceIds(e.connection.id,t):l(p.selectors.actionField,t).trigger("change")})},processInitial(){p.connection.dataLoad()},connection:{getById(n){return p.$elements.$connections.find('.wpforms-builder-provider-connection[data-connection_id="'+n+'"]')},replaceIds(e,n){n.find("input, select, label").each(function(){var n=l(this);n.attr("name")&&n.attr("name",n.attr("name").replace(/%connection_id%/gi,e)),n.attr("id")&&n.attr("id",n.attr("id").replace(/%connection_id%/gi,e)),n.attr("for")&&n.attr("for",n.attr("for").replace(/%connection_id%/gi,e)),n.attr("data-name")&&n.attr("data-name",n.attr("data-name").replace(/%connection_id%/gi,e))})},create(n,e){var t=(new Date).getTime().toString(16),e={id:t,name:e,isNew:!0};p.Cache.addTo(p.provider,"connections",t,e),p.connection.generate({connection:e})},delete(n,e){var t=p.Providers.getProviderHolder(p.provider);e.closest(t).length&&(t=e.data("connection_id"),_.isString(t))&&p.Cache.deleteFrom(p.provider,"connections",t)},generate(n){var e,t,o=p.Cache.get(p.provider,"accounts");if(!_.isEmpty(o)&&p.account.isAccountExists(n.connection.account_id,o))return e=p.Cache.get(p.provider,"actions"),t=p.Cache.get(p.provider,"lists"),p.connection.renderConnections(o,t,e,n)},renderConnections(n,e,t,o){var c,i;p.account.isAccountExists(o.connection.account_id,n)&&(c=p.Templates.get("wpforms-"+p.provider+"-builder-content-connection"),i=p.Templates.get("wpforms-constant-contact-v3-builder-content-connection-conditionals"),i=_.has(o.connection,"isNew")&&o.connection.isNew?i():o.conditional,p.$elements.$connections.prepend(c({accounts:n,lists:e,actions:t,connection:o.connection,conditional:i,provider:p.provider})),p.$elements.$connections.trigger("connectionGenerated",[o]))},dataLoad(){p.Providers.ajax.request(p.provider,{data:{task:"connections_get"}}).done(function(e){e.success&&_.has(e.data,"connections")&&(["accounts","actions","actions_fields","conditionals","connections","custom_fields","lists"].forEach(n=>{p.Cache.set(p.provider,n,jQuery.extend({},e.data[n]))}),p.$elements.$connections.trigger("connectionsDataLoaded",[e.data]))})}},account:{isAccountExists(n,e){return!_.isEmpty(e)&&(!!_.isEmpty(n)||_.has(e,n))}},ui:{accountField:{change(){var n=l(this),e=n.closest(p.selectors.connection),t=l(p.selectors.actionField,e);t.prop("selectedIndex",0).trigger("change"),_.isEmpty(n.val())?(t.prop("disabled",!0),l(p.selectors.actionData,e).html("")):(t.prop("disabled",!1),n.removeClass("wpforms-error"))}},actionField:{change(){var n=l(this),e=n.closest(p.selectors.connection),t=l(p.selectors.accountField,e),o=l(p.selectors.actionField,e);p.ui.actionField.render({action:"action",target:n,account_id:t.val(),action_name:o.val(),connection_id:e.data("connection_id")}),n.removeClass("wpforms-error")},render(n){var e=p.tmpl.renderActionFields(n),t=p.connection.getById(n.connection_id);l(p.selectors.actionData,t).html(e),p.$elements.$holder.trigger("connectionRendered",[p.provider,n.connection_id])},getList(n){var e=p.Cache.get(p.provider,"lists");return _.isEmpty(e)||_.isEmpty(e[n])?[]:e[n]}}},tmpl:{commonsHTML(){return console.warn('WARNING! Function "WPForms.Admin.Builder.Providers.ConstantContactV3.tmpl.commonsHTML()" has been deprecated!'),p.Templates.get("wpforms-"+p.provider+"-builder-content-connection-error")()},renderActionFields(c){let i=wpf.getFields(),r=p.Cache.get(p.provider,"actions_fields"),a=p.Cache.get(p.provider,"custom_fields"),s=p.Cache.getById(p.provider,"connections",c.connection_id),d="";return l.each(r[c.target.val()],function(n,e){var t,o;"custom_fields"===n?(t=p.Templates.get("wpforms-providers-builder-content-connection-fields"),d+=t({connection:s,fields:i,provider:{slug:p.provider,fields:a[c.account_id]},isSupportSubfields:!0})):(t="list"===n?p.ui.actionField.getList(c.account_id):Object.values(i),o="wpforms-"+p.provider+"-builder-content-connection-"+e.type+"-field",o=p.Templates.get(o),d+=o({connection:s,name:n,field:e,provider:{slug:p.provider,fields:r[c.target.val()]},options:t}))}),d}}};return p})(document,(window,jQuery)),WPForms.Admin.Builder.Providers.ConstantContactV3.init();