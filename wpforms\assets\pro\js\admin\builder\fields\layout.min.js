var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.FieldLayout=WPForms.Admin.Builder.FieldLayout||((n,p)=>{let m={},u={init(){p(u.ready)},ready(){u.setup(),u.initLabels(),u.events(),u.rowDisplayHeightBalance(p(".wpforms-layout-display-rows, .wpforms-layout-display-blocks"))},setup(){m={$builder:p("#wpforms-builder"),$fieldOptions:p("#wpforms-field-options"),$sortableFieldsWrap:p("#wpforms-panel-fields .wpforms-field-wrap")}},events(){m.$builder.on("change",".wpforms-field-option-row-preset input",u.presetChange).on("change",".wpforms-field-option-row-display select",u.displayChange).on("mouseenter",".wpforms-field-layout-columns .wpforms-field",u.subfieldMouseEnter).on("mouseleave",".wpforms-field-layout-columns .wpforms-field",u.subfieldMouseLeave).on("wpformsFieldAdd",u.fieldAdd).on("wpformsBeforeFieldAddToDOM",u.beforeFieldAddToDOM).on("wpformsBeforeFieldAddOnClick",u.beforeFieldAddOnClick).on("wpformsBeforeFieldDelete",u.beforeFieldDelete).on("wpformsBeforeFieldDeleteAlert",u.adjustDeleteFieldAlert).on("wpformsFieldOptionTabToggle",u.fieldOptionsUpdate).on("wpformsFieldMoveRejected",u.fieldMoveRejected).on("wpformsBeforeFieldDuplicate",u.beforeFieldDuplicate).on("wpformsFieldDuplicated",u.fieldDuplicated).on("wpformsFieldDelete",u.handleFieldDelete).on("wpformsFieldAdd wpformsFieldChoiceAdd wpformsFieldChoiceDelete wpformsFieldDynamicChoiceToggle wpformsFieldLayoutChangeDisplay",u.handleFieldOperations).on("wpformsFieldMoveRejected",u.handleFieldMoveRejected).on("wpformsFieldMove",u.handleFieldMove).on("wpformsFieldDragOver wpformsFieldDragChange",u.handleFieldDrag).on("change",".wpforms-field-option-row-size select",u.handleFieldSizeChange)},isLayoutBasedField(e){return["layout","repeater"].includes(e)},handleFieldDelete(e,l,o,i){0!==i.length&&i.hasClass("wpforms-layout-display-rows")&&(u.rowDisplayHeightBalance(i),i.closest(".wpforms-field-layout").removeClass("wpforms-field-child-hovered"))},handleFieldSizeChange(){var e=p(this).parent().data("field-id"),e=p("#wpforms-field-"+e).parents(".wpforms-layout-display-rows, .wpforms-layout-display-blocks");e.length&&u.rowDisplayHeightBalance(e)},handleFieldOperations(e,l){l=p("#wpforms-field-"+l).find(".wpforms-layout-display-rows, .wpforms-layout-display-blocks");l.length&&u.rowDisplayHeightBalance(l)},handleFieldMoveRejected(e,l){l=l.prev(".wpforms-field, .wpforms-alert").data("field-id"),l=p("#wpforms-field-"+l).find(".wpforms-layout-display-rows, .wpforms-layout-display-blocks");l.length&&u.rowDisplayHeightBalance(l)},handleFieldMove(e,l){l=l.item.first().parents(".wpforms-layout-display-rows, .wpforms-layout-display-blocks");l.length&&u.rowDisplayHeightBalance(l)},handleFieldDrag(e,l,o){o.hasClass("wpforms-layout-column")&&(o=o.parents(".wpforms-layout-display-rows, .wpforms-layout-display-blocks")).length&&u.rowDisplayHeightBalance(o)},displayChange(){var e=p(this),l=e.val(),o=e.closest(".wpforms-field-option-row-display").data("field-id"),i=p("#wpforms-field-"+o).find(".wpforms-field-layout-columns");e.closest(".wpforms-field-option-row-display").parent().find(".wpforms-field-option-row-preset").toggleClass("wpforms-layout-display-rows","rows"===l),i.toggleClass("wpforms-layout-display-rows","rows"===l).toggleClass("wpforms-layout-display-columns","columns"===l).find(".wpforms-field").css("margin-bottom","columns"===l?5:""),m.$builder.trigger("wpformsFieldLayoutChangeDisplay",[o])},rowDisplayHeightBalance(e){e.each(function(){var e=p(this);let o=[];e.find(".wpforms-field, .wpforms-field-drag-placeholder").each(function(){var e=p(this),l=e.index();o[l]=Math.max(o[l]||0,e.outerHeight())}),e.find(".wpforms-field, .wpforms-field-drag-placeholder").each(function(){var e=p(this);e.css("margin-bottom",o[e.index()]-e.outerHeight()+5)})}),m.$builder.trigger("wpformsLayoutAfterHeightBalance",{$rows:e})},presetChange(e){let l=p(this),o=l.val(),i=l.closest(".wpforms-field-option-row-preset"),t=i.data("field-id"),d=p("#wpforms-field-"+t),s=d.find(".wpforms-field-layout-columns"),r=[],a=(p(n).trigger("wpformsLayoutPresetChanged",i),s.find(".wpforms-layout-column").each(function(e){r[e]=p(this).find(".wpforms-field").detach()}),u.getFieldColumnsData(t)),f=o.split("-").map(function(e,l){return{width_preset:e,fields:a[l]?a[l].fields:[]}});if(u.updateFieldColumnsData(t,f),s.html(u.generatePreviewColumns(f)),s.find(".wpforms-layout-column").each(function(e){var l=p(this);l.append(r[e]),WPForms.Admin.Builder.DragFields.initSortableContainer(l)}),f.length<a.length){let l=p([]);for(let e=f.length;e<a.length;e++)l=l.add(r[e]);l.css("margin-bottom",""),d.after(l)}u.rowDisplayHeightBalance(s),u.reorderLayoutFieldsOptions(d),m.$builder.trigger("wpformsLayoutAfterPresetChange",{fieldId:t,preset:o,newColumnsData:f,oldColumnsData:a})},generatePreviewColumns(e){if(!e?.length)return"";let l=wp.template("wpforms-layout-field-column-plus-placeholder-template")();return e.map(function(e){return`<div class="wpforms-layout-column wpforms-layout-column-${e.width_preset}">
							${l}
						</div>`}).join("")},getFieldColumnsData(e){e=p(`#wpforms-field-option-${e}-columns-json`).val();let l;try{l=JSON.parse(e)}catch(e){l=[]}return l},columnsHasFieldID(e,l){return 0<u.getFieldColumnsData(e).filter(function(e){return e.fields&&e.fields.includes(l)}).length},updateFieldColumnsData(e,l){var o=p(`#wpforms-field-option-${e}-columns-json`),i=o.val(),t=JSON.stringify(l);o.val(t),i!==t&&m.$builder.trigger("wpformsLayoutColumnsDataUpdated",{fieldId:e,data:l}),m.$builder.trigger("wpformsLayoutAfterUpdateColumnsData",{fieldId:e,data:l})},beforeFieldAddToDOM(e,l,o,i,t){t&&t.length&&t.hasClass("wpforms-layout-column")&&(e.skipAddFieldToBaseLevel=!0,u.fieldAddToColumn(o,i,l.position,t))},fieldAdd(e,l,o){var i=p("#wpforms-field-option-"+l).prev(),t=i.find(".wpforms-field-option-hidden-type").val();u.isLayoutBasedField(t)&&(t=i.find(".wpforms-field-option-hidden-id").val(),u.reorderLayoutFieldsOptions(p("#wpforms-field-"+t))),u.isLayoutBasedField(o)&&m.$builder.find(`#wpforms-field-${l} .wpforms-layout-column`).each(function(){WPForms.Admin.Builder.DragFields.initSortableContainer(p(this))})},beforeFieldAddOnClick(e,l,o){var i=m.$sortableFieldsWrap.find(".wpforms-fields-sortable-default");i.length&&!u.isFieldAllowedInColum(l,i)&&(e.preventDefault(),u.fieldMoveRejected(e,o,null,null))},beforeFieldDelete(e,l,o){u.isLayoutBasedField(o)?u.getFieldColumnsData(l).forEach(function(e){Array.isArray(e.fields)&&e.fields.forEach(function(e){WPFormsBuilder.fieldDeleteById(e)})}):(u.removeFieldFromColumns(l),p("#wpforms-field-"+l).closest(".wpforms-field").removeClass("wpforms-field-child-hovered"))},adjustDeleteFieldAlert(e,l,o){u.isLayoutBasedField(o)&&(e.preventDefault(),e=(wpforms_builder[o]?.delete_confirm?wpforms_builder[o]:wpforms_builder.layout).delete_confirm,p.confirm({title:!1,content:e,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){WPFormsBuilder.fieldDeleteById(l.id)}},cancel:{text:wpforms_builder.cancel}}}))},fieldOptionsUpdate(e,l){u.fieldLegacyLayoutSelectorUpdate(l),u.fieldSizeOptionUpdate(l)},fieldLegacyLayoutSelectorUpdate(e){var l,e=p(`#wpforms-field-option-row-${e}-css .layout-selector-display`);e.find(".wpforms-alert-layout").length||(l=p(`
				<div class="wpforms-alert-warning wpforms-alert-layout wpforms-alert wpforms-alert-nomargin">
					<h4>${wpforms_builder.layout.legacy_layout_notice_title}</h4>
					<p>${wpforms_builder.layout.legacy_layout_notice_text}</p>
				</div>
			`),e.append(l),e.find(".heading, .layouts").addClass("wpforms-hidden-strict"))},fieldSizeOptionUpdate(l){var o=p("#wpforms-field-"+l),i=o.data("field-type"),t=o.closest(".wpforms-layout-column"),d=0<t.length,s=t.closest(".wpforms-field").data("field-type")??"layout";if(!(-1<["textarea","richtext"].indexOf(i))){i=p(`#wpforms-field-option-row-${l}-size`);let e=i.find(".wpforms-notice-field-size");l=t.hasClass("wpforms-layout-column-100")&&o.closest(".wpforms-field-layout").length,t=(e.remove(),e=p(`
				<label class="sub-label wpforms-notice-field-size" title="${wpforms_builder[s].size_notice_tooltip}">
					${wpforms_builder[s].size_notice_text}
				</label>
			`),i.append(e),i.find("select")),o=(d?t.attr("title",wpforms_builder[s].size_notice_tooltip):t.attr("title",""),d&&!l);t.toggleClass("wpforms-disabled",o),e.toggleClass("wpforms-hidden",!o)}},receiveFieldToColumn(e,l,o){u.removeFieldFromColumns(e),o&&o.hasClass("wpforms-layout-column")&&(u.positionFieldInColumn(e,l,o),u.fieldOptionsUpdate(null,e),m.$builder.trigger("wpformsLayoutAfterReceiveFieldToColumn",{fieldId:e,position:l,column:o}))},removeFieldFromColumns(o){o=Number(o),m.$builder.find(".wpforms-field").each(function(){var e=p(this);if(u.isLayoutBasedField(e.data("field-type"))){var e=Number(e.data("field-id")),l=u.getFieldColumnsData(e);for(let e=0;e<l.length;e++)Array.isArray(l[e].fields)&&(l[e].fields=l[e].fields.filter(function(e){return Number(e)!==o}));u.updateFieldColumnsData(e,l)}})},positionFieldInColumn(l,e,o){var i,t,d;o&&o.hasClass("wpforms-layout-column")&&(i=o.closest(".wpforms-field").data("field-id"),o=o.index(),t=u.getFieldColumnsData(i))&&t[o]&&((d=t[o]).fields=Array.isArray(d.fields)?d.fields:[],l=Number(l),d.fields=d.fields.filter(function(e){return Number(e)!==l}),d.fields.splice(e,0,l),t[o]=d,u.updateFieldColumnsData(i,t))},duplicateLayoutField(r){var a=p("#wpforms-field-"+r);if(u.isLayoutBasedField(a.data("field-type"))){let e=u.getFieldColumnsData(r),l=WPFormsBuilder.fieldDuplicateRoutine(r,!0),o=p("#wpforms-field-"+l),i=p("#wpforms-field-option-"+l),d=o.find(".wpforms-layout-column"),s=JSON.parse(JSON.stringify(e)),t=p(`#wpforms-field-option-${r} .wpforms-field-option-row-preset input:checked`).val();i.find(`#wpforms-field-option-${l}-preset-`+t).prop("checked",!0),o.find(".wpforms-layout-column .wpforms-field").remove(),o.find(".wpforms-fields-sortable-default").removeClass("wpforms-fields-sortable-default"),e.forEach(function(e,t){if(s[t].fields=[],Array.isArray(e.fields)){let i=d.eq(t);e.fields.forEach(function(e){var l,o=p("#wpforms-field-"+e);o.length&&o.find("> .wpforms-field-duplicate").length&&(o=WPFormsBuilder.fieldDuplicateRoutine(e,!1),e=p("#wpforms-field-"+o).detach().removeClass("active"),l=p("#wpforms-field-option-"+o),i.append(e),l.hide(),s[t].fields.push(o))})}}),u.updateFieldColumnsData(l,s),u.reorderLayoutFieldsOptions(o),o.trigger("click"),WPFormsUtils.triggerEvent(m.$builder,"wpformsFieldDuplicated",[r,a,l,o])}},subfieldMouseEnter(e){p(this).closest(".wpforms-field-layout-columns").closest(".wpforms-field").addClass("wpforms-field-child-hovered")},subfieldMouseLeave(e){p(this).closest(".wpforms-field-layout-columns").closest(".wpforms-field").removeClass("wpforms-field-child-hovered")},initLabels(){p(".wpforms-field-option-layout .wpforms-field-option-row-label input").trigger("input")},fieldAddToColumn(e,l,o,i){var t=i.find(".wpforms-field"),t=("bottom"===o&&(o=t.length),t.eq(o)),d=t.data("field-id"),t=(t.length?t.before(e):i.append(e),p("#wpforms-field-option-"+d));t.length?t.before(l):m.$fieldOptions.append(l),u.receiveFieldToColumn(e.data("field-id"),o,i),u.reorderLayoutFieldsOptions(i.closest(".wpforms-field-layout, .wpforms-field-repeater"))},fieldMoveRejected(e,l,o,i){var t=l.data("field-type"),t=(t?p("#wpforms-add-fields-"+t):l).text(),t={title:wpforms_builder.heads_up,content:wpforms_builder.layout.not_allowed_alert_text.replace(/%s/g,`<strong>${t}</strong>`),type:"red"},t=wp.hooks.applyFilters("wpforms.LayoutField.fieldMoveRejectedModalOptions",t,l,o,i);p.confirm({title:t.title,content:t.content,icon:"fa fa-exclamation-circle",type:t.type,buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},reorderLayoutFieldsOptions(l){if(l?.length&&u.isLayoutBasedField(l.data("field-type"))){let e=l.data("field-id"),i=u.getFieldColumnsData(e),t=p("#wpforms-field-option-"+e);i.forEach(function(e,l){if(Array.isArray(e.fields)){let o=e.fields.slice();e.fields.forEach(function(e){let l=p("#wpforms-field-option-"+e);l.length?(l=l.detach(),t.after(l),t=l):-1!==(e=o.indexOf(e))&&o.splice(e,1)}),e.fields=o,i[l]=e}}),u.updateFieldColumnsData(e,i)}},isFieldAllowedInColum(e,l){var o=wpforms_builder.layout.not_allowed_fields.indexOf(e)<0;return wp.hooks.applyFilters("wpforms.LayoutField.isFieldAllowedInColumn",o,e,l)},beforeFieldDuplicate(e,l,o){u.isLayoutBasedField(o.data("field-type"))&&(e.preventDefault(),u.duplicateLayoutField(l),WPFormsBuilder.increaseNextFieldIdAjaxRequest())},fieldDuplicated(e,l,o,i,t){u.isLayoutBasedField(o.data("field-type"))||u.positionFieldInColumn(i,t.index()-1,t.parent())}};return u})(document,(window,jQuery)),WPForms.Admin.Builder.FieldLayout.init();