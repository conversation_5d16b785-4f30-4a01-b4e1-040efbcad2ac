<?php
/**
 * Logger Interface.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper\Logger;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Interface for consistent logging across components.
 *
 * @since {VERSION}
 */
interface LoggerInterface {

	/**
	 * Log level for emergency events.
	 *
	 * @since {VERSION}
	 */
	public const EMERGENCY = 'emergency';

	/**
	 * Log level for alert events.
	 *
	 * @since {VERSION}
	 */
	public const ALERT = 'alert';

	/**
	 * Log level for critical events.
	 *
	 * @since {VERSION}
	 */
	public const CRITICAL = 'critical';

	/**
	 * Log level for error events.
	 *
	 * @since {VERSION}
	 */
	public const ERROR = 'error';

	/**
	 * Log level for warning events.
	 *
	 * @since {VERSION}
	 */
	public const WARNING = 'warning';

	/**
	 * Log level for notice events.
	 *
	 * @since {VERSION}
	 */
	public const NOTICE = 'notice';

	/**
	 * Log level for info events.
	 *
	 * @since {VERSION}
	 */
	public const INFO = 'info';

	/**
	 * Log level for debug events.
	 *
	 * @since {VERSION}
	 */
	public const DEBUG = 'debug';

	/**
	 * Log an emergency message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function emergency( string $message, array $context = [] ): void;

	/**
	 * Log an alert message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function alert( string $message, array $context = [] ): void;

	/**
	 * Log a critical message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function critical( string $message, array $context = [] ): void;

	/**
	 * Log an error message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function error( string $message, array $context = [] ): void;

	/**
	 * Log a warning message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function warning( string $message, array $context = [] ): void;

	/**
	 * Log a notice message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function notice( string $message, array $context = [] ): void;

	/**
	 * Log an info message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function info( string $message, array $context = [] ): void;

	/**
	 * Log a debug message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function debug( string $message, array $context = [] ): void;

	/**
	 * Log a message with arbitrary level.
	 *
	 * @since {VERSION}
	 *
	 * @param string $level   Log level.
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function log( string $level, string $message, array $context = [] ): void;

	/**
	 * Set the minimum log level.
	 *
	 * @since {VERSION}
	 *
	 * @param string $level Minimum log level.
	 *
	 * @return void
	 */
	public function set_level( string $level ): void;

	/**
	 * Get the current log level.
	 *
	 * @since {VERSION}
	 *
	 * @return string Current log level.
	 */
	public function get_level(): string;

	/**
	 * Check if a log level is enabled.
	 *
	 * @since {VERSION}
	 *
	 * @param string $level Log level to check.
	 *
	 * @return bool True if level is enabled, false otherwise.
	 */
	public function is_level_enabled( string $level ): bool;

	/**
	 * Get all available log levels.
	 *
	 * @since {VERSION}
	 *
	 * @return array Array of log levels.
	 */
	public function get_levels(): array;
}
