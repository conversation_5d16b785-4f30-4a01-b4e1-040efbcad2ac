<?php

namespace Helper;

use Codeception\Module;
use Codeception\Exception\ModuleException;

/**
 * Enhanced Builder methods with robust wait strategies.
 * 
 * This helper provides improved versions of common Builder operations
 * that address timing issues and overlay problems.
 */
class BuilderEnhancements extends Module
{
    /**
     * Enhanced addField method with proper wait strategies
     * 
     * @param string $fieldType Type of field to add
     * @param array $options Field configuration options
     * @return int Field ID of the newly created field
     * @throws ModuleException
     */
    public function addFieldSafely($fieldType, $options = [])
    {
        $webDriver = $this->getModule('WPWebDriver');
        $acceptance = $this->getModule('Acceptance');
        
        // Ensure builder is ready
        $acceptance->waitForOverlayToDisappear();
        $webDriver->waitForElementVisible('#wpforms-builder', 10);
        
        // Click Add Fields tab safely
        $acceptance->safeClick('#add-fields');
        
        // Wait for fields panel to load
        $webDriver->waitForElementVisible('.wpforms-add-fields-button', 5);
        
        // Add the field
        $fieldSelector = ".wpforms-add-fields-button[data-field-type='{$fieldType}']";
        $acceptance->safeClick($fieldSelector);
        
        // Wait for field to be added to form
        $fieldInFormSelector = "[data-field-type='{$fieldType}']";
        $webDriver->waitForElementVisible($fieldInFormSelector, 10);
        
        // Get the field ID
        $fieldId = $this->getLastFieldId($fieldType);
        
        // Configure field options if provided
        if (!empty($options)) {
            $this->configureFieldSafely($fieldType, $fieldId, $options);
        }
        
        return $fieldId;
    }

    /**
     * Enhanced save method with proper wait handling
     * 
     * @throws ModuleException
     */
    public function saveSafely()
    {
        $webDriver = $this->getModule('WPWebDriver');
        $acceptance = $this->getModule('Acceptance');
        
        $acceptance->dismissAnyModals();
        $acceptance->safeClick('#wpforms-save');
        
        // Wait for save to complete
        $webDriver->waitForText('Form saved', 10);
        
        // Wait for any post-save operations
        $acceptance->waitForAjax();
    }

    /**
     * Get the ID of the last field of a specific type
     * 
     * @param string $fieldType Type of field
     * @return int Field ID
     * @throws ModuleException
     */
    private function getLastFieldId($fieldType)
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        $elements = $webDriver->_findElements("[data-field-type='{$fieldType}']");
        if (empty($elements)) {
            throw new \Exception("No {$fieldType} field found after adding");
        }
        
        $lastElement = end($elements);
        return (int) $lastElement->getAttribute('data-field-id');
    }

    /**
     * Configure field settings safely
     * 
     * @param string $fieldType Type of field
     * @param int $fieldId Field ID
     * @param array $options Configuration options
     * @throws ModuleException
     */
    private function configureFieldSafely($fieldType, $fieldId, $options)
    {
        $webDriver = $this->getModule('WPWebDriver');
        $acceptance = $this->getModule('Acceptance');
        
        // Click on the field to open settings
        $fieldSelector = "[data-field-id='{$fieldId}']";
        $acceptance->safeClick($fieldSelector);
        
        // Wait for field settings panel to load
        $webDriver->waitForElementVisible('#wpforms-field-options', 5);
        
        // Apply each option
        foreach ($options as $setting => $value) {
            $this->applyFieldSetting($setting, $value);
        }
    }

    /**
     * Apply a specific field setting
     * 
     * @param string $setting Setting name
     * @param mixed $value Setting value
     * @throws ModuleException
     */
    private function applyFieldSetting($setting, $value)
    {
        $webDriver = $this->getModule('WPWebDriver');
        $acceptance = $this->getModule('Acceptance');
        
        switch ($setting) {
            case 'label':
                $acceptance->safeFillField('#wpforms-field-option-label', $value);
                break;
                
            case 'description':
                $acceptance->safeFillField('#wpforms-field-option-description', $value);
                break;
                
            case 'required':
                if ($value) {
                    $acceptance->safeClick('#wpforms-field-option-required');
                }
                break;
                
            case 'placeholder':
                $acceptance->safeFillField('#wpforms-field-option-placeholder', $value);
                break;
                
            case 'default_value':
                $acceptance->safeFillField('#wpforms-field-option-default_value', $value);
                break;
                
            case 'size':
                $acceptance->safeSelectOption('#wpforms-field-option-size', $value);
                break;
                
            case 'style':
                $acceptance->safeSelectOption('#wpforms-field-option-style', $value);
                break;
                
            case 'max_file_number':
                $acceptance->safeFillField('#wpforms-field-option-max_file_number', $value);
                break;
                
            case 'extensions':
                $acceptance->safeFillField('#wpforms-field-option-extensions', $value);
                break;
                
            default:
                // Try to find a generic field with the setting name
                $settingSelector = "#wpforms-field-option-{$setting}";
                if ($webDriver->_findElements($settingSelector)) {
                    if (is_bool($value)) {
                        if ($value) {
                            $acceptance->safeClick($settingSelector);
                        }
                    } else {
                        $acceptance->safeFillField($settingSelector, $value);
                    }
                }
                break;
        }
        
        // Wait for setting to be applied
        $webDriver->wait(0.3);
    }

    /**
     * Wait for builder to be fully loaded
     * 
     * @param int $timeout Maximum time to wait
     * @throws ModuleException
     */
    public function waitForBuilderReady($timeout = 15)
    {
        $webDriver = $this->getModule('WPWebDriver');
        $acceptance = $this->getModule('Acceptance');
        
        // Wait for main builder elements
        $webDriver->waitForElementVisible('#wpforms-builder', $timeout);
        $webDriver->waitForElementVisible('#wpforms-builder-form', $timeout);
        
        // Wait for overlays to disappear
        $acceptance->waitForOverlayToDisappear($timeout);
        
        // Wait for page to be fully loaded
        $acceptance->waitForPageLoad($timeout);
        
        // Dismiss any initial modals
        $acceptance->dismissAnyModals();
    }

    /**
     * Switch to a specific builder panel safely
     * 
     * @param string $panel Panel name (fields, settings, etc.)
     * @throws ModuleException
     */
    public function switchToPanelSafely($panel)
    {
        $webDriver = $this->getModule('WPWebDriver');
        $acceptance = $this->getModule('Acceptance');
        
        $panelSelector = "[data-panel='{$panel}']";
        $acceptance->safeClick($panelSelector);
        
        // Wait for panel to load
        $webDriver->waitForElementVisible("#wpforms-panel-{$panel}", 5);
        
        // Wait for any panel-specific loading
        $acceptance->waitForAjax();
    }

    /**
     * Verify field was created successfully
     * 
     * @param string $fieldType Type of field
     * @param int $fieldId Field ID
     * @throws ModuleException
     */
    public function verifyFieldCreated($fieldType, $fieldId)
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        $fieldSelector = "[data-field-id='{$fieldId}'][data-field-type='{$fieldType}']";
        $webDriver->seeElement($fieldSelector);
    }

    /**
     * Delete a field safely
     * 
     * @param int $fieldId Field ID to delete
     * @throws ModuleException
     */
    public function deleteFieldSafely($fieldId)
    {
        $webDriver = $this->getModule('WPWebDriver');
        $acceptance = $this->getModule('Acceptance');
        
        // Click on field to select it
        $fieldSelector = "[data-field-id='{$fieldId}']";
        $acceptance->safeClick($fieldSelector);
        
        // Click delete button
        $deleteSelector = "[data-field-id='{$fieldId}'] .wpforms-field-delete";
        $acceptance->safeClick($deleteSelector);
        
        // Confirm deletion if prompted
        try {
            $webDriver->waitForElementVisible('.jconfirm-cell', 3);
            $acceptance->safeClick('.jconfirm-buttons .btn-confirm');
        } catch (\Exception $e) {
            // No confirmation dialog, continue
        }
        
        // Wait for field to be removed
        $webDriver->waitForElementNotVisible($fieldSelector, 5);
    }

    /**
     * Duplicate a field safely
     * 
     * @param int $fieldId Field ID to duplicate
     * @return int ID of the duplicated field
     * @throws ModuleException
     */
    public function duplicateFieldSafely($fieldId)
    {
        $webDriver = $this->getModule('WPWebDriver');
        $acceptance = $this->getModule('Acceptance');
        
        // Get field type before duplication
        $fieldSelector = "[data-field-id='{$fieldId}']";
        $fieldType = $webDriver->grabAttributeFrom($fieldSelector, 'data-field-type');
        
        // Click on field to select it
        $acceptance->safeClick($fieldSelector);
        
        // Click duplicate button
        $duplicateSelector = "[data-field-id='{$fieldId}'] .wpforms-field-duplicate";
        $acceptance->safeClick($duplicateSelector);
        
        // Wait for duplication to complete
        $acceptance->waitForAjax();
        
        // Return the ID of the new field
        return $this->getLastFieldId($fieldType);
    }
}
