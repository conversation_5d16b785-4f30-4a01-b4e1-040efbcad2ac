<?php
/**
 * View for the Modals Generator tab.
 *
 * @var array $types Options for modal types/colors.
 * @var array $sizes Options for modal widths.
 */
?>

<form action="" id="wpforms-admin-devtools-modal-generator">

	<div class="wpforms-setting-row wpforms-setting-row-select">
		<span class="wpforms-setting-label">
			<label for="wpf-mg-icon">Icon:</label>
		</span>

		<span class="wpforms-setting-field">
			<input type="text" id="wpf-mg-icon" name="icon" value="info-circle" placeholder="Modal icon">
			<p class='desc'>Font Awesome icon name, see <a href="https://fontawesome.com/v5/icons/?s=solid&f=classic" target="_blank" rel="noopener noreferrer">here</a>.</p>
		</span>
	</div>

	<div class="wpforms-setting-row wpforms-setting-row-text">
		<span class="wpforms-setting-label">
			<label for="wpf-mg-title">Title:</label>
		</span>

		<span class="wpforms-setting-field">
			<input type="text" id="wpf-mg-title" name="title" value="" placeholder="Modal title">
		</span>
	</div>

	<div class="wpforms-setting-row wpforms-setting-row-textarea">
		<span class="wpforms-setting-label">
			<label for="wpf-mg-content">Content:</label>
		</span>

		<span class="wpforms-setting-field">
			<textarea rows="10" id="wpf-mg-content" name="content" placeholder="Modal content"></textarea>
		</span>
	</div>

	<div class="wpforms-setting-row wpforms-setting-row-radio">
		<span class="wpforms-setting-label">
			<label for="">Type:</label>
		</span>

		<span class="wpforms-setting-field wpforms-admin-devtools-modal-generator-types">
			<?php
			foreach ( $types as $modal_type => $color_value ) {

				printf(
					'<label for="type-%1$s" class="%2$s" style="background-color: %3$s;">
						<input type="radio" id="type-%1$s" name="type" value="%1$s"%4$s>
					</label>',
					esc_attr( $modal_type ),
					$modal_type === 'orange' ? esc_attr( 'wpf-mg-selected' ) : '',
					esc_attr( $color_value ),
					checked( $modal_type, 'orange', false )
				);

			}
			?>
		</span>
	</div>

	<div class="wpforms-setting-row wpforms-setting-row-checkbox">
		<span class="wpforms-setting-label">
			<label for="">Close Icon:</label>
		</span>

		<span class="wpforms-setting-field">
			<label for='wpf-mg-close-icon' class="wpforms-setting-field-inner-label">
				<input type="checkbox" id="wpf-mg-close-icon" name="closeIcon" value="1">
				Display close icon
			</label>
		</span>
	</div>

	<div class="wpforms-setting-row wpforms-setting-row-radio">
		<span class="wpforms-setting-label">
			<label for="">Width:</label>
		</span>

		<span class="wpforms-setting-field wpforms-admin-devtools-modal-generator-width">
			<?php
			foreach ( $sizes as $size ) {

				printf(
					'<label for="wpf-mg-width-%1$s">
						<input type="radio" id="wpf-mg-width-%1$s" name="boxWidth" value="%1$s"%2$s>
						%1$s
					</label>',
					esc_attr( $size ),
					checked( $size, '400px', false )
				);

			}
			?>
		</span>
	</div>

	<div class="wpforms-setting-row wpforms-setting-row-text">
		<span class="wpforms-setting-label">
			<label for="wpf-mg-primary-button">Primary Button:</label>
		</span>

		<span class="wpforms-setting-field">
			<input type="text" id="wpf-mg-primary-button" name="buttonPrimary" value="Ok" placeholder="Button label">
		</span>
	</div>

	<div class="wpforms-setting-row wpforms-setting-row-text">
		<span class="wpforms-setting-label">
			<label for="wpf-mg-secondary-button">Secondary Button:</label>
		</span>

		<span class="wpforms-setting-field">
			<input type="text" id="wpf-mg-secondary-button" name="buttonSecondary" value="Cancel" placeholder="Button label">
		</span>
	</div>

	<p class="submit wpforms-admin-page">
		<button id="wpforms-admin-devtools-modal-generator-open" type="submit" class="wpforms-btn wpforms-btn-md wpforms-btn-orange">Show modal</button>
	</p>

</form>
