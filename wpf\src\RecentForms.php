<?php

namespace WPForms\DevTools;

use WP_Admin_Bar;
use WP_Post;

/**
 * Recent forms class.
 *
 * @since {VERSION}
 */
class RecentForms {

	const CF = 'cf';
	const FP = 'fp';

	const ADDITIONAL = [
		self::CF => [
			'enable' => 'conversational_forms_enable',
			'slug'   => 'wpforms-conversational-forms',
		],
		self::FP => [
			'enable' => 'form_pages_enable',
			'slug'   => 'wpforms-form-pages',
		],
	];

	/**
	 * Adds positions to WPForms menu in admin bar.
	 *
	 * @since {VERSION}
	 *
	 * @param WP_Admin_Bar $wp_admin_bar Admin bar.
	 *
	 * @return void
	 */
	public static function admin_bar_menu_links( $wp_admin_bar ) {

		if ( ! function_exists( 'wpforms' ) ) {
			return;
		}

		// Parent node for recent forms.
		$nodes[] = [
			'parent' => 'wpforms-menu',
			'title'  => esc_html__( 'Recent Forms', 'wpf' ),
			'id'     => 'wpforms-recent-forms',
			'href'   => '#',
		];

		/**
		 * Recent forms.
		 *
		 * @var WP_Post[] $recent_forms Recent forms.
		 */
		$recent_forms = get_posts(
			[
				'post_type'   => 'wpforms',
				'orderby'     => 'modified',
				'numberposts' => 20,
			]
		);

		foreach ( $recent_forms as $form ) {
			$edit_link = add_query_arg(
				[
					'view'    => 'fields',
					'form_id' => $form->ID,
				],
				admin_url( 'admin.php?page=wpforms-builder' )
			);

			$entries_link = add_query_arg(
				[
					'view'    => 'list',
					'form_id' => $form->ID,
				],
				admin_url( 'admin.php?page=wpforms-entries' )
			);

			// Single form.
			$nodes[] = [
				'parent' => 'wpforms-recent-forms',
				'title'  => esc_html( $form->post_title ),
				'id'     => 'wpforms-recent-form-' . $form->ID,
				'href'   => esc_url( $edit_link ),
				'meta'   => [
					'class' => 'wpforms-recent-single-form-link',
				],
			];

			// Child node for single form - edit link.
			$nodes[] = [
				'parent' => 'wpforms-recent-form-' . $form->ID,
				'id'     => 'wpforms-recent-form-' . $form->ID . '-edit',
				'title'  => esc_html__( 'Edit', 'wpf' ),
				'href'   => esc_url( $edit_link ),
			];

			// Child node for single form - preview link.
			$nodes[] = [
				'parent' => 'wpforms-recent-form-' . $form->ID,
				'id'     => 'wpforms-recent-form-' . $form->ID . '-preview',
				'title'  => esc_html__( 'Preview', 'wpf' ),
				'href'   => esc_url( wpforms_get_form_preview_url( $form->ID ) ),
			];

			// Child node for single form - entries link.
			$nodes[] = [
				'parent' => 'wpforms-recent-form-' . $form->ID,
				'id'     => 'wpforms-recent-form-' . $form->ID . '-entries',
				'title'  => esc_html__( 'Entries', 'wpf' ),
				'href'   => esc_url( $entries_link ),
			];

			$form_data = wpforms_decode( $form->post_content );

			$nodes[] = self::append_additional(
				$form,
				$form_data,
				self::CF,
				esc_html__( 'View conversational form', 'wpf' )
			);
			$nodes[] = self::append_additional(
				$form,
				$form_data,
				self::FP,
				esc_html__( 'View form page', 'wpf' )
			);
		}

		foreach ( $nodes as $node ) {
			if ( ! empty( $node ) ) {
				$wp_admin_bar->add_node( $node );
			}
		}
	}

	/**
	 * Add link to conversational form or form page.
	 *
	 * @since {VERSION}
	 *
	 * @param WP_Post $form      Post object.
	 * @param array   $form_data Form data.
	 * @param string  $addon_key Addon key, 'cf' or 'fp' only.
	 * @param string  $title     Menu title.
	 *
	 * @return array|void
	 */
	private static function append_additional( $form, $form_data, $addon_key, $title ) {

		if ( ! array_key_exists( $addon_key, self::ADDITIONAL ) ) {
			return;
		}

		$addons_class = Main::wpforms_obj( 'addons' );

		if ( ! $addons_class ) {
			return;
		}

		$addons = $addons_class->get_by_slugs( [ self::ADDITIONAL[ $addon_key ]['slug'] ] );

		if ( ! isset( $addons[0]['status'] ) || $addons[0]['status'] !== 'active' ) {
			return;
		}

		if (
			isset(
				$form_data['settings']['conversational_forms_page_slug'],
				$form_data['settings'][ self::ADDITIONAL[ $addon_key ]['enable'] ]
			) &&
			(int) $form_data['settings'][ self::ADDITIONAL[ $addon_key ]['enable'] ] === 1
		) {
			return [
				'parent' => 'wpforms-recent-form-' . $form->ID,
				'id'     => 'wpforms-recent-form-' . $form->ID . '-preview-' . $addon_key,
				'title'  => $title,
				'href'   => esc_url( home_url( isset( $form->post_name ) ? $form->post_name : '' ) ),
			];
		}
	}
}
