<?php

namespace WPForms\DevTools;

use WP_Styles;

/**
 * RTL Tester for WPForms Developer Tools.
 *
 * Adds the ability to switch between text directions (RTL/LTR) for testing WPForms.
 *
 * @since {VERSION}
 */
class RTL {

	/**
	 * Singleton instance of the class.
	 *
	 * @since {VERSION}
	 *
	 * @var RTL
	 */
	private static $instance;

	/**
	 * Get the instance of the class.
	 *
	 * @since {VERSION}
	 *
	 * @return RTL
	 */
	public static function get_instance(): RTL {

		if ( ! isset( self::$instance ) || ! ( self::$instance instanceof self ) ) {
			self::$instance = new self();
		}

		return self::$instance;
	}

	/**
	 * Constructor.
	 *
	 * @since {VERSION}
	 */
	private function __construct() {

		$this->hooks();
	}

	/**
	 * Initialize the class.
	 *
	 * @since {VERSION}
	 */
	public static function instance(): void {

		self::get_instance();
	}

	/**
	 * Register hooks.
	 *
	 * @since {VERSION}
	 */
	private function hooks(): void {

		add_action( 'init', [ $this, 'set_direction' ], 20 );
	}

	/**
	 * Set the text direction.
	 *
	 * @since {VERSION}
	 */
	public function set_direction(): void {

		global $wp_locale, $wp_styles;

		$user_id = get_current_user_id();

		if ( isset( $_REQUEST['wpf_utils_action'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification
			$direction = sanitize_key( $_REQUEST['wpf_utils_action'] ) === 'rtl' ? 'rtl' : 'ltr'; // phpcs:ignore WordPress.Security.NonceVerification

			update_user_meta( $user_id, 'rtladminbar', $direction );
		} else {
			$direction = get_user_meta( $user_id, 'rtladminbar', true );

			if ( empty( $direction ) ) {
				$direction = $wp_locale->text_direction ?? 'ltr';
			}
		}

		$wp_locale->text_direction = $direction;

		if ( ! $wp_styles instanceof WP_Styles ) {
			$wp_styles = new WP_Styles(); // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited
		}

		$wp_styles->text_direction = $direction;
	}
}
