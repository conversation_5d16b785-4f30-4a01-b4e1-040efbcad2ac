<?php
/**
 * View for the Hooks tab.
 *
 * @var array  $plugins A list of WPForms plugins.
 * @var array  $hooks   Loaded hooks database subset.
 * @var string $type    Active hook type (actions or filters).
 */

$selected_plugin = ! empty( $_GET['plugin'] ) ? sanitize_key( $_GET['plugin'] ) : 'wpforms'; // phpcs:ignore WordPress.Security.NonceVerification.Recommended
?>

<div class="wpforms-setting-row wpforms-setting-row-select">
	<span class="wpforms-setting-label">
		<label for="wpforms-admin-devtools-hooks-plugin">Plugin:</label>
	</span>

	<span class="wpforms-setting-field">
		<span class="choicesjs-select-wrap">
			<select id="wpforms-admin-devtools-hooks-plugin" class="choicesjs-select" data-sorting="off" data-search="1">
				<?php foreach ( $plugins as $key => $plugin ) : // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited ?>
					<?php $selected = $selected_plugin === $key ? 'selected' : ''; ?>
					<option value="<?php echo esc_attr( $key ); ?>" <?php echo esc_attr( $selected ); ?>>
						<?php echo esc_html( $plugin ); ?>
					</option>
				<?php endforeach; ?>
			</select>
		</span>
	</span>
</div>

<div class="wpforms-setting-row wpforms-setting-row-text">
	<span class="wpforms-setting-label">
		<label for="wpforms-admin-devtools-hooks-search">Search:</label>
	</span>

	<span class="wpforms-setting-field">
		<input type="text" id="wpforms-admin-devtools-hooks-search" name="wpforms-admin-devtools-hooks-search" value="" placeholder="Type a keyword">
	</span>
</div>

<div>
	<ul class="wpforms-hooks-tabs">
		<li class="<?php echo $type === 'actions' ? 'wpforms-hooks-tab-active' : ''; ?>">
			<a href="<?php echo esc_url( add_query_arg( 'type', 'actions' ) ); ?>">
				Actions
			</a>
		</li>
		<li class="<?php echo $type === 'filters' ? 'wpforms-hooks-tab-active' : ''; ?>">
			<a href="<?php echo esc_url( add_query_arg( 'type', 'filters' ) ); ?>">
				Filters
			</a>
		</li>
	</ul>
</div>

<div id="wpforms-admin-devtools-hooks-list">
	<div class="list">
		<?php if ( empty( $hooks ) ) : ?>
			<p class="wpforms-hooks-empty">
				<?php
				printf(
					'No custom %1$s in <strong>%2$s</strong>.',
					esc_html( $type ),
					esc_html( $plugins[ $selected_plugin ] )
				);
				?>
			</p>
		<?php endif; ?>

		<?php foreach ( $hooks as $key => $hook ) : ?>
			<div class="wpforms-hook">
				<p class="wpforms-hook-file">
					<?php echo esc_html( $hook['file'] ); ?>
				</p>

				<h3 class="wpforms-hook-name">
					<?php echo wp_kses( $hook['name'], [ 'code' => [] ] ); ?>
				</h3>

				<?php if ( empty( $hook['doc']['description'] ) ) : ?>

					<p class="wpforms-hook-not-documented">
						<?php printf( 'This %s is not documented yet.', esc_html( $hook['type'] ) ); ?>
					</p>

				<?php else : ?>

					<p class="wpforms-hook-description">
						<?php echo wp_kses( $hook['doc']['description'], [ 'code' => [] ] ); ?>
					</p>

					<div class="wpforms-hook-long-description">
						<?php echo wp_kses( $hook['doc']['long_description'], [ 'code' => [] ] ); ?>
					</div>

					<!-- Version History -->
					<?php if ( ! empty( $hook['doc']['tags']['since'] ) ) : ?>
						<h4 class="wpforms-hook-tag-title">Versions</h4>

						<table class="wpforms-hook-tag-data">
							<?php foreach ( $hook['doc']['tags']['since'] as $index => $since ) : ?>
								<tr>
									<td class="wpforms-hook-tag-code"><?php echo esc_html( $since['content'] ); ?></td>
									<td>
										<?php
										$description = '';

										if ( $index === 0 && empty( $since['description'] ) ) {
											$description = 'Introduced';
										}

										if ( isset( $since['description'] ) ) {
											$description = $since['description'];
										}

										echo wp_kses( $description, [ 'code' => [] ] );
										?>
									</td>
								</tr>
							<?php endforeach; ?>
						</table>
					<?php endif; ?>
					<!--/ Version History -->

					<!-- Hook Parameters -->
					<?php if ( ! empty( $hook['doc']['tags']['param'] ) ) : ?>
						<h4 class="wpforms-hook-tag-title">Parameters</h4>

						<table class="wpforms-hook-tag-data">
							<?php foreach ( $hook['doc']['tags']['param'] as $param ) : ?>
								<tr>
									<td class="wpforms-hook-tag-code"><?php echo esc_html( $param['variable'] ); ?></td>
									<td class="wpforms-hook-tag-code"><?php echo esc_html( implode( ' | ', $param['types'] ) ); ?></td>
									<td><?php echo wp_kses( $param['content'], [ 'code' => [] ] ); ?></td>
								</tr>
							<?php endforeach; ?>
						</table>
					<?php endif; ?>
					<!--/ Hook Parameters -->

				<?php endif; ?>
			</div>
		<?php endforeach; ?>
	</div>
</div>
