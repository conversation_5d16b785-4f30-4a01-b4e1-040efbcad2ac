<?php

namespace WPForms\DevTools\WhatsNew;

use WPForms\Admin\Splash\SplashCache;
use WPForms\Helpers\File;

/**
 * What's New tool utils.
 *
 * @since 0.39
 */
final class Utils {

	/**
	 * Import data from the remote JSON file.
	 *
	 * @since 0.39
	 */
	public function import_remote_data(): void {

		$remote_data = array_reverse( $this->get_remote_data() );

		if ( empty( $remote_data ) ) {
			return;
		}

		// Get previously imported posts.
		$posts = get_posts(
			[
				'post_type'      => 'wpf_whats_new',
				'post_status'    => 'any',
				'posts_per_page' => -1,
				'meta_key'       => '_wpf_is_import', // phpcs:ignore WordPress.DB.SlowDBQuery.slow_db_query_meta_key
				'meta_value'     => '1', // phpcs:ignore WordPress.DB.SlowDBQuery.slow_db_query_meta_value
			]
		);

		// Remove previously imported posts.
		if ( is_array( $posts ) ) {
			foreach ( $posts as $post ) {
				$this->delete_post_images( $post->ID );
				wp_delete_post( $post->ID, true );
			}
		}

		// Import new posts.
		foreach ( $remote_data as $item ) {
			$this->import_single_post( $item );
		}

		// Redirect to the What's New page.
		wp_safe_redirect( admin_url( 'edit.php?post_type=wpf_whats_new' ) );

		exit;
	}

	/**
	 * Get remote data.
	 *
	 * @since 0.39
	 *
	 * @return array
	 * @noinspection PhpUndefinedConstantInspection
	 */
	private function get_remote_data(): array {

		$url = defined( 'WPFORMS_SPLASH_REMOTE_SOURCE' )
			? WPFORMS_SPLASH_REMOTE_SOURCE
			: SplashCache::REMOTE_SOURCE;

		$request = wp_remote_get( $url );

		if ( is_wp_error( $request ) ) {
			return [];
		}

		$json = wp_remote_retrieve_body( $request );
		$data = json_decode( $json, true );

		return empty( $data ) ? [] : $data;
	}

	/**
	 * Import single What's New post.
	 *
	 * @since 0.39
	 *
	 * @param array $data Data from JSON file.
	 *
	 * @noinspection PhpUndefinedFunctionInspection
	 */
	private function import_single_post( array $data ): void { // phpcs:ignore Generic.Metrics.CyclomaticComplexity

		if ( empty( $data['title'] ) || empty( $data['content'] ) ) {
			return;
		}

		// Insert new post.
		$post_id = wp_insert_post(
			[
				'post_title'   => $data['title'],
				'post_content' => $data['content'],
				'post_status'  => 'publish',
				'post_type'    => 'wpf_whats_new',
				'meta_input'   => [
					'_wpf_is_import' => '1',
				],
			]
		);

		// Import version term.
		if ( ! empty( $data['version'] ) ) {
			// Add terms to the `wpf_version` taxonomy.
			wp_insert_term( $data['version'], 'wpf_version' );

			// Get version term.
			$version_terms = get_terms(
				[
					'taxonomy'   => 'wpf_version',
					'hide_empty' => false,
					'name'       => $data['version'],
					'fields'     => 'ids',
				]
			);

			update_field( 'version', $version_terms, $post_id );
		}

		// Import license term.
		if ( ! empty( $data['type'] ) ) {
			// Add terms to the `wpf_license` taxonomy.
			foreach ( $data['type'] as $license ) {
				wp_insert_term( $license, 'wpf_license' );
			}

			wp_set_object_terms( $post_id, $data['type'], 'wpf_license' );
		}

		$img_type = $data['img']['type'] ?? 'icon';

		update_field( 'img', $img_type, $post_id );
		$this->import_single_post_image( $post_id, $data );

		update_field( 'btn', $data['btns']['main']['text'] ?? '', $post_id );
		update_field( 'btn_url', $data['btns']['main']['url'] ?? '', $post_id );

		update_field( 'btn_alt', $data['btns']['alt']['text'] ?? '', $post_id );
		update_field( 'btn_alt_url', $data['btns']['alt']['url'] ?? '', $post_id );

		update_field( 'featured', $data['featured'] ?? false, $post_id );
		update_field( 'new', $data['new'] ?? false, $post_id );
	}

	/**
	 * Import single What's New post image.
	 *
	 * @since 0.39
	 *
	 * @param int   $post_id Post ID.
	 * @param array $data    Data from JSON file.
	 *
	 * @noinspection PhpUndefinedFunctionInspection
	 */
	private function import_single_post_image( int $post_id, array $data ): void {

		if ( empty( $data['img']['url'] ) ) {
			return;
		}

		$img_type = $data['img']['type'] ?? 'icon';

		// Download image.
		$image = wp_remote_get( $data['img']['url'] );

		if ( is_wp_error( $image ) ) {
			return;
		}

		// Get an image file.
		$file_data = wp_remote_retrieve_body( $image );

		if ( empty( $file_data ) ) {
			return;
		}

		$file_ext    = strtolower( pathinfo( $data['img']['url'], PATHINFO_EXTENSION ) );
		$file_name   = $img_type . '-' . strtolower( pathinfo( $data['img']['url'], PATHINFO_BASENAME ) );
		$uploads_dir = File::get_upload_dir();

		$wpforms_images_dir = sprintf(
			'%1$s%2$s/',
			SplashCache::LOCAL_SOURCE_DIR,
			$data['version']
		);

		$uploads_images_dir = sprintf(
			'%1$swhats-new/%2$s/',
			$uploads_dir,
			$data['version']
		);

		$wpforms_file_dir = wp_normalize_path( WPFORMS_PLUGIN_DIR . $wpforms_images_dir );
		$wpforms_file_url = wp_normalize_path( WPFORMS_PLUGIN_URL . $wpforms_images_dir . $file_name );

		$dirs_created =
			File::mkdir( WPFORMS_PLUGIN_DIR . 'assets/data/' ) &&
			File::mkdir( WPFORMS_PLUGIN_DIR . SplashCache::LOCAL_SOURCE_DIR ) &&
			File::mkdir( $wpforms_file_dir ) &&
			File::mkdir( $uploads_dir . 'whats-new/' ) &&
			File::mkdir( $uploads_images_dir );

		// Check if the directories exist.
		if ( ! $dirs_created ) {
			return;
		}

		$uploads_file_path = $uploads_images_dir . $file_name;
		$wpforms_file_path = $wpforms_file_dir . $file_name;

		// Save image to the `uploads/wpforms/whats-new/` directory.
		File::put_contents( $uploads_images_dir . $file_name, $file_data );

		// Save image to the `wpforms/assets/data/whats-new/` directory.
		File::put_contents( $wpforms_file_path, $file_data );

		$attachment = [
		    'guid'           => $wpforms_file_url,    // URL to the file.
		    'post_mime_type' => 'image/' . $file_ext, // File MIME type.
		    'post_title'     => $file_name,           // File title.
		    'post_content'   => '',                   // Optional content.
		    'post_status'    => 'publish',            // Set status to inherit.
		];

		$attachment_id = wp_insert_attachment( $attachment, $uploads_file_path );

		if ( is_wp_error( $attachment_id ) ) {
			return;
		}

		// Store the image file path in the attachment metadata.
		add_post_meta( $attachment_id, 'wpf_whats_new_image_wpforms_file_path', $wpforms_images_dir . $file_name, true );

		// Update the field with the new image id.
		update_field( 'img_' . $img_type, $attachment_id, $post_id );
	}

	/**
	 * Delete post attached images.
	 *
	 * @since 0.39
	 *
	 * @param int $post_id Post ID.
	 *
	 * @return void
	 */
	private function delete_post_images( int $post_id ): void {

		// Get all attachments for the post.
		$attachments = get_posts(
			[
				'post_type'      => 'attachment',
				'posts_per_page' => -1,
				'post_parent'    => $post_id,
				'post_mime_type' => 'image',
				'orderby'        => 'menu_order',
				'order'          => 'ASC',
			]
		);

		// Check if there are attachments.
		if ( empty( $attachments ) ) {
			return;
		}

		foreach ( $attachments as $attachment ) {
			if ( empty( $attachment->ID ) ) {
				continue;
			}

			$file_path = get_post_meta( $attachment->ID, 'wpf_whats_new_image_wpforms_file_path', true );

			if ( ! empty( $file_path ) ) {
				File::delete( WPFORMS_PLUGIN_DIR . $file_path );
			}

			wp_delete_attachment( $attachment->ID, true );
		}
	}
}
