// Form Builder styles.
//
// Alert styles.
// Styling tweaks for jQuery-confirm JS alert library.
//
// @since 1.6.8

// jConfirm type mixin.
//
// @since 1.6.8
//
// @param $color       Main color.
// @param $hover_color Hover color.
// @param $is_default  Is default type?
//
@mixin jconfirm_type( $color, $hover_color, $is_default: false ) {

	@if not $is_default {
		border-top-color: $color !important;

		.jconfirm-title-c .jconfirm-icon-c {
			color: $color !important;
		}
	}

	button.btn-confirm {
		background-color: $color;
		border-color: $color;

		&:hover {
			background-color: $hover_color;
			border-color: $hover_color;
		}
	}
}

.wpforms_page_wpforms-builder, .block-editor-page, .elementor-editor-active {
	.jconfirm {
		.jconfirm-box-container .jconfirm-box {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			justify-items: center;
			animation: none;
			background: $color_white;
			border-radius: $border_radius_m;
			border-top-style: solid;
			border-top-width: 4px;
			box-shadow: 0 3px 6px rgba( 0, 0, 0, .15 );
			padding-top: 34px;

			.jconfirm-title-c,
			.jconfirm-content-pane,
			.jconfirm-buttons {
				grid-column: 1 / -1;
			}

			// Default.
			&.jconfirm-type-default {
				border-top-width: 0;
				padding-top: 25px;

				.jconfirm-title-c {
					margin-bottom: $spacing_m;

					.jconfirm-icon-c {
						font-size: 44px;
						margin-bottom: -6px;
					}
				}

				@include jconfirm_type( $color_orange, $color_dark_orange, true );
			}

			// Red.
			&.jconfirm-type-red {
				@include jconfirm_type( $color_red, $color_dark_red );
			}

			// Orange.
			&.jconfirm-type-orange {
				@include jconfirm_type( $color_orange, $color_dark_orange );
			}

			// Yellow.
			&.jconfirm-type-yellow {
				@include jconfirm_type( $color_yellow, $color_dark_yellow );
			}

			// Blue.
			&.jconfirm-type-blue {
				@include jconfirm_type( $color_bright_blue, $color_blue );
			}

			// Green.
			&.jconfirm-type-green {
				@include jconfirm_type( $color_green, $color_dark_green );
			}

			// Purple.
			&.jconfirm-type-purple {
				@include jconfirm_type( $color_purple, $color_purple_hover );
			}

			.jconfirm-closeIcon {
				color: transparent;
				font-family: $font_fa;
				height: 14px;
				opacity: 1;
				inset-inline-end: $spacing_s;
				top: $spacing_s;
				width: 14px;

				&:after {
					color: $color_close;
					content: "\f00d";
					font-size: $font_size_m;
					inset-inline-start: 0;
					position: absolute;
					top: 0;
				}

				&:hover {
					&:after {
						color: $color_close_hover !important;
					}
				}
			}

			.jconfirm-title-c {
				margin: 0 0 $spacing_m 0;
				padding: 0;
				font-weight: 600;

				.jconfirm-icon-c {
					font-size: 47px;
					margin: 0;

					-ms-transform: none !important;
					-webkit-transform: none !important;
					transform: none !important;
					-webkit-transition: none !important;
					transition: none !important;

					& + .jconfirm-title {
						margin-top: $spacing_m;
					}
				}

				.jconfirm-title {
					color: $color_primary_text;
					display: block;
					line-height: 30px;
				}
			}

			.jconfirm-content-pane {
				display: block;
				margin-bottom: $spacing_m;

				.jconfirm-content {
					color: $color_primary_text;
					font-size: $font_size_m;
					line-height: $font_size_xl;
					margin-bottom: 0;
					overflow: inherit;

					&.lite-upgrade {
						p {
							color: $color_secondary_text;
							font-size: $font_size_l;
							padding: 0 $spacing_m;
						}
					}

					p {
						font-size: inherit;
						line-height: inherit;
						margin: 0 0 16px;

						&:last-of-type {
							margin: 0;
						}

						&.large {
							font-size: $font_size_l;
						}

						&.small {
							font-size: $font_size_s;
						}

						&.bold {
							font-weight: 600;
						}
					}

					strong {
						font-weight: 600;
					}

					input[type=text],
					input[type=number],
					input[type=email],
					input[type=url],
					input[type=password],
					input[type=search],
					input[type=tel],
					textarea,
					select {
						margin: $spacing_s 2px;
						width: calc( 100% - 4px );
					}

					.error {
						color: $color_red;
						font-size: 14px;
						font-weight: 600;
						line-height: 1.4;
						margin-top: 10px;
						display: none;
					}
				}
			}

			.already-purchased {
				display: block;
				grid-row: 5;
				grid-column: 1 / -1;
				color: $color_hint;
				font-size: $font_size_s;
				margin-top: $spacing_ms;
				text-decoration: underline;
				text-align: center;

				&:hover {
					color: $color_secondary_text;
					text-decoration: underline;
				}
			}

			.discount-note {
				grid-row: 4;
				grid-column: 1 / -1;
				margin: 25px 0 0 0;
				text-align: center;
				width: 100%;

				p {
					background-color: $color_lightest_yellow;
					color: $color_secondary_text;
					font-size: $font_size_m;
					margin: 0 -30px;
					padding: 22px 52px 12px 52px;
					position: relative;

					&:after {
						background-color: $color_white;
						border-radius: 50%;
						color: $color_green;
						content: "\f058";
						display: inline-block;
						font: normal normal normal $font_size_s/1 $font_fa;
						font-size: 26px;
						margin-inline-end: -18px;
						padding: $spacing_xs 6px;
						position: absolute;
						inset-inline-end: 50%;
						text-rendering: auto;
						top: -16px;

						@include font_smoothing();
					}
				}

				span {
					color: $color_green;
					font-weight: 700;
				}

				a {
					color: $color_secondary_text;
					display: block;
					margin-top: 12px;
				}
			}

			.feature-video {
				margin: $spacing_l 0 0 0;
			}

			.pro-feature-video {
				margin: $spacing_ms 0 $spacing_s 0;
			}

			input[type=text]:not(.choices__input) {
				display: block;
				margin-top: $spacing_s;
			}

			#wpforms-edu-modal-license-key {
				margin-top: $spacing_m;
			}

			.jconfirm-buttons {
				margin-top: -$spacing_s;

				button {
					background: $color_light_background;
					border: $border_std;
					border-radius: $border_radius_s;
					color: $color_secondary_text;
					font-size: $font_size_m;
					font-weight: 600;
					line-height: $font_size_m + 4;
					outline: none;
					padding: 11px 17px;
					text-transform: none;
					margin: $spacing_s;

					&:hover {
						background: $color_light_background_hover;
						border-color: $color_border;
					}
				}

				button {
					min-width: 83px;

					&[disabled] {
						cursor: no-drop;
						pointer-events: none;
					}

					&.btn-confirm {
						color: $color_white;
					}

					&.btn-fix-with-ai {
						background-color: $color_purple_background;
						border-color: $color_purple;
						color: $color_purple;
						padding: $spacing_s;

						&:hover,
						&:focus {
							background-color: $color_purple_background_hover;
							border-color: $color_purple_hover;
							color: $color_purple_hover;
						}

						&::before {
							content: '';
							display: inline-block;
							vertical-align: middle;
							width: $font_size_m;
							height: $font_size_m;
							background-image: url(../../images/integrations/ai/ai-feature.svg);
							background-size: $font_size_m $font_size_m;
							margin-inline-end: $spacing_ss;
							opacity: .85;
						}
					}

					&.hidden + button {
						margin-left: 0;
						margin-right: 0;
					}

					&.btn-block {
						display: block;
						margin: 0 0 $spacing_s 0 !important;
						text-align: center;
						width: 100%;
					}

					&.btn-normal-case {
						text-transform: none !important;
					}

					i {
						margin-inline-end: $spacing_s;
					}
				}
			}

			&.wpforms-providers-account-add-modal {
				.jconfirm-content {
					.description {
						font-size: $font_size_ss;
						line-height: 1.4;
						margin-top: $spacing_ms;
					}
				}
			}

			&.wpforms-builder-keyboard-shortcuts {
				.jconfirm-content-pane {
					max-height: calc( 100vh - 333px );

					@include transition( max-height, $transition_slow, ease-out );
				}

				.jconfirm-title-c {
					margin-bottom: $spacing_ms;
				}

				.wpforms-columns {
					margin: $spacing_l 0 0 0;
				}

				.wpforms-column {
					border: $border_std;
					border-radius: $border_radius_s;

					li {
						border-bottom: 1px solid $color_divider;
						font-size: $font_size_s;
						line-height: $spacing_l;
						margin: 0;
						padding: $spacing_ss $spacing_s;
						text-align: start;
						display: flex;

						&:last-child {
							border-bottom: none;
						}

						span {
							margin-inline-start: auto;

							i {
								background-color: $color_fields_background;
								border-radius: $border_radius_s;
								color: $color_blue;
								display: inline-block;
								font-style: normal;
								line-height: 16px;
								margin-inline-start: $spacing_xs;
								min-width: $spacing_l;
								padding: $spacing_xs $spacing_s;
								text-transform: capitalize;
							}
						}
					}
				}
			}
		}

		&.jconfirm-wpforms-education {
			.jconfirm-content-pane {
				height: auto !important;
				min-height: fit-content;
			}
		}
	}

	.choices {
		font-size: $font_size_m;
		text-align: start;

		input[type=text].choices__input:not(.wpforms-hidden) {
			display: inline-block !important;
		}
	}

	.jconfirm.has-video {

		.jconfirm-box-container {
			.jconfirm-box {
				padding-bottom: 0;
				padding-top: $spacing_l;
			}

			.already-purchased {
				grid-row: 4;
				grid-column: 1 / 2;
				display: block;
				margin-top: 0;

				&:hover {
					color: $color_secondary_text;
				}
			}

			.discount-note {
				grid-row: 5;
				margin: $spacing_m 0 0;

				p {
					margin: 0 -30px;
					padding: $spacing_m 52px;
					border-radius: 0 0 6px 6px;

					&:after {
						display: none;
					}
				}
			}

			.feature-video, .pro-feature-video {
				grid-row: 1 / span 4;
				grid-column-start: 2;
				margin-top: 0;
				margin-inline-start: $spacing_ms;
			}

			.jconfirm-title-c,
			.jconfirm-content-pane,
			.jconfirm-buttons {
				grid-column: 1 / 2;
			}
		}
	}

	.jconfirm.upgrade-modal {
		.jconfirm-box-container {
			.jconfirm-box {
				padding-bottom: $spacing_l;

				.pro-feature-video {
					margin-bottom: 0;
				}

				.jconfirm-buttons {
					padding-bottom: 0;
				}
			}
		}
	}

	&.rtl {
		.jconfirm .jconfirm-box-container .jconfirm-box .jconfirm-closeIcon {
			left: $spacing_s;
			right: auto;
		}
	}
}

@media screen and (max-width: 1023px) {
	.wpforms_page_wpforms-builder {
		.jconfirm {
			display: none;
		}
	}
}
