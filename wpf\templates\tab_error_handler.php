<?php
/**
 * View for the Error Handler tab.
 *
 * @var string $nonce WordPress' nonce.
 * @var array  $dirs  Dirs for error handler to operate.
 */
?>
<form action="" id="error_handler" method="post">
	<input type="hidden" name="nonce" value="<?php echo esc_html( $nonce ); ?>">

	<div class="wpforms-setting-row wpforms-setting-row-textarea">
		<span class="wpforms-setting-label">
			<label for="wpf-error-handler-dirs">Directories:</label>
		</span>

		<span class="wpforms-setting-field">
			<textarea
					name="dirs" id="wpf-error-handler-dirs"
					placeholder="Directories to suppress deprecation errors from."
					rows="20"><?php echo esc_html( implode( "\n", $dirs ) ); ?></textarea>
		</span>
	</div>

	<p class="submit wpforms-admin-page">
		<button id="error-handler-save-button" type="submit" class="wpforms-btn wpforms-btn-md wpforms-btn-orange">
			Save
		</button>

		<button id="error-handler-reset-button" type="button" class="wpforms-btn wpforms-btn-md wpforms-btn-light-grey">
			Reset to Default
		</button>
	</p>
</form>
