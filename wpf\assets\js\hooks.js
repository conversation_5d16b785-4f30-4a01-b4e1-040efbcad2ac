/* global List, wpf */

/**
 * Hooks Docs.
 *
 * @since {VERSION}
 */

'use strict';

const WPFHooks = window.WPFHooks || ( function( document, window, $ ) {

	/**
	 * Elements holder.
	 *
	 * @since {VERSION}
	 *
	 * @type {object}
	 */
	const el = {
		$hooks: $( '.wpforms-admin-devtools-hooks' ),
	};

	/**
	 * Runtime variables.
	 *
	 * @since {VERSION}
	 *
	 * @type {object}
	 */
	const vars = {};

	/**
	 * Public functions and properties.
	 *
	 * @since {VERSION}
	 *
	 * @type {object}
	 */
	const app = {

		/**
		 * Start the engine.
		 *
		 * @since {VERSION}
		 */
		init: function() {

			$( app.ready );
		},

		/**
		 * Document ready.
		 *
		 * @since {VERSION}
		 */
		ready: function() {

			app.initVars();
			app.events();
		},

		/**
		 * Init variables.
		 *
		 * @since {VERSION}
		 */
		initVars: function() {

			// Hooks list object.
			vars.hooksList = new List(
				'wpforms-admin-devtools-hooks-list',
				{
					valueNames: [
						'wpforms-hook-name',
						'wpforms-hook-description',
						'wpforms-hook-long-description',
						'wpforms-hook-doc',
						'wpforms-hook-usage',
					],
				}
			);
		},

		/**
		 * Register JS events.
		 *
		 * @since {VERSION}
		 */
		events: function() {

			el.$hooks
				.on( 'keyup', '#wpforms-admin-devtools-hooks-search', app.searchHooks )
				.on( 'change', '#wpforms-admin-devtools-hooks-plugin', app.selectPlugin );

		},

		/**
		 * Search hooks.
		 *
		 * @since {VERSION}
		 *
		 * @param {object} e Event object.
		 */
		searchHooks: function( e ) {

			vars.hooksList.search( $( this ).val() );
		},

		/**
		 * Select plugin.
		 *
		 * @since {VERSION}
		 *
		 * @param {object} e Event object.
		 */
		selectPlugin: function( e ) {

			const plugin = $( this ).val();

			window.location = wpf.updateQueryString( 'plugin', plugin );
		},
	};

	// Provide access to public functions/properties.
	return app;

}( document, window, jQuery ) );

// Initialize.
WPFHooks.init();
