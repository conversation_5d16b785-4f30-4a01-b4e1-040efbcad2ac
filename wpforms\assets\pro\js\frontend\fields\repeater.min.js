let WPFormsRepeaterField=window.WPFormsRepeaterField||((e,r,o)=>{let l={},n={init(){o(n.ready)},ready(){l.$doc=o(e),n.initClones(l.$doc),n.updateAllFieldsCloneList(l.$doc),n.initDescriptions(l.$doc,"init"),n.events(),o(r).resize(WPFormsUtils.debounce(()=>n.initDescriptions(l.$doc,"init"),50))},events(){l.$doc.off("click.WPFormsRepeaterAdd").on("click.WPFormsRepeaterAdd",".wpforms-field-repeater-button-add",n.buttonAddClick).on("click",".wpforms-field-repeater-button-remove",n.buttonRemoveClick).on("wpformsProcessConditionalsField",n.processConditionalsField).on("wpformsPageChange",n.pageChange)},initClones(e){var i=e.hasClass("wpforms-field-repeater")?"":".wpforms-field-repeater ",t=e.find(i+"> .wpforms-field-repeater-display-rows .wpforms-field-repeater-display-rows-buttons"),i=e.find(i+"> .wpforms-field-repeater-display-blocks-buttons");n.initRowsButtons(t),n.initBlocksButtons(i),e.find(".wpforms-field-repeater-clone-wrap .wpforms-field-repeater-display-rows-buttons").addClass("wpforms-init"),n.initRichTextClones(e.find(".wpforms-field-repeater-clone-wrap"))},initRowsButtons(e){e.each(function(){var e=o(this),i=e.siblings(".wpforms-layout-column").find(".wpforms-field:not(.wpforms-field-hidden)"),t=i.last().find(".wpforms-field-label"),s=(t.length?r.getComputedStyle(t.get(0)):null)?.getPropertyValue("--wpforms-field-size-input-spacing")||0,t=(t.outerHeight()||0)+Number.parseInt(s,10)+10;i.length?(e.toggleClass("wpforms-init",0<i.length).css({top:t}),n.initMinMaxRows(e)):n.removeAllButtons(e.closest(".wpforms-field-repeater"))})},initBlocksButtons(e){e.each(function(){var e=o(this),i=e.closest(".wpforms-field-repeater");i.find(".wpforms-field-repeater-display-blocks .wpforms-field:not(.wpforms-field-hidden)").length?n.initMinMaxRows(e):n.removeAllButtons(i)})},removeAllButtons(e){e.find(`
					.wpforms-field-repeater-display-blocks-buttons,
					.wpforms-field-repeater-display-rows-buttons
				`).remove()},initMinMaxRows(e){var e=e.closest(".wpforms-field-repeater"),i=e.find(".wpforms-field-repeater-display-rows-buttons, .wpforms-field-repeater-display-blocks-buttons"),t=e.data("rows-min")||1,s=(i.find("button").removeClass("wpforms-disabled"),e.find("> .wpforms-field-layout-rows, .wpforms-field-repeater-clone-wrap")),s=s.length<=t?t:1;e.find(`.wpforms-field-layout-rows:lt(${s}),
				.wpforms-field-repeater-display-blocks-buttons:lt(${s})`).find(".wpforms-field-repeater-button-remove").addClass("wpforms-disabled").attr("tabindex","-1");let r=i.length>=e.data("rows-max");i.find(".wpforms-field-repeater-button-add").toggleClass("wpforms-disabled",r).attr("tabindex",()=>r?"-1":null)},buttonAddClick(){var t=o(this);if(!t.hasClass("wpforms-disabled")){let i=t.closest(".wpforms-field-repeater");var s=i.data("field-id"),r=i.closest(".wpforms-form").data("formid"),s=o(".tmpl-wpforms-field-repeater-template-"+s+"-"+r).text();if(s.length){r=i.data("clone-num")||2,s=s.replaceAll("{CLONE}",r),r=(i.data("clone-num",r+1),t.closest(".wpforms-field-repeater-clone-wrap"));r=(r=r.length?r:t.closest(".wpforms-field-layout-rows")).length?r:i.find("> .wpforms-field-repeater-display-blocks-buttons");let e=o(s);e.hide(),e.find(".wpforms-field-repeater-display-rows-buttons button").removeClass("wpforms-disabled"),e.find(".wpforms-field-repeater-display-blocks-buttons button").removeClass("wpforms-disabled"),r.after(e),n.updateCloneList(i),n.initClones(i),n.initFields(e),n.initDescriptions(i,"add"),e.slideDown(200,function(){l.$doc.trigger("wpformsRepeaterFieldCloneDisplay",[e,i])}),n.updateBlockTitleNumbers(i),l.$doc.trigger("wpformsRepeaterFieldCloneCreated",[e,i])}}},buttonRemoveClick(){var t=o(this);if(!t.hasClass("wpforms-disabled")){let e=t.closest(".wpforms-field-repeater"),i=t.closest(".wpforms-field-repeater-clone-wrap");t=e.find(".wpforms-field-repeater-clone-wrap").last().is(i)?"remove-last":"remove";n.initDescriptions(e,t),i.slideUp(200,function(){i.remove(),n.updateCloneList(e),n.initClones(e),n.updateBlockTitleNumbers(e),l.$doc.trigger("wpformsRepeaterFieldCloneRemoved",[i,e])})}},updateCloneList(e){var s=e.find(".wpforms-field-repeater-clone-list");if(s.length){var r=e.find(".wpforms-field-repeater-clone-wrap");let i=[],t=1;r.each(function(){var e=Number.parseInt(o(this).data("clone"),10)||0;t=e>t?e:t,i.push(o(this).data("clone"))}),s.val(JSON.stringify(i)),e.attr("data-clone-num",t+1)}},updateAllFieldsCloneList(e){(e.hasClass("wpforms-field-repeater")?e.parent():e).find(".wpforms-field-repeater").each(function(){n.updateCloneList(o(this))})},processConditionalsField(e,i,t,s,r){i=o(`#wpforms-${i}-field_${t}-container`);i.hasClass("wpforms-field-repeater")&&("show"===r||s)&&n.initClones(i)},pageChange(e,i,t){n.initClones(t.find(".wpforms-page-"+i))},updateBlockTitleNumbers(e){e.find(".wpforms-wpforms-field-repeater-block-num").each(function(e){o(this).text("#"+(e+2))})},initDescriptions(e,t){o(r).width()<=600||(e.hasClass("wpforms-field-repeater")?e:e.find(".wpforms-field-repeater")).each(function(){var e=o(this);if(e.hasClass("wpforms-field-repeater-display-rows")){let i=e.find(".wpforms-field-repeater-display-rows");"init"===t?i.last().find(".wpforms-field-description").addClass("wpforms-init"):("remove-last"===t?i.filter(e=>2<=i.length&&e===i.length-2):(i.filter(e=>e!==i.length-1).find(".wpforms-field-description").slideUp(200,()=>{o(this).removeClass("wpforms-init",1<i.length)}),i.last())).find(".wpforms-field-description").slideDown(200,()=>{o(this).addClass("wpforms-init")})}})},initFields(e){n.initNumberSlider(e),wpforms.loadDatePicker(e),wpforms.loadTimePicker(e),r.WPFormsPhoneField?.loadSmartField?.(e),wpforms.loadChoicesJS(e),wpforms.loadInputMask(e),wpforms.loadValidationGroups(e),r.WPFormsTextLimit?.initHint("#"+e.attr("id")),r.WPForms?.FrontendModern?.updateGBBlockRatingColor(e),r.WPForms?.FrontendModern?.updateGBBlockIconChoicesColor(e)},initNumberSlider(e){e.find(".wpforms-field-number-slider input").each(function(){var e=o(this),i=e.val(),e=e.siblings(".wpforms-field-number-slider-hint");e.html(e.data("hint")?.replaceAll("{value}",`<b>${i}</b>`))})},initRichTextClones(e){e.each(function(){var e=o(this);let i=e.closest(".wpforms-field-repeater").find("> .wpforms-field-repeater-display-rows .wp-editor-area, > .wpforms-field-repeater-display-blocks .wp-editor-area");e.find(".wp-editor-area").each(function(e){n.initClonedRichTextField(o(this).attr("id"),i.eq(e).attr("id"))})})},initClonedRichTextField(e,i){var t,s;tinyMCEPreInit&&tinymce&&((t={})[e]={...tinyMCEPreInit.mceInit[i]},t[e].body_class=t[e].body_class?.replace(i,e),t[e].selector="#"+e,(s={})[e]={...tinyMCEPreInit.qtInit[i]},s[e].id=e,tinyMCEPreInit.mceInit={...tinyMCEPreInit.mceInit,...t},tinyMCEPreInit.qtInit={...tinyMCEPreInit.qtInit,...s},r.quicktags(tinyMCEPreInit.qtInit[e]),o("#"+e).css("visibility","initial"),tinymce.EditorManager.execCommand("mceAddEditor",!0,e))}};return n})(document,window,jQuery);WPFormsRepeaterField.init();