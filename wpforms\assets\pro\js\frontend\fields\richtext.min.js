var WPFormsRichTextField=window.WPFormsRichTextField||((a,s,n)=>{let r={disableEditor:t=>{setTimeout(function(){try{t.setMode("readonly"),t.getBody().setAttribute("contenteditable","false")}catch(e){}var e=t.getContainer();e&&(e.style.pointerEvents="none",e.style.opacity="0.35",e.classList.add("wpf-disabled"))},200)},enableEditor:t=>{setTimeout(function(){try{t.setMode("design"),t.getBody().setAttribute("contenteditable","true")}catch(e){}var e=t.getContainer();e&&(e.style.pointerEvents="auto",e.style.opacity="1",e.classList.remove("wpf-disabled"))},200)},checkAndDisableEditor:e=>{var t=n(e.getElement()).closest(".wpforms-field");(t.hasClass("wpforms-field-readonly")||t.hasClass("wpf-disable-field"))&&r.disableEditor(e)},disableTextAreas:function(){n(".wpforms-field-richtext").each(function(){var e,t=n(this);(t.hasClass("wpforms-field-readonly")||t.hasClass("wpf-disable-field"))&&(e=t.find(".wp-editor-area")).length&&(e.prop("readonly",!0),t.find(".wp-editor-wrap").addClass("wpf-disabled"))})},disableOnHtmlSwitch:(e,t)=>!(!e.hasClass("wpforms-field-readonly")&&!e.hasClass("wpf-disable-field")||((e=t.find(".wp-editor-area")).prop("readonly",!0),e.css("opacity","0.6"),e.css("pointer-events","none"),t.addClass("wpf-disabled"),0))},e={mediaPostIdUpdateEvent:!1},l={init(){n(a).on("wpformsReady",l.customizeRichTextField).on("wpformsAjaxSubmitSuccessConfirmation",l.updateIframes),s.addEventListener("elementor/popup/show",function(e){l.reInitRichTextFields(e.detail.instance.$element)})},customizeRichTextField(){let o=n(a);r.disableTextAreas(),o.on("tinymce-editor-setup",function(e,t){t.on("keyup",function(){l.validateRichTextField(t)}),t.on("focus",function(e){n(e.target.editorContainer).closest(".wp-editor-wrap").addClass("wpforms-focused")}),t.on("blur",function(e){n(e.target.editorContainer).closest(".wp-editor-wrap").removeClass("wpforms-focused")})}),o.on("wpformsRichTextContentChange",function(e,t,i){l.validateRichTextField(i),l.enableAddMediaButtons(t)}),o.on("tinymce-editor-init",function(e,t){var i=t.getDoc().body.style,d=n("body");i.fontFamily=d.css("font-family"),i.background="transparent",r.checkAndDisableEditor(t),l.initEditorModernMarkupMode(t),l.mediaPostIdUpdate(),l.observeEditorChanges(t),l.cleanImages(t),o.trigger("wpformsRichTextEditorInit",[t])}),n("textarea.wp-editor-area").each(function(){var e=n(this);e.hasClass("wpforms-field-required")&&e.prop("required",!0)}),o.on("click",".media-modal-close, .media-modal-backdrop",l.enableAddMediaButtons),"undefined"!=typeof wp&&"function"==typeof wp.media&&wp.media.view.Modal.prototype.on("escape",function(){l.enableAddMediaButtons("escapeEvent")}),o.on("click",".switch-html",function(){let e=n(this).closest(".wp-editor-wrap"),t=e.closest(".wpforms-field");setTimeout(function(){r.disableOnHtmlSwitch(t,e)||e.find(".wp-editor-area").trigger("focus"),e.addClass("wpforms-focused")},0)}),o.on("click",".switch-tmce",function(e){e.preventDefault();var e=n(this).closest(".wp-editor-wrap"),t=e.find(".wp-editor-area").attr("id");let i=tinyMCE.get(t);i&&(e.addClass("wpforms-focused"),e.closest(".wpforms-field").hasClass("wpf-disable-field")?r.disableEditor(i):setTimeout(()=>{i.focus(!1)},0))}),o.on("focus",".wp-editor-area",function(){n(this).closest(".wp-editor-wrap").addClass("wpforms-focused")}),o.on("blur",".wp-editor-area",function(){n(this).closest(".wp-editor-wrap").removeClass("wpforms-focused")})},cleanImages(e){var t=e.getContent({format:"raw"}),i=a.createElement("div"),d=(i.innerHTML=t,i.querySelectorAll("img"));for(let e=0;e<d.length;e++)d[e].outerHTML=d[e].outerHTML.replace(/"”|”"|"″|″"/g,'"');e.setContent(i.innerHTML)},addMediaButton(e){console.warn('WARNING! Function "WPFormsRichTextField.addMediaButton()" has been deprecated!'),wpforms_settings.richtext_add_media_button&&e.addButton("wp_add_media",{tooltip:"Add Media",icon:"dashicon dashicons-admin-media",cmd:"WP_Medialib"})},enableAddMediaButtons(e){("escapeEvent"===e||l.isCloseEvent(e)||l.isMutationImage(e))&&n(".mce-btn-group button i.dashicons-admin-media").closest(".mce-btn").removeClass("mce-btn-disabled")},isCloseEvent(e){return void 0!==e.target&&(e.target.classList.contains("media-modal-icon")||e.target.classList.contains("media-modal-backdrop"))},isMutationImage(e){if(void 0===e.addedNodes||void 0===e.addedNodes[0])return!1;let t=!1;return e.addedNodes.forEach(function(e){return"IMG"===e.tagName||"A"===e.tagName&&e.querySelector("img")?!(t=!0):void 0}),t},disableAddMediaButtons(){n(".mce-btn-group button i.dashicons-admin-media").closest(".mce-btn").addClass("mce-btn-disabled")},mediaPostIdUpdate(){e.mediaPostIdUpdateEvent||(n(".wpforms-field-richtext-media-enabled .mce-toolbar .mce-btn").on("click touchstart",function(e){var t,e=n(e.target);!e.hasClass("dashicons-admin-media")&&0===e.find(".dashicons-admin-media").length||(t=e.closest("form").data("formid"),e=e.closest(".wpforms-field-richtext").data("field-id"),wp.media.model.settings.post.id="wpforms-"+t+"-field_"+e,l.disableAddMediaButtons())}),e.mediaPostIdUpdateEvent=!0)},observeEditorChanges(i){new MutationObserver(function(e){for(var t in e)"childList"===e[t].type&&n(a).trigger("wpformsRichTextContentChange",[e[t],i])}).observe(i.iframeElement.contentWindow.document.body,{childList:!0,subtree:!0,attributes:!0})},validateRichTextField(e){var t;e&&n(e.iframeElement).closest("form").data("validator")&&(t=n("#"+e.id),e.getContent()!==t.val())&&(e.save(),t.valid())},reInitRichTextFields(e){"undefined"!=typeof tinyMCEPreInit&&"undefined"!=typeof tinymce&&e.find(".wp-editor-area").each(function(){var e=n(this).attr("id");tinymce.get(e)&&tinyMCE.execCommand("mceRemoveEditor",!1,e),s.quicktags(tinyMCEPreInit.qtInit[e]),n("#"+e).css("visibility","initial"),tinymce.init(tinyMCEPreInit.mceInit[e])})},initEditorModernMarkupMode(e){var t,i,d,o,a;wpforms.isModernMarkupEnabled()&&!s.WPFormsEditEntry&&s.WPForms.FrontendModern&&(t=e.getDoc().body.style,a=(i=n(e.getElement())).closest(".wpforms-field"),i=i.closest(".wpforms-form"),d=(i=s.WPForms.FrontendModern.getCssVars(i))["field-size-input-height"]?i["field-size-input-height"].replace("px",""):43,o="medium",o=a.hasClass("wpforms-field-small")?"small":"medium",o=a.hasClass("wpforms-field-large")?"large":o,a=e.getWin().clientWidth,e.theme.resizeTo(a,d*{small:1.8,medium:2.79,large:5.12}[o]),t.color=i["field-text-color"],t.fontSize=i["field-size-font-size"])},updateIframes(e){n(e.target).find(".wpforms-iframe").each(s?.WPFormsIframe?.update)},lockField(e){e=tinyMCE.get(e.find(".wp-editor-area").attr("id"));r.disableEditor(e)},unlockField(e){e=tinyMCE.get(e.find(".wp-editor-area").attr("id"));r.enableEditor(e)}};return l})(document,window,jQuery);WPFormsRichTextField.init();