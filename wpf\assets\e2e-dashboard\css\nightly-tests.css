.test-failures-container {
	margin-top: 20px;
}

/* Accordion Styling */
.cbp-ntaccordion-container {
	max-width: 100%;
	margin: 2em auto;
}

.cbp-ntaccordion {
	list-style: none;
	margin: 0;
	padding: 0;
}

.cbp-ntaccordion > li {
	margin-bottom: 1em;
	background: #fff;
	border: 1px solid #ddd;
	border-radius: 4px;
}

/* Main accordion trigger */
.cbp-nttrigger {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px;
	margin: 0;
	cursor: pointer;
	background: #f8f9fa;
	border-bottom: 1px solid #ddd;
	padding-right: 2.5em;
}

.cbp-nttrigger:hover {
	background: #f1f3f4;
}

/* Content hidden by default */
.cbp-ntcontent {
	padding: 1em;
	background: #fff;
	overflow: hidden;
	display: none;
}

/* Show content when parent is open */
.cbp-ntopen > .cbp-ntcontent {
	display: block;
}

/* Main accordion arrow indicators */
.cbp-nttrigger::after {
	content: '▼';
	position: absolute;
	right: 1em;
	transform: rotate(-90deg);
	transition: transform 0.3s;
}

li.cbp-ntopen > .cbp-nttrigger::after {
	transform: rotate(0);
}

/* Nested accordion for PHP versions */
.cbp-ntsubaccordion {
	list-style: none;
	margin: 0;
	padding: 0;
}

.cbp-ntsubaccordion > li {
	margin-bottom: 0.5em;
	border: 1px solid #eee;
	border-radius: 3px;
	position: relative;
}

/* PHP version trigger */
.cbp-ntsubtrigger-php {
	position: relative;
	display: block;
	padding: 10px 15px;
	cursor: pointer;
	background: #fafbfc;
	font-size: 0.95em;
}

.cbp-ntsubtrigger-php::after {
	content: '▼';
	position: absolute;
	right: 1em;
	transform: rotate(-90deg);
	transition: transform 0.3s;
}

li.cbp-ntopen > .cbp-ntcontent .cbp-ntsubaccordion li.cbp-ntopen > .cbp-ntsubtrigger-php::after {
	transform: rotate(0);
}

/* Nested accordion for test failures */
.cbp-ntsubsubaccordion {
	list-style: none;
	margin: 0;
	padding: 0;
}

.cbp-ntsubsubaccordion > li {
	margin-bottom: 0.5em;
	border: 1px solid #ddd;
	border-radius: 3px;
	position: relative;
}

/* Test failure trigger */
.cbp-ntsubtrigger-test {
	position: relative;
	display: block;
	padding: 8px 12px;
	cursor: pointer;
	background: #fefefe;
	font-size: 0.9em;
}

.cbp-ntsubtrigger-test::after {
	content: '▼';
	position: absolute;
	right: 1em;
	transform: rotate(-90deg);
	transition: transform 0.3s;
}

li.cbp-ntopen > .cbp-ntcontent .cbp-ntsubsubaccordion li.cbp-ntopen > .cbp-ntsubtrigger-test::after {
	transform: rotate(0);
}

/* Failure detail styling */
.failure-detail {
	padding: 1em;
	background: #fff;
}

.info-row {
	display: flex;
	margin-bottom: 0.5em;
}

.info-row .label {
	font-weight: bold;
	min-width: 120px;
}

.workflow-status.status-failure {
	color: #dc3545;
}

.data-provider {
	color: #666;
	font-size: 0.9em;
	margin-left: 0.5em;
}

/* Workflow Name and Status */
.workflow-name {
	font-weight: 600;
}

.workflow-status {
	margin-left: auto;
	padding: 4px 8px;
	border-radius: 4px;
	font-weight: 500;
}

.status-failure {
	background-color: #fde7e9;
	color: #dc3545;
}

/* WordPress Admin Theme Compatibility */
.wp-admin .cbp-ntaccordion > li {
	border-color: #ccd0d4;
}

.wp-admin .cbp-ntsubaccordion > li,
.wp-admin .cbp-ntsubsubaccordion > li {
	border-color: #e5e5e5;
}

.wp-admin .failure-detail {
	border-color: #e5e5e5;
}

/* Responsive Design */
@media screen and (max-width: 782px) {
	.info-row {
		flex-direction: column;
		gap: 4px;
	}

	.info-row .label {
		min-width: auto;
	}

	.info-row .value {
		width: 100%;
	}
}
