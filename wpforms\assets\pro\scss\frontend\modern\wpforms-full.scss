// WPForms frontend for Pro.
//
// Modern Full styles.
//
// @since 1.8.1

// Include base styles.
@import 'wpforms-base';

// Override `base` mixins.
// `Full` mixins use CSS variables.
@import '../../../../scss/frontend/modern/full/mixins';

// Fields-related styles.
@import 'full/field-date-time';
@import 'full/field-layout';
@import 'full/field-repeater';
@import 'full/field-page-break';
@import 'full/field-password';
@import 'full/field-phone';
@import 'full/field-html';
@import 'full/field-custom-captcha';
@import 'full/field-file-upload';
@import 'full/field-camera';
@import 'full/field-rating';
@import 'full/field-richtext';
@import 'full/field-credit-card';
@import 'full/field-payment-single';
@import 'full/field-payment-total';
@import 'full/field-entry-preview';
