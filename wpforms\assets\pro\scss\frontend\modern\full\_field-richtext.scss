// WPForms Modern Full styles.
//
// Rich Text field.
//
// @since 1.8.1

div.wpforms-container-full {
	.wpforms-form {

		div.wpforms-field-richtext {

			&.wpforms-field .wpforms-field-label {
				margin-bottom: calc( var( --wpforms-field-size-input-spacing ) - #{ $spacing_l } );
			}

			.wp-editor-wrap {

				.wp-editor-container {
					border-color: transparent;
					border-width: 0;
					box-shadow: none;
				}

				.wp-editor-tools {
					height: 29.1px;
					padding: 0;
					margin: 0;
					position: relative;
					z-index: 100;
				}

				.wp-editor-tabs {
					padding: 0;

					.wp-switch-editor {
						border-width: var( --wpforms-field-border-size );
						border-style: var( --wpforms-field-border-style );
						border-color: var( --wpforms-field-border-color );
						border-top-left-radius: var( --wpforms-field-border-radius );
						border-top-right-radius: var( --wpforms-field-border-radius );
					}
				}

				&.html-active .wp-editor-tabs button.switch-html,
				&.tmce-active .wp-editor-tabs button.switch-tmce {
					position: relative;
					border-top-width: var( --wpforms-field-border-size );
					border-top-style: var( --wpforms-field-border-style );
					border-top-color: var( --wpforms-field-border-color );
					border-right-width: var( --wpforms-field-border-size );
					border-right-style: var( --wpforms-field-border-style );
					border-right-color: var( --wpforms-field-border-color );
					border-left-width: var( --wpforms-field-border-size );
					border-left-style: var( --wpforms-field-border-style );
					border-left-color: var( --wpforms-field-border-color );

					&:before,
					&:after {
						content: '';
						position: absolute;
						display: block;
						height: 4px;
						background: #f5f5f5;
						bottom: -3px;
						left: 0;
						right: 0;
					}

					&:after {
						bottom: -5px;
					}
				}

				&.html-active .wp-editor-tabs button.switch-html {
					&:after {
						right: 0;
					}
				}

				.mce-toolbar-grp {
					border-top-left-radius: var( --wpforms-field-border-radius );

					.mce-btn-group {
						.mce-caret {
							right: 6px;
							position: static;
							margin: 6px 0;
						}
					}
				}

				&.html-active {
					.quicktags-toolbar {
						border-top-width: var( --wpforms-field-border-size );
						border-top-style: var( --wpforms-field-border-style );
						border-top-color: var( --wpforms-field-border-color );
						border-right-width: var( --wpforms-field-border-size );
						border-right-style: var( --wpforms-field-border-style );
						border-right-color: var( --wpforms-field-border-color );
						border-left-width: var( --wpforms-field-border-size );
						border-left-style: var( --wpforms-field-border-style );
						border-left-color: var( --wpforms-field-border-color );
						border-top-left-radius: var( --wpforms-field-border-radius );
						padding-top: 4px;
						position: relative;
						z-index: 2;

						&:after {
							content: '';
							position: absolute;
							display: block;
							height: 4px;
							background: #f5f5f5;
							bottom: -3px;
							left: 0;
							right: 0;
							border-bottom: 1px solid #cccccc;
						}
					}

					.wp-editor-area {
						border-width: var( --wpforms-field-border-size );
						border-style: var( --wpforms-field-border-style );
						border-color: var( --wpforms-field-border-color );
						border-bottom-left-radius: var( --wpforms-field-border-radius );
						border-bottom-right-radius: var( --wpforms-field-border-radius );
						position: relative;
						z-index: 1;
					}
				}


				.mce-tinymce {
					background-color: transparent;
					border-bottom-left-radius: var( --wpforms-field-border-radius );
					border-top-left-radius: var( --wpforms-field-border-radius );
					border-bottom-right-radius: var( --wpforms-field-border-radius );
					border-width: var( --wpforms-field-border-size ) !important;
					border-style: var( --wpforms-field-border-style );
					border-color: var( --wpforms-field-border-color );
					width: auto !important;
					overflow: hidden;

					& > .mce-container-body {
						border: none;
					}

					.mce-statusbar {
						border-bottom-left-radius: var( --wpforms-field-border-radius );
						border-bottom-right-radius: var( --wpforms-field-border-radius );
						color: var( --wpforms-label-color );
					}

					.mce-edit-area {
						background: var( --wpforms-field-background-color );
					}

					iframe {
						background: transparent;
					}
				}

				&.wpforms-focused {

					&.html-active .wp-editor-tabs button.switch-html,
					&.tmce-active .wp-editor-tabs button.switch-tmce,
					.mce-tinymce,
					.quicktags-toolbar,
					.wp-editor-area {
						@include wpforms-input-focus();
					}

					&.html-active .wp-editor-tabs button.switch-tmce,
					&.tmce-active .wp-editor-tabs button.switch-html {
						border-bottom: 2px solid var( --wpforms-button-background-color );
					}
				}
			}

			&.wpforms-has-error {
				.wp-editor-wrap {

					.wp-editor-tabs button.switch-html,
					.wp-editor-tabs button.switch-tmce,
					.mce-tinymce,
					.quicktags-toolbar,
					.wp-editor-area {
						@include wpforms-input-error();
					}

					&.wpforms-focused {
						&.html-active .wp-editor-tabs button.switch-html,
						&.tmce-active .wp-editor-tabs button.switch-tmce,
						.mce-tinymce,
						.quicktags-toolbar,
						.wp-editor-area {
							@include wpforms-input-error-focus();
						}

						&.html-active .wp-editor-tabs button.switch-tmce,
						&.tmce-active .wp-editor-tabs button.switch-html {
							border-bottom: 2px solid var( --wpforms-label-error-color );
						}
					}

					&:not(.wpforms-focused):hover {
						&.html-active .wp-editor-tabs button.switch-html,
						&.tmce-active .wp-editor-tabs button.switch-tmce,
						.mce-tinymce,
						.quicktags-toolbar {
							@include wpforms-input-error-hover();
						}
					}
				}
			}
		}
	}

	&.wpforms-gutenberg-form-selector {
		div.wpforms-field-richtext {
			// This sizes works only in GB editor.
			// For the frontend sizes please take a look in `assets/pro/js/fields/richtext.js`, `tinymce-editor-init` event handler.
			&.wpforms-field-small {
				.wp-editor-area {
					height: calc( var( --wpforms-field-size-input-height ) * 1.9 ) !important;
				}
			}

			&.wpforms-field-medium {
				.wp-editor-area {
					height: calc( var( --wpforms-field-size-input-height ) * 2.79 ) !important;
				}
			}

			&.wpforms-field-large {
				.wp-editor-area {
					height: calc( var( --wpforms-field-size-input-height ) * 5.12 ) !important;
				}
			}
		}
	}
}
