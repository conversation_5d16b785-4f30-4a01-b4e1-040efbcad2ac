<?php

namespace WPForms\SupportHelper\Admin;

use WPF<PERSON>\SupportHelper\Logger\LoggerInterface;
use WPForms\SupportHelper\Detectors\ModSecurity\DetectionEngine;

/**
 * Data handler for ModSecurity detection interface.
 *
 * @since {VERSION}
 */
class ModSecurityDataHandler {

	/**
	 * Detection engine instance.
	 *
	 * @since {VERSION}
	 *
	 * @var DetectionEngine
	 */
	private $detection_engine;

	/**
	 * Logger instance.
	 *
	 * @since {VERSION}
	 *
	 * @var LoggerInterface
	 */
	private $logger;

	/**
	 * Initialize the data handler.
	 *
	 * @since {VERSION}
	 *
	 * @param DetectionEngine $detection_engine Detection engine instance.
	 * @param LoggerInterface $logger           Logger instance.
	 */
	public function __construct( DetectionEngine $detection_engine, LoggerInterface $logger ) {

		$this->detection_engine = $detection_engine;
		$this->logger           = $logger;
	}

	/**
	 * Get detection data.
	 *
	 * @since {VERSION}
	 *
	 * @return array Detection data.
	 */
	public function get_detection_data(): array {

		try {
			$detection_results = $this->detection_engine->detect();

			return $this->process_detection_data( $detection_results );
		} catch ( \Exception $e ) {
			$this->logger->error( 'Failed to get detection data: ' . $e->getMessage() );

			return $this->get_error_data( $e->getMessage() );
		}
	}

	/**
	 * Process detection data for display.
	 *
	 * @since {VERSION}
	 *
	 * @param array $detection_results Raw detection results.
	 *
	 * @return array Processed detection data.
	 */
	private function process_detection_data( array $detection_results ): array {

		return [
			'status'             => $detection_results['detected'] ? 'detected' : 'not_detected',
			'detected'           => $detection_results['detected'],
			'timestamp'          => $detection_results['timestamp'],
			'successful_methods' => $this->process_successful_methods( $detection_results['detection_method'] ?? [] ),
			'failed_methods'     => $this->process_failed_methods( $detection_results['failed_methods'] ?? [] ),
			'skipped_methods'    => $this->process_skipped_methods( $detection_results['skipped_methods'] ?? [] ),
			'summary'            => $this->generate_summary( $detection_results ),
		];
	}

	/**
	 * Process successful detection methods.
	 *
	 * @since {VERSION}
	 *
	 * @param array $successful_methods Successful detection methods.
	 *
	 * @return array Processed successful methods.
	 */
	private function process_successful_methods( array $successful_methods ): array {

		$processed = [];

		foreach ( $successful_methods as $method ) {
			$processed[] = [
				'name'            => $method['name'] ?? __( 'Unknown Method', 'wpf-support-helper' ),
				'key'             => $method['key'] ?? '',
				'priority'        => $method['priority'] ?? 0,
				'additional_info' => $method['result']['additional_info'] ?? [],
				'status'          => 'success',
			];
		}

		return $processed;
	}

	/**
	 * Process failed detection methods.
	 *
	 * @since {VERSION}
	 *
	 * @param array $failed_methods Failed detection methods.
	 *
	 * @return array Processed failed methods.
	 */
	private function process_failed_methods( array $failed_methods ): array {

		$processed = [];

		foreach ( $failed_methods as $method ) {
			$processed[] = [
				'name'            => $method['name'] ?? __( 'Unknown Method', 'wpf-support-helper' ),
				'key'             => $method['key'] ?? '',
				'priority'        => $method['priority'] ?? 0,
				'reason'          => $method['reason'] ?? __( 'No reason provided', 'wpf-support-helper' ),
				'additional_info' => $method['result']['additional_info'] ?? [],
				'status'          => 'failed',
			];
		}

		return $processed;
	}

	/**
	 * Process skipped detection methods.
	 *
	 * @since {VERSION}
	 *
	 * @param array $skipped_methods Skipped detection methods.
	 *
	 * @return array Processed skipped methods.
	 */
	private function process_skipped_methods( array $skipped_methods ): array {

		$processed = [];

		foreach ( $skipped_methods as $method ) {
			$processed[] = [
				'name'            => $method['detector_name'] ?? __( 'Unknown Method', 'wpf-support-helper' ),
				'key'             => $method['key'] ?? '',
				'reason'          => $method['reason'] ?? __( 'No reason provided', 'wpf-support-helper' ),
				'additional_info' => $method['result']['additional_info'] ?? [],
				'status'          => 'skipped',
			];
		}

		return $processed;
	}

	/**
	 * Generate summary information.
	 *
	 * @since {VERSION}
	 *
	 * @param array $detection_results Detection results.
	 *
	 * @return array Summary information.
	 */
	private function generate_summary( array $detection_results ): array {

		$successful_count = count( $detection_results['detection_method'] ?? [] );
		$failed_count     = count( $detection_results['failed_methods'] ?? [] );
		$skipped_count    = count( $detection_results['skipped_methods'] ?? [] );
		$total_count      = $successful_count + $failed_count + $skipped_count;

		return [
			'total_methods'      => $total_count,
			'successful_methods' => $successful_count,
			'failed_methods'     => $failed_count,
			'skipped_methods'    => $skipped_count,
			'detection_status'   => $detection_results['detected'] ? __( 'ModSecurity Detected', 'wpf-support-helper' ) : __( 'ModSecurity Not Detected', 'wpf-support-helper' ),
		];
	}

	/**
	 * Get error data when detection fails.
	 *
	 * @since {VERSION}
	 *
	 * @param string $error_message Error message.
	 *
	 * @return array Error data.
	 */
	private function get_error_data( string $error_message ): array {

		return [
			'status'             => 'error',
			'detected'           => false,
			'timestamp'          => current_time( 'mysql' ),
			'successful_methods' => [],
			'failed_methods'     => [],
			'skipped_methods'    => [],
			'additional_info'    => [],
			'summary'            => [
				'total_methods'      => 0,
				'successful_methods' => 0,
				'failed_methods'     => 0,
				'skipped_methods'    => 0,
				'detection_status'   => __( 'Detection Error', 'wpf-support-helper' ),
			],
			'error_message'      => $error_message,
		];
	}

	/**
	 * Clear detection cache.
	 *
	 * @since {VERSION}
	 *
	 * @return bool True on success, false on failure.
	 */
	public function clear_cache(): bool {

		try {
			$this->detection_engine->clear_cache();

			return true;
		} catch ( \Exception $e ) {
			$this->logger->error( 'Failed to clear cache: ' . $e->getMessage() );

			return false;
		}
	}
}
