var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.FieldPhone=WPForms.Admin.Builder.FieldPhone||(r=>{let e={},o={init(){r(o.ready)},ready(){o.setup(),o.events()},setup(){e={$builder:r("#wpforms-builder")}},events(){e.$builder.on("change",".wpforms-field-option-phone .wpforms-field-option-row-format select",o.handleFormatChange)},handleFormatChange(){var e=r(this),o=e.closest(".wpforms-field-option-row").data("field-id");r(`#wpforms-field-${o} .wpforms-field-phone-input-container`).attr("data-format",e.val())}};return o})((document,window,jQuery)),WPForms.Admin.Builder.FieldPhone.init();