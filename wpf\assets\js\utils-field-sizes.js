/* global tinymce */

/**
 * WP Admin bar > WPForms > Utils > Change all fields sizes.
 *
 * @since {VERSION}
 *
 * @type {Object}
 */
const WPFUtilsFieldSizes = window.WPFUtilsFieldSizes || ( function( document, $ ) {
	/**
	 * Application object.
	 *
	 * @since {VERSION}
	 *
	 * @type {Object}
	 */
	const app = {

		/**
		 * Initialize the app.
		 *
		 * @since {VERSION}
		 */
		init() {
			// by default, make restore option not visible.
			$( document ).ready( function() {
				$( '#wp-admin-bar-wpf-utils-field-sizes-restore' ).css( 'display', 'none' );
			} );

			$( document ).on( 'click', '#wp-admin-bar-wpf-utils-field-sizes-default li', app.onChange );
		},

		/**
		 * Change all fields sizes.
		 *
		 * @since {VERSION}
		 *
		 * @param {Object} e Event object.
		 */
		onChange( e ) {
			e.preventDefault();

			const $this = $( this );

			// If a user clicks the option to restore default sizes.
			if ( $this.attr( 'id' ) === 'wp-admin-bar-wpf-utils-field-sizes-restore' ) {
				const confirm = window.confirm( 'This will refresh the page, OK?' ); // eslint-disable-line no-alert

				if ( confirm ) {
					window.location.reload();
				}

				return;
			}

			const idToClassMap = {
				'wp-admin-bar-wpf-utils-field-sizes-small': 'wpforms-field-small',
				'wp-admin-bar-wpf-utils-field-sizes-medium': 'wpforms-field-medium',
				'wp-admin-bar-wpf-utils-field-sizes-large': 'wpforms-field-large',
			};

			const allClassNames = Object.values( idToClassMap ).join( ' ' );
			const classToAdd = idToClassMap[ $this.attr( 'id' ) ] || '';

			// mark checkbox as checked.
			$this.siblings().each( function() {
				$( this ).find( 'input' ).prop( 'checked', false );
			} );
			$this.find( 'input' ).prop( 'checked', true );

			// Handle all fields except rich text editor.
			$( '.wpforms-field-container .wpforms-field:not(.wpforms-field-richtext)' )
				.find( '.wpforms-field-small, .wpforms-field-medium, .wpforms-field-large' )
				.removeClass( allClassNames )
				.addClass( classToAdd );

			// Handle rich text editor resizing.
			$( '.wpforms-field-container .wpforms-field.wpforms-field-richtext' )
				.removeClass( allClassNames )
				.addClass( classToAdd )
				.find( 'textarea' )
				.each( function( _i, textarea ) {
					const editor = tinymce.get( $( textarea ).attr( 'id' ) );
					$( document ).trigger( 'tinymce-editor-init', [ editor ] );
				} );

			$( '#wp-admin-bar-wpf-utils-field-sizes-restore' ).css( 'display', 'inline-block' );
		},
	};

	return app;
}( document, jQuery ) );

WPFUtilsFieldSizes.init();
