<?php
/**
 * View for the Forms tab.
 *
 * @var string $nonce  WordPress nonce.
 * @var array  $forms  List of all site forms.
 * @var bool   $is_pro Whether the Pro version is active.
 */
?>
<div class="generator-progress-bar"></div>
<form action="" id="generate_entries">
	<input type="hidden" name="nonce" value="<?php echo esc_html( $nonce ); ?>">

	<?php if ( ! empty( $forms ) ) : ?>
		<div class="wpforms-setting-row wpforms-setting-row-select">
			<div class="wpforms-setting-label"><label for="wpforms-setting-disable-css">Choose a form</label></div>
			<div class="wpforms-setting-field">
				<span class="choicesjs-select-wrap">
					<select class="choicesjs-select" name="wp_forms" id="wp_forms" data-sorting="off" data-search="1">
						<?php foreach ( (array) $forms as $form ) : ?>
							<option value="<?php echo (int) $form['ID']; ?>"><?php echo esc_html( $form['post_title'] ); ?></option>
						<?php endforeach; ?>
					</select>
				</span>
			</div>
		</div>

		<div class="wpforms-setting-row wpforms-setting-row-text">
			<div class="wpforms-setting-label"><label for="wpforms-setting-disable-css">Count</label></div>
			<div class="wpforms-setting-field">
				<input type="number" name="count" <?php echo $is_pro ? 'value="100"' : 'value="5" max="5"'; ?>>
				<p class="desc">How many entries you want to generate.</p>
			</div>
		</div>
		<div class="wpforms-setting-row wpforms-setting-row-text">
			<div class="wpforms-setting-label"><label for="wpforms-setting-disable-css">Chunk size</label></div>
			<div class="wpforms-setting-field">
				<input type="number" name="chunkSize" <?php echo $is_pro ? 'value="100"' : 'value="5" max="5"'; ?>>
				<p class="desc">How many entries will be generated in one iteration.</p>
			</div>
		</div>
		<p class="submit wpforms-admin-page">
			<button id="generate-button" type="submit" class="wpforms-btn wpforms-btn-md wpforms-btn-orange">Generate</button>
			<button id="delete-all-button" type="button" class="wpforms-btn wpforms-btn-md wpforms-btn-light-grey">Delete all</button>
		</p>
	<?php else : ?>
		<a href="<?php echo esc_url( admin_url( 'admin.php?page=wpforms-builder' ) ); ?>">Please create at least 1 form.</a>
	<?php endif; ?>
</form>
