const WPFormsEntriesExport=window.WPFormsEntriesExport||function(e,t,i){const r={$form:i("#wpforms-tools-entries-export"),$selectForm:i("#wpforms-tools-entries-export-selectform"),$selectFormSpinner:i("#wpforms-tools-entries-export-selectform-spinner"),$selectFormMsg:i("#wpforms-tools-entries-export-selectform-msg"),$expOptions:i("#wpforms-tools-entries-export-options"),$fieldsCheckboxes:i("#wpforms-tools-entries-export-options-fields-checkboxes"),$paymentFieldsSection:i("#wpforms-tools-entries-export-options-payment-fields"),$paymentFieldsCheckboxes:i("#wpforms-tools-entries-export-options-payment-fields-checkboxes"),$dateSection:i("#wpforms-tools-entries-export-options-date"),$dateFlatpickr:i("#wpforms-tools-entries-export-options-date-flatpickr"),$searchSection:i("#wpforms-tools-entries-export-options-search"),$searchField:i("#wpforms-tools-entries-export-options-search-field"),$submitButton:i("#wpforms-tools-entries-export-submit"),$cancelButton:i("#wpforms-tools-entries-export-cancel"),$processMsg:i("#wpforms-tools-entries-export-process-msg"),$optionFields:i("#wpforms-tools-entries-export-options-type-info"),$selectStatuses:i("#wpforms-tools-entries-export-select-statuses"),$optionStatuses:i("#wpforms-tools-entries-export-options-status"),$clearDateButton:i(".wpforms-clear-datetime-field")},a=wpforms_tools_entries_export.i18n,n={},l={formsCache:{},init(){i(l.ready)},ready(){n.processing=!1,l.initChoices(),l.initDateRange(),l.initFormContainer(),l.initSubmit(),l.events()},events(){r.$selectForm[0].addEventListener("choice",function(e){l.selectFormEvent(e)}),i(e).on("change","#wpforms-tools-entries-export-options .wpforms-toggle-all",function(){var e=i(this),s=e.find("input");e.siblings().find("input").prop("checked",s.prop("checked"))}),i(e).on("change","#wpforms-tools-entries-export-options-fields-checkboxes label, #wpforms-tools-entries-export-options-payment-fields-checkboxes label, #wpforms-tools-entries-export-options-additional-info label",function(){var e,s,o=i(this);o.hasClass("wpforms-toggle-all")||(s=(e=o.parent().find("label").not(".wpforms-toggle-all").find("input")).filter(":checked"),o.siblings(".wpforms-toggle-all").find("input").prop("checked",s.length===e.length))}),i(e).on("csv_file_error",function(e,s){l.displaySubmitMessage(s,"error")}),i(e).on("change","#wpforms-tools-entries-export-options-type-info input",function(){l.switchDynamicColumnsNotice(i(this))}),i(e).on("click",".wpforms-clear-datetime-field",function(e){e.preventDefault(),r.$dateFlatpickr.flatpickr().clear(),i(this).addClass("wpforms-hidden"),l.initDateRange()})},selectFormEvent(e){e.detail.choice.placeholder?r.$expOptions.addClass("hidden"):n.formID!==e.detail.choice.value&&(n.formID=e.detail.choice.value,l.resetChoices(),r.$optionStatuses.removeClass("wpforms-hidden"),void 0===l.formsCache[n.formID]?l.retrieveFormAndRenderFields():(1<l.formsCache[n.formID].statuses.length?l.setChoices(l.formsCache[n.formID].statuses):r.$optionStatuses.addClass("wpforms-hidden"),l.renderFields(l.formsCache[n.formID].fields),l.renderFields(l.formsCache[n.formID].paymentFields,!0),l.handleSearchFields(l.formsCache[n.formID].fields,l.formsCache[n.formID].paymentFields),l.optionsFields(l.formsCache[n.formID].dynamicColumns),l.addDynamicColumnsNotice(l.formsCache[n.formID].dynamicColumnsNotice)))},switchDynamicColumnsNotice(e){"dynamic_columns"===e.val()&&e.parent().find(".wpforms-tools-entries-export-notice-warning").toggleClass("wpforms-hide")},retrieveFormAndRenderFields(){n.ajaxData={action:"wpforms_tools_entries_export_form_data",nonce:wpforms_tools_entries_export.nonce,form:n.formID},r.$selectFormSpinner.removeClass("hidden"),l.displayFormsMessage(""),i.get(ajaxurl,n.ajaxData).done(function(e){e.success?(l.renderFields(e.data.fields),l.renderFields(e.data.payment_fields,!0),l.optionsFields(e.data.dynamic_columns),e.data.dynamic_columns&&l.addDynamicColumnsNotice(e.data.dynamic_columns_notice),l.handleSearchFields(e.data.fields,e.data.payment_fields),l.formsCache[n.formID]={fields:e.data.fields,paymentFields:e.data.payment_fields,dynamicColumns:e.data.dynamic_columns,dynamicColumnsNotice:e.data.dynamic_columns_notice,statuses:e.data.statuses},r.$expOptions.removeClass("hidden"),1<e.data.statuses.length?l.setChoices(e.data.statuses):r.$optionStatuses.addClass("wpforms-hidden")):(l.displayFormsMessage(e.data.error),r.$expOptions.addClass("hidden"))}).fail(function(e,s,o){l.displayFormsMessage(a.error_prefix+"<br>"+o),r.$expOptions.addClass("hidden")}).always(function(){r.$selectFormSpinner.addClass("hidden")})},exportAjaxStep(e){n.processing&&(e=l.getAjaxPostData(e),i.post(ajaxurl,e).done(function(e){var s;clearTimeout(n.timerId),e.success?0===e.data.count?l.displaySubmitMessage(a.prc_2_no_entries):(s=a.prc_3_done,s+="<br>"+a.prc_3_download+', <a href="#" class="wpforms-download-link">'+a.prc_3_click_here+"</a>.",l.displaySubmitMessage(s,"info"),l.triggerDownload(e.data.request_id)):l.displaySubmitMessage(e.data.error,"error")}).fail(function(e,s,o){clearTimeout(n.timerId),l.displaySubmitMessage(a.error_prefix+"<br>"+o,"error")}).always(function(){l.displaySubmitSpinner(!1)}))},getAjaxPostData(e){let s;if("first-step"===e){const o=[];s=r.$form.serializeArray().reduce(function(e,s){return"statuses"===s.name?o.push(s.value):e[s.name]=s.value,e},{}),r.$fieldsCheckboxes.find("input").length<1&&(s.date="",s["search[term]"]=""),s.statuses=o}else s={action:"wpforms_tools_entries_export_step",nonce:wpforms_tools_entries_export.nonce,request_id:e};return s},initSubmit(){r.$submitButton.on("click",function(e){e.preventDefault(),i(this).hasClass("wpforms-btn-spinner-on")||(r.$submitButton.blur(),l.displaySubmitSpinner(!0),l.displaySubmitMessage(""),n.timerId=setTimeout(function(){l.displaySubmitMessage(a.prc_1_filtering+"<br>"+a.prc_1_please_wait,"info")},3e3),l.exportAjaxStep("first-step"))}),r.$cancelButton.on("click",function(e){e.preventDefault(),r.$cancelButton.blur(),l.displaySubmitMessage(""),l.displaySubmitSpinner(!1)})},initFormContainer(){0<wpforms_tools_entries_export.form_id&&(r.$expOptions.removeClass("hidden"),r.$fieldsCheckboxes.find("input").length<1)&&(r.$dateSection.addClass("hidden"),r.$searchSection.addClass("hidden"))},initChoices(){n.Choices=new Choices(r.$selectStatuses[0],{removeItemButton:!0,allowHTML:!1,itemSelectText:""})},resetChoices(){n.Choices.clearInput(),n.Choices.clearStore(),n.Choices.setChoices([],"value","label",!0)},setChoices(e){var s=e.filter(function(e){return"spam"===e.value})[0];s&&n.Choices.setChoices([s],"value","label",!0),e=e.filter(function(e){return"spam"!==e.value}),n.Choices.setValue(e,"value","label",!0)},initDateRange(){var e=wpforms_tools_entries_export.lang_code,s=t.flatpickr;let o={rangeSeparator:" - "};"undefined"!==s&&s.hasOwnProperty("l10ns")&&s.l10ns.hasOwnProperty(e)&&((o=s.l10ns[e]).rangeSeparator=" - "),r.$dateFlatpickr.flatpickr({altInput:!0,altFormat:"M j, Y",dateFormat:"Y-m-d",locale:o,mode:"range",defaultDate:wpforms_tools_entries_export.dates,onChange(e){r.$clearDateButton.toggleClass("wpforms-hidden",2!==e.length)}})},renderFields(i,e=!1){if("object"==typeof i){const n={checkboxes:"",options:""};var s=Object.keys(i);r.$paymentFieldsSection.show(),0===s.length?n.checkboxes="<span>"+a.error_form_empty+"</span>":(n.checkboxes+='<label class="wpforms-toggle-all"><input type="checkbox" checked> '+a.label_select_all+"</label>",s.forEach(function(e){let s='<label><input type="checkbox" name="fields[{index}]" value="{id}" checked> {label}</label>';var o=parseInt(i[e].id,10);s=(s=(s=s.replace("{index}",parseInt(e,10)+"-"+o)).replace("{id}",o)).replace("{label}",i[e].label),n.checkboxes+=s;let t='<option value="{id}">{label}</option>';t=(t=t.replace("{id}",o)).replace("{label}",i[e].label),n.options+=t}),r.$dateSection.removeClass("hidden"),r.$searchSection.removeClass("hidden")),(e?r.$paymentFieldsCheckboxes:r.$fieldsCheckboxes).html(n.checkboxes),e&&0===s.length&&r.$paymentFieldsSection.hide(),r.$searchField.find("optgroup:first-child option:not(:first-child)").remove(),r.$searchField.find("optgroup:first-child").append(n.options)}},handleSearchFields(e,s){e=Object.keys(e).length,s=Object.keys(s).length;0===e&&0===s&&(r.$dateSection.addClass("hidden"),r.$searchSection.addClass("hidden"))},optionsFields(e){l.switchDynamicColumns(e),e&&r.$optionFields.find("input[value=dynamic_columns]").prop("checked",!1)},switchDynamicColumns(e){r.$optionFields.find("input[value=dynamic_columns]").parent().toggle(e)},addDynamicColumnsNotice(e){r.$optionFields.find(".wpforms-tools-entries-export-notice-warning").remove(),r.$optionFields.find("input[value=dynamic_columns]").parent().append('<div class="wpforms-tools-entries-export-notice-warning wpforms-hide">'+e+"</div>")},displaySubmitSpinner(e){e?(r.$submitButton.addClass("wpforms-btn-spinner-on"),r.$cancelButton.removeClass("hidden"),n.processing=!0):(r.$submitButton.removeClass("wpforms-btn-spinner-on"),r.$cancelButton.addClass("hidden"),n.processing=!1)},displayFormsMessage(e){r.$selectFormMsg.html("<p>"+e+"</p>"),0<e.length?r.$selectFormMsg.removeClass("wpforms-hidden"):r.$selectFormMsg.addClass("wpforms-hidden")},displaySubmitMessage(e,s=""){s&&"error"===s?r.$processMsg.addClass("wpforms-error"):r.$processMsg.removeClass("wpforms-error"),r.$processMsg.html("<p>"+e+"</p>"),0<e.length?r.$processMsg.removeClass("wpforms-hidden"):r.$processMsg.addClass("wpforms-hidden")},triggerDownload(e){var s=wpforms_tools_entries_export.export_page,s=(s+="&action=wpforms_tools_entries_export_download")+("&nonce="+wpforms_tools_entries_export.nonce)+("&request_id="+e);r.$expOptions.find("iframe").remove(),r.$expOptions.append('<iframe src="'+s+'"></iframe>'),r.$processMsg.find(".wpforms-download-link").attr("href",s)}};return l}(document,window,jQuery);WPFormsEntriesExport.init();