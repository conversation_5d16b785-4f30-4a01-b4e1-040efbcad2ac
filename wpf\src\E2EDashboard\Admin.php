<?php

namespace WPF\E2EDashboard;

/**
 * Admin class.
 *
 * @since 1.0
 *
 * @codingStandardsIgnoreFile
 */
class Admin {

	/**
	 * Initialize the admin functionalities.
	 *
	 * @since 1.0
	 *
	 * @return void
	 */
	public function init() {

		add_action( 'admin_menu', [ $this, 'register_admin_menu' ] );
		add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_custom_styles' ] );
		add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_scripts' ] );
		add_action( 'wp_ajax_run_github_action', [ $this, 'run_github_action' ] );
		add_action( 'wp_ajax_get_branches', [ $this, 'get_branches' ] );
		add_action( 'wp_ajax_get_files_for_branch', [ $this, 'get_files_for_branch' ] );
		add_action( 'wp_ajax_check_workflow_status', [ $this, 'checkWorkflowStatus' ] );
		add_action( 'wp_ajax_cancel_workflow_run', [ $this, 'cancel_workflow_run' ] );
		add_action( 'wp_ajax_get_active_workflows', [ $this, 'get_active_workflows' ] );
		add_action( 'admin_notices', [ $this, 'display_github_rate_limit_notice' ] );
	}

	/**
	 * Register the admin menu page.
	 *
	 * @since 1.0
	 */
	public function register_admin_menu() {

		add_menu_page(
			'E2E Dashboard',
			'E2E Dashboard',
			'manage_options',
			'e2e-dashboard',
			[ $this, 'render_admin_page' ],
			'dashicons-controls-play',
			100
		);
	}

	/**
	 * Enqueue custom script and styles for the E2E Dashboard plugin.
	 *
	 * @since 1.0
	 */
	public function enqueue_custom_styles() {

		$screen = get_current_screen();

		if ( $screen->id !== 'toplevel_page_e2e-dashboard' ) {

			return;
		}

		wp_enqueue_style(
			'admin-custom-styles',
			WPFORMS_DEV_TOOLS_URL . '/assets/e2e-dashboard/css/main-admin-style.css',
			[],
			'1.0.0'
		);

		wp_enqueue_style(
			'select2-css',
			'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css',
			[],
			'4.0.13'
		);

		wp_enqueue_style(
			'jstree-css',
			'https://cdnjs.cloudflare.com/ajax/libs/jstree/3.2.1/themes/default/style.min.css',
			[],
			'3.2.1'
		);

		wp_enqueue_style( 'dashicons' );
	}

	/**
	 * Enqueue scripts.
	 *
	 * @since 1.0
	 */
	public function enqueue_scripts() {

		$screen = get_current_screen();

		if ( $screen->id !== 'toplevel_page_e2e-dashboard' ) {

			return;
		}

		wp_enqueue_script(
			'select2-js',
			'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js',
			[ 'jquery' ],
			'4.0.13',
			true
		);

		wp_enqueue_script(
			'jstree-js',
			'https://cdnjs.cloudflare.com/ajax/libs/jstree/3.2.1/jstree.min.js',
			[ 'jquery' ],
			'3.2.1',
			true
		);

		wp_enqueue_script(
			'cbp-ntaccordion-js',
			WPFORMS_DEV_TOOLS_URL . '/assets/e2e-dashboard/js/cbpNTAccordion.js',
			[ 'jquery' ],
			'1.0.0',
			true
		);

		wp_enqueue_script(
			'e2e-dashboard-trigger',
			WPFORMS_DEV_TOOLS_URL . '/assets/e2e-dashboard/js/admin.js',
			[
				'jquery',
				'select2-js',
				'jstree-js',
				'cbp-ntaccordion-js',
			],
			'1.0',
			true
		);

		wp_localize_script(
			'e2e-dashboard-trigger',
			'e2eDashboard',
			[
				'ajax_url' => admin_url( 'admin-ajax.php' ),
			]
		);

		// Add nonce for AJAX security
		wp_localize_script(
			'e2e-dashboard-trigger',
			'e2eDashboardAdmin',
			[
				'nonce' => wp_create_nonce( 'e2e_dashboard_nonce' ),
			]
		);
	}

	/**
	 * Render the admin page with data.
	 *
	 * @since 1.0
	 */
	public function render_admin_page() {

		// Check if GitHub is properly configured.
		$github_action = new GitHubAction();

		if ( ! $github_action->isConfigured() ) {

			$error_message = $github_action->getErrorMessage();
			?>
			<div class="wrap">
				<h1>Trigger E2E GitHub Action</h1>
				<div class="notice notice-error">
					<p><strong>GitHub API Configuration Error:</strong> <?php echo esc_html( $error_message ); ?></p>
					<p>Please add the GitHub API token to your wp-config.php file to use this feature.</p>
					<p>Example:</p>
					<pre style="background: #f0f0f0; padding: 10px; overflow: auto;">
						define('GITHUB_API_TOKEN', 'your-github-personal-access-token');
					</pre>
					<p>You can generate a GitHub Personal Access Token in your <a href="https://github.com/settings/tokens" target="_blank">GitHub account settings</a>.
					</p>
				</div>
			</div>
			<?php
			return;
		}
		?>
		<div class="wrap">
			<h1>Trigger E2E GitHub Action</h1>
			<form id="e2e-github-action-form">
				<label for="branch">Select Branch:</label>
				<select id="branch" name="branch">
					<option value="">Select a branch</option>
				</select>

				<label for="test">Specify Test:</label>
				<input type="text" id="test" name="test" placeholder="Enter test path or group">

				<button type="button" id="run-github-action">Run GitHub Action</button>
				<button id="cancel-all" class="button button-secondary">Cancel All</button>
			</form>

			<div id="result"></div>

			<input type="text" id="jstree_search_input" placeholder="Search files" style="display: none;">
			<div id="jstree_demo_div"></div>
		</div>
		<?php
	}

	/**
	 * Handle the AJAX request to trigger the GitHub action.
	 *
	 * @since 1.0
	 */
	public function run_github_action() {

		// Verify nonce to prevent CSRF attacks
		check_ajax_referer( 'e2e_dashboard_nonce', 'nonce' );

		// Verify user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( 'Insufficient permissions' );
			return;
		}

		$branch = sanitize_text_field( $_POST['branch'] );
		$test   = sanitize_text_field( $_POST['test'] );

		$github_action = new GitHubAction();
		$response      = $github_action->runSpecifiedTest( $branch, $test );

		if ( is_wp_error( $response ) ) {

			wp_send_json_error( 'Error triggering the action.' );
		} else {

			wp_send_json_success( [
				'message'         => 'GitHub Action triggered successfully!',
				'workflow_run_id' => $response['workflow_run_id'] ?? null,
				'status'          => $response['status'] ?? null,
				'test_name'       => $test,
			] );
		}
	}

	/**
	 * Handle the AJAX request to get the branches.
	 *
	 * @since 1.0
	 */
	public function get_branches() {

		// Verify nonce to prevent CSRF attacks
		check_ajax_referer( 'e2e_dashboard_nonce', 'nonce' );

		// Verify user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( 'Insufficient permissions' );
			return;
		}

		$github_action = new GitHubAction();
		$branches      = $github_action->getBranches();

		if ( is_wp_error( $branches ) ) {

			wp_send_json_error( 'Error fetching branches.' );
		} else {

			wp_send_json_success( $branches );
		}
	}

	/**
	 * Handle the AJAX request to get files for the selected branch.
	 */
	public function get_files_for_branch() {

		// Verify nonce to prevent CSRF attacks
		check_ajax_referer( 'e2e_dashboard_nonce', 'nonce' );

		// Verify user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( 'Insufficient permissions' );
			return;
		}

		$branch = sanitize_text_field( $_POST['branch'] );

		if ( empty( $branch ) ) {

			wp_send_json_error( 'No branch selected.' );

			return;
		}

		$github_action = new GitHubAction();
		$files         = $github_action->getRepoFiles( $branch );

		$filtered_files = $github_action->filterFiles( $files, function ( $file ) {

			if ( strpos( $file, '.codeception' ) === 0 ) {

				return false;
			}

			return strpos( $file, 'Cest.php' ) !== false;
		} );

		if ( is_wp_error( $files ) ) {

			wp_send_json_error( 'Error fetching files for the selected branch.' );
		} else {

			wp_send_json_success( $filtered_files );
		}
	}

	/**
	 * Handle the AJAX request to check the status of the workflow.
	 */
	public function checkWorkflowStatus() {

		// Verify nonce to prevent CSRF attacks
		check_ajax_referer( 'e2e_dashboard_nonce', 'nonce' );

		// Verify user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( 'Insufficient permissions' );
			return;
		}

		if ( ! isset( $_POST['workflow_run_id'] ) ) {

			wp_send_json_error( 'No workflow run ID provided.' );

			return;
		}

		$workflow_run_id = intval( $_POST['workflow_run_id'] );
		$github_action   = new GitHubAction();
		$job_details     = $github_action->getJobDetails( $workflow_run_id );

		if ( is_wp_error( $job_details ) ) {

			wp_send_json_error( $job_details->get_error_message() );

			return;
		}

		// Get the test name from the original request if available
		$test_name = isset( $_POST['test_name'] ) ? sanitize_text_field( $_POST['test_name'] ) : '';

		wp_send_json_success( [
			'job_details' => $job_details,
			'test_name'   => $test_name,
		] );
	}

	/**
	 * Handle the AJAX request to cancel a workflow run.
	 *
	 * @since 1.0
	 */
	public function cancel_workflow_run() {

		// Verify nonce to prevent CSRF attacks
		check_ajax_referer( 'e2e_dashboard_nonce', 'nonce' );

		// Verify user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( 'Insufficient permissions' );
			return;
		}

		$workflowRunID = intval( $_POST['workflow_run_id'] );

		$githubAction = new GitHubAction();
		$result       = $githubAction->cancelWorkflowRun( $workflowRunID );

		if ( is_wp_error( $result ) ) {

			wp_send_json_error( $result->get_error_message(), 500 );
		} else {

			wp_send_json_success( $result );
		}
	}

	/**
	 * Handle the AJAX request to get active workflows.
	 *
	 * @since 1.0
	 */
	public function get_active_workflows() {

		// Verify nonce to prevent CSRF attacks
		check_ajax_referer( 'e2e_dashboard_nonce', 'nonce' );

		// Verify user capabilities
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( 'Insufficient permissions' );
			return;
		}

		$githubAction = new GitHubAction();
		$workflows    = $githubAction->getActiveWorkflows();

		if ( is_wp_error( $workflows ) ) {

			wp_send_json_error( $workflows->get_error_message() );
		} else {

			// Get full details for each workflow
			$detailed_workflows = [];

			foreach ( $workflows as $workflow ) {

				$details = $githubAction->getWorkflowDetails( $workflow['id'] );

				if ( ! is_wp_error( $details ) ) {

					$workflow['inputs']   = $details['inputs'] ?? [];
					$detailed_workflows[] = $workflow;
				}
			}
			wp_send_json_success( $detailed_workflows );
		}
	}

	/**
	 * Display Github Rate Limit Notice as message.
	 * 
	 * @return void
	 */
	public function display_github_rate_limit_notice() {

		if ( $message = get_transient( 'github_rate_limit_notice' ) ) {

			echo '<div class="notice notice-warning is-dismissible"><p>' . esc_html( $message ) . '</p></div>';

			// Remove the transient so it only displays once.
			delete_transient( 'github_rate_limit_notice' );
		}
	}

}
