<?php

namespace WPForms\DevTools;

/**
 * Class GenerateForms.
 *
 * @since 0.10
 */
class GenerateForms {

	/**
	 * List of all site templates.
	 *
	 * @since {VERSION}
	 *
	 * @var array
	 */
	public static $templates = [];

	/**
	 * AJAX [wpf_generate_forms].
	 *
	 * @since 0.10
	 */
	public static function ajax_generate_forms() {

		check_ajax_referer( DevTools::NONCE, 'nonce' );

		$selected_template = sanitize_key( wpforms_list_get( $_POST, 'template', 'blank' ) ); // phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotValidated
		$count             = max( 1, (int) wpforms_list_get( $_POST, 'count', 1 ) );
		$chunk             = max( 1, (int) wpforms_list_get( $_POST, 'chunk', 1 ) );

		self::$templates = Main::wpforms_obj( 'builder_templates' )->get_templates();

		$forms    = [];
		$template = [];

		if ( $selected_template === 'blank' ) {
			$template = [
				'name' => 'Blank',
				'slug' => 'blank',
			];
		}

		for ( $i = 1; $i <= $chunk; $i ++ ) {
			$forms[] = self::create_form_from_template(
				! empty( $template ) ? $template : self::get_template( $selected_template ),
				$i,
				$count
			);
		}

		wp_send_json_success( array_values( $forms ) );
	}

	/**
	 * Retrieve a template.
	 *
	 * @since {VERSION}
	 *
	 * @param string $selected_template Some template.
	 *
	 * @return array
	 */
	public static function get_template( $selected_template ) {

		if ( $selected_template === 'random' ) {
			$template = self::$templates[ array_rand( self::$templates ) ];
		} elseif ( ! in_array( $selected_template, array_column( self::$templates, 'slug' ), true ) ) {
			$template = [];
		} else {
			foreach ( self::$templates as $slug => $template ) {
				if ( $slug === $selected_template ) {
					break;
				}
			}
		}

		return $template;
	}

	/**
	 * Create a new form from a template.
	 *
	 * @since 0.10
	 *
	 * @param array $template Template data.
	 * @param int   $index    Current number of the form.
	 * @param int   $count    Total number of forms to create.
	 *
	 * @return int
	 */
	public static function create_form_from_template( $template, $index, $count ) {

		if ( empty( $template ) ) {
			return 0;
		}

		// Create form.
		return (int) Main::wpforms_obj( 'form' )->add(
			$template['name'] . ' (' . $index . '/' . $count . ':' . time() . ')',
			[],
			[
				'template' => $template['slug'],
				'builder'  => false,
			]
		);
	}
}
