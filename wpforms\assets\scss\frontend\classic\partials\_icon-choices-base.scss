// WPForms Classic styles.
//
// Icon choices.
//
// @since 1.8.1

ul.wpforms-icon-choices,
ul.wpforms-icon-choices * {
	box-sizing: border-box;
}

// Base list container styles, applies to 1 column layout option too.
ul.wpforms-icon-choices {
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 0 1px 0 1px !important; // Compensate box-shadow on the right and bottom.
	margin: 12px 0 -20px 0 !important; // Compensate for right-most and bottom-most items margin.

	// Descriptions and errors after the list should be spaced out consistently.
	& + .wpforms-field-description,
	& + .wpforms-error {
		margin-top: 15px;
	}

	// Base list item styles, applies to 1 column layout option too.
	li {
		min-width: 120px;
		padding-right: 0 !important;
		margin: 0 0 20px 0 !important; // Faux gaps.
	}

	label {
		position: relative;
		display: block;
		margin: 0;
		cursor: pointer;
	}

	.wpforms-icon-choices-icon {
		display: block;
	}

	svg {
		margin: 0 auto;
		fill: var(--wpforms-icon-choices-color);
	}

	&.wpforms-icon-choices-none {

		svg {
			margin: 0;
		}
	}

	&.wpforms-icon-choices-default,
	&.wpforms-icon-choices-modern {

		li {
			margin: 0 0 22px 0 !important; // Faux gaps.
		}
	}

	/* Style: Default */
	&.wpforms-icon-choices-default {

		label {
			text-align: center;
			//padding: 0;

			&:focus-within {

				.wpforms-icon-choices-icon {
					box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
				}
			}
		}

		.wpforms-icon-choices-icon {
			position: relative;
			z-index: 1;
			margin-bottom: 10px;
			padding: 15px 20px 45px 20px;
			background-color: #ffffff;
			box-shadow: 0 0 0 1px #cccccc;
			border-radius: 6px;

			&:hover {
				box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
			}

			svg {
				position: relative;
				z-index: 2;
			}
		}

		.wpforms-icon-choices-icon-bg {
			display: block;
			position: absolute;
			z-index: 0;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			border-radius: 6px;
			background-color: #ffffff;
		}

		.wpforms-selected, li:has( input:checked ) {
			.wpforms-icon-choices-icon {
				background-color: transparent;
				box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
				box-sizing: border-box;
			}

			.wpforms-icon-choices-icon-bg {
				background-color: var(--wpforms-icon-choices-color);
				opacity: .1;
			}
		}
	}

	/* Style: Modern */
	&.wpforms-icon-choices-modern {

		li {

			label {
				background-color: #ffffff !important;
				box-shadow: 0 0 0 1px #cccccc;
				border-radius: 6px;
				height: 100%;
				padding: 20px 20px 15px 20px;
				text-align: center;

				&:hover {
					box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
				}
			}

			&:focus-within {

				label {
					box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
				}
			}

			&.wpforms-selected, &:has( input:checked ) {

				label {
					box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color), 0 2px 10px rgba(0, 0, 0, 0.15);
				}
			}

			.wpforms-icon-choices-icon {
				margin-bottom: 10px;
			}
		}
	}

	/* Style: Classic */
	&.wpforms-icon-choices-classic {

		li {

			label {
				background-color: #ffffff !important;
				height: 100%;
				padding: 20px 20px 15px 20px;
				text-align: center;

				&:hover {
					box-shadow: 0 0 0 1px #999999;
				}
			}

			&:focus-within {

				label {
					box-shadow: 0 0 0 1px #999999;
				}
			}

			&.wpforms-selected, &:has( input:checked ) {

				label {
					box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
				}
			}

			.wpforms-icon-choices-icon {
				margin-bottom: 10px;
			}
		}
	}
}

// Custom controls for radio inputs (Default style).
.wpforms-field-radio,
.wpforms-field-payment-multiple {

	ul.wpforms-icon-choices-default {

		li {

			.wpforms-icon-choices-icon:before {
				content: "";
				position: absolute;
				z-index: 2;
				bottom: 15px;
				left: calc(50% - 8px);
				display: block;
				width: 16px;
				height: 16px;
				background-color: #ffffff;
				box-shadow: 0 0 0 1px #cccccc;
				border-radius: 50%;
				margin: 15px auto 0;
			}

			&.wpforms-selected, &:has( input:checked ) {

				.wpforms-icon-choices-icon:before {
					box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
				}

				.wpforms-icon-choices-icon:after {
					content: "";
					position: absolute;
					z-index: 2;
					bottom: calc(15px + 4px);
					left: calc(50% - 4px);
					display: block;
					width: 8px;
					height: 8px;
					background-color: var(--wpforms-icon-choices-color);
					border-radius: 50%;
				}
			}
		}
	}
}

// Custom controls for checkbox inputs (Default style).
.wpforms-field-checkbox,
.wpforms-field-payment-checkbox {

	ul.wpforms-icon-choices-default {

		li {

			.wpforms-icon-choices-icon:before {
				content: "";
				position: absolute;
				z-index: 2;
				bottom: 15px;
				left: calc(50% - 8px);
				display: block;
				width: 16px;
				height: 16px;
				background-color: #ffffff;
				box-shadow: 0 0 0 1px #cccccc;
				border-radius: 3px;
				margin: 15px auto 0;
			}

			&.wpforms-selected, &:has( input:checked ) {

				.wpforms-icon-choices-icon:before {
					box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
				}

				.wpforms-icon-choices-icon:after {
					content: "";
					box-sizing: border-box;
					display: block;
					position: absolute;
					z-index: 2;
					bottom: 23px;
					left: calc(50% - 6px);
					width: 6px;
					height: 10px;
					border-style: solid;
					border-color: var(--wpforms-icon-choices-color);
					border-width: 0 2px 2px 0;
					transform-origin: bottom left;
					transform: rotate(45deg);
				}
			}
		}
	}
}

// All other layout options, except one column.
.wpforms-list-2-columns ul.wpforms-icon-choices,
.wpforms-list-3-columns ul.wpforms-icon-choices,
.wpforms-list-inline ul.wpforms-icon-choices {
	flex-direction: row;
	flex-wrap: wrap;

	li {
		margin-right: 20px !important;
	}

	&.wpforms-icon-choices-default,
	&.wpforms-icon-choices-modern {

		li {
			margin-right: 22px !important;
			margin-bottom: 22px !important;
		}
	}
}

// Two columns layout.
.wpforms-list-2-columns ul.wpforms-icon-choices {

	li {
		width: calc( 100% / 2 - 20px / 2 );

		&:nth-child(2n) {
			margin-right: 0 !important;
		}
	}

	&.wpforms-icon-choices-default,
	&.wpforms-icon-choices-modern {

		li {
			width: calc( 100% / 2 - 22px / 2 );
		}
	}
}

// Three column layout.
.wpforms-list-3-columns ul.wpforms-icon-choices {

	li {
		width: calc( 100% / 3 - 20px * 2 / 3 );

		&:nth-child(3n) {
			margin-right: 0 !important;
		}
	}

	&.wpforms-icon-choices-default,
	&.wpforms-icon-choices-modern {

		li {
			width: calc( 100% / 3 - 22px * 2 / 3 );
		}
	}
}

// Inline: fluid sizing.
.wpforms-list-inline ul.wpforms-icon-choices {

	li {
		width: auto;
		max-width: calc( 100% / 4 - 20px );
	}
}
