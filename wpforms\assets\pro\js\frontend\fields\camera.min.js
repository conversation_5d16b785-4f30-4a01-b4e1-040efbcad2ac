var WPForms=window.WPForms||{};WPForms.Camera=WPForms.Camera||((n,c,s)=>{let p=wpforms_camera_frontend.strings,f={stream:null,currentFacingMode:"user",init(){s(f.ready)},ready(){f.events()},events(){n.querySelectorAll(".wpforms-container").forEach(function(e){f.addCameraClickHandler(e)}),s(n).on("click",".wpforms-camera-link, .wpforms-camera-button",f.openModal),s(n).on("click",".wpforms-camera-capture",f.capture),s(n).on("click",".wpforms-camera-remove-file",f.clearSelectedFile),s(n).on("click",".wpforms-camera-flip",f.flipCamera)},addCameraClickHandler(e){e.addEventListener("click",function(e){if(e.target.classList.contains("wpforms-camera")||e.target.closest(".wpforms-camera"))return e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),f.openModal(e),!1},!0)},openModal(e){e.preventDefault(),e.target.blur();var e=s(e.target.closest(".wpforms-field")),r=e.data("field-id"),e=e.closest(".wpforms-form").data("formid");let a=s(`#wpforms-camera-modal-${e}-`+r);var o,t=a.find(".wpforms-camera-video-countdown");t.length&&(o=parseInt(t.data("time-limit"),10)||30,t.find("span").text(f.formatTime(o))),s("body").addClass("wpforms-camera-modal-open"),f.isPhone()&&a.find(".wpforms-camera-flip").addClass("wpforms-camera-flip-active"),a.css("display","flex"),f.initCamera(e,r),a.find(".wpforms-camera-modal-close").off("click").on("click",function(){f.closeModal(a)}),f.addKeyboardListener(a)},closeModal(e){f.stopCamera(),f.removeKeyboardListener(e),s("body").removeClass("wpforms-camera-modal-open"),e.css("display","none"),e.find(".wpforms-camera-captured-photo").remove(),e.find(".wpforms-camera-captured-video").remove(),e.find(".wpforms-camera-modal-buttons").hide(),e.find(".wpforms-camera-stop").hide(),e.find("cropper-canvas").remove(),e.removeData("cropper"),e.removeData("cropped-blob"),e.find("video").show(),e.find(".wpforms-camera-modal-actions").css("display","flex"),e.find(".wpforms-camera-capture").show()},addKeyboardListener(r){function e(e){32===e.keyCode&&"flex"===r.css("display")&&(e.preventDefault(),(e=r.find(".wpforms-camera-stop")).is(":visible")?e.trigger("click"):(e=r.find(".wpforms-camera-capture")).is(":visible")&&!e.prop("disabled")&&e.trigger("click"))}r.data("keyboard-handler",e),s(n).on("keydown.wpforms-camera",e)},removeKeyboardListener(e){var r=e.data("keyboard-handler");r&&(s(n).off("keydown.wpforms-camera",r),e.removeData("keyboard-handler"))},initCamera(e,r){var a=s(`#wpforms-camera-video-${e}-`+r);let o=s(`#wpforms-camera-modal-${e}-${r} .wpforms-camera-preview`),t=s(`#wpforms-camera-modal-${e}-${r} .wpforms-camera-error`);e=s(`#wpforms-camera-modal-${e}-`+r);let c=a.get(0),i=e.find(".wpforms-camera-capture"),n=e.hasClass("wpforms-camera-format-video");o.css("display","flex"),t.hide(),f.stopCamera();r={audio:n,video:{facingMode:f.currentFacingMode,height:{ideal:960,max:1280},frameRate:{ideal:24,max:30}}};navigator.mediaDevices.getUserMedia(r).then(function(e){f.stream=e,c.srcObject=e,i.removeAttr("disabled")}).catch(function(){var e=n?p.camera_video_access_error:p.camera_access_error;o.hide(),t.text(e).show()})},isPhone(){var e,r,a,o,t;return void 0!==navigator.userAgentData?.mobile?navigator.userAgentData.mobile:(e="MacIntel"===navigator.platform&&1<navigator.maxTouchPoints,r=navigator.userAgent.toLowerCase(),r=/(iphone|ipod|android.*mobile|windows phone|bb10|opera mini|mobile safari)/.test(r),a=matchMedia("(pointer: coarse)").matches,o=matchMedia("(hover: none)").matches,t="ontouchstart"in c||0<navigator.maxTouchPoints,!e&&r||(a||o)&&t)},stopCamera(){s(".wpforms-camera-modal-overlay").each(function(){var e=s(this),r=e.data("media-recorder"),r=(r&&"recording"===r.state&&r.stop(),e.data("countdown-timer"));r&&clearInterval(r)}),f.stream&&(s.each(f.stream.getTracks(),function(e,r){r.stop()}),f.stream=null)},flipCamera(e){var r;f.isPhone()&&(e&&e.preventDefault(),(e=s(e.target).closest(".wpforms-camera-modal-overlay")).length)&&(r=e.data("form-id"),e=e.data("field-id"),f.currentFacingMode="user"===f.currentFacingMode?"environment":"user",f.initCamera(r,e))},capture(e){e.preventDefault();var e=s(e.currentTarget),r=e.closest(".wpforms-camera-modal-overlay"),a=r.data("form-id"),r=r.data("field-id");f.captureAnimation(e,a,r)},captureAnimation(e,r,a){let o=e.parent().find(".wpforms-camera-countdown"),t=o.find("span"),c=o.closest(".wpforms-camera-modal-overlay").hasClass("wpforms-camera-format-photo")?"photo":"video",i=(e.closest(".wpforms-camera-modal-overlay").find(".wpforms-camera-flip").removeClass("wpforms-camera-flip-active"),e.hide(),o.css("display","flex"),setTimeout(function(){o.addClass("animate")},50),wpforms_camera_frontend.wait_time);var n=0<i?1e3:0;function p(){0<--i?(t.text(i),setTimeout(p,1e3)):(o.removeClass("animate").hide(),t.text(wpforms_camera_frontend.wait_time.toString()),"photo"==c?(e.css("display","flex"),f.takePhoto(r,a)):f.recordVideo(r,a))}setTimeout(p,n)},takePhoto(e,r){var a,o,t,c,i,n,p;f.stream&&(a=s(`#wpforms-camera-modal-${e}-`+r),o=s(`#wpforms-camera-video-${e}-`+r),c=s(`#wpforms-camera-canvas-${e}-`+r),t=a.find(".wpforms-camera-preview"),n=o.get(0),c=c.get(0),p=n.videoWidth,i=n.videoHeight,c.width=p,c.height=i,c.getContext("2d").drawImage(n,0,0,p,i),o.hide(),n=s("<img>",{class:"wpforms-camera-captured-photo",src:c.toDataURL("image/jpeg",.92)}),t.append(n),a.find(".wpforms-camera-modal-actions").hide(),(p=a.find(".wpforms-camera-modal-buttons")).show(),this.addPhotoHandlers(p,c,a,e,r,t,o))},addPhotoHandlers(r,a,o,t,c,i,n){r.find(".wpforms-camera-accept").off("click").on("click",function(e){e.preventDefault();e=o.data("cropped-blob");e?(f.closeModal(o),f.attachFile(t,c,e),o.css("display","none")):a.toBlob(function(e){e&&(f.closeModal(o),f.attachFile(t,c,e),o.css("display","none"))},"image/jpeg",.92)}),r.find(".wpforms-camera-cancel").off("click").on("click",function(e){e.preventDefault(),i.find(".wpforms-camera-captured-photo").remove(),n.show(),o.find(".wpforms-camera-modal-actions").css("display","flex"),r.hide(),o.removeData("cropped-blob"),o.find("cropper-canvas").remove(),r.find(".wpforms-camera-accept-crop").hide(),r.find(".wpforms-camera-crop-cancel").hide(),r.find(".wpforms-camera-crop").show(),r.find(".wpforms-camera-accept").show(),o.find(".wpforms-camera-flip").addClass("wpforms-camera-flip-active")}),r.off("click",".wpforms-camera-crop").on("click",".wpforms-camera-crop",function(e){e.preventDefault();e=f.initCropper(i);o.data("cropper",e),s(this).hide(),r.find(".wpforms-camera-accept").hide(),r.find(".wpforms-camera-accept-crop").show(),r.find(".wpforms-camera-crop-cancel").show()}),r.off("click",".wpforms-camera-crop-cancel").on("click",".wpforms-camera-crop-cancel",function(e){e.preventDefault(),f.cancelCrop(o,r)}),r.off("click",".wpforms-camera-accept-crop").on("click",".wpforms-camera-accept-crop",function(e){e.preventDefault(),f.acceptCrop(o,r)})},acceptCrop(a,e){var r=a.data("cropper"),o=r.getCropperSelection();let t=a.find(".wpforms-camera-captured-photo");r=f.createManualCroppedCanvas(r,o,t);r?(r.toBlob(function(e){var r;e&&(r=URL.createObjectURL(e),t.attr("src",r),a.data("cropped-blob",e))},"image/jpeg",.92),a.find("cropper-canvas").remove(),a.removeData("cropper"),a.find(".wpforms-camera-captured-photo").show(),e.find(".wpforms-camera-crop-cancel").hide(),e.find(".wpforms-camera-accept-crop").hide(),e.find(".wpforms-camera-crop").show(),e.find(".wpforms-camera-accept").show()):f.cancelCrop(a,e)},cancelCrop(e,r){e.data("cropper")&&(e.find("cropper-canvas").remove(),e.removeData("cropper"),e.find(".wpforms-camera-captured-photo").show()),e.removeData("cropped-blob"),r.find(".wpforms-camera-crop-cancel").hide(),r.find(".wpforms-camera-accept-crop").hide(),r.find(".wpforms-camera-accept").show(),r.find(".wpforms-camera-crop").show()},createManualCroppedCanvas(e,r,a){var o,t,c,i,a=a[0];return!a||!a.complete||(e=e.getCropperCanvas().getBoundingClientRect(),o=((r=r.getBoundingClientRect()).left-e.left)/e.width,t=(r.top-e.top)/e.height,c=r.width/e.width,r=r.height/e.height,e=a.naturalWidth||a.width,i=a.naturalHeight||a.height,o=Math.max(0,Math.floor(o*e)),t=Math.max(0,Math.floor(t*i)),c=Math.min(e-o,Math.floor(c*e)),e=Math.min(i-t,Math.floor(r*i)),c<=0)||e<=0?null:((r=n.createElement("canvas")).width=c,r.height=e,(i=r.getContext("2d")).imageSmoothingEnabled=!0,i.imageSmoothingQuality="high",i.drawImage(a,o,t,c,e,0,0,c,e),r)},initCropper(e){var r=c.Cropper&&c.Cropper.default||c.Cropper,a=e[0],r=new r(e.find(".wpforms-camera-captured-photo")[0],{container:a,template:f.getCropperTemplate()}),e=r.getCropperCanvas(),a=r.getCropperSelection();return n.querySelector("cropper-grid").$addStyles(`
					:host>span+span {
						border-top: 1px solid #fff;
					}
					:host>span>span+span {
						border-left: 1px solid #fff;
					}
					`),e&&a&&f.limitCropperBoundaries(e,a),r},getCropperTemplate(){return`
				<cropper-canvas background>
					<cropper-image></cropper-image>
					<cropper-shade theme-color="rgba(0, 0, 0, 0.75)"></cropper-shade>
					<cropper-handle action="select" plain></cropper-handle>
					<cropper-selection initial-coverage="0.7" movable resizable>
						<cropper-grid role="grid" theme-color="#fff" bordered covered></cropper-grid>
						<cropper-handle action="move" theme-color="transparent"></cropper-handle>
						<cropper-handle theme-color="#fff" action="n-resize"></cropper-handle>
						<cropper-handle theme-color="#fff" action="e-resize"></cropper-handle>
						<cropper-handle theme-color="#fff" action="s-resize"></cropper-handle>
						<cropper-handle theme-color="#fff" action="w-resize"></cropper-handle>
						<cropper-handle theme-color="#fff" action="ne-resize"></cropper-handle>
						<cropper-handle theme-color="#fff" action="nw-resize"></cropper-handle>
						<cropper-handle theme-color="#fff" action="se-resize"></cropper-handle>
						<cropper-handle theme-color="#fff" action="sw-resize"></cropper-handle>
					</cropper-selection>
				</cropper-canvas>
				`},limitCropperBoundaries(n,e){e.addEventListener("change",e=>{var{x:r,y:a,width:o,height:t}=e.detail,{width:c,height:i}=n.getBoundingClientRect();0<=r&&0<=a&&r+o<=c&&a+t<=i||e.preventDefault()})},isMediaRecorderSupported(){return"undefined"!=typeof MediaRecorder},handleUnsupportedMediaRecorder(e,r){e=s(`#wpforms-camera-modal-${e}-`+r),r=e.find(".wpforms-camera-preview"),e=e.find(".wpforms-camera-error");r.hide(),e.text(p.video_recording_error).show()},getSupportedVideoType(){let e={mimeType:"video/webm;codecs=vp9,opus",extension:"webm",videoBitsPerSecond:25e5};var r,a=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);if(a)MediaRecorder.isTypeSupported("video/mp4")&&(e={mimeType:"video/mp4",extension:"mp4",videoBitsPerSecond:25e5});else for(r of["video/webm;codecs=vp9,opus","video/webm;codecs=vp8,opus","video/webm"])if(MediaRecorder.isTypeSupported(r)){e.mimeType=r;break}return e},createMediaRecorder(e,r,a,o){try{return new MediaRecorder(f.stream,{mimeType:e.mimeType,videoBitsPerSecond:e.videoBitsPerSecond})}catch(e){try{return new MediaRecorder(f.stream)}catch(e){return r.hide(),a.text(p.video_recording_error).show(),o.hide(),null}}},setupRecordingTimer(e,r,a,o,t){let c=e,i=(r.text(f.formatTime(c)),setInterval(function(){c--,r.text(f.formatTime(c)),c<=0&&(clearInterval(i),"recording"===a.state&&a.stop(),o.hide())},1e3));return t.data("countdown-timer",i),i},setupStopButton(r,a,o,t,c){r.off("click").on("click",function(e){e.preventDefault(),clearInterval(a.data("countdown-timer")),r.hide(),"recording"===o.state&&o.stop(),c.text(f.formatTime(t))})},setupCancelVideoButton(r){r.find(".wpforms-camera-cancel-video").show().off("click").on("click",function(e){e.preventDefault(),f.closeModal(r)})},handleRecordingComplete(e,r,a,o,t,c,i){var n=URL.createObjectURL(e),p=s(`#wpforms-camera-video-${a}-`+o),d=(p.hide(),this.createRecordedVideoElement(n));i.append(d),this.setupRecordingAcceptUI(c,e,r,n,a,o,t,p,i)},createRecordedVideoElement(e){return s("<video>",{class:"wpforms-camera-captured-video",src:e,controls:!0,autoplay:!0,muted:!1,playsinline:!0})},setupRecordingAcceptUI(r,a,o,t,c,i,n,p,d){r.find(".wpforms-camera-modal-actions").hide();let m=r.find(".wpforms-camera-modal-buttons");m.show(),m.find(".wpforms-camera-accept").off("click").on("click",function(e){e.preventDefault(),f.closeModal(r);e=n.extension||"webm",e=`camera-video-${Date.now()}.`+e,e=new File([a],e,{type:o});f.attachFile(c,i,e),r.css("display","none")}),m.find(".wpforms-camera-cancel").off("click").on("click",function(e){e.preventDefault(),d.find(".wpforms-camera-captured-video").remove(),URL.revokeObjectURL(t),p.show(),r.find(".wpforms-camera-capture").css("display","flex"),r.find(".wpforms-camera-modal-actions").css("display","flex"),r.find(".wpforms-camera-flip").addClass("wpforms-camera-flip-active"),m.hide()})},recordVideo(n,p){if(f.stream)if(f.isMediaRecorderSupported()){let o=s(`#wpforms-camera-modal-${n}-`+p);var e=o.find(".wpforms-camera-stop"),r=o.find(".wpforms-camera-video-countdown");let t=o.find(".wpforms-camera-preview");var d=o.find(".wpforms-camera-error"),m=parseInt(r.data("time-limit"),10)||30;e.show();let c=f.getSupportedVideoType(),i=f.createMediaRecorder(c,t,d,e);if(i){let a=[];i.ondataavailable=function(e){e.data&&0<e.data.size&&a.push(e.data)},i.onstop=function(){var e=i.mimeType||c.mimeType||"video/webm",r=new Blob(a,{type:e});f.handleRecordingComplete(r,e,n,p,c,o,t)},i.start(1e3),o.data("media-recorder",i);d=r.find("span");f.setupRecordingTimer(m,d,i,e,o),f.setupStopButton(e,o,i,m,d),f.setupCancelVideoButton(o)}}else f.handleUnsupportedMediaRecorder(n,p)},attachFile(e,r,a){let o,t;t=a.type.startsWith("video/")?(i=a.type.includes("mp4")?"mp4":"webm",o=`camera-video-${Date.now()}.`+i,a.type||("mp4"==i?"video/mp4":"video/webm")):(o=`camera-photo-${Date.now()}.jpg`,"image/jpeg");var c,i=new File([a],o,{type:t}),a=s(`.wpforms-uploader[data-form-id="${e}"][data-field-id="${r}"]`);0<a.length?(c=a[0])&&c.dropzone&&(c.dropzone.addFile(i),(a=a.parents(".wpforms-field-camera").find('input[name="'+c.dataset.inputName+'"]')).length)&&f.setCameraFieldState(a,i):(c=s(`#wpforms-${e}-field_`+r)).length&&((a=new DataTransfer).items.add(i),c[0].files=a.files,c.trigger("change"),f.setCameraFieldState(c,i))},setCameraFieldState(e,r){if(e.length&&n.contains(e[0])){var a=e.closest(".wpforms-field-camera");if(a.length){r=r?.name??"";if(!r)return;var o=a.find(".wpforms-camera-link, .wpforms-camera-button");if(!o.length)return;o.hide(),a.find(".wpforms-camera-selected-file").addClass("wpforms-camera-selected-file-active").find("span").text(r),a.removeClass("wpforms-has-error"),e.removeClass("wpforms-error"),a.find(".wpforms-error").remove(),f.checkAndHideGeneralFormError(a)}o=e.closest(".wpforms-field-file-upload");o.length&&(o.removeClass("wpforms-has-error"),o.find(".wpforms-error:not(input)").remove(),e.removeClass("wpforms-error"),e.show().css("display","block"),e.data("validator")&&e.valid(),f.checkAndHideGeneralFormError(o))}},clearSelectedFile(e){e.preventDefault();var r,a,e=s(e.currentTarget).closest(".wpforms-field-camera");e.length&&(r=e.find('input[type="file"]')[0])&&(a=e.find(".wpforms-camera-selected-file"),e=e.find(".wpforms-camera-link, .wpforms-camera-button"),r.value="",a.removeClass("wpforms-camera-selected-file-active"),e.show())},formatTime(e){return Math.floor(e/60)+":"+(e%60).toString().padStart(2,"0")},checkAndHideGeneralFormError(e){e=e.closest("form");!e.length||0<e.find(".wpforms-has-error, .wpforms-error").length||e.find(".wpforms-error-container").remove()}};return f})(document,window,jQuery),WPForms.Camera.init();