/* Base */
.body-inner {
	padding-top: 25px;
	padding-bottom: 25px;
}

.wrapper {
	max-width: 700px;
}

.wrapper-inner {
	background-color: $backgroundContent;
	border: 1px solid lighten($fontColor, 60%);
	padding: 5px 30px 30px 30px;
}

.header {
	text-align: center;
	padding: 0 0 25px 0;

	.header-image {
		/* This is needed to center the logo in Outlook. */
		margin: 0 auto 0 auto;
	}
}

.footer {
	font-size: 13px;
	line-height: 24px;
	padding-top: 25px;
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
	line-height: 20px;
}

/* Content */
.content {
	td {
		border-bottom: 1px solid lighten($fontColor, 65%);
		padding-top: 25px;
		padding-bottom: 25px;
	}

	a {
		text-decoration: none;
	}

	.field-name {
		min-width: 113px;
		padding-right: 10px;

		.is-rtl & {
			padding-left: 10px;
		}

		&:not(.field-value) {
			line-height: 22px;
		}
	}

	.field-value {
		vertical-align: middle;
	}

	.field-name.field-value {
		line-height: 20px;
	}

	.wpforms-layout-table-row-display-rows {
		& ~ tr {
			td {
				border-bottom: none;
				padding-top: 0;

				&.field-name {
					opacity: 0;
				}
			}
		}

		& + tr {
			td {
				border-top: 1px solid lighten($fontColor, 65%);
				padding-top: 25px;

				&.field-name {
					opacity: 1;
				}
			}
		}
	}

	.wpforms-layout-table-row-display-blocks {
		& ~ tr {
			td {
				border-bottom: 1px solid lighten($fontColor, 65%);
				padding-top: 25px;

				&.field-name {
					opacity: 1;
				}
			}
		}
	}

	.wpforms-layout-table-row {
		& ~ tr {
			td {
				&.field-repeater-name {
					opacity: 1;
					padding-top: 25px;
					border-top: 1px solid lighten($fontColor, 65%);
					border-bottom: 1px solid lighten($fontColor, 65%);

					& + td {
						border-top: 1px solid lighten($fontColor, 65%);
						border-bottom: 1px solid lighten($fontColor, 65%);
					}
				}
			}
		}
	}
}

// Order summary table.
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview {
	border-radius: 0;
}
