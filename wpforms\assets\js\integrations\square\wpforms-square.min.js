let WPFormsSquare=window.WPFormsSquare||((i,o,s)=>{let a,n={cardNumber:{empty:!0,valid:!1},expirationDate:{empty:!0,valid:!1},cvv:{empty:!0,valid:!1},postalCode:{empty:!0,valid:!1}},l={payments:null,lockedPageToSwitch:0,init(){l.payments=l.getPaymentsInstance(),null!==l.payments&&s(i).on("wpformsReady",l.setupForms).on("wpformsBeforePageChange",l.pageChange).on("wpformsPageChange",l.afterPageChange).on("wpformsProcessConditionalsField",l.conditional<PERSON><PERSON>)},setupForms(){void 0!==s.fn.validate&&s(".wpforms-square form").filter((e,r)=>"number"==typeof s(r).data("formid")).each(l.updateSubmitHandler)},async updateSubmitHandler(){var e=s(this),r=e.data("validator");!r||e.hasClass("wpforms-square-initialization")||e.hasClass("wpforms-square-initialized")||e.closest(".elementor-location-popup").length&&!e.closest(".elementor-popup-modal").length||(e.addClass("wpforms-square-initialization"),a=r.settings.submitHandler,r.settings.submitHandler=l.submitHandler,await l.getCardInstance(e))},conditionalLogicHandler(e,r,t,a,s){l.isVisibleField(a,s)&&(a=i.getElementById("wpforms-"+r+"-field_"+t))&&a.classList.contains("wpforms-field-square-cardnumber")&&o.dispatchEvent(new Event("resize"))},isVisibleField(e,r){return"show"===r&&e||"hide"===r&&!e},submitHandler(e){var e=s(e),r=e.data("validator").form(),t=e.find(".wpforms-square-credit-card-hidden-input").data("square-card");r&&void 0!==t&&l.isProcessedCard(e)?l.tokenize(e,t):a(e)},async tokenize(e,r){l.disableSubmitBtn(e),null===await l.getSourceId(e,r)?l.enableSubmitBtn(e):l.submitForm(e)},getPaymentsInstance(){if(!o.Square)return l.displaySdkError(s(".wpforms-square form"),wpforms_square.i18n.missing_sdk_script),null;try{return Square.payments(wpforms_square.client_id,wpforms_square.location_id)}catch(e){var r="object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"message")?e.message:wpforms_square.i18n.missing_creds;return l.displaySdkError(s(".wpforms-square form"),r),null}},async getCardInstance(r){var t={};t.style=wpforms_square.card_config.style||l.getModernMarkupCardStyles(r);try{var a=await l.payments.card(t),s=(await a.attach(r.find(".wpforms-field-square-cardnumber").get(0)),["focusClassAdded","focusClassRemoved"]),i=s.length;let e=0;for(;e<i;e++)a.addEventListener(s[e],function(e){n[e.detail.field].empty=e.detail.currentState.isEmpty,n[e.detail.field].valid=e.detail.currentState.isCompletelyValid,n[e.detail.field].valid&&l.removeFieldError(r)});return r.find(".wpforms-square-credit-card-hidden-input").data("square-card",a),r.removeClass("wpforms-square-initialization"),r.addClass("wpforms-square-initialized"),a}catch(e){return l.displaySdkError(r,wpforms_square.i18n.card_init_error),r.removeClass("wpforms-square-initialization"),console.log("Error:",e),console.log("Config",t),null}},async getSourceId(r,e){try{var t=await e.tokenize(l.getChargeVerifyBuyerDetails(r));return(r.find(".wpforms-square-payment-source-id").remove(),"OK"===t.status&&t.token)?(r.append('<input type="hidden" name="wpforms[square][source_id]" class="wpforms-square-payment-source-id" value="'+l.escapeTextString(t.token)+'">'),t.token):(l.displayFormError(l.getCreditCardInput(r),l.getResponseError(t)),null)}catch(e){l.displayFormError(l.getCreditCardInput(r),wpforms_square.i18n.token_process_fail)}return null},getResponseError(e){return e.errors&&Array.isArray(e.errors)&&e.errors.length?e.errors[0].message:wpforms_square.i18n.token_status_error+" "+e.status},getChargeVerifyBuyerDetails(e){return{amount:l.getTotalInMinorUnits(wpforms.amountTotalCalc(e)),billingContact:l.getBillingContactDetails(e),currencyCode:wpforms_settings.currency_code,intent:"CHARGE",customerInitiated:!0,sellerKeyedIn:!1}},getTotalInMinorUnits(e){return parseInt(wpforms.numberFormat(e,wpforms_settings.currency_decimal,"",""),10).toString()},getBillingContactDetails(e){var e=e.data("formid"),e=wpforms_square.billing_details&&wpforms_square.billing_details[e]||{},r={},t=e.buyer_email?s(`.wpforms-field-email[data-field-id="${e.buyer_email}"]`):"",a=e.billing_name?s(`.wpforms-field-name[data-field-id="${e.billing_name}"]`):"",e=e.billing_address?s(`.wpforms-field-address[data-field-id="${e.billing_address}"]`):"";return t.length&&(t=t.find("input").first().val())&&""!==t.trim()&&(r.email=t),a.length&&jQuery.extend(r,l.getBillingNameDetails(a)),e.length&&jQuery.extend(r,l.getBillingAddressDetails(e)),r},getBillingNameDetails(e){var r={};let t="",a="";var s=e.find(".wpforms-field-name-first"),i=e.find(".wpforms-field-name-last");return s.length&&i.length?(t=s.val()||"",a=i.val()||"",t&&""!==t.trim()&&(r.givenName=t),a&&""!==a.trim()&&(r.familyName=a)):(s=e.find("input")).length&&(i=s.val().trim()).length&&(e=i.split(" "),t=e.shift()||"",a=e.join(" ")||"",t&&""!==t.trim()&&(r.givenName=t),a)&&""!==a.trim()&&(r.familyName=a),r},getBillingAddressDetails(e){var r={},e=e.closest(".wpforms-field"),t=e.find(".wpforms-field-address-address1").val()||"",a=e.find(".wpforms-field-address-address2").val()||"",s=e.find(".wpforms-field-address-city").val()||"",i=e.find(".wpforms-field-address-state").val()||"",e=e.find(".wpforms-field-address-country").val()||"US",t=[t,a].filter(e=>e&&""!==e.trim());return t.length&&(r.addressLines=t),s&&""!==s.trim()&&(r.city=s),i&&""!==i.trim()&&(r.state=i),e&&""!==e.trim()&&(r.countryCode=e),r},getCreditCardInput(e){return e.find(".wpforms-square-credit-card-hidden-input")},submitForm(e){e.data("validator")&&a(e)},isProcessedCard(e){var e=e.find(".wpforms-field-square-cardnumber"),r=e.closest(".wpforms-field-square").hasClass("wpforms-conditional-hide"),e=!!e.data("required");return!r&&(e||l.isCardDataNotEmpty())},isCardDataNotEmpty(){return!(n.cardNumber.empty&&n.expirationDate.empty&&n.cvv.empty&&n.postalCode.empty)},isCardDataValid(){return n.cardNumber.valid&&n.expirationDate.valid&&n.cvv.valid&&n.postalCode.valid},displaySdkError(e,r){e.find(".wpforms-square-credit-card-hidden-input").closest(".wpforms-field-square-number").append(s("<label></label>",{text:r,class:"wpforms-error"}))},removeFieldError(e){e.find(".wpforms-field-square-number .wpforms-error").remove()},displayFormError(e,r){var t=e.attr("name"),a=e.closest("form"),s={};s[t]=r,wpforms.displayFormAjaxFieldErrors(a,s),wpforms.scrollToError(e)},disableSubmitBtn(e){e.find(".wpforms-submit").prop("disabled",!0)},enableSubmitBtn(e){e.find(".wpforms-submit").prop("disabled",!1)},escapeTextString(e){return s("<span></span>").text(e).html()},pageChange(e,r,t,a){var s=t.find(".wpforms-field-square-cardnumber");!s.is(":visible")||!s.data("required")&&!l.isCardDataNotEmpty()||l.lockedPageToSwitch&&l.lockedPageToSwitch!==r||"prev"===a||(l.isCardDataValid()?l.removeFieldError(t):(l.lockedPageToSwitch=r,l.displayFormError(l.getCreditCardInput(t),wpforms_square.i18n.empty_details),e.preventDefault()))},afterPageChange(){o.dispatchEvent(new Event("resize"))},getCssPropertyValue(e,r){try{return e.css(r)}catch(e){return""}},needsStyles(){return!!(o.WPForms&&WPForms.FrontendModern||s("#wpforms-conversational-form-page").length||s(".wpforms-lead-forms-container").length)},getModernMarkupCardStyles(e){var r;return l.needsStyles()?(e=l.getCreditCardInput(e),r=l.getCssPropertyValue(e,"color"),e={fontSize:l.getCssPropertyValue(e,"font-size"),colorText:r,colorTextPlaceholder:r},WPFormsUtils.hasOwnProperty("cssColorsUtils")&&"function"==typeof WPFormsUtils.cssColorsUtils.getColorWithOpacity&&(e.colorText=WPFormsUtils.cssColorsUtils.getColorWithOpacity(r),e.colorTextPlaceholder=WPFormsUtils.cssColorsUtils.getColorWithOpacity(r,"0.5")),{input:{color:e.colorText,fontSize:e.fontSize},"input::placeholder":{color:e.colorTextPlaceholder},"input.is-error":{color:e.colorText}}):{}}};return l})(document,window,jQuery);WPFormsSquare.init();