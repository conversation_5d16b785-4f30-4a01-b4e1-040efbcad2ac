// WPForms Modern Full styles.
//
// Dropdown field.
//
// @since 1.8.1

// Specific spacing for dropdowns.
$spacing_dropdown_m: 14px;

div.wpforms-container-full {
	.wpforms-form {

		// Classic style.
		.wpforms-field.wpforms-field-select-style-classic {
			select {
				padding-inline-start: $spacing_dropdown_m;
				&[multiple] {
					padding: 0;
					overflow: auto;

					@supports (font: -apple-system-body) and (-webkit-appearance: none) and (-webkit-hyphens: none) {
						padding: $spacing_s 12px; // Can't use 14px because Safari has 2px default option padding.
						line-height: 1;
					}

					& > option {
						padding: $spacing_s $spacing_dropdown_m;
						box-shadow: 0 1px 0 0 rgba( 0, 0, 0, 0.1 );

						&.placeholder,
						&[disabled] {
							box-shadow: 0 1px 0 0 rgba( 0, 0, 0, 0.2 );
						}
					}
				}
			}
		}

		// Modern style.
		.wpforms-field.wpforms-field-select-style-modern {

			$button-text-color: var( --wpforms-button-text-color-alt, var( --wpforms-button-text-color ) );

			.choices {
				font-size: var( --wpforms-field-size-font-size );
				line-height: 19px;
				color: var( --wpforms-field-text-color );
				margin-bottom: 5px;

				.choices__input--cloned {
					background-color: transparent;
				}

				.choices__inner {
					background-color: var( --wpforms-field-background-color );
					background-clip: padding-box;
					min-height: var( --wpforms-field-size-input-height );
					line-height: var( --wpforms-field-size-input-height );
					padding: 0 24px 0 7px;
					border-width: var( --wpforms-field-border-size );
					border-style: var( --wpforms-field-border-style );
					border-color: var( --wpforms-field-border-color );
					border-radius: var( --wpforms-field-border-radius );
					cursor: pointer;

					.choices__list--single {
						padding: 0 16px 0 4px;

						.choices__item--selectable {
							background-color: transparent;
							font-size: var( --wpforms-field-size-font-size );
							color: var( --wpforms-field-text-color );

							&.choices__placeholder {
								opacity: 0.5;
							}
						}
					}

					.choices__list--multiple {
						display: inline !important;

						&:empty {
							display: none;

							+ .choices__input {
								margin-left: 4px !important;
								min-width: 100% !important;
								text-overflow: ellipsis;
								padding-right: 20px !important;
								white-space: nowrap;
							}
						}

						.choices__item {
							position: relative;
							top: -1.5px;
							background-color: var( --wpforms-button-background-color );
							border: 1px solid var( --wpforms-button-background-color );
							border-radius: calc( max( var( --wpforms-field-border-radius ), 6px ) / 2 );
							color: $button-text-color;
							margin: 0 6px 6px 0;
							line-height: 1;
						}
					}
				}

				.choices__list--dropdown {
					background: var( --wpforms-field-menu-color ) !important;
					color: var( --wpforms-field-text-color );

					.choices__item--selectable.is-highlighted {
						background-color: var( --wpforms-button-background-color );
						color: $button-text-color;
					}
				}

				input.choices__input {
					display: inline-block;
					padding: 0 !important;
				}

				&[data-type*="select-one"] {
					input.choices__input {
						background: none !important;
						margin: $spacing_xs !important;
						padding: $spacing_xs !important;
						width: calc( 100% - #{ 2 * $spacing_xs } ) !important;
						border: 0 !important;
						box-shadow: none !important;
					}
				}

				::-webkit-input-placeholder {
					color: inherit;
					opacity: 0.5;
				}

				::-moz-placeholder {
					color: inherit;
					opacity: 0.5;
				}

				:-ms-input-placeholder {
					color: inherit;
					opacity: 0.5;
				}

				$arrow-color: var( --wpforms-field-border-color-spare );

				// Clear button.
				&[data-type*="select-one"] {
					.choices__button {
						opacity: 0.7;

						// Draw `x` using gradients.
						background-image:
							linear-gradient( 45deg, transparent 44%, $arrow-color 44%, $arrow-color 56%, transparent 56% ),
							linear-gradient( 135deg, transparent 44%, $arrow-color 44%, $arrow-color 56%, transparent 56% );
						background-position: 50% 50%, 50% 50%;
						background-size: 8px 8px, 8px 8px;
						background-repeat: no-repeat;

						&:hover {
							opacity: 1;
						}

						&:focus {
							@include wpforms-input-focus();
						}
					}
				}

				// Down arrow.
				&[data-type*="select-one"],
				&[data-type*="select-multiple"] {
					&:after {
						width: 0;
						height: 0;
						right: #{ $spacing_s + 2px };
						background: none;
						border-left: 5px solid transparent;
						border-right: 5px solid transparent;
						border-top: 5px solid $arrow-color;
					}

					&.is-open {
						&:after {
							border-top: 5px solid transparent;
							border-bottom: 5px solid $arrow-color;
						}
					}
				}

				&.is-focused .choices__inner,
				&.is-open .choices__inner,
				&.is-open .choices__list--dropdown {
					@include wpforms-input-focus();
				}

				&.is-open {
					&:before {
						content: '';
						position: absolute;
						height: 3px;
						background: var( --wpforms-field-background-color );
						width: calc( 100% - 2px );
						left: 1px;
						right: 1px;
						z-index: 100000000000;
						opacity: 1;
						border-radius: 0;
					}

					&:not(.is-flipped) {
						&:before {
							top: unset;
							bottom: 1px;
						}

						.choices__inner {
							border-radius: var( --wpforms-field-border-radius ) var( --wpforms-field-border-radius ) 0 0;
						}

						.choices__list--dropdown {
							border-radius: 0 0 var( --wpforms-field-border-radius ) var( --wpforms-field-border-radius );
							margin-top: 0;
						}
					}

					&.is-flipped {
						&:before {
							top: 1px;
						}

						.choices__inner {
							border-radius: 0 0 var( --wpforms-field-border-radius ) var( --wpforms-field-border-radius );
						}

						.choices__list--dropdown {
							border-radius: var( --wpforms-field-border-radius ) var( --wpforms-field-border-radius ) 0 0;
							margin-bottom: 0;
						}
					}
				}
			}

			&.wpforms-has-error {
				.choices {
					.choices__inner {
						@include wpforms-input-error();
					}

					&:hover {
						.choices__inner {
							@include wpforms-input-error-hover();
						}
					}

					&.is-focused .choices__inner,
					&.is-open .choices__inner,
					&.is-open .choices__list--dropdown {
						@include wpforms-input-error-focus();
					}
				}
			}
		}
	}
}
