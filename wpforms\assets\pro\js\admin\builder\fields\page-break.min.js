var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.FieldPageBreak=WPForms.Admin.Builder.FieldPageBreak||(o=>{let e={init(){o(e.ready)},ready(){e.events()},events(){o("#wpforms-builder").on("change",".wpforms-pagebreak-progress-indicator",e.handlePageBreakProgressIndicatorChange)},handlePageBreakProgressIndicatorChange(e){var r=o(e.target).closest(".wpforms-field-option-row").data("field-id");o(`#wpforms-field-option-row-${r}-progress_text`).toggleClass("wpforms-hidden","progress"!==e.target.value)}};return e})((document,window,jQuery)),WPForms.Admin.Builder.FieldPageBreak.init();