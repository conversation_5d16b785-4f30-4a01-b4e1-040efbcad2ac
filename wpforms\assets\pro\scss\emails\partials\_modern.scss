/* Base */
.body-inner {
	padding-top: 50px;
	padding-bottom: 50px;
}

.wrapper {
	max-width: 700px;
}

.wrapper-inner {
	background-color: $backgroundContent;
	border-radius: 12px;
	padding: 40px 50px 50px 50px;
}

.header {
	text-align: center;
	padding: 0 0 50px 0;

	.header-image {
		/* This is needed to center the logo in Outlook. */
		margin: 0 auto 0 auto;
	}
}

.footer {
	padding-top: 10px;
	font-size: 14px;
	line-height: 24px;
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
	line-height: 24px;
}

/* Tables */
.content {
	.field-name {
		padding-top: 10px;
		padding-bottom: $marginBottom;

		&:not(.field-value) {
			font-size: 18px;
			line-height: 20.7px;
		}

		/* Repeater & Layout */
		&.field-repeater-name,
		&.field-layout-name {
			font-size: 22px;
			padding-top: 30px;
			padding-bottom: 30px;
		}
	}

	.field-value {
		padding-bottom: 30px;
	}

	.field-name.field-value {
		line-height: 24px;
	}
}
