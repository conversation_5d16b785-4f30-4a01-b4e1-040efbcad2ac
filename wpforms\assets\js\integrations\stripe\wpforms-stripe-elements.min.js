var WPFormsStripeElements=window.WPFormsStripeElements||((n,t,o)=>{let l={stripe:null,lockedPageToSwitch:0,init:function(){l.stripe=Stripe(wpforms_stripe.publishable_key,{locale:wpforms_stripe.data.element_locale}),o(n).on("wpformsReady",function(){o(".wpforms-stripe form").filter((e,t)=>"number"==typeof o(t).data("formid")).each(l.setupStripeForm)}),o(n).on("wpformsBeforePageChange",l.pageChange)},setupStripeForm:function(){var e=o(this);l.updateFormSubmitHandler(e),e.on("wpformsAjaxSubmitActionRequired",l.handleCardActionCallback),l.updateCardElementStylesModern(e)},setupCardElement:function(r,s){let i=r.find(".wpforms-stripe-credit-card-hidden-input");if(i&&0!==i.length){var t=i.data("stripe-element");if(!t){let e=wpforms_stripe.data.element_style;o.isEmptyObject(e)&&(e=l.getElementStyleDefault(i));var a={classes:wpforms_stripe.data.element_classes,hidePostalCode:!0,style:e};(t=l.stripe.elements().create("card",a)).mount(r.find(".wpforms-field-stripe-credit-card-cardnumber").get(0)),t.on("change",function(t){if(t.error){let e=t.error.message;"incomplete_number"!==t.error.code&&"invalid_number"!==t.error.code||(e=wpforms_settings.val_creditcard),l.displayStripeError(r,e)}else s.hideThese(s.errorsFor(i.get(0)))}),t.on("focus",function(){o(n).trigger("wpformsStripePaymentElementFocus",[r])}),i.data("stripe-element",t)}return t}},getElementStyleDefault:function(t){if(t&&0!==t.length){var r=t.css("color"),s=t.css("font-size"),s={base:{fontSize:s,color:r,"::placeholder":{color:r,fontSize:s}},invalid:{color:r}};let e=t.css("font-family");r=/[“”<>!@$%^&*=~`|{}[\]]/;return!r.test(e)&&-1===e.indexOf("MS Shell Dlg")||(e=o("p").css("font-family")),r.test(e)||(s.base.fontFamily=e,s.base["::placeholder"].fontFamily=e),s}},updateFormSubmitHandler:function(a){let e=a.validate(),n=e.settings.submitHandler,o=l.setupCardElement(a,e),d=a.find(".wpforms-field-stripe-credit-card-cardnumber");e.settings.submitHandler=function(){let e=a.validate().form(),t=d.hasClass(wpforms_stripe.data.element_classes.empty),r=d.data("required"),s=d.closest(".wpforms-field-stripe-credit-card").hasClass("wpforms-conditional-hide"),i=s?!1:r||!t&&!r;if(e&&i)a.find(".wpforms-submit").prop("disabled",!0),l.createPaymentMethod(a,o,r,n);else{if(e)return a.find(".wpforms-submit").prop("disabled",!1),n(a);a.find(".wpforms-submit").prop("disabled",!1),a.validate().cancelSubmit=!0}}},createPaymentMethod:function(t,e,r,s){l.stripe.createPaymentMethod("card",e,{billing_details:{name:t.find(".wpforms-field-stripe-credit-card-cardname").val()}}).then(function(e){e.error&&r?(t.find(".wpforms-submit").prop("disabled",!1),l.displayStripeError(t,e.error.message),t.validate().cancelSubmit=!0):(e.error||(t.find(".wpforms-stripe-payment-method-id").remove(),e.paymentMethod&&t.append('<input type="hidden" class="wpforms-stripe-payment-method-id" name="wpforms[payment_method_id]" value="'+e.paymentMethod.id+'">')),s(t))})},handleCardActionCallback:function(e,t){let r=o(this);t.success&&t.data.action_required&&l.stripe.handleCardPayment(t.data.payment_intent_client_secret,{payment_method:t.data.payment_method_id}).then(function(e){l.handleCardPaymentCallback(r,e)})},handleCardPaymentCallback(e,t){t.error?(wpforms.restoreSubmitButton(e,e.closest(".wpforms-container")),e.find(".wpforms-field-stripe-credit-card-cardnumber").addClass(wpforms_stripe.data.element_classes.invalid),l.displayStripeError(e,t.error.message)):t.paymentIntent&&"succeeded"===t.paymentIntent.status?(e.find(".wpforms-stripe-payment-method-id").remove(),e.find(".wpforms-stripe-payment-intent-id").remove(),e.append('<input type="hidden" class="wpforms-stripe-payment-intent-id" name="wpforms[payment_intent_id]" value="'+t.paymentIntent.id+'">'),wpforms.formSubmitAjax(e)):wpforms.restoreSubmitButton(e,e.closest(".wpforms-container"))},displayStripeError:function(e,t){var r=e.find(".wpforms-stripe-credit-card-hidden-input").attr("name"),s=e.find(".wpforms-field-stripe-credit-card-cardnumber"),i={};i[r]=t,wpforms.displayFormAjaxFieldErrors(e,i),!s.is(":visible")&&0<e.find(".wpforms-page-indicator-steps").length&&wpforms.setCurrentPage(e,{}),wpforms.scrollToError(s)},formAjaxUnblock(e){console.warn('WARNING! Function "WPFormsStripeElements.formAjaxUnblock()" has been deprecated, please use the new "wpforms.restoreSubmitButton()" function instead!'),wpforms.restoreSubmitButton(e,e.closest(".wpforms-container"))},pageChange:function(e,t,r,s){var i=r.find(".wpforms-field-stripe-credit-card-cardnumber"),a=i.hasClass(wpforms_stripe.data.element_classes.complete),n=i.hasClass(wpforms_stripe.data.element_classes.empty),o=i.hasClass(wpforms_stripe.data.element_classes.invalid);!i.is(":visible")||!i.data("required")&&n||l.lockedPageToSwitch&&l.lockedPageToSwitch!==t||"prev"===s||(a?i.find(".wpforms-error").remove():(l.lockedPageToSwitch=t,e.preventDefault(),o||l.displayStripeError(r,wpforms_stripe.i18n.empty_details)))},getCssPropertyValue(e,t){try{return e.css(t)}catch(e){return""}},updateCardElementStylesModern(e){t.WPForms&&WPForms.FrontendModern&&o.isEmptyObject(wpforms_stripe.data.element_style)&&e&&0!==e.length&&e.find(".wpforms-stripe-credit-card-hidden-input").each(function(){var e=o(this),t=e.data("stripe-element"),e={fontSize:l.getCssPropertyValue(e,"font-size"),colorText:l.getCssPropertyValue(e,"color")};t&&(e={base:{color:e.colorText,fontSize:e.fontSize,"::placeholder":{color:WPForms.FrontendModern.getColorWithOpacity(e.colorText,"0.5"),fontSize:e.fontSize}},invalid:{color:e.colorText}},t.update({style:e}))})}};return l})(document,window,jQuery);WPFormsStripeElements.init();