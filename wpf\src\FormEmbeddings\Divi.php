<?php

namespace WPForms\DevTools\FormEmbeddings;

/**
 * Class Divi.
 *
 * Embed Form into Divi built page.
 *
 * @since 0.27
 */
class Divi extends Embeddings {

	/**
	 * Divi minimum version.
	 *
	 * @since 0.27
	 *
	 * @var string
	 */
	private static $min_version = '4.23.0';

	/**
	 * Get post content.
	 *
	 * @since 0.27
	 *
	 * @param int $form_id Form ID.
	 *
	 * @return string
	 */
	protected function get_post_content( int $form_id ): string {

		$format =
			'[et_pb_section fb_built="1" theme_builder_area="post_content" _builder_version="%1$s" _module_preset="default"]' .
			'[et_pb_row _builder_version="%1$s" _module_preset="default" theme_builder_area="post_content"]' .
			'[et_pb_column _builder_version="%1$s" _module_preset="default" type="4_4" theme_builder_area="post_content"]' .
			'[wpforms_selector _builder_version="%1$s" _module_preset="default" theme_builder_area="post_content" form_id="%2$d" hover_enabled="0" sticky_enabled="0"]' .
			'[/wpforms_selector]' .
			'[/et_pb_column]' .
			'[/et_pb_row]' .
			'[/et_pb_section]';

		return sprintf(
			$format,
			$this->get_version(),
			$form_id
		);
	}

	/**
	 * Get post meta.
	 *
	 * @since 0.27
	 *
	 * @param int $form_id Form ID.
	 *
	 * @return array
	 */
	protected function get_post_meta( int $form_id ): array {

		return [
			'_et_pb_use_builder'               => 'on',
			'_et_gb_content_width'             => '',
			'_et_pb_built_for_post_type'       => 'page',
			'_et_pb_ab_subjects'               => '',
			'_et_pb_enable_shortcode_tracking' => '',
			'_et_pb_ab_current_shortcode'      => sprintf( '[et_pb_split_track id="%d" /]', $form_id ),
			'_et_pb_custom_css'                => '',
			'_et_pb_gutter_width'              => '3',
			'_et_builder_version'              => sprintf( 'VB|Divi|%s', $this->get_version() ),
			'_et_pb_show_page_creation'        => 'off',
		];
	}

	/**
	 * Get embed name.
	 *
	 * @since 0.27
	 *
	 * @return string
	 */
	protected function get_name(): string {

		return 'Divi';
	}

	/**
	 * Get edit link.
	 *
	 * @since 0.27
	 *
	 * @param int $post_id Post ID.
	 *
	 * @return string
	 */
	public function get_edit_link( int $post_id ): string {

		$permalink = get_permalink( $post_id );

		return add_query_arg(
			[
				'et_fb'     => '1',
				'PageSpeed' => 'off',
			],
			$permalink
		);
	}

	/**
	 * Get Divi version.
	 *
	 * @since 0.27
	 *
	 * @return string
	 */
	private function get_version(): string {

		// phpcs:disable WordPress.NamingConventions.ValidVariableName.VariableNotSnakeCase
		global $ET_CORE_VERSION;

		return $ET_CORE_VERSION ?? self::$min_version;
		// phpcs:enable WordPress.NamingConventions.ValidVariableName.VariableNotSnakeCase
	}
}
