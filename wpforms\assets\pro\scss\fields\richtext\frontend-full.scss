// Rich Text field styles.
//
// Frontend Full.
//
// @since 1.7.0

@import "vars";
@import "common";

div.wpforms-container-full .wpforms-form div.wpforms-field-richtext {

	label.wpforms-field-label {
		margin-bottom: -25px;
		margin-top: 8px;
		max-width: 70%;
	}

	&.wpforms-has-error {

		.mce-toolbar-grp {
			border-top-color: $border_error_color;
			border-left-color: $border_error_color;
			border-right-color: $border_error_color;
		}

		.mce-tinymce > .mce-container-body {
			border-color: $border_error_color;
		}
	}

	.mce-edit-area {
		overflow: hidden;
	}

	.mce-tinymce > .mce-container-body {
		visibility: inherit;
		border: 1px solid $border_color;
		border-bottom-left-radius: 2px;
		border-top-left-radius: 2px;
		border-bottom-right-radius: 2px;

		& * {
			visibility: inherit;
		}
	}

	.wp-media-buttons {
		display: none;
	}

	.wp-editor-wrap {
		box-shadow: none;
	}

	.mce-container::before {
		position: inherit;
	}

	.mce-toolbar-grp {
		color: $text;
		border-bottom: 1px solid $bd_color;
		background: $panel_bg;
		position: relative;
		border-top-left-radius: 2px;

		& > div {
			padding: 3px;
		}

		.mce-caret {
			right: 8px;
			border-right: 4px solid transparent;
			border-left: 4px solid transparent;
			border-top: 6px solid $text;
			border-bottom: 0;
			margin: 6px 0 6px 0;
		}
	}

	.mce-btn.mce-listbox {
		background: $white;
		border: 1px solid $bd_color;
		margin: 2px;
		padding: 0 5px;

		&:hover {
			border-color: $bd_color_hover;
		}

		button {
			padding: 2px 3px;

			span {
				overflow-x: hidden;
				text-overflow: ellipsis;
				width: 82px;
			}
		}
	}

	.mce-toolbar .mce-btn-group .mce-widget.mce-btn:not(.mce-btn-has-text) button,
	.mce-toolbar .mce-btn-group .mce-widget.mce-btn:not(.mce-btn-has-text):hover button {
		width: 23px;
		font-size: 18px;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 24px;
		background-color: transparent;
		color: inherit;
	}

	.mce-toolbar .mce-btn-group .mce-widget.mce-btn {

		&.mce-btn-disabled {
			opacity: 0.5;
			pointer-events: none;
		}

		&.mce-colorbutton {
			display: inline-flex;

			button {
				position: relative;
			}

			.mce-preview {
				position: absolute;
				bottom: 2px;
				left: 50%;
				transform: translateX(-50%);
			}
		}

		.mce-open {
			border-left: 1px solid transparent;
		}

		&:hover {
			.mce-open {
				border-color: inherit;
			}
		}

		&.mce-btn-has-text button {
			font-size: 13px;
			line-height: 1.538;
			padding-left: 6px;
			background-color: $white;
			height: 24px;
			display: block;
			color: inherit;
		}
	}

	iframe {
		display: block;
		font-size: initial;
		margin: auto;
		padding: initial;
		border: inherit;
		background: $white;
	}

	.mce-statusbar {
		border-top: 1px solid $bd_color;
		background: $white;
		border-bottom-left-radius: 2px;
		border-bottom-right-radius: 2px;
		color: rgba( 0, 0, 0, 0.7 );

		.mce-container-body {
			position: relative;
			font-size: 11px;

			.mce-resizehandle {
				position: absolute;
			}
		}

		.mce-path.mce-flow-layout-item.mce-first {
			padding: 2px 10px;
			margin: 2px 0 2px 2px;

			div {
				font-size: 12px;
			}
		}
	}
}

.mce-container {
	.mce-menu-item:focus,
	.mce-menu-item:hover,
	.mce-menu-item.mce-active.mce-menu-item-normal {
		background-color: $link_hover;
		color: $white;

		.mce-text,
		.mce-ico {
			color: inherit;
		}
	}
}

#wpforms-form-page-page div.wpforms-field-richtext .mce-menubtn button {
	font-size: 13px;
}

@media screen and ( max-width: map-get( $breakpoints, 'tablet' ) ) {

	.media-frame-content .attachments-browser.has-load-more .attachments-wrapper {
		top: 82px;
	}

	.mce-window {
		width: auto !important;
		top: 0 !important;
		left: 0 !important;
		right: 0 !important;
		bottom: 0 !important;
	}

	.mce-foot {
		& > .mce-container-body {
			padding: 10px !important;
		}

		.mce-btn.mce-primary {
			margin-inline-end: 10px !important;
		}
	}

	.mce-panel {
		max-width: 100% !important;
	}

	.mce-container {
		max-width: 100% !important;
		height: auto !important;
	}

	.mce-container-body {
		max-width: 100% !important;
		height: auto !important;
	}

	.mce-form {
		padding: 10px !important;
	}

	.mce-tabs {
		max-width: 100% !important;
	}

	.mce-formitem {
		margin: 10px 0 !important;
	}

	.mce-abs-layout-item {
		position: static !important;
		width: auto !important;
	}

	.mce-abs-layout-item.mce-label {
		display: block !important;
	}

	.mce-abs-layout-item.mce-textbox {
		box-sizing: border-box !important;
		display: block !important;
		width: 100% !important;
	}

	.mce-abs-layout-item.mce-combobox {
		display: flex !important;
	}

	.mce-abs-layout-item.mce-combobox > .mce-textbox {
		flex: 1 1 auto;
		height: 29px !important;
	}
}
