// Form Builder styles.
//
// Choices list styles.
//
// @since 1.9.6.1

@import '../partials/common';

$item_padding: 10px;
$control_size: 18px;
$start_gap: 10px;

.wpforms-choices-list {

	border: $border_std;
	border-radius: $border_radius_s;
	max-height: 177px;
	overflow-y: auto;

	.checkbox-item {
		display: flex;
		align-items: center;

		&:first-child {
			border-top-left-radius: $border_radius_s;
			border-top-right-radius: $border_radius_s;
		}

		&:last-child {
			border-bottom-left-radius: $border_radius_s;
			border-bottom-right-radius: $border_radius_s;
		}

		&:nth-child(odd) {
			background-color: $color_light_background;
		}

		&:hover {
			background-color: $color_light_background_hover;
		}

		// Hide the default checkbox.
		input[type="checkbox"] {
			display: none;

			// Focus style.
			&:focus + label::before {
				outline: 2px solid $color_light_blue; // Tailwind blue-300.
				outline-offset: 1px;
			}

			// Style when checkbox is checked.
			&:checked + label::before {
				background-color: $color_blue;
				border-color: $color_blue;
				content: '✓';
				color: $color_white;
				font-size: $font_size_xs;
				font-weight: bold;
				text-align: center;
				line-height: 16px;
			}

			// Style when checkbox is indeterminate.
			&:indeterminate + label::before {
				background-color: $color_bright_blue;
				border-color: $color_bright_blue;
				content: '-';
				color: $color_white;
				font-size: $font_size_s;
				font-weight: bold;
				text-align: center;
				line-height: 14px;
			}
		}

		// Style the label to act as the container.
		label {
			position: relative;
			padding: $item_padding 0 $item_padding 0;
			padding-inline-start: calc(#{$control_size} + 10px + #{$start_gap});
			padding-inline-end: $item_padding;
			cursor: pointer;
			display: inline-block;
			line-height: 1.25rem;
			color: $color_black;
			flex-grow: 1;
			margin: unset;

			// Style the custom checkbox appearance.
			&::before {
				content: "";
				position: absolute;
				inset-inline-start: $start_gap;
				width: $control_size;
				height: $control_size;
				border: $border_std;
				border-radius: $border_radius_s;
				background-color: $color_white;
				transition: background-color 0.2s ease, border-color 0.2s ease;
			}
		}
	}
}
