/* eslint-disable */
jQuery( document ).ready( function( $ ) {

	// Initialize main accordion for runs
	if ( $( '#cbp-ntaccordion' ).length > 0 ) {
		$( '#cbp-ntaccordion' ).cbpNTAccordion();
	}

	// Handle clicks for PHP version sub-accordions
	$( document ).on( 'click', '.cbp-ntsubaccordion .cbp-ntsubtrigger-php', function( e ) {
		e.preventDefault();
		e.stopPropagation();

		var $listItem = $( this ).parent();
		var $content = $listItem.find( '> .cbp-ntcontent' );

		if ( $listItem.hasClass( 'cbp-ntopen' ) ) {
			$listItem.removeClass( 'cbp-ntopen' );
			$content.slideUp( 200 );
		} else {
			var $siblings = $listItem.siblings( '.cbp-ntopen' );
			$siblings.removeClass( 'cbp-ntopen' );
			$siblings.find( '> .cbp-ntcontent' ).slideUp( 200 );

			$listItem.addClass( 'cbp-ntopen' );
			$content.slideDown( 200 );
		}
	} );

	// Handle clicks for test failure sub-sub-accordions
	$( document ).on( 'click', '.cbp-ntsubsubaccordion .cbp-ntsubtrigger-test', function( e ) {
		e.preventDefault();
		e.stopPropagation();

		var $listItem = $( this ).parent();
		var $content = $listItem.find( '> .cbp-ntcontent' );

		if ( $listItem.hasClass( 'cbp-ntopen' ) ) {
			$listItem.removeClass( 'cbp-ntopen' );
			$content.slideUp( 200 );
		} else {
			var $siblings = $listItem.siblings( '.cbp-ntopen' );
			$siblings.removeClass( 'cbp-ntopen' );
			$siblings.find( '> .cbp-ntcontent' ).slideUp( 200 );

			$listItem.addClass( 'cbp-ntopen' );
			$content.slideDown( 200 );
		}
	} );

	// Hide all nested content initially
	$( '.cbp-ntsubaccordion .cbp-ntcontent' ).hide();
	$( '.cbp-ntsubsubaccordion .cbp-ntcontent' ).hide();
} );
