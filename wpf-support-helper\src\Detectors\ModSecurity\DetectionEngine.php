<?php
/**
 * ModSecurity Main Class.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper\Detectors\ModSecurity;

use Exception;
use WPF<PERSON>\SupportHelper\ActivationInterface;
use WPF<PERSON>\SupportHelper\Cache\CacheInterface;
use WPForms\SupportHelper\Detectors\Base\DetectorInterface; // phpcs:ignore WPForms.PHP.UseStatement.UnusedUseStatement
use WPForms\SupportHelper\Logger\LoggerInterface;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Main ModSecurity detection coordinator.
 *
 * @since {VERSION}
 */
class DetectionEngine implements ActivationInterface {

	/**
	 * ModSecurity detection transient key.
	 *
	 * @since {VERSION}
	 */
	public const CACHE_KEY = 'modsecurity_detection';

	/**
	 * Cache expiration time (1 hour).
	 *
	 * @since {VERSION}
	 *
	 * @var int
	 */
	private $cache_expiration = 3600;

	/**
	 * Detection methods and their instances.
	 *
	 * @since {VERSION}
	 *
	 * @var DetectorInterface[]
	 */
	private $detectors = [];

	/**
	 * Cache instance.
	 *
	 * @since {VERSION}
	 *
	 * @var CacheInterface
	 */
	private $cache;

	/**
	 * Logger instance.
	 *
	 * @since {VERSION}
	 *
	 * @var LoggerInterface
	 */
	private $logger;


	/**
	 * Constructor.
	 *
	 * @since {VERSION}
	 *
	 * @param CacheInterface  $cache  Cache instance.
	 * @param LoggerInterface $logger Logger instance.
	 */
	public function __construct( CacheInterface $cache, LoggerInterface $logger ) {

		$this->cache  = $cache;
		$this->logger = $logger;

		$this->register_detectors();
		$this->init_detectors();
	}

	/**
	 * Register default detector classes.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	private function register_detectors(): void {

		$this->detectors = [
			'server_variables'      => new ServerVariablesDetector( $this->logger ),
			'apache_modules'        => new ApacheModulesDetector( $this->logger ),
			'http_headers'          => new HttpHeadersDetector( $this->logger ),
			'response_patterns'     => new ResponsePatternsDetector( $this->logger ),
			'error_logs'            => new ErrorLogsDetector( $this->logger ),
			'configuration_files'   => new ConfigurationFilesDetector( $this->logger ),
			'environment_detection' => new EnvironmentDetector( $this->logger ),
		];
	}

	/**
	 * Initialize detector instances.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	private function init_detectors(): void {

		try {
			// Sort detectors by priority (lower numbers = higher priority).
			uasort(
				$this->detectors,
				function ( $a, $b ) {
					return $a->get_priority() <=> $b->get_priority();
				}
			);

			$this->logger->info( 'Initialized ' . count( $this->detectors ) . ' detectors' );
		} catch ( Exception $e ) {
			$this->logger->error( 'Failed to initialize detectors: ' . $e->getMessage() );
			$this->detectors = [];
		}
	}

	/**
	 * Run all detection methods and return results.
	 *
	 * @since {VERSION}
	 *
	 * @return array Detection results.
	 * @throws Exception When detector instantiation or execution fails.
	 */
	public function detect(): array {

		// Check cache first.
		$cached_results = $this->cache->get( self::CACHE_KEY );

		if ( $cached_results !== null ) {
			$this->logger->debug( 'Returning cached detection results' );

			return $cached_results;
		}

		$results = [
			'detected'         => false,
			'detection_method' => [],
			'failed_methods'   => [],
			'skipped_methods'  => [],
			'timestamp'        => current_time( 'mysql' ),
		];

		$this->logger->info( 'Starting ModSecurity detection with ' . count( $this->detectors ) . ' detectors' );

		// Run each detection method in priority order.
		foreach ( $this->detectors as $method_key => $detector ) {
			// Check if detector is supported in current environment.
			if ( ! $detector->supports() ) {
				$this->logger->debug( 'Skipping detector: ' . $detector->get_name() . ' (not supported)' );

				$skip_data = [
					'detector_name' => $detector->get_name(),
					'key'           => $method_key,
					'reason'        => __( 'Detector not supported in current environment', 'wpf-support-helper' ),
					'requirements'  => $detector->get_requirements(),
				];

				$results['skipped_methods'][] = $skip_data;

				continue;
			}

			try {
				$this->logger->debug( 'Running detector: ' . $detector->get_name() );
				$method_result = $detector->detect();

				if ( $method_result['detected'] ) {
					$this->logger->info( 'ModSecurity detected by: ' . $detector->get_name() );
					$results['detected'] = true;

					$success_data = [
						'name'     => $detector->get_name(),
						'key'      => $method_key,
						'priority' => $detector->get_priority(),
						'result'   => $method_result,
					];

					$results['detection_method'][] = $success_data;
				} else {
					$this->logger->debug( 'No detection by: ' . $detector->get_name() . ' - ' . $method_result['reason'] );

					$failure_data = [
						'name'     => $detector->get_name(),
						'key'      => $method_key,
						'priority' => $detector->get_priority(),
						'reason'   => $method_result['reason'],
					];

					$results['failed_methods'][] = $failure_data;
				}

				// Add any additional info.
				if ( ! empty( $method_result['additional_info'] ) ) {
					$results['additional_info'][ $method_key ] = $method_result['additional_info'];
				}
			} catch ( Exception $e ) {
				$this->logger->error( 'Detector failed: ' . $detector->get_name() . ' - ' . $e->getMessage() );
				$results['failed_methods'][] = [
					'name'     => $detector->get_name(),
					'key'      => $method_key,
					'priority' => $detector->get_priority(),
					/* translators: %s - Error message. */
					'reason'   => sprintf( __( 'Error: %s', 'wpf-support-helper' ), $e->getMessage() ),
				];
			}
		}

		// Cache results.
		if ( $this->cache->set( self::CACHE_KEY, $results, $this->cache_expiration ) ) {
			$this->logger->debug( 'Detection results cached successfully' );
		} else {
			$this->logger->warning( 'Failed to cache detection results' );
		}

		$detection_status = $results['detected'] ? 'detected' : 'not detected';

		$this->logger->info( 'ModSecurity detection completed: ' . $detection_status );

		return $results;
	}

	/**
	 * Clear cached detection results.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	public function clear_cache(): void {

		if ( $this->cache->delete( self::CACHE_KEY ) ) {
			$this->logger->info( 'Detection cache cleared successfully' );
		} else {
			$this->logger->warning( 'Failed to clear detection cache' );
		}
	}

	/**
	 * Handle plugin activation.
	 *
	 * Clears existing cached detection results to ensure fresh detection on activation.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	public function activate_plugin(): void {

		// Clear existing cached detection results.
		$this->clear_cache();

		$this->logger->info( 'DetectionEngine activated - cache cleared' );
	}

	/**
	 * Handle plugin deactivation.
	 *
	 * Clears all DetectionEngine related cache and transients.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	public function deactivate_plugin(): void {

		// Clear all DetectionEngine related cache and transients.
		$this->clear_cache();

		$this->logger->info( 'DetectionEngine deactivated - cache cleared' );
	}
}
