<?php

namespace WPForms\DevTools;

/**
 * Assets service.
 *
 * @since 0.5
 */
class Assets {

	/**
	 * Add scripts and style only for dev tools page.
	 *
	 * @since 0.5
	 * @since 0.5.1 Added using method for retrieve a version.
	 */
	public static function dev_tools_scripts() {

		$version = self::get_version();

		wp_enqueue_script(
			'wpf-admin',
			WPFORMS_DEV_TOOLS_URL . '/assets/js/admin.js',
			[ 'jquery', 'wp-util' ],
			$version,
			true
		);

		wp_enqueue_style(
			'wpf-admin',
			WPFORMS_DEV_TOOLS_URL . '/assets/css/admin.css',
			[],
			$version
		);

		// Enqueue Modals Generator tab assets.
		if (
			// phpcs:disable WordPress.Security.NonceVerification.Recommended
			! empty( $_REQUEST['page'] ) && $_REQUEST['page'] === DevTools::PAGE_SLUG &&
			! empty( $_REQUEST['view'] ) && $_REQUEST['view'] === 'modals'
			// phpcs:enable WordPress.Security.NonceVerification.Recommended
		) {

			wp_enqueue_style(
				'wpf-modals',
				WPFORMS_DEV_TOOLS_URL . '/assets/css/modals.css',
				[],
				$version
			);

			wp_enqueue_script(
				'wpf-modals',
				WPFORMS_DEV_TOOLS_URL . '/assets/js/modals.js',
				[ 'jquery' ],
				$version,
				true
			);
		}

		// Enqueue Hooks tab assets.
		if (
			// phpcs:disable WordPress.Security.NonceVerification.Recommended
			! empty( $_REQUEST['page'] ) && $_REQUEST['page'] === DevTools::PAGE_SLUG &&
			! empty( $_REQUEST['view'] ) && $_REQUEST['view'] === 'hooks'
			// phpcs:enable WordPress.Security.NonceVerification.Recommended
		) {

			wp_enqueue_style(
				'wpf-hooks',
				WPFORMS_DEV_TOOLS_URL . '/assets/css/hooks.css',
				[],
				$version
			);

			wp_enqueue_script(
				'listjs',
				WPFORMS_DEV_TOOLS_URL . '/assets/js/vendor/list.min.js',
				[],
				'1.5.0'
			);

			wp_enqueue_script(
				'wpforms-admin-utils',
				WPFORMS_PLUGIN_URL . 'assets/js/admin-utils.js',
				[ 'jquery' ],
				WPFORMS_VERSION,
				true
			);

			wp_enqueue_script(
				'wpf-hooks',
				WPFORMS_DEV_TOOLS_URL . '/assets/js/hooks.js',
				[ 'jquery', 'listjs' ],
				$version,
				true
			);

			wp_localize_script(
				'wpf-hooks',
				'wpf_hooks',
				[]
			);
		}

		// Enqueue Requirements tab assets.
		if (
			// phpcs:disable WordPress.Security.NonceVerification.Recommended
			! empty( $_REQUEST['page'] ) && $_REQUEST['page'] === DevTools::PAGE_SLUG &&
			! empty( $_REQUEST['view'] ) && $_REQUEST['view'] === 'requirements'
			// phpcs:enable WordPress.Security.NonceVerification.Recommended
		) {

			wp_enqueue_style(
				'wpf-requirements',
				WPFORMS_DEV_TOOLS_URL . '/assets/css/requirements.css',
				[],
				$version
			);

			wp_enqueue_script(
				'listjs',
				WPFORMS_DEV_TOOLS_URL . '/assets/js/vendor/list.min.js',
				[],
				'1.5.0'
			);

			wp_enqueue_script(
				'wpf-requirements',
				WPFORMS_DEV_TOOLS_URL . '/assets/js/requirements.js',
				[ 'jquery', 'listjs' ],
				$version,
				true
			);
		}
	}

	/**
	 * Add inline script early to catch and report any JavaScript errors.
	 *
	 * New Zapier Widget generates an odd error on init: `ResizeObserver loop completed with undelivered notifications.`
	 * This error is a "valid" behavior and does not break anything for the customers. However, it may interfere
	 * with our E2E tests if caught by our error listener. So for now we have to turn it off on Zapier tab
	 * in the Builder. This turns error monitoring off on this tab completely though.
	 *
	 * @since 0.15
	 */
	public static function dev_tools_inline_scripts() {

		?>
		<style>
		#wp-admin-bar-wpforms-menu.wpforms-has-console-error {
			background-color: #d63638;
		}

		#wp-admin-bar-wpforms-menu.wpforms-has-console-error svg {
			display: inline-block;
			width: 18px;
			height: 18px;
			margin: 7px 6px 0 0;
			line-height: 1.6;
			vertical-align: top;
		}

		#wp-admin-bar-wpforms-menu.wpforms-has-console-error .wpforms-console-error-notice {
			background-color: #d63638;
		}

		#wp-admin-bar-wpforms-menu.wpforms-has-console-error .wpforms-console-error-notice a.ab-item {
			color: #ffffff !important;
			cursor: default;
		}
		</style>

		<script>
		window.addEventListener( 'error', function( event ) {

			const node = document.getElementById( 'wp-admin-bar-wpforms-menu' );
			const list = document.getElementById( 'wp-admin-bar-wpforms-menu-default' );

			if ( node.classList.contains( 'wpforms-has-console-error' ) ) {
				return;
			}

			const zapierTab = document.getElementsByClassName( 'wpforms-panel-sidebar-section-zapier' );

			if ( zapierTab.length > 0 && zapierTab[0].classList.contains( 'active' ) ) {
				return;
			}

			node.classList.add( 'wpforms-has-console-error' );

			node.firstChild.insertAdjacentHTML(
				'afterbegin',
				`<svg viewBox="0 0 512 512" fill="currentColor">
					<path d="M256 0C114.6 0 0 114.6 0 256s114.6 256 256 256s256-114.6 256-256S397.4 0 256 0zM232 152C232 138.8 242.8 128 256 128s24 10.75 24 24v128c0 13.25-10.75 24-24 24S232 293.3 232 280V152zM256 400c-17.36 0-31.44-14.08-31.44-31.44c0-17.36 14.07-31.44 31.44-31.44s31.44 14.08 31.44 31.44C287.4 385.9 273.4 400 256 400z"/>
				</svg>`
			);

			list.insertAdjacentHTML(
				'beforeend',
				`<li class="wpforms-console-error-notice">
					<a href="#" class="ab-item">There are errors in the console</a>
				</li>`
			);
		}, { once: true } );
		</script>
		<?php
	}

	/**
	 * Add scripts and styles for admin panel.
	 *
	 * @since 0.5
	 * @since 0.5.1 Added using method for retrieve a version.
	 */
	public static function scripts_styles() {

		if ( ! is_user_logged_in() ) {
			return;
		}

		$version = self::get_version();

		wp_enqueue_style(
			'wpf-bar-inline',
			WPFORMS_DEV_TOOLS_URL . '/assets/css/bar-inline.css',
			[],
			$version
		);

		wp_enqueue_style(
			'wpf-bulk-embed',
			WPFORMS_DEV_TOOLS_URL . '/assets/css/bulk-embed.css',
			[],
			$version
		);

		wp_enqueue_script(
			'wpf-utils-field-sizes',
			WPFORMS_DEV_TOOLS_URL . '/assets/js/utils-field-sizes.js',
			[ 'jquery' ],
			$version
		);
	}

	/**
	 * Add scripts and styles for admin panel.
	 *
	 * @since 0.5
	 * @since 0.5.1 Added using method for retrieve a version.
	 */
	public static function scripts_styles_builder() {

		if ( ! is_user_logged_in() ) {
			return;
		}

		$version = self::get_version();

		wp_enqueue_script(
			'wpf-builder',
			WPFORMS_DEV_TOOLS_URL . '/assets/js/builder.js',
			[ 'wpforms-builder' ],
			$version,
			true
		);

		wp_localize_script(
			'wpf-builder',
			'wpfBuilder',
			[
				'hideContextMenu' => Utils::get_option( 'hide-context-menu-in-builder' ),
				'bulkEmbeds'      => FormEmbeddings\Utils::get_embed_list(),
				'ajaxUrl'         => admin_url( 'admin-ajax.php' ),
				'nonce'           => wp_create_nonce( 'wpf-utils' ),
			]
		);
	}

	/**
	 * Prevent assets caching.
	 *
	 * @since 0.5.1
	 */
	public static function prevent_assets_cache() {

		if (
			// WPForms debugging is disabled.
			! defined( 'WPFORMS_DEBUG' ) || ! WPFORMS_DEBUG ||
			// Menu item allows caching.
			// phpcs:ignore WPForms.Comments.PHPDocHooks.RequiredHookDocumentation, WPForms.PHP.ValidateHooks.InvalidHookName
			apply_filters( 'wpforms_cache_assets', false )
		) {
			return;
		}

		if ( is_admin() ) {

			add_action( 'admin_enqueue_scripts', [ __CLASS__, 'filter_styles' ], 100000 );
			add_action( 'admin_enqueue_scripts', [ __CLASS__, 'filter_scripts' ], 100000 );
			add_action( 'wpforms_builder_enqueues', [ __CLASS__, 'filter_scripts' ], 100000 );
			add_action( 'wpforms_builder_enqueues', [ __CLASS__, 'filter_styles' ], 100000 );

		} else {

			// Filter Styles - HEAD.
			add_action( 'wp_print_styles', [ __CLASS__, 'filter_styles' ], 100000 );

			// Filter Scripts - HEAD.
			add_action( 'wp_print_scripts', [ __CLASS__, 'filter_scripts' ], 100000 );

			// Filter Scripts & Styles - FOOTER.
			add_action( 'wp_print_footer_scripts', [ __CLASS__, 'filter_styles' ], 1 );
			add_action( 'wp_print_footer_scripts', [ __CLASS__, 'filter_scripts' ], 1 );
		}
	}

	/**
	 * Prepare `$wp_styles` global variable for changing WPForms assets version.
	 *
	 * @since 0.5.1
	 */
	public static function filter_styles() {

		$styles = wp_styles();

		self::version_process( $styles );
	}

	/**
	 * Prepare `$wp_scripts` global variable for changing WPForms assets version.
	 *
	 * @since 0.5.1
	 */
	public static function filter_scripts() {

		$scripts = wp_scripts();

		self::version_process( $scripts );
	}

	/**
	 * Replace version for a WPForms css/js registered files.
	 *
	 * @since 0.5.1
	 *
	 * @param object $bundle WP_Scripts|WP_Styles instance.
	 */
	public static function version_process( $bundle ) {

		// Nothing to do if passed bundle are empty.
		if ( $bundle === null || empty( $bundle->registered ) ) {
			return;
		}

		$wpforms_url = plugins_url( 'wpforms' );
		$new_version = time();

		foreach ( $bundle->registered as $handle => $asset ) {

			// Skip if it's not a WPForms plugin/addons asset.
			if ( strpos( (string) $asset->src, $wpforms_url ) === false ) {
				continue;
			}

			// Change an asset version.
			$bundle->registered[ $handle ]->ver = $new_version;
		}
	}

	/**
	 * Retrieve a plugin version - constant or dynamic value.
	 *
	 * @since 0.5.1
	 */
	public static function get_version() {

		return defined( 'WPFORMS_DEBUG' ) && WPFORMS_DEBUG ? time() : WPFORMS_DEV_TOOLS_VERSION;
	}
}
