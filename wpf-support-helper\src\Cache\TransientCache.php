<?php
/**
 * WordPress Transients Cache Implementation.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper\Cache;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * WordPress transients-based cache implementation.
 *
 * @since {VERSION}
 */
class TransientCache implements CacheInterface {


	/**
	 * Default expiration time.
	 *
	 * @since {VERSION}
	 *
	 * @var int
	 */
	private $default_expiration;

	/**
	 * Constructor.
	 *
	 * @since {VERSION}
	 *
	 * @param int $default_expiration Default expiration time in seconds.
	 */
	public function __construct( int $default_expiration = 3600 ) {

		$this->default_expiration = $default_expiration;
	}

	/**
	 * Get a cached value.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key           Cache key.
	 * @param mixed  $default_value Default value if key doesn't exist.
	 *
	 * @return mixed Cached value or default.
	 */
	public function get( string $key, $default_value = null ) {

		$value = get_transient( $this->get_prefixed_key( $key ) );

		return $value !== false ? $value : $default_value;
	}

	/**
	 * Set a cached value.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key        Cache key.
	 * @param mixed  $value      Value to cache.
	 * @param int    $expiration Expiration time in seconds (0 = use default).
	 *
	 * @return bool True on success, false on failure.
	 */
	public function set( string $key, $value, int $expiration = 0 ): bool {

		if ( $expiration === 0 ) {
			$expiration = $this->default_expiration;
		}

		return set_transient( $this->get_prefixed_key( $key ), $value, $expiration );
	}

	/**
	 * Delete a cached value.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key Cache key.
	 *
	 * @return bool True on success, false on failure.
	 */
	public function delete( string $key ): bool {

		return delete_transient( $this->get_prefixed_key( $key ) );
	}

	/**
	 * Check if a key exists in cache.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key Cache key.
	 *
	 * @return bool True if key exists, false otherwise.
	 */
	public function has( string $key ): bool {

		return get_transient( $this->get_prefixed_key( $key ) ) !== false;
	}

	/**
	 * Clear all cached values.
	 *
	 * @since {VERSION}
	 *
	 * @return bool True on success, false on failure.
	 */
	public function clear(): bool {

		global $wpdb;

		// Delete all transients with our prefix.
		// phpcs:ignore WordPress.DB.DirectDatabaseQuery.DirectQuery,WordPress.DB.DirectDatabaseQuery.NoCaching
		$result = $wpdb->query(
			$wpdb->prepare(
				"DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
				'_transient_' . self::CACHE_PREFIX . '%',
				'_transient_timeout_' . self::CACHE_PREFIX . '%'
			)
		);

		return $result !== false;
	}

	/**
	 * Get prefixed cache key.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key Original cache key.
	 *
	 * @return string Prefixed cache key.
	 */
	private function get_prefixed_key( string $key ): string {

		return self::CACHE_PREFIX . $key;
	}
}
