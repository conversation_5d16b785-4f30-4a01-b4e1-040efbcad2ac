<?php

namespace WPForms\DevTools;

use Faker\Factory;
use WP<PERSON>orms_Lite;

/**
 * Generate service.
 *
 * @since 0.5.0
 */
class GenerateEntries {

	/**
	 * AJAX [delete_all].
	 *
	 * @since 0.5.0
	 */
	public static function ajax_delete_all() {

		check_ajax_referer( DevTools::NONCE, 'nonce' );
		wp_send_json_success( self::delete_all() );
	}

	/**
	 * Delete all generated entries.
	 *
	 * @since 0.5.0
	 *
	 * @return bool
	 */
	public static function delete_all() {

		return Main::wpforms_obj( 'entry' )->delete_by( 'user_agent', 'GENERATED' );
	}

	/**
	 * AJAX [wpf_generate_entries].
	 *
	 * @since 0.5.0
	 */
	public static function ajax_generate_entries() {

		check_ajax_referer( DevTools::NONCE, 'nonce' );
		$form_data = self::parse_form( $_POST );
		$count     = self::parse_count( $_POST );

		wp_send_json_success(
			self::generate_entries( $form_data, $count )
		);
	}

	/**
	 * GenerateEntries entries.
	 *
	 * @since 0.5.0
	 *
	 * @param array $form_data Some form.
	 * @param int   $count     Generated count.
	 *
	 * @return array
	 */
	public static function generate_entries( $form_data, $count ) {

		return wpforms_chain( array_fill( 0, $count, $form_data ) )
			->map( [ __CLASS__, 'generate_entry_data' ] )
			->map( [ __CLASS__, 'add_entry' ] )
			->value();
	}

	/**
	 * Generate entry data.
	 *
	 * @since 0.5.0
	 *
	 * @param array $form_data Form data.
	 *
	 * @return array
	 */
	public static function generate_entry_data( $form_data ) {

		$faker        = Factory::create();
		$field_values = EntryGenerator::fields( $form_data['fields'] );
		$date         = $faker->dateTimeBetween( '-5 years' );

		return [
			[
				'form_id'       => absint( $form_data['id'] ),
				'user_id'       => get_current_user_id(),
				'fields'        => wp_json_encode( $field_values ),
				'ip_address'    => $faker->ipv4,
				'status'        => self::get_status( $field_values ),
				'user_agent'    => 'GENERATED',
				'user_uuid'     => $faker->uuid,
				'date'          => $date->format( 'Y-m-d H:i:s' ),
				'date_modified' => $date->format( 'Y-m-d H:i:s' ),
			],
			$field_values,
			$form_data,
		];
	}

	/**
	 * Get random status.
	 *
	 * @since {VERSION}
	 *
	 * @param array $field_values Field values.
	 *
	 * @return string
	 */
	private static function get_status( $field_values ): string {

		if ( wpforms_has_payment( 'entry', $field_values ) ) {
			return '';
		}

		$statuses = [
			'',
			'spam',
			'trash',
			'partial',
			'abandoned',
		];

		return $statuses[ array_rand( $statuses ) ];
	}

	/**
	 * Add entry to db.
	 *
	 * @since 0.5.0
	 *
	 * @param array $data Content: [form_data, fields].
	 *
	 * @return int
	 */
	public static function add_entry( $data ) {

		[ $entry_data, $field_values, $form_data ] = $data;

		if ( class_exists( 'WPForms_Lite' ) ) {
			self::add_entry_lite( $entry_data, $field_values, $form_data );

			return 0;
		}

		// Create entry.
		$entry_id = Main::wpforms_obj( 'entry' )->add( $entry_data );

		self::add_entry_meta( $field_values, $form_data, $entry_id );

		// Save entry fields.
		Main::wpforms_obj( 'entry_fields' )->save( $field_values, $form_data, $entry_id );

		return $entry_id;
	}

	/**
	 * Process and submit a form entry using WPForms Lite functionalities.
	 *
	 * @since 0.44
	 *
	 * @param array $entry_data   The entry data to be submitted.
	 * @param array $field_values The field values related to the entry.
	 * @param array $form_data    The complete form data.
	 */
	private static function add_entry_lite( array $entry_data, array $field_values, array $form_data ): void { // phpcs:ignore WPForms.PHP.HooksMethod.InvalidPlaceForAddingHooks

		add_filter( 'wpforms_integrations_lite_connect_is_allowed', '__return_true' );
		add_filter( 'wpforms_lite_integrations_lite_connect_is_enabled', '__return_true' );

		$payment_id = 0;

		if ( wpforms_has_payment( 'entry', $field_values ) ) {
			$payment_data = self::prepare_payment_data( $field_values, $form_data );
			$payment_id   = wpforms()->obj( 'payment' )->add( $payment_data );
		}

		( new WPForms_Lite() )->entry_submit( $field_values, $entry_data, $form_data, 0, $payment_id );
	}

	/**
	 * Add entry meta to db.
	 *
	 * @since 0.18.0
	 *
	 * @param array $field_submit Submitted field value.
	 * @param array $form_data    Form data and settings.
	 * @param int   $entry_id     Entry ID.
	 */
	private static function add_entry_meta( $field_submit, $form_data, $entry_id ) {

		$entry = Main::wpforms_obj( 'entry' )->get( $entry_id );

		if ( empty( $entry ) ) {
			return;
		}

		if ( wpforms_has_payment( 'entry', $field_submit ) ) {
			self::add_payment_meta( $field_submit, $form_data, $entry_id );
		}

		if ( $entry->status === 'spam' ) {
			self::add_spam_meta( $entry_id, $form_data );
		}
	}

	/**
	 * Add spam meta to db.
	 *
	 * @since {VERSION}
	 *
	 * @param int   $entry_id  Entry ID.
	 * @param array $form_data Form data and settings.
	 */
	private static function add_spam_meta( $entry_id, $form_data ) {

		Main::wpforms_obj( 'entry_meta' )->add(
			[
				'entry_id' => $entry_id,
				'form_id'  => $form_data['id'],
				'type'     => 'spam',
				'data'     => 'GENERATED',
			],
			'entry_meta'
		);
	}

	/**
	 * Add payment meta to db.
	 *
	 * @since 0.18.0
	 *
	 * @param array $field_submit Submitted field value.
	 * @param array $form_data    Form data and settings.
	 * @param int   $entry_id     Entry ID.
	 */
	private static function add_payment_meta( $field_submit, $form_data, $entry_id ) {

		$faker    = Factory::create();
		$statuses = [ 'completed', 'pending' ];

		$meta = [
			'payment_type'         => 'stripe',
			'payment_mode'         => 'test',
			'payment_total'        => wpforms_get_total_payment( $field_submit ),
			'payment_currency'     => wpforms_get_currency(),
			'payment_transaction'  => $faker->bothify( 'pi_#?????##????????##?????#' ),
			'payment_subscription' => '',
			'payment_customer'     => '',
			'payment_period'       => '',
		];

		if ( ! empty( $form_data['payments']['stripe']['recurring']['enable'] ) ) {
			$meta['payment_subscription'] = $faker->bothify( 'sub_#?????##????????##?????#' );
			$meta['payment_customer']     = $faker->bothify( 'cus_?????????????#' );
			$meta['payment_period']       = ! empty( $form_data['payments']['stripe']['recurring']['period'] ) ? $form_data['payments']['stripe']['recurring']['period'] : '';
		}

		$data = [
			'status' => $statuses[ array_rand( $statuses ) ],
			'type'   => 'payment',
			'meta'   => wp_json_encode( $meta ),
		];

		// Update payment data into entries table.
		Main::wpforms_obj( 'entry' )->update( $entry_id, $data, '', '', [ 'cap' => false ] );

		// Insert payment data into entry_meta table.
		Main::wpforms_obj( 'entry' )->insert_payment_meta( $entry_id, $meta );
	}

	/**
	 * Prepare payment data.
	 *
	 * @since 0.44
	 *
	 * @param array $field_values Field values.
	 * @param array $form_data    Form data and settings.
	 *
	 * @return array
	 */
	private static function prepare_payment_data( array $field_values, array $form_data ): array {

		$faker    = Factory::create();
		$statuses = [ 'completed', 'pending' ];

		return [
			'form_id'          => absint( $form_data['id'] ),
			'subtotal_amount'  => wpforms_get_total_payment( $field_values ),
			'total_amount'     => wpforms_get_total_payment( $field_values ),
			'currency'         => wpforms_get_currency(),
			'entry_id'         => 0,
			'date_created_gmt' => $faker->dateTimeBetween( '-5 years' )->format( 'Y-m-d H:i:s' ),
			'date_updated_gmt' => $faker->dateTimeBetween( '-5 years' )->format( 'Y-m-d H:i:s' ),
			'status'           => $statuses[ array_rand( $statuses ) ],
			'type'             => 'one-time',
			'gateway'          => 'stripe',
			'mode'             => 'test',
			'transaction_id'   => $faker->bothify( 'pi_#?????##????????##?????#' ),
			'customer_id'      => '',
			'title'            => $faker->name,
		];
	}

	/**
	 * Parse count.
	 *
	 * @since 0.5.0
	 *
	 * @param array $request Some request.
	 *
	 * @return int
	 */
	public static function parse_count( $request ) {

		return max( 1, (int) wpforms_list_get( $request, 'count', 1 ) );
	}

	/**
	 * Parse form.
	 *
	 * @since 0.5.0
	 *
	 * @param array $request Some request.
	 *
	 * @return array
	 */
	public static function parse_form( $request ) {

		$form_data = Main::wpforms_obj( 'form' )->get(
			wpforms_list_get( $request, 'form', 0 ),
			[ 'content_only' => true ]
		);

		if ( ! $form_data ) {
			wp_send_json_error( [ 'msg' => 'Form not found!' ] );
		}

		return $form_data;
	}
}
