@import '../../../../scss/emails/partials/appearance';
@import '../../../../scss/emails/partials/layout_media_queries';

@media only screen and (max-width: 599px) {
	.body-inner {
		padding-top: 25px !important;
		padding-bottom: 25px !important;
	}

	.wrapper-inner {
		padding: 25px !important;
	}

	.header {
		padding-bottom: 25px !important;

		// Maximum height sizes to scale down the header image.
		$sizes:
			"small" 100,
			"medium" 140,
			"large" 180;

		@each $name, $height in $sizes {
			.has-image-size-#{$name} {
				img {
					max-height: #{$height}px !important;
				}
			}
		}
	}

	.content tr td {
		padding-top: 0 !important;
	}

	.footer {
		padding-top: 0 !important;
	}
}
