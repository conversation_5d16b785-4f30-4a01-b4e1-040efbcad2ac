<?php

namespace WPForms\SupportHelper;

/**
 * Interface for classes that need to handle plugin activation and deactivation.
 *
 * This interface defines the contract for classes that need to perform
 * specific actions when the plugin is activated or deactivated.
 *
 * @since {VERSION}
 */
interface ActivationInterface {

	/**
	 * Handle plugin activation.
	 *
	 * This method is called when the plugin is activated and should contain
	 * any initialization logic specific to the implementing class.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	public function activate_plugin(): void;

	/**
	 * Handle plugin deactivation.
	 *
	 * This method is called when the plugin is deactivated and should contain
	 * any cleanup logic specific to the implementing class.
	 *
	 * @since {VERSION}
	 *
	 * @return void
	 */
	public function deactivate_plugin(): void;
}
