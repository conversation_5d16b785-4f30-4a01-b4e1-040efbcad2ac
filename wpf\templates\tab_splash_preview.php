<?php
/**
 * View for What's New tab.
 *
 * @var array  $license_types List of all available license types.
 * @var string $error         Error message.
 * @var array  $options       Options.
 */
?>
<form action="" method="post">
	<?php wp_nonce_field( 'splash_screen_preview', 'splash_screen_preview_nonce' ); ?>

	<?php if ( ! empty( $error ) ) : ?>
		<div class="notice notice-error wpforms-notice wpforms-error">
			<p>
				<?php echo esc_html( $error ); ?>
			</p>
		</div>
	<?php endif; ?>

	<div class="wpforms-setting-row wpforms-setting-row-select" id="wpforms-splash-screen-license-container">
		<div class="wpforms-setting-label">
			<label for="wpforms-splash-screen-license">Choose a license</label>
		</div>
		<div class="wpforms-setting-field">
			<span class="choicesjs-select-wrap">
				<select class="choicesjs-select" name="license" id="wpforms-splash-screen-license" required>
					<option value="" selected>Please select</option>
					<?php foreach ( $license_types as $license_type ) : ?>
						<option value="<?php echo esc_attr( $license_type ); ?>" <?php selected( $options['license'], $license_type ); ?>>
							<?php echo esc_html( ucfirst( $license_type ) ); ?>
						</option>
					<?php endforeach; ?>
				</select>
			</span>
		</div>
	</div>
	<div class="wpforms-setting-row">
		<p><i>"Hide Announcements" option will be set to off to be able to display the Splash Screen correctly after clicking the "Preview" button.</i></p>
	</div>
	<div class="submit wpforms-admin-page">
		<button type="submit" class="wpforms-btn wpforms-btn-md wpforms-btn-orange">Preview</button>
	</div>
</form>
