// Popup styles for the intl-tel-input field.
//
// @since 1.8.7

:root {
	--iti-mobile-popup-margin: 30px;
}

//* Overrides for mobile popup.
.iti--fullscreen-popup {
	&.iti--container {
		background-color: rgba(0, 0, 0, 0.5);
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		position: fixed;
		padding: var(--iti-mobile-popup-margin);
		//* Short country lists should be vertically centred.
		display: flex;
		flex-direction: column;
		//* The country search input auto-focuses, so mobile keyboard appears, so stick to top (also because when filter countries down, the height changes and the vertical centring would make it jump around).
		justify-content: flex-start;
	}
	.iti__dropdown-content {
		display: flex;
		flex-direction: column;
		max-height: 100%;
		position: relative; //* Override needed in order to get full-width working properly.
	}
	.iti__country {
		padding: 10px 10px;
		//* Increase line height because dropdown copy is v likely to overflow on mobile and when it does it needs to be well spaced.
		line-height: 1.5em;
	}
}
