// WPForms Classic styles.
//
// Payment Quantities.
//
// @since 1.8.7

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled {

	&.wpforms-field-payment-single .wpforms-single-item-price-content {
		display: flex;
		align-items: center;
		gap: 15px;

		.wpforms-single-item-price {
			width: calc(60% - 85px);

			&.wpforms-field-small {
				text-wrap: balance;
				width: calc(25% - 85px);
			}

			&.wpforms-field-large {
				width: calc(100% - 85px);
			}
		}

		select.wpforms-payment-quantity {
			margin-left: 0;
			height: 30px;
			min-height: 30px;
			font-size: 14px;
			padding-top: 0;
			padding-bottom: 0;
		}
	}

	select.wpforms-payment-price {
		display: inline-block;
		max-width: calc( 60% - 85px );

		&.wpforms-field-small {
			max-width: calc( 25% - 85px );
		}

		&.wpforms-field-large {
			max-width: calc( 100% - 85px );
		}
	}

	select.wpforms-payment-quantity {
		display: inline-block;
		margin-inline-start: 15px;
		width: 70px;
		min-width: 70px;
	}

	&.wpforms-field-select-style-modern {
		display: flex;
		flex-wrap: wrap;
		column-gap: 15px;
		align-items: flex-start;

		.wpforms-field-label {
			min-width: 100%;
		}

		.choices {
			margin-bottom: 5px;
		}

		.wpforms-field-row {
			flex-grow: 1;
			max-width: calc( 60% - 85px );

			&.wpforms-field-small {
				max-width: calc( 25% - 85px );
			}

			&.wpforms-field-large {
				max-width: calc( 100% - 85px );
			}
		}

		.wpforms-payment-quantity {
			flex-basis: 70px;
			max-width: 70px;

			.choices__list--dropdown {
				min-width: 70px;
			}
		}

		.wpforms-field-description {
			flex-basis: 100%;
			margin-top: 0;
		}

		.wpforms-error {
			flex-basis: 100%;
		}
	}
}

// Responsive
@media only screen and (max-width: 600px) {
	.wpforms-form .wpforms-payment-quantities-enabled {

		select.wpforms-payment-price {
			width: calc( 100% - 85px ) !important;
			max-width: 100% !important;
		}

		.wpforms-single-item-price-content .wpforms-single-item-price {
			width: calc( 100% - 70px ) !important;
		}

		&.wpforms-field-select-style-modern {
			.wpforms-field-row {
				width: calc( 100% - 85px ) !important;
				max-width: 100% !important;
			}
		}
	}
}

// Form Pages addon compatibility.
#wpforms-form-page-page .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price {

	&.wpforms-field-medium,
	&.wpforms-field-small {
		max-width: calc( 100% - 85px );
	}
}
