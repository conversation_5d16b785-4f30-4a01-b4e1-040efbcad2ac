// WPForms Modern Base styles.
//
// Field Pagebreak.
//
// @since 1.8.1

.wpforms-container {

	// Page Indicator themes.
	.wpforms-page-indicator {
		margin: 0 0 $spacing_ml 0;
		overflow: hidden;

		// Circles theme.
		&.circles {
			border-top: 1px solid $color_modern_border_dark;
			border-bottom: 1px solid $color_modern_border_dark;
			padding: $spacing_m $spacing_s;
			display: flex;
			justify-content: flex-start;

			.wpforms-page-indicator-page {
				margin: 0 $spacing_ml 0 0;

				&:last-of-type {
					margin: 0;
				}
			}

			.wpforms-page-indicator-page-number {
				height: 40px;
				width: 40px;
				border-radius: 50%;
				display: inline-block;
				margin: 0 $spacing_s 0 0;
				line-height: 40px;
				text-align: center;
				background-color: $color_modern_border_dark;
				color: $color_modern_secondary_dark;
			}

			.active .wpforms-page-indicator-page-number {
				color: $color_white;
			}
		}

		// Connector theme.
		&.connector {
			display: flex;
			justify-content: flex-start;

			.wpforms-page-indicator-page {
				text-align: center;
				line-height: 1.2;
			}

			.wpforms-page-indicator-page-number {
				display: block;
				text-indent: -9999px;
				height: 6px;
				background-color: $color_modern_border_dark;
				margin: 0 0 16px 0;
				position: relative;
			}

			.wpforms-page-indicator-page-triangle {
				position: absolute;
				top: 100%;
				left: 50%;
				width: 0;
				height: 0;
				margin-left: -5px;
				border-style: solid;
				border-width: 6px 5px 0 5px;
				border-color: transparent transparent transparent transparent;
			}

			.wpforms-page-indicator-page-title {
				display: inline-block;
				padding: 0 $spacing_m;
				font-size: 16px;
			}
		}

		// Progress theme.
		&.progress {
			font-size: 18px;

			.wpforms-page-indicator-page-progress-wrap {
				display: block;
				width: 100%;
				background-color: $color_modern_border_dark;
				height: 18px;
				border-radius: 10px;
				overflow: hidden;
				position: relative;
				margin: 5px 0 0;
			}

			.wpforms-page-indicator-page-progress {
				height: 18px;
				position: absolute;
				left: 0;
				top: 0;
			}
		}
	}

	.wpforms-field-pagebreak:empty {
		display: none;
	}
}
