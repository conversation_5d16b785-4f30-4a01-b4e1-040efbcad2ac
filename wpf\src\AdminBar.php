<?php

namespace WPForms\DevTools;

use WP_Admin_Bar;

/**
 * AdminBar class.
 *
 * @since {VERSION}
 */
class AdminBar {

	/**
	 * Init.
	 *
	 * @since {VERSION}
	 */
	public static function init() {

		if ( is_admin() || ! function_exists( 'wpforms' ) ) {
			return;
		}

		self::hooks();
	}

	/**
	 * Hooks.
	 *
	 * @since {VERSION}
	 */
	private static function hooks() {

		add_action( 'admin_bar_menu', [ self::class, 'add_style_indicator_menu' ], 1001 );
	}

	/**
	 * Add an indicator to the WordPress Admin Bar to show the current style of the WPForms frontend.
	 *
	 * @since {VERSION}
	 *
	 * @param WP_Admin_Bar $wp_admin_bar WordPress Admin Bar object.
	 */
	public static function add_style_indicator_menu( WP_Admin_Bar $wp_admin_bar ) {

		// Return early if the "Hide WPForms Styles indicator" is turned on.
		$hide_indicator_option = ( (array) get_option( 'wpf-utils', [] ) )['hide-styles-indicator'] ?? false;

		if ( $hide_indicator_option === 1 ) {
			return;
		}

		// Return early if it is a Conversational Form page.
		if ( did_action( 'wpforms_conversational_form_detected' ) ) {
			return;
		}

		// Prepare the data for the indicator.
		$styles_type          = wpforms_setting( 'disable-css', '1' );
		$render_engine        = wpforms_get_render_engine();
		$target_render_engine = $render_engine !== 'modern' ? 'modern' : 'classic';
		$change_style_url     = add_query_arg( 'wpf_utils_action', 'frontend_style_render_' . $target_render_engine );
		$styles_types_titles  = [
			'1' => 'Full',
			'2' => 'Base',
			'3' => 'None',
		];
		$indicator_title      = ucfirst( $render_engine ) . " ({$styles_types_titles[ $styles_type ]})";

		// Set the indicator class based on the current style type.
		if ( $styles_type === 3 ) {
			$indicator_class = 'wpf-styles-indicator-disabled';
		} else {
			$indicator_class = 'wpf-styles-indicator-' . $render_engine . ' wpf-styles-indicator-' . strtolower( $styles_types_titles[ $styles_type ] );
		}

		// Add the indicator to the WordPress Admin Bar.
		$wp_admin_bar->add_menu(
			[
				'id'    => 'wpf-styles-indicator',
				'title' => $indicator_title,
				'href'  => $change_style_url,
				'meta'  => [
					'class' => $indicator_class,
					'title' => 'Click to switch the render engine',
				],
			]
		);

		// Add the sub-menu to the main indicator menu.
		self::add_sub_menu( $wp_admin_bar, $styles_type );
	}

	/**
	 * Add sub-menu items the style indicator menu.
	 *
	 * @since {VERSION}
	 *
	 * @param WP_Admin_Bar $wp_admin_bar WordPress Admin Bar object.
	 * @param string       $styles_type  The current style type.
	 */
	private static function add_sub_menu( WP_Admin_Bar $wp_admin_bar, string $styles_type ) {

		$nodes = [];

		$nodes[] = [
			'parent' => 'wpf-styles-indicator',
			'title'  => 'Full',
			'id'     => 'wpf-utils-frontend-style-full',
			'meta'   => [
				'class' => $styles_type === '1' ? 'active' : '',
			],
			'href'   => add_query_arg( 'wpf_utils_action', 'frontend_style_full' ),
		];

		$nodes[] = [
			'parent' => 'wpf-styles-indicator',
			'title'  => 'Base',
			'id'     => 'wpf-utils-frontend-style-base',
			'meta'   => [
				'class' => $styles_type === '2' ? 'active' : '',
			],
			'href'   => add_query_arg( 'wpf_utils_action', 'frontend_style_base' ),
		];

		$nodes[] = [
			'parent' => 'wpf-styles-indicator',
			'title'  => 'None',
			'id'     => 'wpf-utils-frontend-style-none',
			'meta'   => [
				'class' => $styles_type === '3' ? 'active' : '',
			],
			'href'   => add_query_arg( 'wpf_utils_action', 'frontend_style_none' ),
		];

		array_map(
			static function ( $node ) use ( $wp_admin_bar ) {

				$wp_admin_bar->add_node( $node );
			},
			$nodes
		);
	}
}
