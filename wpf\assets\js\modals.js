/**
 * Modals Generator.
 *
 * @since {VERSION}
 */

'use strict';

const WPFModals = window.WPFModals || ( function( document, window, $ ) {

	/**
	 * Elements holder.
	 *
	 * @since {VERSION}
	 *
	 * @type {object}
	 */
	const elements = {};

	/**
	 * Default config options for the jquery-confirm modal.
	 *
	 * @since {VERSION}
	 *
	 * @type {object}
	 */
	const defaults = {
		icon: '',
		title: '',
		content: '',
		type: 'orange',
		typeAnimated: true,
		closeIcon: false,
		boxWidth: '400px',
		draggable: false,
		backgroundDismiss: true,
		escapeKey: true,
		buttons: false,
	};

	/**
	 * Custom config options for the jquery-confirm modal.
	 *
	 * @since {VERSION}
	 *
	 * @type {object}
	 */
	let options = {};

	/**
	 * Public functions and properties.
	 *
	 * @since {VERSION}
	 *
	 * @type {object}
	 */
	const app = {

		/**
		 * Start the engine.
		 *
		 * @since {VERSION}
		 */
		init: function() {

			// Cache elements.
			elements.form      = $( '#wpforms-admin-devtools-modal-generator' );
			elements.button    = $( '#wpforms-admin-devtools-modal-generator-open' );
			elements.typeInput = $( '.wpforms-admin-devtools-modal-generator-types input[name="type"]' );

			// Bind events.
			app.events();
		},

		/**
		 * Register JS events.
		 *
		 * @since {VERSION}
		 */
		events: function() {

			elements.typeInput.on( 'change', function() {

				const $this = $( this );

				$this.parents( '.wpforms-admin-devtools-modal-generator-types' ).find( 'label' ).removeClass( 'wpf-mg-selected' );
				$this.parent().addClass( 'wpf-mg-selected' );
			} );

			// Build options object and open modal.
			elements.button.on( 'click', function( event ) {

				event.preventDefault();
				app.setOptions().openModal();
			} );
		},

		/**
		 * Grab all custom options and update the object.
		 *
		 * @since {VERSION}
		 *
		 * @returns {object} App instance for chaining.
		 */
		setOptions: function() {

			// Serialize form data into an object.
			const data = elements.form
				.serializeArray()
				.reduce( function( json, { name, value } ) {
					json[name] = value.trim();

					return json;
				}, {} );

			// Convert icon name to CSS classnames.
			if ( data.icon.length > 0 ) {
				data.icon = 'fa fa-' + data.icon;
			}

			// Make sure content is never empty.
			if ( data.content.length === 0 ) {
				data.content = 'Modal content is required.';
			}

			// Convert close icon value to boolean.
			data.closeIcon = data.closeIcon === '1';

			// Do we have buttons?
			data.buttons = data.buttonPrimary.length === 0 && data.buttonSecondary.length === 0 ? false : {};

			// Define primary button.
			if ( data.buttonPrimary.length > 0 ) {
				data.buttons.primary = {
					text: data.buttonPrimary,
					btnClass: 'btn-confirm',
					keys: [ 'enter' ],
				};
			}

			// Define secondary button.
			if ( data.buttonSecondary.length > 0 ) {
				data.buttons.secondary = {
					text: data.buttonSecondary,
					btnClass: '',
					keys: [ 'esc' ],
				};
			}

			// Clean things up.
			delete data.buttonPrimary;
			delete data.buttonSecondary;

			options = data;

			return app;
		},

		/**
		 * Merge custom options with defaults and prepare usable jquery-confirm options object.
		 *
		 * @since {VERSION}
		 *
		 * @returns {object} Merged options object.
		 */
		getOptions: function() {

			return $.extend( true, {}, defaults, options );
		},

		/**
		 * Open jquery-confirm modal.
		 *
		 * @since {VERSION}
		 */
		openModal: function() {

			$.confirm( app.getOptions() );
		},
	};

	// Provide access to public functions/properties.
	return app;

}( document, window, jQuery ) );

// Initialize.
WPFModals.init();
