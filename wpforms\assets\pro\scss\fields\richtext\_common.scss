// Rich Text field styles.
//
// Common.
//
// @since 1.7.0

div.wpforms-container .wpforms-form div.wpforms-field-richtext {

	.insert-media.add_media {
		display: none !important;
	}

	.mce-container {
		color: initial;
	}

	&.wpforms-has-error {

		.quicktags-toolbar {
			border-top-color: $border_error_color;
			border-left-color: $border_error_color;
			border-right-color: $border_error_color;
		}

		.wp-switch-editor {
			border-color: $border_error_color;
		}

		.wp-editor-container textarea.wp-editor-area {
			border-color: $border_error_color;
		}
	}

	.wp-switch-editor {
		float: left;
		box-sizing: border-box;
		position: relative;
		top: var( --wpforms-field-border-size, 1px );
		background: $non_active_tab_bg;
		color: $text;
		cursor: pointer;
		font-size: 13px;
		font-weight: normal;
		line-height: 1.46153846;
		height: 29px;
		margin: 0 0 0 5px;
		padding: 3px 8px 4px;
		border: 1px solid $border_color;
		border-top-left-radius: 2px;
		border-top-right-radius: 2px;
	}

	.wp-editor-tabs {
		float: right;
		position: relative;
		z-index: 1;
	}

	.html-active button.switch-html,
	.tmce-active button.switch-tmce,
	.mce-toolbar .mce-btn-group .mce-widget.mce-btn button {
		border-bottom-color: transparent;
	}

	.tmce-active button.switch-tmce,
	.html-active button.switch-html {
		background: $panel_bg;
	}

	.tmce-active.wpforms-focused button.switch-tmce,
	.html-active.wpforms-focused button.switch-html {
		top: 0;
	}

	.html-active .quicktags-toolbar {
		display: flex;
		flex-wrap: wrap;
	}

	.mce-toolbar .mce-btn-group .mce-btn,
	.qt-dfw.active {
		&.active,
		&:active,
		&.mce-active {
			background-color: transparent;
			color: inherit;
			border-color: $bd_color_hover;

			&.mce-btn-has-text {
				background-color: $white;
			}
		}

		&:focus,
		&:hover {
			border-color: $bd_color_hover;
			box-shadow: inset 0 1px 0 #fff, 0 1px 0 rgba(0, 0, 0, .08);
		}
	}

	.quicktags-toolbar {
		padding: 3px;
		position: relative;
		border: 1px solid $bd_color;
		border-top-left-radius: 2px;
		background: $panel_bg;

		.button {
			height: 26px;
			min-height: 26px;
			line-height: 24px;
			border-width: 1px;
			border-style: solid;
			-webkit-appearance: none;
			border-radius: 3px;
			font-weight: 400;
			color: $primary_button_color;
			border-color: $primary_button_bd;
			background: $primary_button_bg;
			vertical-align: top;
			padding: 0 8px;
			margin-right: 4px;
			text-transform: none;
			text-decoration: none;

			&:hover {
				text-decoration: none;
				background: $primary_button_bg;
				border-color: $primary_button_bd_hover;
				color: $primary_button_color_hover;
			}

			&[value="b"],
			&[value="/b"] {
				font-weight: bold;
			}

			&[value="i"],
			&[value="/i"] {
				font-style: italic;
			}

			&[value="link"] {
				text-decoration: underline;
			}

			&[value="del"],
			&[value="/del"] {
				text-decoration: line-through;
			}
		}
	}

	.wp-editor-container textarea.wp-editor-area {
		border-radius: 0 0 2px 2px;
		border-top: 0;
		border-color: $border_color;

		&:focus {
			outline: none;
		}
	}

	.mce-toolbar-grp .mce-active {

		i {
			color: inherit;
		}

		.mce-caret {
			border-top: 0;
			border-bottom: 6px solid $text;
		}
	}
}

#wpforms-form-page-page div.wpforms-field-richtext button.wp-switch-editor {
	font-size: 13px;
}

.rtl div.wpforms-container .wpforms-form div.wpforms-field-richtext  div.wp-editor-tabs {
	float: left;

	button.switch-tmce {
		margin-left: 0;

		&:after {
			left: 0 !important;
		}
	}
}

.rtl .wpforms-form div.wpforms-field-richtext .mce-container-body .mce-resizehandle {
	right: auto;
	left: 0;
	padding-left: 0;

	.mce-i-resize {
		transform: rotate(90deg);
	}
}

