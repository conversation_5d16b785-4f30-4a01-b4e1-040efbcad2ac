// Form Builder styles.
//
// Field options and settings.
// Toggle control.
//
// @since 1.6.8

.wpforms-toggle-control {
	align-items: flex-start;
	display: flex;
	gap: $spacing_s;

	&.wpforms-toggle-control-disabled {
		pointer-events: none;
		opacity: 50%;
	}

	input[type=checkbox] {
		display: none;
		height: 0;
		width: 0;

		&:checked {
			& + label.wpforms-toggle-control-icon {
				background-color: $color_blue;

				&:after {
					inset-inline-start: calc( 100% - #{$option_toggle_width} - 2px );
				}
			}
		}
	}

	span,
	label {
		align-items: flex-start;
		display: flex;
		gap: $spacing_s;
		margin: 0;
		vertical-align: unset;
	}

	.wpforms-toggle-control-label {

		&:hover {
			cursor: pointer;
		}
	}

	.wpforms-toggle-control-status {
		color: $color_fields_secondary_text;
		font-size: $font_size_xs;
		line-height: $font_size_s;
		margin: 2px $spacing_xs;
	}

	.wpforms-toggle-control-icon {
		background-color: $color_hint;
		border-radius: 8.5px;
		cursor: pointer;
		display: inline-block;
		height: 17px;
		margin: 0 1px;
		position: relative;
		text-indent: -9999px;
		width: 27px;
		flex: 0 0 auto;
		&:after {
			background: $color_white;
			border-radius: 50%;
			content: "";
			height: 13px;
			inset-inline-start: 2px;
			position: absolute;
			top: 2px;
			width: $option_toggle_width;

			@include transition( all, $transition_slow, ease-out );
		}
	}

	.wpforms-help-tooltip {
		margin: 0 !important; /* Override default margin set with ID in general.scss */
	}

	&:hover {
		input:checked + label.wpforms-toggle-control-icon {
			background-color: $color_dark_blue;
		}

		.wpforms-toggle-control-icon {
			background-color: $color_secondary_text;
		}
	}
}

.wpforms-panel-sidebar {
	.wpforms-toggle-control {
		.wpforms-toggle-control-icon {
			background-color: $color_fields_border;
		}

		&:hover {
			.wpforms-toggle-control-icon {
				background-color: $color_fields_secondary_text;
			}
		}

		&.wpforms-field-option-in-label-right {
			.wpforms-toggle-control-label {
				color: $color_fields_secondary_text;
				font-size: $font_size_xs;
				line-height: $font_size_s;
				margin: 2px $spacing_xs;
				max-width: initial;
			}
		}
	}

	.wpforms-field-option-row.wpforms-disabled:has( .wpforms-toggle-control ) {
		pointer-events: initial;

		&:hover {
			label, i {
				cursor: default !important;
			}

			i {
				pointer-events: none;
			}

			.wpforms-toggle-control-icon {
				cursor: default !important;
				background-color: $color_fields_border;
			}
		}
	}
}
