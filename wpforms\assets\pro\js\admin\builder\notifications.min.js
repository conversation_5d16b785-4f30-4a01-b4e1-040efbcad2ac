var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.Notifications=WPForms.Admin.Builder.Notifications||(o=>{let l={},c={removeItemButton:!0,shouldSort:!1},s,i=[{toggle:{className:".notifications_enable_file_upload_attachment_toggle",actionElements:["file_upload_attachment_fields"]},choicesJS:{fieldName:"file_upload_attachment_fields",choices:"fileUpload"}},{toggle:{className:".notifications_enable_entry_csv_attachment_toggle",actionElements:["entry_csv_attachment_entry_information","entry_csv_attachment_file_name"]},choicesJS:{fieldName:"entry_csv_attachment_entry_information",choices:"entryInformation"}}],d={init:function(){(s=Object.assign({},c)).noChoicesText=wpforms_builder.notifications_file_upload.no_choices_text,d.setup(),d.bindEvents()},ready(){},setup:function(){l={$builder:o("#wpforms-builder")}},initBlock:function(l,a=!1,r=""){let e=l.data("blockId");"notification"===l.data("blockType")&&e&&i.forEach(function(t){d.initToggle(l,e,t.toggle.className,t.toggle.actionElements);var n,o=l.find("."+t.choicesJS.fieldName).first();if(!(o.length<=0)){let e,i=c;"fileUpload"===t.choicesJS.choices?0===(e=d.choicesJSHelperMethods.fileUploadFields.getAllChoices())[0].choices.length&&(i=s):e=d.choicesJSHelperMethods.entryInformation.getAllChoices(),a&&(n=!r,d.initDynamicallyAddedChoicesJS(l,t.choicesJS.fieldName,n),d.setDynamicallyAddedEntryInformationFileNameFieldValue(l,r)),d.initChoicesJS(o,e,i),"fileUpload"===t.choicesJS.choices&&(d.computeForFileUploadAttachmentSize(o),o.on("change",d.fileUploadFieldChange))}})},initToggle(e,i,t,n){e=e.find(t).first();return!(e.length<=0)&&(e.on("change",function(){var e=o(this),i=d.choicesJSHelperMethods.getFormFields(["file-upload"]);e.trigger("wpformsNotificationsToggleConditionalChange",[i])}),d.setupToggleConditional(e,i,n))},setupToggleConditional:function(e,i,t){var t=t.map(function(e){return`#wpforms-panel-field-notifications-${i}-${e}-wrap`});return!(t.length<=0||(t=t.join(","),e.conditions([{conditions:{element:e,type:"checked",operator:"is",condition:"1"},actions:{if:{element:t,action:"show"},else:{element:t,action:"hide"}},effect:"appear"}]),0))},initChoicesJS:function(e,i,t){var n,o;return!!e.attr("name")&&(n=e.val(),void 0!==(o=e.data("choicesjs"))&&o.destroy(),o=new Choices(e[0],t),e.data("choicesjs",o),e.on("change",d.changeChoicesJS),d.choicesJSHelperMethods.populateInstance(o,i,n),o.passedElement.element.addEventListener("addItem",function(e){l.$builder.trigger("wpformsNotificationFieldAdded",[e.detail.value,e.target])}),o.passedElement.element.addEventListener("removeItem",function(e){l.$builder.trigger("wpformsNotificationFieldRemoved",[e.detail.value,e.target])}),o)},changeChoicesJS:function(){var e=o(this),i=e.data("choicesjs").getValue(),t=e.attr("name");if(t&&i){t=t+"[hidden]",e=e.closest(".wpforms-panel-field").find(`input[name="${t}"]`);if(!(e.length<=0)){var n=[];for(let e=0;e<i.length;e++)n.push(i[e].value);e.val(JSON.stringify(n))}}},fileUploadFieldChange:function(){d.computeForFileUploadAttachmentSize(o(this))},computeForFileUploadAttachmentSize:function(e){var t=e.data("choicesjs").getValue(),e=e.parents(".wpforms-panel-field").find(".notifications-file-upload-attachment-size");if(!(e.length<=0))if(t.length<=0)e.text(0);else{var n=Number(wpforms_builder.notifications_file_upload.wp_max_upload_size);let i=0;for(let e=0;e<t.length;e++){var o,l=wpf.getField(t[e].value);"file-upload"===l.type&&(o=d.utils.convertToNumber(l.max_size,n),l=d.utils.convertToNumber(l.max_file_number,1),i+=o*l)}e.text(+wpf.numberFormat(i,2,".",",").replace(",",""))}},initDynamicallyAddedChoicesJS:function(e,i,t){var n=e.data("blockId"),e=e.find(`#wpforms-panel-field-notifications-${n}-${i}-wrap`);e.length<=0||(n=e.find(".choices")).length<=0||(i=n.find("."+i).first()).length<=0||(i.removeClass("choices__input").removeAttr("hidden").removeAttr("data-choice").removeData("choice").prependTo(e.first()),e.find("label:first").prependTo(e.first()),n.first().remove(),t&&i.val([]))},bindEvents:function(){l.$builder.on("wpformsSettingsBlockAdded",d.notificationBlockAdded).on("wpformsSettingsBlockCloned",d.notificationsBlockCloned).on("wpformsPanelSwitch",d.panelSwitch).on("wpformsPanelSectionSwitch",d.panelSectionSwitch)},notificationBlockAdded:function(e,i){d.initBlock(i,!0)},setDynamicallyAddedEntryInformationFileNameFieldValue:function(e,i){e=e.find(".entry_csv_attachment_file_name");e.length<=0||(i=i?o(`#wpforms-panel-field-notifications-${i}-entry_csv_attachment_file_name`).val():"",e.val(0===i.length?wpforms_builder.entry_information.default_file_name:i))},notificationsBlockCloned:function(e,i,t){d.initBlock(i,!0,t)},panelSwitch:function(e,i){"settings"===i&&"notifications"===o("#wpforms-panel-settings .wpforms-panel-sidebar").find(".wpforms-panel-sidebar-section.active").data("section")&&d.loopAllNotificationsBlock(function(e,i){d.initBlock(e)})},loopAllNotificationsBlock:function(n){o(".wpforms-notification.wpforms-builder-settings-block").each(function(e,i){var i=o(i),t=i.data("blockId");"notification"===i.data("blockType")&&t&&n(i,t)})},panelSectionSwitch:function(e,i){"notifications"===i&&d.loopAllNotificationsBlock(function(e,i){d.initBlock(e)})},maybeSaveFormState:function(){console.warn('WARNING! Function "WPForms.Admin.Builder.Notifications.maybeSaveFormState()" has been deprecated, please use the new "wpf._getCurrentFormState()" function instead!'),wpf._getCurrentFormState()},choicesJSHelperMethods:{fileUploadFields:{getAllChoices:function(){return[{label:"hidden",choices:d.choicesJSHelperMethods.getFormFields(["file-upload"])}]}},entryInformation:{getAllChoices:function(){return[{label:"hidden",choices:[{value:"all_fields",label:wpforms_builder.entry_information.localized.all_fields}]},{label:wpforms_builder.fields_available,choices:d.choicesJSHelperMethods.getFormFields(!1,wpforms_builder.entry_information.excluded_field_types)},{label:wpforms_builder.other,choices:d.choicesJSHelperMethods.entryInformation.getOtherChoices()}]},getOtherChoices:function(){var e,i,t=[];for(e in wpforms_builder.smart_tags)wpforms_builder.entry_information.excluded_tags.includes(e)||(i=Object.hasOwn(wpforms_builder.entry_information.replacement_tags,e)?wpforms_builder.entry_information.replacement_tags[e]:e,t.push({label:wpf.encodeHTMLEntities(wpforms_builder.smart_tags[e]),value:wpf.sanitizeHTML(i)}));return t}},getFormFields(e,i=[]){var t,n=[],o=wpf.getFields(e,!0,!0);if(!o)return[];for(t in o){var l=o[t];if(void 0!==l.label&&!d.choicesJSHelperMethods.isFieldExcluded(l,i)){var a=d.choicesJSHelperMethods.fieldHasRestrictions(l.id);let e=wpf.encodeHTMLEntities(d.choicesJSHelperMethods.getFieldLabel(l));a&&(e+='<span class="wpfroms-notifications-restrictions-enabled">'+wpforms_builder.notifications_file_upload.restrictions_enabled+"</span>"),n.push({label:e,value:wpf.sanitizeHTML(l.id.toString()),disabled:a})}}return n},isFieldExcluded:function(e,i){return Array.isArray(i)&&0<i.length&&i.includes(e.type)},fieldHasRestrictions(e){return Boolean(o(`#wpforms-field-option-${e}-is_restricted`).prop("checked"))},getFieldLabel:function(e){return 0===e.label.length?""+wpforms_builder.empty_label_alternative_text+e.id:e.label},populateInstance:function(i,e,t=[]){i&&(i.clearStore(),i.setChoices(e),Array.isArray(t))&&t.forEach(function(e){i.setChoiceByValue(e)})}},utils:{convertToNumber:function(e,i){e=Number(e);return e<=0?i:e}}};return d})((document,window,jQuery)),WPForms.Admin.Builder.Notifications.init();