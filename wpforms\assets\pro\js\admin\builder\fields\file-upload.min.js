var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.FieldFileUpload=WPForms.Admin.Builder.FieldFileUpload||(a=>{let e={},s={init(){a(s.ready)},ready(){s.setup(),s.initUserRestrictionsSelects(),s.events()},setup(){e={$builder:a("#wpforms-builder")}},events(){e.$builder.on("change",".wpforms-file-upload-media-library",s.mediaLibraryOptionHandler).on("change",".wpforms-camera-media-library",s.mediaLibraryOptionHandler).on("change",".wpforms-file-upload-access-restrictions",s.accessRestrictionsOptionHandler).on("change",".wpforms-file-upload-password-restrictions",s.passwordRestrictionsOptionHandler).on("change",".wpforms-file-upload-user-restrictions",s.userRestrictionsOptionHandler).on("keyup focus",".wpforms-file-upload-password",s.cleanPasswordButtonHandler).on("keyup",".wpforms-file-upload-password-confirm",s.checkPasswordMatch).on("change",".wpforms-file-upload-password",s.sanitizePasswordValue).on("click",".wpforms-file-upload-password-clean",s.cleanPasswordInput).on("click",".wpforms-notifications-disabled-option",s.displayNotificationAlert).on("click",'[post-submissions-disabled="1"]',s.displayPostSubmissionsDisabledAlert).on("wpformsFieldAdd",s.initFieldUserRestrictionsSelects).on("wpformsBeforeSave",s.checkPasswordMatchBeforeSave).on("wpformsBeforeSave",s.checkNotificationsBeforeSave).on("wpformsNotificationFieldAdded",s.notificationFileUploadFieldAdded).on("wpformsNotificationFieldRemoved",s.notificationFileUploadFieldRemoved).on("wpformsNotificationsToggleConditionalChange",s.notificationToggleConditionalChange).on("wpformsNotificationsToggle",s.notificationsToggle).on("wpformsBuilderReady",s.disableRestrictions)},initUserRestrictionsSelects(){a(".wpforms-file-upload-user-roles-select").each(function(){s.initChoicesJS(a(this)[0],{},wpforms_builder.file_upload.all_user_roles_selected)}),a(".wpforms-file-upload-user-names-select").each(function(){s.initUserNamesSelect(a(this)[0])})},initFieldUserRestrictionsSelects(e,o,i){"file-upload"!==i&&"camera"!==i||(s.initChoicesJS(a(`#wpforms-field-option-${o}-user_roles_restrictions`)[0],{},wpforms_builder.file_upload.all_user_roles_selected),s.initUserNamesSelect(a(`#wpforms-field-option-${o}-user_names_restrictions`)[0]))},initUserNamesSelect(e){var o={action:"wpforms_ajax_search_user_names",nonce:wpforms_builder.nonce};s.initChoicesJS(e,o)},initChoicesJS(e,o={},i=""){let t=WPForms.Admin.Builder.WPFormsChoicesJS.setup(e,{removeItemButton:!0,noChoicesText:i,callbackOnInit(){wpf.showMoreButtonForChoices(this.containerOuter.element)}},o);t.getValue().length||(i=a(e).data("field-id"),o=a(e).data("field-name"),e=s.getHiddenValues(i,o),t.setChoiceByValue(e)),t.passedElement.element.addEventListener("removeItem",function(e){"administrator"===e.detail.value&&t.setChoiceByValue("administrator"),e.target.classList.contains("wpforms-file-upload-user-names-select")&&t.clearChoices()}),t.passedElement.element.addEventListener("addItem",function(e){e.target.classList.contains("wpforms-file-upload-user-names-select")&&(t.hideDropdown(),t.clearChoices())})},getHiddenValues(e,o){e=a(`#wpforms-field-${e}-${o}-select-multiple-options`).val();let i;try{i=JSON.parse(e)}catch(e){i={}}return(i=Object.values(i)).map(function(e){return e.toString()})},mediaLibraryOptionHandler(){var e=a(this),o={mediaLibraryEnabled:e.prop("checked"),accessRestrictionsEnabled:!1,passwordRestrictionsEnabled:!1};s.optionsHandler(e,o)},accessRestrictionsOptionHandler(){var e=a(this),o=e.prop("checked"),i={accessRestrictionsEnabled:o};o?i.mediaLibraryEnabled=!1:i.passwordRestrictionsEnabled=!1,s.optionsHandler(e,i)},passwordRestrictionsOptionHandler(){var e,o=a(this),i={passwordRestrictionsEnabled:o.prop("checked"),accessRestrictionsEnabled:!0};i.passwordRestrictionsEnabled||(e=o.closest(".wpforms-field-option").data("field-id"),a(`#wpforms-field-option-${e}-password_restrictions_clean_button`).addClass("wpforms-hidden"),a(`#wpforms-field-option-${e}-protection_password`).val(""),a(`#wpforms-field-option-${e}-protection_password_confirm`).val("")),s.optionsHandler(o,i)},optionsHandler(e,o={}){e=e.closest(".wpforms-field-option").data("field-id");a(`#wpforms-field-option-${e}-is_restricted`).prop("checked",o.accessRestrictionsEnabled),a(`#wpforms-field-option-${e}-media_library`).prop("checked",o.mediaLibraryEnabled),a(`#wpforms-field-option-${e}-is_protected`).prop("checked",o.passwordRestrictionsEnabled),a(`#wpforms-field-option-row-${e}-user_restrictions`).toggleClass("wpforms-hidden",!o.accessRestrictionsEnabled),a(`#wpforms-field-option-row-${e}-password_restrictions`).toggleClass("wpforms-hidden",!o.accessRestrictionsEnabled),a(`#wpforms-field-option-row-${e}-user_roles_restrictions`).toggleClass("wpforms-hidden",!s.isLoggedInRestrictionSelected(e)||!o.accessRestrictionsEnabled),a(`#wpforms-field-option-row-${e}-user_names_restrictions`).toggleClass("wpforms-hidden",!s.isLoggedInRestrictionSelected(e)||!o.accessRestrictionsEnabled),a(`#wpforms-field-option-row-${e}-protection_password_label`).toggleClass("wpforms-hidden",!o.passwordRestrictionsEnabled),a(`#wpforms-field-option-row-${e}-protection_password_columns`).toggleClass("wpforms-hidden",!o.passwordRestrictionsEnabled)},userRestrictionsOptionHandler(){var e=a(this),o=e.val(),e=e.closest(".wpforms-field-option").data("field-id");a(`#wpforms-field-option-row-${e}-user_roles_restrictions`).toggleClass("wpforms-hidden","logged"!==o),a(`#wpforms-field-option-row-${e}-user_names_restrictions`).toggleClass("wpforms-hidden","logged"!==o)},isLoggedInRestrictionSelected(e){return"logged"===a(`#wpforms-field-option-${e}-user_restrictions`).val()},cleanPasswordButtonHandler(){var e=a(this),o=e.val();e.closest(".wpforms-field-option-row").find(".wpforms-file-upload-password-clean").toggleClass("wpforms-hidden",o.length<1)},sanitizePasswordValue(){var e=a(this),o=e.closest(".wpforms-field-option").data("field-id"),i=e.val(),t=wpf.sanitizeHTML(i);a(`#wpforms-field-option-${o}-protection_password_confirm`).attr("type",i!==t?"text":"password"),e.attr("type",i!==t?"text":"password").val(t)},checkPasswordMatch(){var e=a(this),o=e.val(),i=e.closest(".wpforms-field-option").data("field-id"),t=a(`#wpforms-field-option-${i}-protection_password`).val(),i=a(`#wpforms-field-option-row-${i}-protection_password_confirm_error`);e.removeClass("wpforms-error"),i.addClass("wpforms-hidden"),o!==t&&(e.addClass("wpforms-error"),i.removeClass("wpforms-hidden"))},checkNotificationsBeforeSave(e){var t=a(".wpforms-field-option-file-upload, .wpforms-field-option-camera");if(!(t.length<1)){let o=[],i=s.getNotificationsFileUploadFields();t.each(function(){var e=a(this).data("field-id");a(`#wpforms-field-option-${e}-is_restricted`).prop("checked")&&i.includes(parseInt(e,10))&&o.push(a(`#wpforms-field-option-${e}-label`).val()||""+wpforms_builder.empty_label_alternative_text+e)}),o.length&&(e.preventDefault(),s.displayNotificationRestrictionAlert(o))}},checkPasswordMatchBeforeSave(e){var o=a(".wpforms-field-option-file-upload, .wpforms-field-option-camera");if(!(o.length<1)){let r=[],n=[];o.each(function(){var e,o,i,t,s=a(this).data("field-id");a(`#wpforms-field-option-${s}-is_protected`).prop("checked")&&(e=a(`#wpforms-field-option-${s}-protection_password`).val(),i=(o=a(`#wpforms-field-option-${s}-protection_password_confirm`)).val(),t=a(`#wpforms-field-option-${s}-label`).val()||""+wpforms_builder.empty_label_alternative_text+s,e!==i&&(r.push(t),o.toggleClass("wpforms-error",!0),a(`#wpforms-field-option-row-${s}-protection_password_confirm_error`).removeClass("wpforms-hidden")),""===e)&&""===i&&n.push(t)}),n.length?(e.preventDefault(),s.displayEmptyPasswordAlert(n)):r.length&&(e.preventDefault(),s.displayNotMatchPasswordAlert(r))}},displayNotMatchPasswordAlert(e){e=e.join(", "),e=wpforms_builder.file_upload.password_match_error_text.replace("{fields}",e);s.displayAlert(wpforms_builder.file_upload.password_match_error_title,e)},displayEmptyPasswordAlert(e){e=e.join(", "),e=wpforms_builder.file_upload.password_empty_error_text.replace("{fields}",e);s.displayAlert(wpforms_builder.file_upload.password_empty_error_title,e)},displayNotificationRestrictionAlert(e){e=e.join(", "),e=wpforms_builder.file_upload.notification_error_text.replace("{fields}",e);s.displayAlert(wpforms_builder.file_upload.notification_error_title,e)},displayAlert(e,o){a.alert({title:e,content:o,type:"red",icon:"fa fa-exclamation-triangle",buttons:{confirm:{text:wpforms_builder.close,btnClass:"btn-confirm",keys:["enter"]}}})},cleanPasswordInput(){var e=a(this),o=e.data("field-id");a(`#wpforms-field-option-${o}-protection_password`).val(""),a(`#wpforms-field-option-${o}-protection_password_confirm`).val("").removeClass("wpforms-error"),a(`#wpforms-field-option-row-${o}-protection_password_confirm_error`).addClass("wpforms-hidden"),e.addClass("wpforms-hidden")},disableRestrictions(){var e=s.getNotificationsFileUploadFields();e.length&&e.forEach(function(e){s.disallowRestriction(e)})},disallowRestriction(e){a(`#wpforms-field-option-${e}-is_restricted`).prop("checked")||a(`#wpforms-field-option-row-${e}-access_restrictions`).addClass("wpforms-notifications-disabled-option")},allowRestriction(e){a(`#wpforms-field-option-row-${e}-access_restrictions`).removeClass("wpforms-notifications-disabled-option")},notificationFileUploadFieldAdded(e,o,i){s.isFileUploadAttachmentField(i)&&s.disallowRestriction(o)},notificationFileUploadFieldRemoved(e,o,i){s.isFileUploadAttachmentField(i)&&setTimeout(function(){s.getNotificationsFileUploadFields().includes(parseInt(o,10))||s.allowRestriction(o)},0)},notificationToggleConditionalChange(e,o){var i=s.getNotificationsFileUploadFields(),o=o.map(e=>e.value);s.handleRestrictions(i,o)},notificationsToggle(){var e=WPForms.Admin.Builder.Notifications.choicesJSHelperMethods.getFormFields(["file-upload","camera"]).map(e=>e.value),o=s.getNotificationsFileUploadFields();s.handleRestrictions(o,e)},handleRestrictions(o,e){setTimeout(function(){e.forEach(function(e){o.includes(parseInt(e,10))?s.disallowRestriction(e):s.allowRestriction(e)})},0)},isFileUploadAttachmentField(e){return a(e).hasClass("file_upload_attachment_fields")},getAllNotifications(){return a("#wpforms-panel-field-settings-notification_enable").prop("checked")&&a(".wpforms-notification")||[]},getNotificationsFileUploadFields(){let i=[];var e=s.getAllNotifications();return e.length?(e.each(function(){var e=a(this).data("block-id");if(a(`#wpforms-panel-field-notifications-${e}-file_upload_attachment_enable`).prop("checked")){e=a(`input[name="settings[notifications][${e}][file_upload_attachment_fields][hidden]"]`).val();let o;try{o=JSON.parse(e)}catch(e){o=[]}o.length&&o.forEach(function(e){i.push(e)})}}),i.filter((e,o,i)=>i.indexOf(e)===o)):i},displayNotificationAlert(e){e.preventDefault(),a.alert({title:wpforms_builder.file_upload.notification_warning_title,content:wpforms_builder.file_upload.notification_warning_text,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},displayPostSubmissionsDisabledAlert(e){e.preventDefault(),a.alert({title:wpforms_education.addon_incompatible.title,content:wpforms_builder.file_upload.incompatible_addon_text,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_education.addon_incompatible.button_text,btnClass:"btn-confirm",keys:["enter"],action(){var e;return"undefined"==typeof WPFormsBuilder?location.href=wpforms_education.addon_incompatible.button_url:(this.$$confirm.prop("disabled",!0).html('<i class="wpforms-loading-spinner wpforms-loading-white wpforms-loading-inline"></i>'+this.$$confirm.text()),this.$$cancel.prop("disabled",!0),WPFormsBuilder.formIsSaved()?location.href=wpforms_education.addon_incompatible.button_url:(e=WPFormsBuilder.formSave(!1))&&e.done(function(){location.href=wpforms_education.addon_incompatible.button_url})),!1}},cancel:{text:wpforms_education.cancel}}})}};return s})((document,window,jQuery)),WPForms.Admin.Builder.FieldFileUpload.init();