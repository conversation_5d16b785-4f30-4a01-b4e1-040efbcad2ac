// Education styles.

@media screen and (max-width: 1023px) {
	.wpforms-dyk {
		display: none !important;
	}
}

.wpforms-dyk {
	td {
		position: relative;
		background-color: #ffffff;
		border-top: 1px solid #cbcfd4;
		padding: 10px 10px 10px 14px;
		font-size: 14px;

		&:before {
			content: "";
			position: absolute;
			left: 0;
			top: 0;
			width: 4px;
			height: 100%;
			background-color: #2271b1;
		}
	}

	.wpforms-dyk-fbox {
		align-items: center;
		align-content: stretch;
		justify-content: flex-start;
		display: flex;
		opacity: 1;
		transition: all .3s;

		&.out {
			opacity: 0;
			transform: scaleY(0);
		}
	}

	.wpforms-dyk-bulb {
		width: 25px;
		height: 25px;
		margin-right: 10px;
		border-radius: 50%;
		fill: #ffffff;
		background-color: #2271b1;
		padding: 8px;
	}

	.wpforms-dyk-message {
		color: #50575e;

		b {
			color: #1d2327;
			font-weight: 700;
		}
	}

	.wpforms-dyk-buttons {
		margin-left: auto;
		vertical-align: middle;
		min-width: 280px;
		text-align: right;

		& > a,
		& > button {
			vertical-align: middle;
			margin-left: 10px;
		}

		.wpforms-dismiss-button {
			padding: 0;
			margin-left: 5px;
			background: 0 0;
			border: none;
			color: #a0a5aa;
			cursor: pointer;

			&:before {
				display: block;
				width: 13px;
				height: 13px;
				background: 0 0;
				content: "\f057";
				font: normal 13px/13px FontAwesome;
				text-align: center;
				speak: none;
				-webkit-font-smoothing: antialiased;
			}

			&:hover {
				color: #dc3232;
			}

			&:focus {
				outline: none;
			}
		}

		.learn-more {
			text-decoration: underline;
		}
	}
}
