/**
 * ModSecurity Interface Styles
 * Basic styling for the ModSecurity detection interface
 */
/* Main container */
.wpf-support-helper-content {
  max-width: 800px;
  margin-top: 20px;
}

/* Section styling */
.wpf-support-helper-section {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.wpf-support-helper-section h2 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #23282d;
  font-size: 1.3em;
  font-weight: 600;
}

.wpf-support-helper-section h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  color: #0073aa;
  font-size: 1.1em;
  font-weight: 600;
}

.wpf-support-helper-section p {
  margin-bottom: 15px;
  line-height: 1.5;
  color: #555;
}

/* Status indicators */
.wpf-status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-inline-end: 8px;
}

.wpf-status-badge.status-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.wpf-status-badge.status-failed {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.wpf-status-badge.status-skipped {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.wpf-status-badge.status-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Detection methods list */
.wpf-detection-methods {
  margin-top: 15px;
}

.wpf-detection-methods .method-item {
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 8px;
  background: #fff;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  transition: border-color 0.2s ease;
}

.wpf-detection-methods .method-item:hover {
  border-color: #c3c4c7;
}

.wpf-detection-methods .method-item .method-name {
  flex: 1;
  font-weight: 500;
  color: #23282d;
}

.wpf-detection-methods .method-item .method-reason {
  flex: 2;
  color: #666;
  font-size: 0.9em;
  margin-inline-start: 15px;
}

.wpf-detection-methods .method-item.expandable {
  display: block;
  padding: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.wpf-detection-methods .method-item.expandable:hover {
  border-color: #0073aa;
}

.wpf-detection-methods .method-item.expandable .method-toggle {
  display: none;
}

.wpf-detection-methods .method-item.expandable .method-header {
  display: flex;
  align-items: center;
  padding: 10px;
  position: relative;
}

.wpf-detection-methods .method-item.expandable .method-header .method-name {
  flex: 1;
  font-weight: 500;
  color: #23282d;
}

.wpf-detection-methods .method-item.expandable .method-header .method-reason {
  flex: 2;
  color: #666;
  font-size: 0.9em;
  margin-inline-start: 15px;
}

.wpf-detection-methods .method-item.expandable .method-header .expand-arrow {
  margin-inline-start: auto;
  transition: transform 0.2s ease;
  color: #666;
  flex-shrink: 0;
}

.wpf-detection-methods .method-item.expandable .method-additional-info {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  border-top: 1px solid #e1e1e1;
}

.wpf-detection-methods .method-item.expandable .method-additional-info .additional-info-textarea {
  width: 100%;
  min-height: 150px;
  padding: 10px;
  border: none;
  background: #f8f9fa;
  font-family: 'Courier New', Courier, monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  resize: vertical;
  outline: none;
  white-space: pre;
  overflow-wrap: break-word;
}

.wpf-detection-methods .method-item.expandable .method-toggle:checked ~ .method-header .expand-arrow {
  transform: rotate(180deg);
}

.wpf-detection-methods .method-item.expandable .method-toggle:checked ~ .method-additional-info {
  max-height: 500px;
}

/* Summary section */
.wpf-detection-summary {
  background: #f0f6fc;
  border: 1px solid #c8d3e0;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.wpf-detection-summary .summary-title {
  margin: 0 0 10px 0;
  color: #0969da;
  font-size: 1.1em;
  font-weight: 600;
}

.wpf-detection-summary .summary-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.wpf-detection-summary .summary-stats .stat-item .stat-number {
  display: block;
  font-size: 1.5em;
  font-weight: 700;
  color: #23282d;
}

.wpf-detection-summary .summary-stats .stat-item .stat-label {
  display: block;
  font-size: 0.9em;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Error message styling */
.wpf-error-message {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
}

.wpf-error-message .error-title {
  font-weight: 600;
  margin-bottom: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  .wpf-support-helper-content {
    margin: 10px;
    max-width: none;
  }
  .wpf-detection-methods .method-item {
    flex-direction: column;
    align-items: flex-start;
  }
  .wpf-detection-methods .method-item .method-reason {
    margin-inline-start: 0;
    margin-top: 5px;
  }
  .wpf-detection-summary .summary-stats {
    flex-direction: column;
    gap: 10px;
  }
  .wpf-http-headers .header-item {
    flex-direction: column;
  }
  .wpf-http-headers .header-item .header-name {
    flex: none;
    margin-bottom: 5px;
  }
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibW9kc2VjdXJpdHktaW50ZXJmYWNlLmNzcyIsInNvdXJjZXMiOlsibW9kc2VjdXJpdHktaW50ZXJmYWNlLnNjc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBNb2RTZWN1cml0eSBJbnRlcmZhY2UgU3R5bGVzXG4gKiBCYXNpYyBzdHlsaW5nIGZvciB0aGUgTW9kU2VjdXJpdHkgZGV0ZWN0aW9uIGludGVyZmFjZVxuICovXG5cbi8qIE1haW4gY29udGFpbmVyICovXG4ud3BmLXN1cHBvcnQtaGVscGVyLWNvbnRlbnQge1xuICAgIG1heC13aWR0aDogODAwcHg7XG4gICAgbWFyZ2luLXRvcDogMjBweDtcbn1cblxuLyogU2VjdGlvbiBzdHlsaW5nICovXG4ud3BmLXN1cHBvcnQtaGVscGVyLXNlY3Rpb24ge1xuICAgIGJhY2tncm91bmQ6ICNmZmY7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2NjZDBkNDtcbiAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgcGFkZGluZzogMjBweDtcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICAgIGJveC1zaGFkb3c6IDAgMXB4IDFweCByZ2JhKDAsIDAsIDAsIDAuMDQpO1xuXG4gICAgaDIge1xuICAgICAgICBtYXJnaW4tdG9wOiAwO1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4O1xuICAgICAgICBjb2xvcjogIzIzMjgyZDtcbiAgICAgICAgZm9udC1zaXplOiAxLjNlbTtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICB9XG5cbiAgICBoMyB7XG4gICAgICAgIG1hcmdpbi10b3A6IDIwcHg7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7XG4gICAgICAgIGNvbG9yOiAjMDA3M2FhO1xuICAgICAgICBmb250LXNpemU6IDEuMWVtO1xuICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgIH1cblxuICAgIHAge1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4O1xuICAgICAgICBsaW5lLWhlaWdodDogMS41O1xuICAgICAgICBjb2xvcjogIzU1NTtcbiAgICB9XG59XG5cbi8qIFN0YXR1cyBpbmRpY2F0b3JzICovXG4ud3BmLXN0YXR1cy1iYWRnZSB7XG4gICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuICAgIHBhZGRpbmc6IDRweCA4cHg7XG4gICAgYm9yZGVyLXJhZGl1czogM3B4O1xuICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG4gICAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4O1xuXHRtYXJnaW4taW5saW5lLWVuZDogOHB4O1xuXG5cdCYuc3RhdHVzLXN1Y2Nlc3Mge1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZDRlZGRhO1xuICAgICAgICBjb2xvcjogIzE1NTcyNDtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2MzZTZjYjtcbiAgICB9XG5cbiAgICAmLnN0YXR1cy1mYWlsZWQge1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhkN2RhO1xuICAgICAgICBjb2xvcjogIzcyMWMyNDtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Y1YzZjYjtcbiAgICB9XG5cbiAgICAmLnN0YXR1cy1za2lwcGVkIHtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjNjZDtcbiAgICAgICAgY29sb3I6ICM4NTY0MDQ7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNmZmVhYTc7XG4gICAgfVxuXG4gICAgJi5zdGF0dXMtZXJyb3Ige1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhkN2RhO1xuICAgICAgICBjb2xvcjogIzcyMWMyNDtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Y1YzZjYjtcbiAgICB9XG59XG5cbi8qIERldGVjdGlvbiBtZXRob2RzIGxpc3QgKi9cbi53cGYtZGV0ZWN0aW9uLW1ldGhvZHMge1xuICAgIG1hcmdpbi10b3A6IDE1cHg7XG5cbiAgICAubWV0aG9kLWl0ZW0ge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBwYWRkaW5nOiAxMHB4O1xuICAgICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XG4gICAgICAgIGJhY2tncm91bmQ6ICNmZmY7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMWUxZTE7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgICAgdHJhbnNpdGlvbjogYm9yZGVyLWNvbG9yIDAuMnMgZWFzZTtcblxuICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2MzYzRjNztcbiAgICAgICAgfVxuXG4gICAgICAgIC5tZXRob2QtbmFtZSB7XG4gICAgICAgICAgICBmbGV4OiAxO1xuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgICAgIGNvbG9yOiAjMjMyODJkO1xuICAgICAgICB9XG5cbiAgICAgICAgLm1ldGhvZC1yZWFzb24ge1xuICAgICAgICAgICAgZmxleDogMjtcbiAgICAgICAgICAgIGNvbG9yOiAjNjY2O1xuICAgICAgICAgICAgZm9udC1zaXplOiAwLjllbTtcblx0XHRcdG1hcmdpbi1pbmxpbmUtc3RhcnQ6IDE1cHg7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBFeHBhbmRhYmxlIG1ldGhvZCBpdGVtIHN0eWxlc1xuICAgICAgICAmLmV4cGFuZGFibGUge1xuICAgICAgICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICAgICAgICBwYWRkaW5nOiAwO1xuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcblxuICAgICAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjMDA3M2FhO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBIaWRlIHRoZSBjaGVja2JveFxuICAgICAgICAgICAgLm1ldGhvZC10b2dnbGUge1xuICAgICAgICAgICAgICAgIGRpc3BsYXk6IG5vbmU7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIE1ldGhvZCBoZWFkZXIgKGFsd2F5cyB2aXNpYmxlKVxuICAgICAgICAgICAgLm1ldGhvZC1oZWFkZXIge1xuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAxMHB4O1xuICAgICAgICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcblxuICAgICAgICAgICAgICAgIC5tZXRob2QtbmFtZSB7XG4gICAgICAgICAgICAgICAgICAgIGZsZXg6IDE7XG4gICAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjMjMyODJkO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIC5tZXRob2QtcmVhc29uIHtcbiAgICAgICAgICAgICAgICAgICAgZmxleDogMjtcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM2NjY7XG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45ZW07XG5cdFx0XHRcdFx0bWFyZ2luLWlubGluZS1zdGFydDogMTVweDtcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAvLyBFeHBhbmQgYXJyb3dcbiAgICAgICAgICAgICAgICAuZXhwYW5kLWFycm93IHtcblx0XHRcdFx0XHRtYXJnaW4taW5saW5lLXN0YXJ0OiBhdXRvO1xuICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlO1xuICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzY2NjtcbiAgICAgICAgICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBBZGRpdGlvbmFsIGluZm8gc2VjdGlvbiAoaGlkZGVuIGJ5IGRlZmF1bHQpXG4gICAgICAgICAgICAubWV0aG9kLWFkZGl0aW9uYWwtaW5mbyB7XG4gICAgICAgICAgICAgICAgbWF4LWhlaWdodDogMDtcbiAgICAgICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgICAgICAgICAgICAgIHRyYW5zaXRpb246IG1heC1oZWlnaHQgMC4zcyBlYXNlO1xuICAgICAgICAgICAgICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTFlMWUxO1xuXG4gICAgICAgICAgICAgICAgLmFkZGl0aW9uYWwtaW5mby10ZXh0YXJlYSB7XG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgICAgICAgICAgICBtaW4taGVpZ2h0OiAxNTBweDtcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogMTBweDtcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICAgICAgICAgICAgICAgICAgICBmb250LWZhbWlseTogJ0NvdXJpZXIgTmV3JywgQ291cmllciwgbW9ub3NwYWNlO1xuICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICAgICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjQ7XG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjMzMzO1xuICAgICAgICAgICAgICAgICAgICByZXNpemU6IHZlcnRpY2FsO1xuICAgICAgICAgICAgICAgICAgICBvdXRsaW5lOiBub25lO1xuICAgICAgICAgICAgICAgICAgICB3aGl0ZS1zcGFjZTogcHJlO1xuICAgICAgICAgICAgICAgICAgICBvdmVyZmxvdy13cmFwOiBicmVhay13b3JkO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8gV2hlbiBjaGVja2JveCBpcyBjaGVja2VkIChleHBhbmRlZCBzdGF0ZSlcbiAgICAgICAgICAgIC5tZXRob2QtdG9nZ2xlOmNoZWNrZWQgfiAubWV0aG9kLWhlYWRlciAuZXhwYW5kLWFycm93IHtcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IHJvdGF0ZSgxODBkZWcpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAubWV0aG9kLXRvZ2dsZTpjaGVja2VkIH4gLm1ldGhvZC1hZGRpdGlvbmFsLWluZm8ge1xuICAgICAgICAgICAgICAgIG1heC1oZWlnaHQ6IDUwMHB4OyAvLyBBZGp1c3QgYXMgbmVlZGVkXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG59XG5cbi8qIFN1bW1hcnkgc2VjdGlvbiAqL1xuLndwZi1kZXRlY3Rpb24tc3VtbWFyeSB7XG4gICAgYmFja2dyb3VuZDogI2YwZjZmYztcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjYzhkM2UwO1xuICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICBwYWRkaW5nOiAxNXB4O1xuICAgIG1hcmdpbi1ib3R0b206IDIwcHg7XG5cbiAgICAuc3VtbWFyeS10aXRsZSB7XG4gICAgICAgIG1hcmdpbjogMCAwIDEwcHggMDtcbiAgICAgICAgY29sb3I6ICMwOTY5ZGE7XG4gICAgICAgIGZvbnQtc2l6ZTogMS4xZW07XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgfVxuXG4gICAgLnN1bW1hcnktc3RhdHMge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBnYXA6IDIwcHg7XG4gICAgICAgIGZsZXgtd3JhcDogd3JhcDtcblxuICAgICAgICAuc3RhdC1pdGVtIHtcbiAgICAgICAgICAgIC5zdGF0LW51bWJlciB7XG4gICAgICAgICAgICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxLjVlbTtcbiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNzAwO1xuICAgICAgICAgICAgICAgIGNvbG9yOiAjMjMyODJkO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAuc3RhdC1sYWJlbCB7XG4gICAgICAgICAgICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjllbTtcbiAgICAgICAgICAgICAgICBjb2xvcjogIzY2NjtcbiAgICAgICAgICAgICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICAgICAgICAgICAgICAgIGxldHRlci1zcGFjaW5nOiAwLjVweDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbn1cblxuLyogRXJyb3IgbWVzc2FnZSBzdHlsaW5nICovXG4ud3BmLWVycm9yLW1lc3NhZ2Uge1xuICAgIGJhY2tncm91bmQ6ICNmOGQ3ZGE7XG4gICAgY29sb3I6ICM3MjFjMjQ7XG4gICAgYm9yZGVyOiAxcHggc29saWQgI2Y1YzZjYjtcbiAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgcGFkZGluZzogMTVweDtcbiAgICBtYXJnaW46IDE1cHggMDtcblxuICAgIC5lcnJvci10aXRsZSB7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICB9XG59XG5cbi8qIFJlc3BvbnNpdmUgZGVzaWduICovXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAud3BmLXN1cHBvcnQtaGVscGVyLWNvbnRlbnQge1xuICAgICAgICBtYXJnaW46IDEwcHg7XG4gICAgICAgIG1heC13aWR0aDogbm9uZTtcbiAgICB9XG5cbiAgICAud3BmLWRldGVjdGlvbi1tZXRob2RzIC5tZXRob2QtaXRlbSB7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuXG4gICAgICAgIC5tZXRob2QtcmVhc29uIHtcblx0XHRcdG1hcmdpbi1pbmxpbmUtc3RhcnQ6IDA7XG4gICAgICAgICAgICBtYXJnaW4tdG9wOiA1cHg7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAud3BmLWRldGVjdGlvbi1zdW1tYXJ5IC5zdW1tYXJ5LXN0YXRzIHtcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgZ2FwOiAxMHB4O1xuICAgIH1cblxuICAgIC53cGYtaHR0cC1oZWFkZXJzIC5oZWFkZXItaXRlbSB7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG5cbiAgICAgICAgLmhlYWRlci1uYW1lIHtcbiAgICAgICAgICAgIGZsZXg6IG5vbmU7XG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiA1cHg7XG4gICAgICAgIH1cbiAgICB9XG59XG5cbiJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7O0dBR0c7QUFFSCxvQkFBb0I7QUFDcEIsQUFBQSwyQkFBMkIsQ0FBQztFQUN4QixTQUFTLEVBQUUsS0FBSztFQUNoQixVQUFVLEVBQUUsSUFBSTtDQUNuQjs7QUFFRCxxQkFBcUI7QUFDckIsQUFBQSwyQkFBMkIsQ0FBQztFQUN4QixVQUFVLEVBQUUsSUFBSTtFQUNoQixNQUFNLEVBQUUsaUJBQWlCO0VBQ3pCLGFBQWEsRUFBRSxHQUFHO0VBQ2xCLE9BQU8sRUFBRSxJQUFJO0VBQ2IsYUFBYSxFQUFFLElBQUk7RUFDbkIsVUFBVSxFQUFFLENBQUMsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLG1CQUFtQjtDQXVCNUM7O0FBN0JELEFBUUksMkJBUnVCLENBUXZCLEVBQUUsQ0FBQztFQUNDLFVBQVUsRUFBRSxDQUFDO0VBQ2IsYUFBYSxFQUFFLElBQUk7RUFDbkIsS0FBSyxFQUFFLE9BQU87RUFDZCxTQUFTLEVBQUUsS0FBSztFQUNoQixXQUFXLEVBQUUsR0FBRztDQUNuQjs7QUFkTCxBQWdCSSwyQkFoQnVCLENBZ0J2QixFQUFFLENBQUM7RUFDQyxVQUFVLEVBQUUsSUFBSTtFQUNoQixhQUFhLEVBQUUsSUFBSTtFQUNuQixLQUFLLEVBQUUsT0FBTztFQUNkLFNBQVMsRUFBRSxLQUFLO0VBQ2hCLFdBQVcsRUFBRSxHQUFHO0NBQ25COztBQXRCTCxBQXdCSSwyQkF4QnVCLENBd0J2QixDQUFDLENBQUM7RUFDRSxhQUFhLEVBQUUsSUFBSTtFQUNuQixXQUFXLEVBQUUsR0FBRztFQUNoQixLQUFLLEVBQUUsSUFBSTtDQUNkOztBQUdMLHVCQUF1QjtBQUN2QixBQUFBLGlCQUFpQixDQUFDO0VBQ2QsT0FBTyxFQUFFLFlBQVk7RUFDckIsT0FBTyxFQUFFLE9BQU87RUFDaEIsYUFBYSxFQUFFLEdBQUc7RUFDbEIsU0FBUyxFQUFFLElBQUk7RUFDZixXQUFXLEVBQUUsR0FBRztFQUNoQixjQUFjLEVBQUUsU0FBUztFQUN6QixjQUFjLEVBQUUsS0FBSztFQUN4QixpQkFBaUIsRUFBRSxHQUFHO0NBeUJ0Qjs7QUFqQ0QsQUFVQyxpQkFWZ0IsQUFVZixlQUFlLENBQUM7RUFDVixnQkFBZ0IsRUFBRSxPQUFPO0VBQ3pCLEtBQUssRUFBRSxPQUFPO0VBQ2QsTUFBTSxFQUFFLGlCQUFpQjtDQUM1Qjs7QUFkTCxBQWdCSSxpQkFoQmEsQUFnQlosY0FBYyxDQUFDO0VBQ1osZ0JBQWdCLEVBQUUsT0FBTztFQUN6QixLQUFLLEVBQUUsT0FBTztFQUNkLE1BQU0sRUFBRSxpQkFBaUI7Q0FDNUI7O0FBcEJMLEFBc0JJLGlCQXRCYSxBQXNCWixlQUFlLENBQUM7RUFDYixnQkFBZ0IsRUFBRSxPQUFPO0VBQ3pCLEtBQUssRUFBRSxPQUFPO0VBQ2QsTUFBTSxFQUFFLGlCQUFpQjtDQUM1Qjs7QUExQkwsQUE0QkksaUJBNUJhLEFBNEJaLGFBQWEsQ0FBQztFQUNYLGdCQUFnQixFQUFFLE9BQU87RUFDekIsS0FBSyxFQUFFLE9BQU87RUFDZCxNQUFNLEVBQUUsaUJBQWlCO0NBQzVCOztBQUdMLDRCQUE0QjtBQUM1QixBQUFBLHNCQUFzQixDQUFDO0VBQ25CLFVBQVUsRUFBRSxJQUFJO0NBNEduQjs7QUE3R0QsQUFHSSxzQkFIa0IsQ0FHbEIsWUFBWSxDQUFDO0VBQ1QsT0FBTyxFQUFFLElBQUk7RUFDYixXQUFXLEVBQUUsTUFBTTtFQUNuQixPQUFPLEVBQUUsSUFBSTtFQUNiLGFBQWEsRUFBRSxHQUFHO0VBQ2xCLFVBQVUsRUFBRSxJQUFJO0VBQ2hCLE1BQU0sRUFBRSxpQkFBaUI7RUFDekIsYUFBYSxFQUFFLEdBQUc7RUFDbEIsVUFBVSxFQUFFLHNCQUFzQjtDQWlHckM7O0FBNUdMLEFBYVEsc0JBYmMsQ0FHbEIsWUFBWSxBQVVQLE1BQU0sQ0FBQztFQUNKLFlBQVksRUFBRSxPQUFPO0NBQ3hCOztBQWZULEFBaUJRLHNCQWpCYyxDQUdsQixZQUFZLENBY1IsWUFBWSxDQUFDO0VBQ1QsSUFBSSxFQUFFLENBQUM7RUFDUCxXQUFXLEVBQUUsR0FBRztFQUNoQixLQUFLLEVBQUUsT0FBTztDQUNqQjs7QUFyQlQsQUF1QlEsc0JBdkJjLENBR2xCLFlBQVksQ0FvQlIsY0FBYyxDQUFDO0VBQ1gsSUFBSSxFQUFFLENBQUM7RUFDUCxLQUFLLEVBQUUsSUFBSTtFQUNYLFNBQVMsRUFBRSxLQUFLO0VBQ3pCLG1CQUFtQixFQUFFLElBQUk7Q0FDbkI7O0FBNUJULEFBK0JRLHNCQS9CYyxDQUdsQixZQUFZLEFBNEJQLFdBQVcsQ0FBQztFQUNULE9BQU8sRUFBRSxLQUFLO0VBQ2QsT0FBTyxFQUFFLENBQUM7RUFDVixNQUFNLEVBQUUsT0FBTztFQUNmLFVBQVUsRUFBRSxhQUFhO0NBd0U1Qjs7QUEzR1QsQUFxQ1ksc0JBckNVLENBR2xCLFlBQVksQUE0QlAsV0FBVyxBQU1QLE1BQU0sQ0FBQztFQUNKLFlBQVksRUFBRSxPQUFPO0NBQ3hCOztBQXZDYixBQTBDWSxzQkExQ1UsQ0FHbEIsWUFBWSxBQTRCUCxXQUFXLENBV1IsY0FBYyxDQUFDO0VBQ1gsT0FBTyxFQUFFLElBQUk7Q0FDaEI7O0FBNUNiLEFBK0NZLHNCQS9DVSxDQUdsQixZQUFZLEFBNEJQLFdBQVcsQ0FnQlIsY0FBYyxDQUFDO0VBQ1gsT0FBTyxFQUFFLElBQUk7RUFDYixXQUFXLEVBQUUsTUFBTTtFQUNuQixPQUFPLEVBQUUsSUFBSTtFQUNiLFFBQVEsRUFBRSxRQUFRO0NBc0JyQjs7QUF6RWIsQUFxRGdCLHNCQXJETSxDQUdsQixZQUFZLEFBNEJQLFdBQVcsQ0FnQlIsY0FBYyxDQU1WLFlBQVksQ0FBQztFQUNULElBQUksRUFBRSxDQUFDO0VBQ1AsV0FBVyxFQUFFLEdBQUc7RUFDaEIsS0FBSyxFQUFFLE9BQU87Q0FDakI7O0FBekRqQixBQTJEZ0Isc0JBM0RNLENBR2xCLFlBQVksQUE0QlAsV0FBVyxDQWdCUixjQUFjLENBWVYsY0FBYyxDQUFDO0VBQ1gsSUFBSSxFQUFFLENBQUM7RUFDUCxLQUFLLEVBQUUsSUFBSTtFQUNYLFNBQVMsRUFBRSxLQUFLO0VBQy9CLG1CQUFtQixFQUFFLElBQUk7Q0FDYjs7QUFoRWpCLEFBbUVnQixzQkFuRU0sQ0FHbEIsWUFBWSxBQTRCUCxXQUFXLENBZ0JSLGNBQWMsQ0FvQlYsYUFBYSxDQUFDO0VBQ3pCLG1CQUFtQixFQUFFLElBQUk7RUFDVixVQUFVLEVBQUUsbUJBQW1CO0VBQy9CLEtBQUssRUFBRSxJQUFJO0VBQ1gsV0FBVyxFQUFFLENBQUM7Q0FDakI7O0FBeEVqQixBQTRFWSxzQkE1RVUsQ0FHbEIsWUFBWSxBQTRCUCxXQUFXLENBNkNSLHVCQUF1QixDQUFDO0VBQ3BCLFVBQVUsRUFBRSxDQUFDO0VBQ2IsUUFBUSxFQUFFLE1BQU07RUFDaEIsVUFBVSxFQUFFLG9CQUFvQjtFQUNoQyxVQUFVLEVBQUUsaUJBQWlCO0NBaUJoQzs7QUFqR2IsQUFrRmdCLHNCQWxGTSxDQUdsQixZQUFZLEFBNEJQLFdBQVcsQ0E2Q1IsdUJBQXVCLENBTW5CLHlCQUF5QixDQUFDO0VBQ3RCLEtBQUssRUFBRSxJQUFJO0VBQ1gsVUFBVSxFQUFFLEtBQUs7RUFDakIsT0FBTyxFQUFFLElBQUk7RUFDYixNQUFNLEVBQUUsSUFBSTtFQUNaLFVBQVUsRUFBRSxPQUFPO0VBQ25CLFdBQVcsRUFBRSxpQ0FBaUM7RUFDOUMsU0FBUyxFQUFFLElBQUk7RUFDZixXQUFXLEVBQUUsR0FBRztFQUNoQixLQUFLLEVBQUUsSUFBSTtFQUNYLE1BQU0sRUFBRSxRQUFRO0VBQ2hCLE9BQU8sRUFBRSxJQUFJO0VBQ2IsV0FBVyxFQUFFLEdBQUc7RUFDaEIsYUFBYSxFQUFFLFVBQVU7Q0FDNUI7O0FBaEdqQixBQW9HWSxzQkFwR1UsQ0FHbEIsWUFBWSxBQTRCUCxXQUFXLENBcUVSLGNBQWMsQUFBQSxRQUFRLEdBQUcsY0FBYyxDQUFDLGFBQWEsQ0FBQztFQUNsRCxTQUFTLEVBQUUsY0FBYztDQUM1Qjs7QUF0R2IsQUF3R1ksc0JBeEdVLENBR2xCLFlBQVksQUE0QlAsV0FBVyxDQXlFUixjQUFjLEFBQUEsUUFBUSxHQUFHLHVCQUF1QixDQUFDO0VBQzdDLFVBQVUsRUFBRSxLQUFLO0NBQ3BCOztBQUtiLHFCQUFxQjtBQUNyQixBQUFBLHNCQUFzQixDQUFDO0VBQ25CLFVBQVUsRUFBRSxPQUFPO0VBQ25CLE1BQU0sRUFBRSxpQkFBaUI7RUFDekIsYUFBYSxFQUFFLEdBQUc7RUFDbEIsT0FBTyxFQUFFLElBQUk7RUFDYixhQUFhLEVBQUUsSUFBSTtDQStCdEI7O0FBcENELEFBT0ksc0JBUGtCLENBT2xCLGNBQWMsQ0FBQztFQUNYLE1BQU0sRUFBRSxVQUFVO0VBQ2xCLEtBQUssRUFBRSxPQUFPO0VBQ2QsU0FBUyxFQUFFLEtBQUs7RUFDaEIsV0FBVyxFQUFFLEdBQUc7Q0FDbkI7O0FBWkwsQUFjSSxzQkFka0IsQ0FjbEIsY0FBYyxDQUFDO0VBQ1gsT0FBTyxFQUFFLElBQUk7RUFDYixHQUFHLEVBQUUsSUFBSTtFQUNULFNBQVMsRUFBRSxJQUFJO0NBa0JsQjs7QUFuQ0wsQUFvQlksc0JBcEJVLENBY2xCLGNBQWMsQ0FLVixVQUFVLENBQ04sWUFBWSxDQUFDO0VBQ1QsT0FBTyxFQUFFLEtBQUs7RUFDZCxTQUFTLEVBQUUsS0FBSztFQUNoQixXQUFXLEVBQUUsR0FBRztFQUNoQixLQUFLLEVBQUUsT0FBTztDQUNqQjs7QUF6QmIsQUEyQlksc0JBM0JVLENBY2xCLGNBQWMsQ0FLVixVQUFVLENBUU4sV0FBVyxDQUFDO0VBQ1IsT0FBTyxFQUFFLEtBQUs7RUFDZCxTQUFTLEVBQUUsS0FBSztFQUNoQixLQUFLLEVBQUUsSUFBSTtFQUNYLGNBQWMsRUFBRSxTQUFTO0VBQ3pCLGNBQWMsRUFBRSxLQUFLO0NBQ3hCOztBQUtiLDJCQUEyQjtBQUMzQixBQUFBLGtCQUFrQixDQUFDO0VBQ2YsVUFBVSxFQUFFLE9BQU87RUFDbkIsS0FBSyxFQUFFLE9BQU87RUFDZCxNQUFNLEVBQUUsaUJBQWlCO0VBQ3pCLGFBQWEsRUFBRSxHQUFHO0VBQ2xCLE9BQU8sRUFBRSxJQUFJO0VBQ2IsTUFBTSxFQUFFLE1BQU07Q0FNakI7O0FBWkQsQUFRSSxrQkFSYyxDQVFkLFlBQVksQ0FBQztFQUNULFdBQVcsRUFBRSxHQUFHO0VBQ2hCLGFBQWEsRUFBRSxHQUFHO0NBQ3JCOztBQUdMLHVCQUF1QjtBQUN2QixNQUFNLEVBQUUsU0FBUyxFQUFFLEtBQUs7RUFDcEIsQUFBQSwyQkFBMkIsQ0FBQztJQUN4QixNQUFNLEVBQUUsSUFBSTtJQUNaLFNBQVMsRUFBRSxJQUFJO0dBQ2xCO0VBRUQsQUFBQSxzQkFBc0IsQ0FBQyxZQUFZLENBQUM7SUFDaEMsY0FBYyxFQUFFLE1BQU07SUFDdEIsV0FBVyxFQUFFLFVBQVU7R0FNMUI7RUFSRCxBQUlJLHNCQUprQixDQUFDLFlBQVksQ0FJL0IsY0FBYyxDQUFDO0lBQ3BCLG1CQUFtQixFQUFFLENBQUM7SUFDYixVQUFVLEVBQUUsR0FBRztHQUNsQjtFQUdMLEFBQUEsc0JBQXNCLENBQUMsY0FBYyxDQUFDO0lBQ2xDLGNBQWMsRUFBRSxNQUFNO0lBQ3RCLEdBQUcsRUFBRSxJQUFJO0dBQ1o7RUFFRCxBQUFBLGlCQUFpQixDQUFDLFlBQVksQ0FBQztJQUMzQixjQUFjLEVBQUUsTUFBTTtHQU16QjtFQVBELEFBR0ksaUJBSGEsQ0FBQyxZQUFZLENBRzFCLFlBQVksQ0FBQztJQUNULElBQUksRUFBRSxJQUFJO0lBQ1YsYUFBYSxFQUFFLEdBQUc7R0FDckIifQ== */
