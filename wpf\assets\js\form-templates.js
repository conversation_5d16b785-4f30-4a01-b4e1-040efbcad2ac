/* global wpf_form_templates */

/**
 * Form Templates.
 *
 * @since 0.24
 */
const WPFFormTemplates = window.WPFFormTemplates || ( function( document, window, $ ) {
	/**
	 * Public functions and properties.
	 *
	 * @since 0.24
	 *
	 * @type {Object}
	 */
	const app = {

		/**
		 * Start the engine.
		 *
		 * @since 0.24
		 */
		init() {
			$( app.ready );
		},

		/**
		 * Document ready.
		 *
		 * @since 0.24
		 */
		ready() {
			app.events();
		},

		/**
		 * Events.
		 *
		 * @since 0.24
		 */
		events() {
			$( '.wpforms-form-templates' ).on( 'click', app.selectTemplate );
		},

		/**
		 * Create form from template.
		 *
		 * @since 0.24
		 *
		 * @param {Object} e Event object.
		 */
		selectTemplate( e ) {
			e.preventDefault();

			const $this = $( this ).find( 'a' ).attr( 'href' ),
				urlParams = new URLSearchParams( $this ),
				formTemplates = urlParams.get( 'form-template' ),
				formTitle = urlParams.get( 'form-title' );

			app.selectTemplateProcessAjax( formTitle, formTemplates );
		},

		/**
		 * Select template. Create or update form AJAX call.
		 *
		 * @since 0.24
		 *
		 * @param {string} formName Name of the form.
		 * @param {string} template Template slug.
		 */
		selectTemplateProcessAjax( formName, template ) {
			const data = {
				title: formName,
				action: 'wpforms_new_form',
				template,
				// eslint-disable-next-line camelcase
				form_id: 0,
				// eslint-disable-next-line camelcase
				nonce: wpf_form_templates.nonce,
			};

			// eslint-disable-next-line camelcase
			$.post( wpf_form_templates.ajax_url, data )
				.done( function( res ) {
					if ( res.success ) {
						window.location.href = res.data.redirect;
					} else {
						// eslint-disable-next-line no-alert
						alert( res.data.message );
					}
				} )
				.fail( function( xhr, textStatus ) {
					// eslint-disable-next-line no-console
					console.log( textStatus );
					// eslint-disable-next-line no-console
					console.log( xhr );
				} );
		},
	};

	// Provide access to public functions/properties.
	return app;
}( document, window, jQuery ) );

// Initialize.
WPFFormTemplates.init();
