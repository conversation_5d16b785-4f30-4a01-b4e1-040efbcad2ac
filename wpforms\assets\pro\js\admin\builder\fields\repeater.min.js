var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.FieldRepeater=WPForms.Admin.Builder.FieldRepeater||((r,e,s)=>{let d=wpforms_builder.repeater,a={},n={},p={init(){s(p.ready)},ready(){p.setup(),p.hooks(),p.events()},setup(){a={$builder:s("#wpforms-builder")}},initFields(){a.$builder.find(".wpforms-field-repeater").each(function(){var e=s(this).data("field-id");p.adjustRowsAppearance(e)})},hooks(){wp.hooks.addFilter("wpforms.LayoutField.isFieldAllowedDragInColumn","wpforms/field-repeater",p.filterIsFieldAllowedDragInColumn),wp.hooks.addFilter("wpforms.LayoutField.isFieldAllowedInColumn","wpforms/field-repeater",p.filterIsFieldAllowedInColumn),wp.hooks.addFilter("wpforms.LayoutField.fieldMoveRejectedModalOptions","wpforms/field-repeater",p.filterFieldMoveRejectedModalOptions),wp.hooks.addFilter("wpforms.ConditionalLogicCore.BeforeRemoveUnsupportedFields","wpforms/field-repeater",p.removeRepeaterFieldsAndChildren)},events(){a.$builder.on("click",".wpforms-field-option-row-display input",p.handleDisplayClick).on("change",".wpforms-field-option-row-display input",p.handleDisplayChange).on("change",".wpforms-field-option-row-preset input",p.handlePresetChange).on("change",".wpforms-field-option-row-button-type select",p.handleButtonTypeChange).on("input",".wpforms-field-option-row-label input",p.handleFieldLabelChange).on("input",".wpforms-field-option-row-button-labels input",p.handleButtonLabelsChange).on("change",".wpforms-field-option-row-rows-limit input",p.handleRowsLimitChange).on("wpformsLayoutAfterPresetChange",p.handleAfterPresetChange).on("wpformsLayoutAfterHeightBalance",p.handleAfterHeightBalance).on("wpformsFieldAdd",p.handleFieldAdd).on("wpformsFieldDelete",p.handleFieldDelete).on("wpformsFieldMoveRejected",p.handleFieldMoveRejected).on("wpformsFieldDuplicated",p.handleFieldDuplicated).on("wpformsBuilderReady",p.initFields).on("wpformsFieldOptionTabToggle",p.handleFieldOptionTabToggle).on("wpformsFieldMove",p.handleFieldMove).on("change",".wpforms-field-option-layout .wpforms-field-option-row-conditional_logic input",p.handleUpdateFieldCLOption),s(e).on("resize",_.debounce(p.handleWindowResize,50))},fieldsMappingNotice(t){function e(l){let i={sections:[],$field:null};var e,o;s("select[data-field-map-allowed], select.wpforms-builder-provider-connection-field-value").each(function(){var e,o=s(this);e=o.val(),parseInt(e,10)===parseInt(t,10)&&(i.sections.push(o.closest(".wpforms-panel-content-section").find(".wpforms-panel-content-section-title")[0].firstChild.nodeValue.trim()),i.$field=l)}),i.$field&&(e=[...new Set(i.sections)].join(" "+wpforms_builder.repeater.fields_mapping.and+" "),e=e,o=i.$field,a.$builder.on("wpformsBeforeFieldMapSelectUpdate",e=>e.preventDefault()),s.confirm({title:wpforms_builder.repeater.fields_mapping.title,content:wpforms_builder.repeater.fields_mapping.content.replace("%s",e),icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action:()=>{a.$builder.off("wpformsBeforeFieldMapSelectUpdate");var e=wpf.getFields(!1,!0,!1,!1);s(r).trigger("wpformsFieldUpdate",[e])}},cancel:{text:wpforms_builder.cancel,action:()=>{WPForms.Admin.Builder.DragFields.revertMoveFieldToColumn(o),WPForms.Admin.Builder.FieldLayout.removeFieldFromColumns(o.data("field-id")),p.initFields()}}}}))}var o;!(o=s("#wpforms-field-"+t)).length||o.hasClass("wpforms-field-repeater")||o.hasClass("wpforms-field-layout")||o.closest(".wpforms-field-repeater").length&&e(o)},handleDisplayClick(){var e=s(this);return"rows"!==e.val()||(e=e.closest(".wpforms-field-option-repeater").data("field-id"),!!WPForms.Admin.Builder.FieldLayout.getFieldColumnsData(e).every(e=>e?.fields?.length<=1))||(p.errorModal(d.not_allowed,d.cant_switch_to_rows_alert),!1)},handleDisplayChange(){var e=s(this),o=e.val(),e=e.closest(".wpforms-field-option-repeater"),l=e.data("field-id"),i=s("#wpforms-field-"+l),t=e.find(".wpforms-field-option-row-button-type select").val();e.find(".wpforms-field-option-row-button-type").toggleClass("wpforms-hidden","rows"===o),e.find(".wpforms-field-option-row-button-labels").toggleClass("wpforms-hidden","rows"===o||"icons"===t),i.find(".wpforms-field-layout-columns").toggleClass("wpforms-layout-display-rows","rows"===o).toggleClass("wpforms-layout-display-blocks","blocks"===o),"blocks"===o&&i.find(".wpforms-layout-column-placeholder").css("top",""),i.find(".wpforms-field-repeater-display-rows-buttons").toggleClass("wpforms-hidden","rows"!==o),i.find(".wpforms-field-repeater-display-blocks-buttons").toggleClass("wpforms-hidden","blocks"!==o),p.adjustRowsAppearance(l)},handlePresetChange(){var e=s(this),o=e.val(),e=e.closest(".wpforms-field-option-repeater");s(r).trigger("wpformsLayoutPresetChanged",e),e.find(".wpforms-field-option-row-size").toggleClass("wpforms-disabled","100"!==o)},handleAfterPresetChange(e,o){var l=s("#wpforms-field-"+o.fieldId),i=s(`#wpforms-field-option-row-${o.fieldId}-display input:checked`).val(),t=wp.template("wpforms-repeater-field-display-rows-buttons-template"),i="rows"===i?"":"wpforms-hidden";l.find(".wpforms-field-layout-columns").append(t({class:i})),p.adjustRowsAppearance(o.fieldId)},handleButtonTypeChange(){var e=s(this),o=e.val(),e=e.closest(".wpforms-field-option-repeater"),l=s("#wpforms-field-"+e.data("field-id"));e.find(".wpforms-field-option-row-button-labels").toggleClass("wpforms-hidden","icons"===o),l.find(".wpforms-field-repeater-display-blocks-buttons").attr("data-button-type",o)},handleFieldLabelChange(){var e=s(this).closest(".wpforms-field-option-repeater").data("field-id");p.adjustRowsAppearance(e)},handleWindowResize(){a.$builder.find(".wpforms-field-repeater").each(function(){p.adjustRowsAppearance(s(this).data("field-id"))})},handleButtonLabelsChange(){var e=s(this),o=e.closest(".wpforms-field-option-repeater").data("field-id"),o=s("#wpforms-field-"+o),l=e.val(),e=e.attr("class");o.find(`.wpforms-field-repeater-display-blocks-buttons-${e} span`).text(l)},handleRowsLimitChange(){var e=s(this);"rows-limit-min"===e.attr("class")?p.normalizeLimitMin(e):p.normalizeLimitMax(e),e.val(Math.round(e.val()))},normalizeLimitMin(e){var o=e.val(),l=parseInt(o,10)||0;""===o||l<1?e.val(1):(o=e.closest(".wpforms-field-option-row-rows-limit").find("input.rows-limit-max"),(o=parseInt(o.val(),10))<=l&&e.val(o-1))},normalizeLimitMax(e){var o=e.val(),l=e.closest(".wpforms-field-option-row-rows-limit").find("input.rows-limit-min"),l=parseInt(l.val(),10);""===o?e.val(l+(1===l?9:10)):(o=parseInt(o,10))<=l?e.val(l+1):o>d.rows_limit_max&&e.val(d.rows_limit_max)},handleAfterHeightBalance(e,o){o?.$rows&&(o=o.$rows.closest(".wpforms-field-repeater").data("field-id"))&&p.adjustRowsAppearance(o)},handleFieldAdd(e,o,l){o&&l&&0!==(l=s("#wpforms-field-"+o).closest(".wpforms-field-repeater")).length&&p.adjustRowsAppearance(l.data("field-id"))},handleFieldDelete(e,o,l,i){var t;0!==i.length&&(t=i.closest(".wpforms-field-repeater"),p.adjustRowsAppearance(t.data("field-id")),i.closest(".wpforms-field-layout").removeClass("wpforms-field-child-hovered"))},handleFieldMoveRejected(e,o){p.adjustRowsAppearance(o.closest(".wpforms-field-repeater").data("field-id"))},handleFieldDuplicated(e,o,l,i,t){"repeater"===l.data("field-type")&&(l=s(`#wpforms-field-option-${o} .wpforms-field-option-row-display input:checked`).val(),s(`#wpforms-field-option-${i}-display-`+l).prop("checked",!0),p.adjustRowsAppearance(i))},handleFieldOptionTabToggle(e,o){p.updateFieldCLOption(o),p.updateFieldCalculationOption(o),p.updateFieldGeolocationRequirementsAlerts(o),p.updateFieldSignatureRequirementsAlerts(o)},handleFieldMove(e,o){o=o.item.first().data("field-id");p.updateFieldCLOption(o),p.updateFieldCalculationOption(o),p.updateFieldGeolocationRequirementsAlerts(o),p.updateFieldSignatureRequirementsAlerts(o),p.fieldsMappingNotice(o)},handleUpdateFieldCLOption(){var e=s(this).parents(".wpforms-field-option-row").data("field-id"),e=s("#wpforms-field-"+e);e.length&&e.find(".wpforms-field").each(function(){p.updateFieldCLOption(s(this).data("field-id"))})},updateFieldGeolocationRequirementsAlerts(o){var l=s("#wpforms-field-"+o);if(!(!l?.length||l.hasClass("wpforms-field-repeater")||p.isInsideRepeaterAddonAllowed("wpforms-geolocation")||"undefined"!=typeof wpforms_addons&&wpforms_addons["wpforms-geolocation"])){l=0<l.closest(".wpforms-field-repeater").length,o=s(`#wpforms-field-option-row-${o}-enable_address_autocomplete`);let e=o.siblings(".wpforms-alert-field-requirements");e.length||(e=s(wpforms_builder.repeater.addons_requirements_alert["wpforms-geolocation"]),o.before(e)),e.toggleClass("wpforms-hidden",!l)}},updateFieldSignatureRequirementsAlerts(o){var l=s("#wpforms-field-"+o);if(l?.length&&!l.hasClass("wpforms-field-repeater")&&!p.isInsideRepeaterAddonAllowed("wpforms-signatures")){l=0<l.closest(".wpforms-field-repeater").length,o=s(`.wpforms-field-option-signature #wpforms-field-option-row-${o}-label`);let e=o.siblings(".wpforms-alert-field-requirements");e.length||(e=s(wpforms_builder.repeater.addons_requirements_alert["wpforms-signatures"]),o.before(e)),e.toggleClass("wpforms-hidden",!l)}},isInsideRepeaterAddonAllowed(e){return wpforms_builder.repeater.addons_requirements[e]},updateFieldCalculationOption(o){var l=s("#wpforms-field-"+o);if(!(!l?.length||l.hasClass("wpforms-field-repeater")||"undefined"!=typeof wpforms_addons&&wpforms_addons["wpforms-calculations"])){var l=0<l.closest(".wpforms-field-repeater").length,o=s(`#wpforms-field-option-row-${o}-calculation_is_enabled`),i=o.find("input");let e=o.siblings(".wpforms-notice-field-calculation_is_enabled");e.length||(e=s(`<div class="wpforms-alert wpforms-alert-warning wpforms-notice-field-calculation_is_enabled" title="${d.calculation_notice_tooltip}">
						<p>${d.calculation_notice_text}</p>
					</div>`),o.before(e)),e.find("p").text(i.prop("checked")?d.calculation_notice_text_grp:d.calculation_notice_text),l&&i.prop("checked",!1).trigger("change"),o.toggleClass("wpforms-disabled",l),e.toggleClass("wpforms-hidden",!l)}},updateFieldCLOption(l){var i=s("#wpforms-field-"+l);if(i?.length&&!i.hasClass("wpforms-field-repeater")&&!i.hasClass("wpforms-field-layout")){var t=0<i.closest(".wpforms-field-repeater").length;let e=0<i.closest(".wpforms-field-layout").length;var l=s(`#wpforms-field-option-row-${l}-conditional_logic`),r=l.closest(".wpforms-conditional-block"),d=t?"repeater":"layout";e&&!p.isLayoutCLEnabled(i.closest(".wpforms-field-layout"))&&(e=!1);let o=r.siblings(".wpforms-notice-field-conditional_logic");o.length||(o=s(`<div class="wpforms-alert wpforms-alert-warning wpforms-notice-field-conditional_logic">
						<p>${wpforms_builder[d].cl_notice_text}</p>
					</div>`),r.before(o));i=l.find(".wpforms-toggle-control").find("input"),i=((t||e)&&i.prop("checked",!1),t||e||i.is(":checked")||(l=l.siblings(".wpforms-conditional-groups").length,i.prop("checked",l)),r.find(".wpforms-conditional-groups").length);o.find("p").text(i?wpforms_builder[d].cl_notice_text_grp:wpforms_builder[d].cl_notice_text),r.toggleClass("wpforms-disabled",t||e),o.toggleClass("wpforms-hidden",!t&&!e)}},isLayoutCLEnabled(e){e=e.data("field-id");return s(`#wpforms-field-option-row-${e}-conditional_logic`).find(".wpforms-toggle-control").find("input").is(":checked")},adjustRowsAppearance(e){var o=s("#wpforms-field-"+e+".wpforms-field-repeater");if(o.length){let i=s(`#wpforms-field-option-row-${e}-display input:checked`).val();e=o.find(".wpforms-layout-column");let t=o.find(".wpforms-field-repeater-display-rows-buttons"),r=0;e.each(function(){var e=s(this),o=e.find(".wpforms-field").first(),l=e.find(".wpforms-alert");e.toggleClass("hide-placeholder",(0<o.length||0<l.length)&&"rows"===i),(o.length||l.length)&&(e=o.find(".label-title").height()||0,e=0<l.length?l.height()/2-t.height()/2-4:e,r=e<r||0===r?e:r)});var l=o.find("> .label-title").outerHeight()||20,d=l+30,d=(o.find(".wpforms-field-layout-columns").css("margin-top","-"+d+"px").find(".wpforms-layout-column").css({"padding-top":d,"min-height":d+55}),0===e.find(".wpforms-field-drag-placeholder").length&&(d=o.find(".wpforms-layout-display-rows .wpforms-layout-column-placeholder"),l=0!==r?r+l+47:l+16,t.css("top",l),d.css("top",l+14)),o.find(".wpforms-layout-column.hide-placeholder"));o.find(".wpforms-field-layout-columns").toggleClass("hidden-placeholders",d.length===e.length)}},filterIsFieldAllowedDragInColumn(e,o,l){return n.fieldMoveToRowsRejected=!1,n.fieldTypeRejected=!e&&o,e&&l?.length&&l?.hasClass("wpforms-layout-column")&&(o=l?.closest(".wpforms-field-repeater"))?.length&&(o=o.data("field-id"),"rows"===s(`#wpforms-field-option-row-${o}-display input:checked`).val())&&l.find(".wpforms-field:not(.wpforms-field-dragging)").length?!(n.fieldMoveToRowsRejected=!0):e},filterFieldMoveRejectedModalOptions(e,o,l,i){if(!(i?.closest(".wpforms-field-repeater"))?.length)return e;var t,i={title:d.not_allowed,content:d.move_to_rows_rejected_alert,type:"orange"};if(n.fieldTypeRejected)t=s("#wpforms-add-fields-"+n.fieldTypeRejected).text(),i.content=d.not_allowed_alert_text.replace(/%s/g,`<strong>${t}</strong>`);else{if(!n.fieldMoveToRowsRejected)return e;i.content=d.move_to_rows_rejected_alert}return i},removeRepeaterFieldsAndChildren(e){if(!e)return{};for(var o in e)if("repeater"===e[o].type){var l=e[o]["columns-json"];if(delete e[o],l.length)for(var i of l)if(i.fields?.length)for(var t of i.fields)delete e[t]}return e},errorModal(e,o){s.confirm({title:e,content:o,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},isFieldAllowedInColum(e){return d.not_allowed_fields.indexOf(e)<0},filterIsFieldAllowedInColumn(e,o,l){return e&&l?.length&&l?.hasClass("wpforms-layout-column")&&(l?.closest(".wpforms-field-repeater"))?.length?p.isFieldAllowedInColum(o):e}};return p})(document,window,jQuery),WPForms.Admin.Builder.FieldRepeater.init();