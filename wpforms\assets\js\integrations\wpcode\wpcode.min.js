var WPFormsWPCode=window.WPFormsWPCode||(t=>{let p={spinnerBlue:'<i class="wpforms-loading-spinner wpforms-loading-blue wpforms-loading-inline"></i>',spinnerWhite:'<i class="wpforms-loading-spinner wpforms-loading-white wpforms-loading-inline"></i>',snippetSearch:null,init(){t(p.ready)},ready(){p.snippetSearch=new List("wpforms-wpcode-snippets-list",{valueNames:["wpforms-wpcode-snippet-title"]}),p.events()},events(){t(".wpforms-wpcode-snippet-button").on("click",p.installSnippet),t(".wpforms-wpcode-popup-button").on("click",p.installPlugin),t("#wpforms-wpcode-snippet-search").on("keyup search",function(){p.searchSnippet(this)})},installSnippet(){var i,n=t(this);"edit"!==n.data("action")&&(i=n.width(),n.prev(".wpforms-wpcode-snippet-badge").addClass("wpforms-wpcode-installing-in-progress").text(wpformsWpcodeVars.installing_text),n.width(i).html(p.spinnerBlue))},searchSnippet(i){var i=t(i).val(),i=p.snippetSearch.search(i),n=t("#wpforms-wpcode-no-results");0===i.length?n.show():n.hide()},installPlugin(){var i,n,e,s=t(this);s.hasClass("disabled")||(e=s.attr("data-action"),i=s.attr("data-plugin"),n=JSON.stringify({overwrite_package:!0}),e="activate"===e?"wpforms_activate_addon":"wpforms_install_addon",s.width(s.width()).html(p.spinnerWhite).addClass("disabled"),s={action:e,nonce:wpforms_admin.nonce,plugin:i,args:n,type:"plugin"},t.post(wpforms_admin.ajax_url,s).done(function(){location.reload()}))}};return p})((document,window,jQuery));WPFormsWPCode.init();