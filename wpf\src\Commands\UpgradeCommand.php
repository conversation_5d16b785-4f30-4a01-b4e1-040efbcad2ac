<?php

namespace WPForms\DevTools\Commands;

use WP_CLI;
use WPForms\DevTools\Upgrades;

/**
 * Simulates WPForms upgrades.
 *
 * @since 0.32
 */
class UpgradeCommand {

	/**
	 * Simulate a WPForms upgrade by providing a zip file.
	 *
	 * ## OPTIONS
	 *
	 * <file>
	 * : Path to the WPForms zip file to use for upgrade simulation.
	 *
	 * ## EXAMPLES
	 *
	 *     # Simulate upgrade using a specific version zip file
	 *     $ wp wpforms upgrade simulate /path/to/wpforms-1.9.5.zip
	 *
	 * @since 0.37
	 *
	 * @param array $args Arguments.
	 *
	 * @throws WP_CLI\ExitException Returns error exit code.
	 */
	public function simulate( $args ) {

		if ( empty( $args[0] ) ) {
			WP_CLI::error( 'Please provide a path to the WPForms zip file.' );
		}

		$file_path = $args[0];

		if ( ! file_exists( $file_path ) ) {
			WP_CLI::error( 'The specified file does not exist.' );
		}

		$file = [
			'name'     => basename( $file_path ),
			'type'     => 'application/zip',
			'tmp_name' => $file_path,
			'error'    => 0,
			'size'     => filesize( $file_path ),
		];

		$upgrades = new Upgrades();

		$upgrades->process_plugin_upload_form( $file );

		$notices = $upgrades->get_notices();

		if ( strpos( $notices, 'notice-error' ) !== false ) {
			WP_CLI::error( wp_strip_all_tags( $notices ) );
		}

		WP_CLI::success( wp_strip_all_tags( $notices ) );
	}
}
