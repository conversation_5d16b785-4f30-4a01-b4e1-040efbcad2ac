// Rich Text field styles mixin.
//
// @since 1.7.0
//
// @param $images Path to images.
//
@mixin richtext( $images ) {

	div.wpforms-container-full,
	.wpforms-container {
		.wpforms-form div.wpforms-field-richtext {

			// Emulate TinyMCE toolbar.
			.quicktags-toolbar {
				height: 39px;
				display: block;
				background: #f6f7f7 url( "#{$images}/richtext/tinymce-toolbar-full.png" ) no-repeat left center;
				background-size: auto 34px;
			}

			&.wpforms-field-richtext-toolbar-basic {
				.quicktags-toolbar {
					background-image: url( "#{$images}/richtext/tinymce-toolbar-basic.png" );
				}
			}

			&.wpforms-field-richtext-media-enabled {
				&.wpforms-field-richtext-toolbar-basic {
					.quicktags-toolbar {
						background-image: url("#{$images}/richtext/tinymce-toolbar-basic-mb.png");
					}
				}

				.quicktags-toolbar {
					background-image: url( "#{$images}/richtext/tinymce-toolbar-full-mb.png" );
				}
			}

			// Adjust editor tabs.
			.wp-editor-tabs button {
				pointer-events: none;
				min-height: auto;
			}
		}
	}

	.wpforms-container {
		.wpforms-form div.wpforms-field-richtext {
			.wp-editor-tabs button {
				min-height: 29px;
			}
		}
	}
}
