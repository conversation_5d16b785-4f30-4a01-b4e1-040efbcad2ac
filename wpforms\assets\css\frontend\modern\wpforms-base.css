.wpforms-container .wpforms-error-container,
.wpforms-container .wpforms-error-noscript {
  color: #D63637;
}

.wpforms-container .wpforms-error-styled-container {
  padding: 15px 0;
}

.wpforms-container .wpforms-error-styled-container p {
  margin: 0;
}

.wpforms-container .wpforms-error-styled-container + .wpforms-submit-container {
  margin-top: 10px;
}

.wpforms-container label.wpforms-error,
.wpforms-container em.wpforms-error {
  display: block;
  color: #D63637;
  font-size: 0.9em;
  font-style: normal;
  cursor: default;
  min-width: 120px;
}

.wpforms-container .wpforms-field input.wpforms-error, .wpforms-container .wpforms-field input.user-invalid,
.wpforms-container .wpforms-field textarea.wpforms-error,
.wpforms-container .wpforms-field textarea.user-invalid,
.wpforms-container .wpforms-field select.wpforms-error,
.wpforms-container .wpforms-field select.user-invalid {
  border: 1px solid #D63637;
}

.wpforms-container .wpforms-field input[type=checkbox].wpforms-error, .wpforms-container .wpforms-field input[type=checkbox].user-invalid,
.wpforms-container .wpforms-field input[type=radio].wpforms-error,
.wpforms-container .wpforms-field input[type=radio].user-invalid {
  border: none;
}

.wpforms-container .wpforms-field.wpforms-has-error .choices__inner {
  border: 1px solid #D63637;
}

.wpforms-container .wpforms-error-alert {
  border: 1px solid rgba(0, 0, 0, 0.25);
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  padding: 10px 15px;
  font-size: 14px;
  margin: 0 0 10px 0;
}

.wpforms-container .wpforms-error-alert {
  color: #D63637;
  background-color: #f2dede;
  border-color: #f2dede;
}

div[style*="z-index: 2147483647"] div[style*="border-width: 11px"][style*="position: absolute"][style*="pointer-events: none"] {
  border-style: none;
}

.wpforms-container .wpforms-screen-reader-element {
  position: absolute !important;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  width: 1px;
  border: 0;
  overflow: hidden;
  word-wrap: normal !important;
}

.wpforms-container .wpforms-field-hp {
  display: none !important;
  position: absolute !important;
  left: -9000px !important;
}

.wpforms-container .wpforms-recaptcha-container {
  padding: 0;
  clear: both;
}

.wpforms-container .wpforms-recaptcha-container iframe {
  display: block;
  width: 100%;
  max-width: 100%;
}

.wpforms-container .wpforms-recaptcha-container .g-recaptcha {
  padding: 10px 0 0;
}

.wpforms-container .wpforms-recaptcha-container.wpforms-is-turnstile.wpforms-is-turnstile-invisible {
  padding: 0;
  height: 0;
}

.wpforms-container .wpforms-recaptcha-container.wpforms-is-turnstile iframe {
  position: relative !important;
  visibility: inherit !important;
}

.wpforms-container .wpforms-recaptcha-container.wpforms-is-recaptcha-type-v3 .g-recaptcha, .wpforms-container .wpforms-recaptcha-container.wpforms-is-turnstile-invisible .g-recaptcha, .wpforms-container .wpforms-recaptcha-container.wpforms-is-recaptcha-type-invisible .g-recaptcha {
  padding: 0;
}

.wpforms-container .wpforms-recaptcha-container.wpforms-is-recaptcha-type-v3 .wpforms-error:first-of-type, .wpforms-container .wpforms-recaptcha-container.wpforms-is-turnstile-invisible .wpforms-error:first-of-type, .wpforms-container .wpforms-recaptcha-container.wpforms-is-recaptcha-type-invisible .wpforms-error:first-of-type {
  margin-top: 10px;
}

.wpforms-container amp-img > img {
  position: absolute;
}

.wpforms-container .amp-form-submit-success .wpforms-field-container,
.wpforms-container .amp-form-submit-success .wpforms-submit-container {
  display: none;
}

.wpforms-container .wpforms-preview-notice-links {
  line-height: 2.4;
}

body.rtl .wpforms-container .wpforms-form input[type=tel] {
  direction: ltr;
  text-align: right;
}

body.rtl .wpforms-container .wpforms-form input[type=url] {
  direction: ltr;
  text-align: right;
}

body.rtl .wpforms-container .wpforms-form input[type=url]::-webkit-textfield-decoration-container {
  display: flex;
  flex-direction: row-reverse;
}

body.rtl .wpforms-container .wpforms-form input[type=email] {
  direction: ltr;
  text-align: right;
}

body.rtl .wpforms-container .wpforms-form input[type=email]::-webkit-textfield-decoration-container {
  display: flex;
  flex-direction: row-reverse;
}

body.rtl .wpforms-container .wpforms-form input[type=number]::-webkit-textfield-decoration-container {
  flex-direction: row-reverse;
}

body.rtl .wpforms-container .wpforms-form .wpforms-field-date-time .wpforms-datepicker-wrap .wpforms-datepicker-clear {
  right: auto;
  left: 10px;
}

body.rtl .wpforms-container .wpforms-form .wpforms-field-date-time .ui-timepicker-list li {
  padding: 3px 5px 3px 0 !important;
}

body.rtl .wpforms-container .wpforms-form .wpforms-field-date-time .wpforms-field-medium + .wpforms-datepicker-clear {
  left: calc( 40% + 10px);
  right: auto;
}

body.rtl .wpforms-container .wpforms-form .wpforms-field-file-upload .dz-remove {
  right: auto;
  left: 0;
}

body.rtl .wpforms-container .wpforms-form .wpforms-image-choices-none .wpforms-image-choices-item .wpforms-image-choices-label {
  margin-left: 0;
  margin-right: 10px;
}

body.rtl .ui-timepicker-list li {
  padding: 3px 5px 3px 0;
}

.wpforms-container .wpforms-form .wpforms-field.wpforms-field-email .wpforms-field-row, .wpforms-container .wpforms-form .wpforms-field.wpforms-field-address .wpforms-field-row, .wpforms-container .wpforms-form .wpforms-field.wpforms-field-password .wpforms-field-row {
  flex-wrap: wrap;
  container-type: inline-size;
  container-name: wpforms-field-row-responsive;
}

.wpforms-container .wpforms-form .wpforms-field.wpforms-field-name .wpforms-field-row {
  flex-wrap: wrap;
  container-type: inline-size;
  container-name: wpforms-field-row-responsive-name-field;
}

.wpforms-container .wpforms-form .wpforms-field .wpforms-field-row-responsive {
  flex-wrap: wrap;
  container-type: inline-size;
  container-name: wpforms-field-row-responsive;
}

@container wpforms-field-row-responsive (max-width: 200px) {
  .wpforms-container .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block {
    width: 100%;
    padding: 0;
  }
  .wpforms-container .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block:only-child {
    padding: 0;
  }
  .wpforms-container .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block:not(:last-child) {
    margin-bottom: 15px;
  }
}

@container wpforms-field-row-responsive-name-field (max-width: 260px) {
  .wpforms-container .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block {
    width: 100%;
    padding: 0;
  }
  .wpforms-container .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block:only-child {
    padding: 0;
  }
  .wpforms-container .wpforms-form .wpforms-field .wpforms-field-row .wpforms-field-row-block:not(:last-child) {
    margin-bottom: 15px;
  }
}

.wpforms-container .wpforms-form .wpforms-checkbox-2-columns,
.wpforms-container .wpforms-form .wpforms-multiplechoice-2-columns,
.wpforms-container .wpforms-form .wpforms-list-2-columns {
  container-type: inline-size;
  container-name: wpforms-field-2-columns-responsive;
}

@container wpforms-field-2-columns-responsive (max-width: 320px) {
  .wpforms-container .wpforms-form .wpforms-checkbox-2-columns ul,
  .wpforms-container .wpforms-form .wpforms-multiplechoice-2-columns ul,
  .wpforms-container .wpforms-form .wpforms-list-2-columns ul {
    grid-template-columns: 1fr !important;
  }
  .wpforms-container .wpforms-form .wpforms-checkbox-2-columns ul li,
  .wpforms-container .wpforms-form .wpforms-multiplechoice-2-columns ul li,
  .wpforms-container .wpforms-form .wpforms-list-2-columns ul li {
    width: 100%;
  }
}

.wpforms-container .wpforms-form .wpforms-checkbox-3-columns,
.wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns,
.wpforms-container .wpforms-form .wpforms-list-3-columns {
  container-type: inline-size;
  container-name: wpforms-field-3-columns-responsive;
}

@container wpforms-field-3-columns-responsive (max-width: 480px) {
  .wpforms-container .wpforms-form .wpforms-checkbox-3-columns ul,
  .wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns ul,
  .wpforms-container .wpforms-form .wpforms-list-3-columns ul {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
  .wpforms-container .wpforms-form .wpforms-checkbox-3-columns ul li,
  .wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns ul li,
  .wpforms-container .wpforms-form .wpforms-list-3-columns ul li {
    width: 100%;
  }
}

@container wpforms-field-3-columns-responsive (max-width: 320px) {
  .wpforms-container .wpforms-form .wpforms-checkbox-3-columns ul,
  .wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns ul,
  .wpforms-container .wpforms-form .wpforms-list-3-columns ul {
    grid-template-columns: 1fr !important;
  }
  .wpforms-container .wpforms-form .wpforms-checkbox-3-columns ul li,
  .wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns ul li,
  .wpforms-container .wpforms-form .wpforms-list-3-columns ul li {
    width: 100%;
  }
}

@media only screen and (max-width: 600px) {
  div.wpforms-container .wpforms-form .wpforms-field > * {
    max-width: 100%;
  }
  div.wpforms-container .wpforms-form .wpforms-field {
    padding-right: 1px;
    padding-left: 1px;
  }
  div.wpforms-container .wpforms-form .wpforms-field input.wpforms-field-small, div.wpforms-container .wpforms-form .wpforms-field input.wpforms-field-medium, div.wpforms-container .wpforms-form .wpforms-field input.wpforms-field-large,
  div.wpforms-container .wpforms-form .wpforms-field select.wpforms-field-small,
  div.wpforms-container .wpforms-form .wpforms-field select.wpforms-field-medium,
  div.wpforms-container .wpforms-form .wpforms-field select.wpforms-field-large,
  div.wpforms-container .wpforms-form .wpforms-field .wpforms-field-row.wpforms-field-small,
  div.wpforms-container .wpforms-form .wpforms-field .wpforms-field-row.wpforms-field-medium,
  div.wpforms-container .wpforms-form .wpforms-field .wpforms-field-row.wpforms-field-large {
    max-width: 100%;
  }
  div.wpforms-container .wpforms-form .wpforms-mobile-full {
    width: 100%;
    margin-left: 0;
  }
  div.wpforms-container .wpforms-form .wpforms-field:not(.wpforms-field-phone):not(.wpforms-field-select-style-modern):not(.wpforms-field-radio):not(.wpforms-field-checkbox):not(.wpforms-field-layout):not(.wpforms-field-repeater) {
    overflow-x: hidden;
  }
  div.wpforms-container .wpforms-form .wpforms-checkbox-2-columns ul,
  div.wpforms-container .wpforms-form .wpforms-multiplechoice-2-columns ul,
  div.wpforms-container .wpforms-form .wpforms-list-2-columns ul,
  div.wpforms-container .wpforms-form .wpforms-checkbox-3-columns ul,
  div.wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns ul,
  div.wpforms-container .wpforms-form .wpforms-list-3-columns ul {
    grid-template-columns: 1fr !important;
  }
  div.wpforms-container .wpforms-form .wpforms-checkbox-2-columns ul li,
  div.wpforms-container .wpforms-form .wpforms-multiplechoice-2-columns ul li,
  div.wpforms-container .wpforms-form .wpforms-list-2-columns ul li,
  div.wpforms-container .wpforms-form .wpforms-checkbox-3-columns ul li,
  div.wpforms-container .wpforms-form .wpforms-multiplechoice-3-columns ul li,
  div.wpforms-container .wpforms-form .wpforms-list-3-columns ul li {
    width: 100%;
  }
  div.wpforms-container .wpforms-form .wpforms-page-indicator.circles .wpforms-page-indicator-page {
    display: block;
    margin: 0 0 10px 0;
  }
  div.wpforms-container .wpforms-form .wpforms-page-indicator.circles .wpforms-page-indicator-page-number {
    width: 30px;
    height: 30px;
    line-height: 30px;
  }
  div.wpforms-container .wpforms-form .wpforms-page-indicator.connector .wpforms-page-indicator-page {
    width: 100% !important;
    padding: 5px 10px;
  }
  div.wpforms-container .wpforms-form .wpforms-page-indicator.connector .wpforms-page-indicator-page-number {
    display: none;
  }
  div.wpforms-container .wpforms-form .wpforms-page-indicator.connector .wpforms-page-indicator-page.active {
    font-weight: 700;
  }
  div.wpforms-container .wpforms-form.inline-fields .wpforms-field-container,
  div.wpforms-container .wpforms-form.inline-fields .wpforms-field {
    display: block;
    width: 100%;
  }
  div.wpforms-container .wpforms-form.inline-fields .wpforms-submit-container {
    width: 100%;
  }
}

.wpforms-container {
  margin-bottom: 26px;
}

.wpforms-container .wpforms-form * {
  word-break: break-word;
  box-sizing: border-box;
}

.wpforms-container .wpforms-form .wpforms-field-label,
.wpforms-container .wpforms-form .wpforms-field-sublabel,
.wpforms-container .wpforms-form .wpforms-field-description,
.wpforms-container .wpforms-form textarea,
.wpforms-container .wpforms-form li,
.wpforms-container .wpforms-form th {
  hyphens: auto;
}

.wpforms-container ul,
.wpforms-container ul li {
  background: none;
  border: 0;
  margin: 0;
}

.wpforms-container .wpforms-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 10px 0;
}

.wpforms-container .wpforms-submit-container {
  clear: both;
  position: relative;
}

.wpforms-container .wpforms-submit-spinner {
  margin-inline-start: 15px;
  display: inline-block;
  vertical-align: middle;
}

.wpforms-container .wpforms-hidden {
  display: none !important;
}

.wpforms-clear:before {
  content: " ";
  display: table;
}

.wpforms-clear:after {
  clear: both;
  content: " ";
  display: table;
}

.wpforms-container .wpforms-notice {
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-left-width: 12px;
  color: #333333;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 30px;
  padding: 20px 36px 20px 26px;
  position: relative;
}

.wpforms-container .wpforms-notice .wpforms-delete {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  background-color: rgba(10, 10, 10, 0.2);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: inline-block;
  height: 20px;
  margin: 0;
  padding: 0;
  vertical-align: top;
  width: 20px;
  position: absolute;
  right: 10px;
  top: 10px;
}

.wpforms-container .wpforms-notice .wpforms-delete:before, .wpforms-container .wpforms-notice .wpforms-delete:after {
  background-color: #ffffff;
  content: "";
  display: block;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform-origin: center center;
}

.wpforms-container .wpforms-notice .wpforms-delete:before {
  height: 2px;
  width: 50%;
}

.wpforms-container .wpforms-notice .wpforms-delete:after {
  height: 50%;
  width: 2px;
}

.wpforms-container .wpforms-notice .wpforms-delete:hover, .wpforms-container .wpforms-notice .wpforms-delete:focus {
  background-color: rgba(10, 10, 10, 0.3);
}

.wpforms-container .wpforms-notice a {
  text-decoration: underline;
}

.wpforms-container .wpforms-notice p {
  margin: 0 0 20px 0;
}

.wpforms-container .wpforms-notice p:last-of-type {
  margin-bottom: 0;
}

.wpforms-container .wpforms-notice .wpforms-notice-actions {
  margin-top: 20px;
}

.wpforms-container .wpforms-notice .wpforms-notice-action {
  border: 2px solid;
  margin-right: 20px;
  padding: 5px;
  text-decoration: none;
}

.wpforms-container .wpforms-notice .wpforms-notice-action:hover, .wpforms-container .wpforms-notice .wpforms-notice-action:focus, .wpforms-container .wpforms-notice .wpforms-notice-action:active {
  color: #ffffff;
}

.wpforms-container .wpforms-notice.wpforms-info {
  border-color: #3273dc;
}

.wpforms-container .wpforms-notice.wpforms-info .wpforms-notice-action {
  border-color: #3273dc;
}

.wpforms-container .wpforms-notice.wpforms-info .wpforms-notice-action:hover, .wpforms-container .wpforms-notice.wpforms-info .wpforms-notice-action:focus, .wpforms-container .wpforms-notice.wpforms-info .wpforms-notice-action:active {
  background-color: #3273dc;
}

.wpforms-container .wpforms-notice.wpforms-success {
  border-color: #23d160;
}

.wpforms-container .wpforms-notice.wpforms-success .wpforms-notice-action {
  border-color: #23d160;
}

.wpforms-container .wpforms-notice.wpforms-success .wpforms-notice-action:hover, .wpforms-container .wpforms-notice.wpforms-success .wpforms-notice-action:focus, .wpforms-container .wpforms-notice.wpforms-success .wpforms-notice-action:active {
  background-color: #23d160;
}

.wpforms-container .wpforms-notice.wpforms-warning {
  border-color: #ffdd57;
}

.wpforms-container .wpforms-notice.wpforms-warning .wpforms-notice-action {
  border-color: #ffdd57;
}

.wpforms-container .wpforms-notice.wpforms-warning .wpforms-notice-action:hover, .wpforms-container .wpforms-notice.wpforms-warning .wpforms-notice-action:focus, .wpforms-container .wpforms-notice.wpforms-warning .wpforms-notice-action:active {
  background-color: #ffdd57;
  color: inherit;
}

.wpforms-container .wpforms-notice.wpforms-error {
  border-color: #D63637;
}

.wpforms-container .wpforms-notice.wpforms-error .wpforms-notice-action {
  border-color: #D63637;
}

.wpforms-container .wpforms-notice.wpforms-error .wpforms-notice-action:hover, .wpforms-container .wpforms-notice.wpforms-error .wpforms-notice-action:focus, .wpforms-container .wpforms-notice.wpforms-error .wpforms-notice-action:active {
  background-color: #D63637;
}

.wpforms-container .wpforms-preview-notice-links {
  line-height: 2.4;
}

.wpforms-container input.wpforms-field-medium,
.wpforms-container select.wpforms-field-medium,
.wpforms-container .wpforms-field-row.wpforms-field-medium,
.wp-core-ui div.wpforms-container input.wpforms-field-medium,
.wp-core-ui div.wpforms-container select.wpforms-field-medium,
.wp-core-ui div.wpforms-container .wpforms-field-row.wpforms-field-medium {
  max-width: 60%;
}

.wpforms-container input.wpforms-field-small,
.wpforms-container select.wpforms-field-small,
.wpforms-container .wpforms-field-row.wpforms-field-small,
.wp-core-ui div.wpforms-container input.wpforms-field-small,
.wp-core-ui div.wpforms-container select.wpforms-field-small,
.wp-core-ui div.wpforms-container .wpforms-field-row.wpforms-field-small {
  max-width: 25%;
}

.wpforms-container input.wpforms-field-large,
.wpforms-container select.wpforms-field-large,
.wpforms-container .wpforms-field-row.wpforms-field-large,
.wp-core-ui div.wpforms-container input.wpforms-field-large,
.wp-core-ui div.wpforms-container select.wpforms-field-large,
.wp-core-ui div.wpforms-container .wpforms-field-row.wpforms-field-large {
  max-width: 100%;
}

.wpforms-container fieldset,
.wp-core-ui div.wpforms-container fieldset {
  display: block;
  border: none;
  margin: 0;
  padding: 0;
}

.wpforms-container .wpforms-field,
.wp-core-ui div.wpforms-container .wpforms-field {
  padding: 15px 0;
  position: relative;
}

.wpforms-container .wpforms-field.wpforms-field-hidden,
.wp-core-ui div.wpforms-container .wpforms-field.wpforms-field-hidden {
  display: none;
  padding: 0;
}

.wpforms-container .wpforms-field-description,
.wpforms-container .wpforms-field-limit-text,
.wp-core-ui div.wpforms-container .wpforms-field-description,
.wp-core-ui div.wpforms-container .wpforms-field-limit-text {
  font-size: 0.8em;
  margin: 5px 0 0 0;
  word-break: break-word;
  word-wrap: break-word;
  line-height: 1.3;
}

.wpforms-container .wpforms-field-description.wpforms-disclaimer-description,
.wp-core-ui div.wpforms-container .wpforms-field-description.wpforms-disclaimer-description {
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.25);
  padding: 15px 15px 0;
  height: 125px;
  overflow-y: scroll;
  overflow-x: hidden;
}

.wpforms-container .wpforms-field-description.wpforms-disclaimer-description p,
.wp-core-ui div.wpforms-container .wpforms-field-description.wpforms-disclaimer-description p {
  margin: 0 0 15px 0;
}

.wpforms-container .wpforms-field-description-before,
.wpforms-container .wpforms-field-description.before,
.wp-core-ui div.wpforms-container .wpforms-field-description-before,
.wp-core-ui div.wpforms-container .wpforms-field-description.before {
  font-size: 0.85em;
  margin: 0 0 5px 0;
}

.wpforms-container .wpforms-field-label,
.wp-core-ui div.wpforms-container .wpforms-field-label {
  display: block;
  font-weight: 700;
  font-style: normal;
  word-break: break-word;
  word-wrap: break-word;
}

.wpforms-container .wpforms-field-label-inline,
.wp-core-ui div.wpforms-container .wpforms-field-label-inline {
  display: inline;
  vertical-align: baseline;
  font-weight: 400;
  font-style: normal;
  word-break: break-word;
  word-wrap: break-word;
}

.wpforms-container .wpforms-field-sublabel,
.wp-core-ui div.wpforms-container .wpforms-field-sublabel {
  display: block;
  font-size: 0.8em;
  font-weight: 400;
  font-style: normal;
  min-width: 120px;
}

.wpforms-container .wpforms-field-label.wpforms-label-hide,
.wpforms-container .wpforms-field-sublabel.wpforms-sublabel-hide,
.wp-core-ui div.wpforms-container .wpforms-field-label.wpforms-label-hide,
.wp-core-ui div.wpforms-container .wpforms-field-sublabel.wpforms-sublabel-hide {
  position: absolute;
  clip: rect(0 0 0 0);
  width: 1px;
  height: 1px;
  margin: -1px;
  overflow: hidden;
}

.wpforms-container .wpforms-required-label,
.wp-core-ui div.wpforms-container .wpforms-required-label {
  color: var(--wpforms-label-error-color);
  font-weight: normal;
}

.wpforms-container input[type=date],
.wpforms-container input[type=datetime],
.wpforms-container input[type=datetime-local],
.wpforms-container input[type=email],
.wpforms-container input[type=month],
.wpforms-container input[type=number],
.wpforms-container input[type=password],
.wpforms-container input[type=range],
.wpforms-container input[type=search],
.wpforms-container input[type=tel],
.wpforms-container input[type=text],
.wpforms-container input[type=time],
.wpforms-container input[type=url],
.wpforms-container input[type=week],
.wpforms-container select,
.wpforms-container textarea,
.wp-core-ui div.wpforms-container input[type=date],
.wp-core-ui div.wpforms-container input[type=datetime],
.wp-core-ui div.wpforms-container input[type=datetime-local],
.wp-core-ui div.wpforms-container input[type=email],
.wp-core-ui div.wpforms-container input[type=month],
.wp-core-ui div.wpforms-container input[type=number],
.wp-core-ui div.wpforms-container input[type=password],
.wp-core-ui div.wpforms-container input[type=range],
.wp-core-ui div.wpforms-container input[type=search],
.wp-core-ui div.wpforms-container input[type=tel],
.wp-core-ui div.wpforms-container input[type=text],
.wp-core-ui div.wpforms-container input[type=time],
.wp-core-ui div.wpforms-container input[type=url],
.wp-core-ui div.wpforms-container input[type=week],
.wp-core-ui div.wpforms-container select,
.wp-core-ui div.wpforms-container textarea {
  display: block;
  width: 100%;
  box-sizing: border-box;
  font-family: inherit;
  font-style: normal;
  font-weight: 400;
  margin: 0;
}

.wpforms-container input[type=date]:read-only,
.wpforms-container input[type=datetime]:read-only,
.wpforms-container input[type=datetime-local]:read-only,
.wpforms-container input[type=email]:read-only,
.wpforms-container input[type=month]:read-only,
.wpforms-container input[type=number]:read-only,
.wpforms-container input[type=password]:read-only,
.wpforms-container input[type=range]:read-only,
.wpforms-container input[type=search]:read-only,
.wpforms-container input[type=tel]:read-only,
.wpforms-container input[type=text]:read-only,
.wpforms-container input[type=time]:read-only,
.wpforms-container input[type=url]:read-only,
.wpforms-container input[type=week]:read-only,
.wpforms-container select:read-only,
.wpforms-container textarea:read-only,
.wp-core-ui div.wpforms-container input[type=date]:read-only,
.wp-core-ui div.wpforms-container input[type=datetime]:read-only,
.wp-core-ui div.wpforms-container input[type=datetime-local]:read-only,
.wp-core-ui div.wpforms-container input[type=email]:read-only,
.wp-core-ui div.wpforms-container input[type=month]:read-only,
.wp-core-ui div.wpforms-container input[type=number]:read-only,
.wp-core-ui div.wpforms-container input[type=password]:read-only,
.wp-core-ui div.wpforms-container input[type=range]:read-only,
.wp-core-ui div.wpforms-container input[type=search]:read-only,
.wp-core-ui div.wpforms-container input[type=tel]:read-only,
.wp-core-ui div.wpforms-container input[type=text]:read-only,
.wp-core-ui div.wpforms-container input[type=time]:read-only,
.wp-core-ui div.wpforms-container input[type=url]:read-only,
.wp-core-ui div.wpforms-container input[type=week]:read-only,
.wp-core-ui div.wpforms-container select:read-only,
.wp-core-ui div.wpforms-container textarea:read-only {
  cursor: default;
}

.wpforms-container textarea,
.wp-core-ui div.wpforms-container textarea {
  resize: vertical;
}

.wpforms-container input[type=checkbox],
.wpforms-container input[type=radio],
.wp-core-ui div.wpforms-container input[type=checkbox],
.wp-core-ui div.wpforms-container input[type=radio] {
  width: 16px;
  height: 16px;
  margin: 2px 10px 0 3px;
  display: inline-block;
  vertical-align: baseline;
  font-style: normal;
  font-weight: 400;
}

.wpforms-container .wpforms-five-sixths,
.wpforms-container .wpforms-four-sixths,
.wpforms-container .wpforms-four-fifths,
.wpforms-container .wpforms-one-fifth,
.wpforms-container .wpforms-one-fourth,
.wpforms-container .wpforms-one-half,
.wpforms-container .wpforms-one-sixth,
.wpforms-container .wpforms-one-third,
.wpforms-container .wpforms-three-fourths,
.wpforms-container .wpforms-three-fifths,
.wpforms-container .wpforms-three-sixths,
.wpforms-container .wpforms-two-fourths,
.wpforms-container .wpforms-two-fifths,
.wpforms-container .wpforms-two-sixths,
.wpforms-container .wpforms-two-thirds {
  float: left;
  margin-left: 20px;
  clear: none;
}

.wpforms-container .wpforms-one-half,
.wpforms-container .wpforms-three-sixths,
.wpforms-container .wpforms-two-fourths {
  width: calc( 50% - 10px);
}

.wpforms-container .wpforms-one-third,
.wpforms-container .wpforms-two-sixths {
  width: calc( 100% / 3 - 20px);
}

.wpforms-container .wpforms-one-third.wpforms-first,
.wpforms-container .wpforms-two-sixths.wpforms-first {
  width: calc( 100% / 3);
}

.wpforms-container .wpforms-four-sixths,
.wpforms-container .wpforms-two-thirds {
  width: calc( 2 * 100% / 3 - 20px);
}

.wpforms-container .wpforms-four-sixths.wpforms-first,
.wpforms-container .wpforms-two-thirds.wpforms-first {
  width: calc( 2 * 100% / 3);
}

.wpforms-container .wpforms-one-fourth {
  width: calc( 25% - 20px);
}

.wpforms-container .wpforms-one-fourth.wpforms-first {
  width: 25%;
}

.wpforms-container .wpforms-three-fourths {
  width: calc( 75% - 20px);
}

.wpforms-container .wpforms-three-fourths.wpforms-first {
  width: 75%;
}

.wpforms-container .wpforms-one-fifth {
  width: calc( 100% / 5 - 20px);
}

.wpforms-container .wpforms-one-fifth.wpforms-first {
  width: calc( 100% / 5);
}

.wpforms-container .wpforms-two-fifths {
  width: calc( 2 * 100% / 5 - 20px);
}

.wpforms-container .wpforms-two-fifths.wpforms-first {
  width: calc( 2 * 100% / 5);
}

.wpforms-container .wpforms-three-fifths {
  width: calc( 3 * 100% / 5 - 20px);
}

.wpforms-container .wpforms-three-fifths.wpforms-first {
  width: calc( 3 * 100% / 5);
}

.wpforms-container .wpforms-four-fifths {
  width: calc( 4 * 100% / 5 - 20px);
}

.wpforms-container .wpforms-four-fifths.wpforms-first {
  width: calc( 4 * 100% / 5);
}

.wpforms-container .wpforms-one-sixth {
  width: calc( 100% / 6 - 20px);
}

.wpforms-container .wpforms-one-sixth.wpforms-first {
  width: calc( 100% / 6);
}

.wpforms-container .wpforms-five-sixths {
  width: calc( 5 * 100% / 6 - 20px);
}

.wpforms-container .wpforms-five-sixths.wpforms-first {
  width: calc( 5 * 100% / 6);
}

.wpforms-container .wpforms-first {
  clear: both !important;
  margin-left: 0 !important;
}

.wpforms-container .wpforms-field {
  float: none;
  clear: both;
}

.wpforms-container .wpforms-field.wpforms-five-sixths, .wpforms-container .wpforms-field.wpforms-four-sixths, .wpforms-container .wpforms-field.wpforms-four-fifths, .wpforms-container .wpforms-field.wpforms-one-fifth, .wpforms-container .wpforms-field.wpforms-one-fourth, .wpforms-container .wpforms-field.wpforms-one-half, .wpforms-container .wpforms-field.wpforms-one-sixth, .wpforms-container .wpforms-field.wpforms-one-third, .wpforms-container .wpforms-field.wpforms-three-fourths, .wpforms-container .wpforms-field.wpforms-three-fifths, .wpforms-container .wpforms-field.wpforms-three-sixths, .wpforms-container .wpforms-field.wpforms-two-fourths, .wpforms-container .wpforms-field.wpforms-two-fifths, .wpforms-container .wpforms-field.wpforms-two-sixths, .wpforms-container .wpforms-field.wpforms-two-thirds {
  float: left;
  margin-left: 20px;
  clear: none;
}

.wpforms-container .wpforms-field .wpforms-field-row {
  align-items: start;
  position: relative;
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-field-row-block {
  padding: 0 10px;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-field-row-block:first-child {
  padding-inline-start: 0;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-field-row-block:last-child {
  padding-inline-end: 0;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-field-row-block:only-child {
  margin-right: auto;
  padding-right: 10px;
}

.wpforms-container .wpforms-field .wpforms-field-row:before {
  content: "";
  display: table;
}

.wpforms-container .wpforms-field .wpforms-field-row:after {
  clear: both;
  content: "";
  display: table;
}

.wpforms-container .wpforms-field .wpforms-field-row:last-of-type {
  margin-bottom: 0;
}

.wpforms-container .wpforms-field .wpforms-field-row > :only-child {
  width: 100%;
}

.wpforms-container .wpforms-field .wpforms-field-row.wpforms-no-columns {
  display: block;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-five-sixths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-four-sixths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-four-fifths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-fifth,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-fourth,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-half,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-sixth,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-third,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-three-fourths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-three-fifths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-three-sixths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-fourths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-fifths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-sixths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-thirds {
  float: none;
  margin-left: 0;
  clear: initial;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-half,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-three-sixths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-fourths {
  width: 50%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-third,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-sixths {
  width: 33.33333%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-four-sixths,
.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-thirds {
  width: 66.66667%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-fourth {
  width: 25%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-three-fourths {
  width: 75%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-fifth {
  width: 20%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-two-fifths {
  width: 40%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-three-fifths {
  width: 60%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-four-fifths {
  width: 80%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-one-sixth {
  width: 16.66667%;
}

.wpforms-container .wpforms-field .wpforms-field-row .wpforms-five-sixths {
  width: 83.33333%;
}

.wpforms-container .wpforms-field .wpforms-checkbox-2-columns ul,
.wpforms-container .wpforms-field .wpforms-multiplechoice-2-columns ul,
.wpforms-container .wpforms-field .wpforms-list-2-columns ul,
.wpforms-container .wpforms-field .wpforms-checkbox-3-columns ul,
.wpforms-container .wpforms-field .wpforms-multiplechoice-3-columns ul,
.wpforms-container .wpforms-field .wpforms-list-3-columns ul {
  display: grid;
  gap: 15px 30px;
}

.wpforms-container .wpforms-field .wpforms-checkbox-2-columns ul,
.wpforms-container .wpforms-field .wpforms-multiplechoice-2-columns ul,
.wpforms-container .wpforms-field .wpforms-list-2-columns ul {
  grid-template-columns: repeat(2, 1fr);
}

.wpforms-container .wpforms-field .wpforms-checkbox-3-columns ul,
.wpforms-container .wpforms-field .wpforms-multiplechoice-3-columns ul,
.wpforms-container .wpforms-field .wpforms-list-3-columns ul {
  grid-template-columns: repeat(3, 1fr);
}

.wpforms-container .wpforms-field .wpforms-list-inline ul li {
  display: inline-block;
  vertical-align: top;
  margin-right: 20px;
}

.wpforms-container.inline-fields {
  overflow: visible;
}

.wpforms-container.inline-fields .wpforms-form {
  display: flex;
  justify-content: space-between;
}

.wpforms-container.inline-fields .wpforms-field-container {
  display: flex;
  justify-content: space-between;
  width: calc( 100% - 175px);
}

.wpforms-container.inline-fields .wpforms-field-container .wpforms-field {
  padding-right: 7px;
  padding-left: 8px;
}

.wpforms-container.inline-fields .wpforms-field-container .wpforms-field:first-of-type {
  padding-left: 0;
}

.wpforms-container.inline-fields .wpforms-field-container .wpforms-field:last-of-type {
  padding-right: 0;
}

.wpforms-container.inline-fields .wpforms-field-row:first-of-type .wpforms-field-row-block:first-child {
  padding-left: 0;
}

.wpforms-container.inline-fields .wpforms-submit-container {
  width: 160px;
  padding-bottom: 16px;
  align-self: flex-end;
}

.wpforms-container.inline-fields .wpforms-submit {
  display: block;
  width: 100%;
}

.wpforms-container.inline-fields input.wpforms-field-medium,
.wpforms-container.inline-fields select.wpforms-field-medium,
.wpforms-container.inline-fields .wpforms-field-row.wpforms-field-medium {
  max-width: 100%;
}

.wpforms-container ul.wpforms-image-choices label:not(.wpforms-error) {
  cursor: pointer;
  position: relative;
}

.wpforms-container ul.wpforms-image-choices label input {
  top: 50%;
}

.wpforms-container .wpforms-image-choices-modern img {
  display: inline-block;
  margin: 0 auto;
  max-width: 100%;
}

.wpforms-container .wpforms-image-choices-modern label:not(.wpforms-error) {
  background: none;
  display: inline-block;
  margin: 0 auto;
  cursor: pointer;
  border: 1px solid #ffffff;
  border-radius: 3px;
  padding: 20px;
  transition: all 0.5s;
  text-align: center;
}

.wpforms-container .wpforms-image-choices-modern label:not(.wpforms-error):hover {
  border: 1px solid rgba(0, 0, 0, 0.25);
}

.wpforms-container .wpforms-image-choices-modern label:not(.wpforms-error):focus, .wpforms-container .wpforms-image-choices-modern label:not(.wpforms-error):focus-within {
  border: 1px solid rgba(0, 0, 0, 0.25);
}

.wpforms-container .wpforms-image-choices-modern .wpforms-selected label, .wpforms-container .wpforms-image-choices-modern li:has(input:checked) label {
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);
}

.wpforms-container .wpforms-image-choices-modern .wpforms-selected .wpforms-image-choices-label, .wpforms-container .wpforms-image-choices-modern li:has(input:checked) .wpforms-image-choices-label {
  font-weight: 700;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-selected .wpforms-image-choices-image:after, .wpforms-container .wpforms-image-choices-modern li:has(input:checked) .wpforms-image-choices-image:after {
  opacity: 1;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-image-choices-image {
  display: block;
  position: relative;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-image-choices-image:after {
  content: "\2714";
  font-size: 22px;
  line-height: 32px;
  color: #ffffff;
  background: var(--wpforms-button-background-color, #066aab);
  opacity: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -16px 0 0 -16px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.5s;
}

.wpforms-container .wpforms-image-choices-modern .wpforms-image-choices-label {
  display: block;
  margin-top: 12px;
}

.wpforms-container .wpforms-list-inline .wpforms-image-choices-modern li {
  margin: 5px !important;
}

.wpforms-container .wpforms-image-choices-classic img {
  display: inline-block;
  margin: 0 auto;
  max-width: 100%;
}

.wpforms-container .wpforms-image-choices-classic label:not(.wpforms-error) {
  background: none;
  display: inline-block;
  margin: 0 auto;
  cursor: pointer;
  border: 1px solid transparent;
  padding: 10px;
  text-align: center;
}

.wpforms-container .wpforms-image-choices-classic label:not(.wpforms-error):hover {
  border: 1px solid rgba(0, 0, 0, 0.25);
}

.wpforms-container .wpforms-image-choices-classic label:not(.wpforms-error):focus {
  border: 1px solid rgba(0, 0, 0, 0.25);
}

.wpforms-container .wpforms-image-choices-classic .wpforms-image-choices-image {
  display: block;
}

.wpforms-container .wpforms-image-choices-classic .wpforms-selected label, .wpforms-container .wpforms-image-choices-classic li:has(input:checked) label {
  border-color: rgba(0, 0, 0, 0.7);
}

.wpforms-container .wpforms-image-choices-classic .wpforms-image-choices-label {
  display: block;
  margin-top: 8px;
}

.wpforms-container .wpforms-list-inline .wpforms-image-choices-classic li {
  margin: 0 10px 10px 0 !important;
}

.wpforms-container .wpforms-image-choices-none .wpforms-image-choices-item img {
  display: inline-block;
  margin: 0 auto;
  max-width: 100%;
}

.wpforms-container .wpforms-image-choices-none .wpforms-image-choices-item input {
  vertical-align: middle;
}

.wpforms-container .wpforms-image-choices-none .wpforms-image-choices-item .wpforms-image-choices-label {
  display: inline-block;
  margin-top: 5px;
  margin-left: 10px;
  vertical-align: middle;
}

.wpforms-container ul.wpforms-icon-choices,
.wpforms-container ul.wpforms-icon-choices * {
  box-sizing: border-box;
}

.wpforms-container ul.wpforms-icon-choices {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 1px 0 1px !important;
  margin: 12px 0 -20px 0 !important;
  /* Style: Default */
  /* Style: Modern */
  /* Style: Classic */
}

.wpforms-container ul.wpforms-icon-choices + .wpforms-field-description,
.wpforms-container ul.wpforms-icon-choices + .wpforms-error {
  margin-top: 15px;
}

.wpforms-container ul.wpforms-icon-choices li {
  min-width: 120px;
  padding-right: 0 !important;
  margin: 0 0 20px 0 !important;
}

.wpforms-container ul.wpforms-icon-choices label {
  position: relative;
  display: block;
  margin: 0;
  cursor: pointer;
}

.wpforms-container ul.wpforms-icon-choices .wpforms-icon-choices-icon {
  display: block;
}

.wpforms-container ul.wpforms-icon-choices svg {
  margin: 0 auto;
  fill: var(--wpforms-icon-choices-color);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-none svg {
  margin: 0;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  margin: 0 0 22px 0 !important;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default label {
  text-align: center;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default label:focus-within .wpforms-icon-choices-icon {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon {
  position: relative;
  z-index: 1;
  margin-bottom: 10px;
  padding: 15px 20px 45px 20px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 6px;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon:hover {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon svg {
  position: relative;
  z-index: 2;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-icon-choices-icon-bg {
  display: block;
  position: absolute;
  z-index: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 6px;
  background-color: #ffffff;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon, .wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon {
  background-color: transparent;
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
  box-sizing: border-box;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default .wpforms-selected .wpforms-icon-choices-icon-bg, .wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon-bg {
  background-color: var(--wpforms-icon-choices-color);
  opacity: .1;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li label {
  background-color: #ffffff !important;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 6px;
  height: 100%;
  padding: 20px 20px 15px 20px;
  text-align: center;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li label:hover {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li:focus-within label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li.wpforms-selected label, .wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li:has(input:checked) label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color), 0 2px 10px rgba(0, 0, 0, 0.15);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-modern li .wpforms-icon-choices-icon {
  margin-bottom: 10px;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-classic li label {
  background-color: #ffffff !important;
  height: 100%;
  padding: 20px 20px 15px 20px;
  text-align: center;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-classic li label:hover {
  box-shadow: 0 0 0 1px #999999;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-classic li:focus-within label {
  box-shadow: 0 0 0 1px #999999;
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-classic li.wpforms-selected label, .wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-classic li:has(input:checked) label {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container ul.wpforms-icon-choices.wpforms-icon-choices-classic li .wpforms-icon-choices-icon {
  margin-bottom: 10px;
}

.wpforms-container .wpforms-field-radio ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before,
.wpforms-container .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: 15px;
  left: calc(50% - 8px);
  display: block;
  width: 16px;
  height: 16px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 50%;
  margin: 15px auto 0;
}

.wpforms-container .wpforms-field-radio ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before, .wpforms-container .wpforms-field-radio ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before,
.wpforms-container .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before,
.wpforms-container .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container .wpforms-field-radio ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after, .wpforms-container .wpforms-field-radio ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after,
.wpforms-container .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after,
.wpforms-container .wpforms-field-payment-multiple ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: calc(15px + 4px);
  left: calc(50% - 4px);
  display: block;
  width: 8px;
  height: 8px;
  background-color: var(--wpforms-icon-choices-color);
  border-radius: 50%;
}

.wpforms-container .wpforms-field-checkbox ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before,
.wpforms-container .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li .wpforms-icon-choices-icon:before {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: 15px;
  left: calc(50% - 8px);
  display: block;
  width: 16px;
  height: 16px;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px #cccccc;
  border-radius: 3px;
  margin: 15px auto 0;
}

.wpforms-container .wpforms-field-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before, .wpforms-container .wpforms-field-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before,
.wpforms-container .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:before,
.wpforms-container .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:before {
  box-shadow: 0 0 0 1px var(--wpforms-icon-choices-color);
}

.wpforms-container .wpforms-field-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after, .wpforms-container .wpforms-field-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after,
.wpforms-container .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li.wpforms-selected .wpforms-icon-choices-icon:after,
.wpforms-container .wpforms-field-payment-checkbox ul.wpforms-icon-choices-default li:has(input:checked) .wpforms-icon-choices-icon:after {
  content: "";
  box-sizing: border-box;
  display: block;
  position: absolute;
  z-index: 2;
  bottom: 23px;
  left: calc(50% - 6px);
  width: 6px;
  height: 10px;
  border-style: solid;
  border-color: var(--wpforms-icon-choices-color);
  border-width: 0 2px 2px 0;
  transform-origin: bottom left;
  transform: rotate(45deg);
}

.wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices,
.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices,
.wpforms-container .wpforms-list-inline ul.wpforms-icon-choices {
  flex-direction: row;
  flex-wrap: wrap;
}

.wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices li,
.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices li,
.wpforms-container .wpforms-list-inline ul.wpforms-icon-choices li {
  margin-right: 20px !important;
}

.wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li,
.wpforms-container .wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-default li,
.wpforms-container .wpforms-list-inline ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  margin-right: 22px !important;
  margin-bottom: 22px !important;
}

.wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices li {
  width: calc( 100% / 2 - 20px / 2);
}

.wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices li:nth-child(2n) {
  margin-right: 0 !important;
}

.wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-container .wpforms-list-2-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  width: calc( 100% / 2 - 22px / 2);
}

.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices li {
  width: calc( 100% / 3 - 20px * 2 / 3);
}

.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices li:nth-child(3n) {
  margin-right: 0 !important;
}

.wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-default li, .wpforms-container .wpforms-list-3-columns ul.wpforms-icon-choices.wpforms-icon-choices-modern li {
  width: calc( 100% / 3 - 22px * 2 / 3);
}

.wpforms-container .wpforms-list-inline ul.wpforms-icon-choices li {
  width: auto;
  max-width: calc( 100% / 4 - 20px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price {
  width: calc(60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price.wpforms-field-small {
  text-wrap: balance;
  width: calc(25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content .wpforms-single-item-price.wpforms-field-large {
  width: calc(100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-payment-single .wpforms-single-item-price-content select.wpforms-payment-quantity {
  margin-left: 0;
  height: 30px;
  min-height: 30px;
  font-size: 14px;
  padding-top: 0;
  padding-bottom: 0;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price {
  display: inline-block;
  max-width: calc( 60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-small {
  max-width: calc( 25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-large {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-quantity {
  display: inline-block;
  margin-inline-start: 15px;
  width: 70px;
  min-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern {
  display: flex;
  flex-wrap: wrap;
  column-gap: 15px;
  align-items: flex-start;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-label {
  min-width: 100%;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .choices {
  margin-bottom: 5px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row {
  flex-grow: 1;
  max-width: calc( 60% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row.wpforms-field-small {
  max-width: calc( 25% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row.wpforms-field-large {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-payment-quantity {
  flex-basis: 70px;
  max-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-payment-quantity .choices__list--dropdown {
  min-width: 70px;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-description {
  flex-basis: 100%;
  margin-top: 0;
}

.wpforms-container .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-error {
  flex-basis: 100%;
}

@media only screen and (max-width: 600px) {
  .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price {
    width: calc( 100% - 85px) !important;
    max-width: 100% !important;
  }
  .wpforms-form .wpforms-payment-quantities-enabled .wpforms-single-item-price-content .wpforms-single-item-price {
    width: calc( 100% - 70px) !important;
  }
  .wpforms-form .wpforms-payment-quantities-enabled.wpforms-field-select-style-modern .wpforms-field-row {
    width: calc( 100% - 85px) !important;
    max-width: 100% !important;
  }
}

#wpforms-form-page-page .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-medium, #wpforms-form-page-page .wpforms-form .wpforms-payment-quantities-enabled select.wpforms-payment-price.wpforms-field-small {
  max-width: calc( 100% - 85px);
}

.wpforms-container .wpforms-form .choices .choices__inner {
  border-radius: 3px;
  min-height: 35px;
}

.wpforms-container .wpforms-form .choices .choices__inner .choices__list--single {
  height: auto;
}

.wpforms-container .wpforms-form .choices .choices__inner .choices__list--multiple .choices__item {
  line-height: 1.3;
}

.wpforms-container .wpforms-form .choices.is-open.is-flipped .choices__inner,
.wpforms-container .wpforms-form .choices.is-open .choices__list--dropdown {
  border-radius: 0 0 3px 3px;
}

.wpforms-container .wpforms-form .choices.is-open.is-flipped .choices__list--dropdown,
.wpforms-container .wpforms-form .choices.is-open .choices__inner {
  border-radius: 3px 3px 0 0;
}

.wpforms-container textarea {
  line-height: 1.3;
}

.wpforms-container textarea.wpforms-field-small {
  height: 70px;
}

.wpforms-container textarea.wpforms-field-medium {
  height: 120px;
}

.wpforms-container textarea.wpforms-field-large {
  height: 220px;
}

.wpforms-container .wpforms-order-summary-container {
  display: block;
  max-width: 60%;
}

.wpforms-container .wpforms-order-summary-container * {
  word-break: break-word;
  box-sizing: border-box;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview {
  width: 100%;
  table-layout: fixed;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th {
  text-align: center;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: left;
}

.rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-label, .rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-label {
  text-align: right;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
  width: 8ch;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
  display: none;
}

@media (max-width: 600px) {
  .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity {
    width: 4ch;
  }
  .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-short {
    display: inline;
  }
  .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-quantity .wpforms-order-summary-item-quantity-label-full {
    display: none;
  }
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  width: 6ch;
  text-align: right;
}

.rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr td.wpforms-order-summary-item-price, .rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr th.wpforms-order-summary-item-price {
  text-align: left;
}

.wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: left;
}

.rtl .wpforms-container .wpforms-order-summary-container table.wpforms-order-summary-preview tr.wpforms-order-summary-placeholder td {
  text-align: right;
}

.wpforms-container .size-large > .wpforms-order-summary-container,
.wpforms-container .wpforms-field-large > .wpforms-order-summary-container {
  max-width: 100%;
}

.wpforms-container .size-medium > .wpforms-order-summary-container,
.wpforms-container .wpforms-field-medium > .wpforms-order-summary-container {
  max-width: 60%;
}

.wpforms-container .wpforms-order-summary-container tr.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
  color: #D63637;
}

.wpforms-field.wpf-disable-field select,
.wpforms-field.wpf-disable-field textarea,
.wpforms-field.wpf-disable-field button,
.wpforms-field.wpf-disable-field input[type=text],
.wpforms-field.wpf-disable-field input[type=number],
.wpforms-field.wpf-disable-field input[type=email],
.wpforms-field.wpf-disable-field input[type=url],
.wpforms-field.wpf-disable-field input[type=tel],
.wpforms-field.wpf-disable-field input[type=password],
.wpforms-field.wpf-disable-field input[type=radio],
.wpforms-field.wpf-disable-field input[type=checkbox],
.wpforms-field.wpf-disable-field input[type=range],
.wpforms-field.wpf-disable-field input[type=file],
.wpforms-field.wpf-disable-field input.wpforms-field-date-time-date,
.wpforms-field.wpf-disable-field .wpforms-uploader,
.wpforms-field.wpf-disable-field .choices,
.wpforms-field.wpf-disable-field .wpforms-image-choices-image,
.wpforms-field.wpf-disable-field .wpforms-field-rating-wrapper,
.wpforms-field.wpf-disable-field .wpforms-field-password-input-icon,
.wpforms-field.wpf-disable-field .wpforms-disclaimer-description,
.wpforms-field.wpf-disable-field .mce-tinymce,
.wpforms-field.wpf-disable-field .iti__selected-country,
.wpforms-field.wpf-disable-field .StripeElement,
.wpforms-field.wpf-disable-field .wpforms-stripe-element,
.wpforms-field.wpf-disable-field .wpforms-field-square-cardnumber,
.wpforms-field.wpf-disable-field .wpforms-square-cardnumber,
.wpforms-field.wpf-disable-field .wpforms-geolocation-map,
.wpforms-field.wpf-disable-field .wpforms-signature-wrap,
.wpforms-field.wpf-disable-field .wpforms-paypal-commerce-card-fields,
.wpforms-field.wpf-disable-field.wpforms-field-net_promoter_score table.modern > tbody > tr > td,
.wpforms-field.wpf-disable-field .wpforms-camera-link, .wpforms-field.wpforms-field-readonly select,
.wpforms-field.wpforms-field-readonly textarea,
.wpforms-field.wpforms-field-readonly button,
.wpforms-field.wpforms-field-readonly input[type=text],
.wpforms-field.wpforms-field-readonly input[type=number],
.wpforms-field.wpforms-field-readonly input[type=email],
.wpforms-field.wpforms-field-readonly input[type=url],
.wpforms-field.wpforms-field-readonly input[type=tel],
.wpforms-field.wpforms-field-readonly input[type=password],
.wpforms-field.wpforms-field-readonly input[type=radio],
.wpforms-field.wpforms-field-readonly input[type=checkbox],
.wpforms-field.wpforms-field-readonly input[type=range],
.wpforms-field.wpforms-field-readonly input[type=file],
.wpforms-field.wpforms-field-readonly input.wpforms-field-date-time-date,
.wpforms-field.wpforms-field-readonly .wpforms-uploader,
.wpforms-field.wpforms-field-readonly .choices,
.wpforms-field.wpforms-field-readonly .wpforms-image-choices-image,
.wpforms-field.wpforms-field-readonly .wpforms-field-rating-wrapper,
.wpforms-field.wpforms-field-readonly .wpforms-field-password-input-icon,
.wpforms-field.wpforms-field-readonly .wpforms-disclaimer-description,
.wpforms-field.wpforms-field-readonly .mce-tinymce,
.wpforms-field.wpforms-field-readonly .iti__selected-country,
.wpforms-field.wpforms-field-readonly .StripeElement,
.wpforms-field.wpforms-field-readonly .wpforms-stripe-element,
.wpforms-field.wpforms-field-readonly .wpforms-field-square-cardnumber,
.wpforms-field.wpforms-field-readonly .wpforms-square-cardnumber,
.wpforms-field.wpforms-field-readonly .wpforms-geolocation-map,
.wpforms-field.wpforms-field-readonly .wpforms-signature-wrap,
.wpforms-field.wpforms-field-readonly .wpforms-paypal-commerce-card-fields,
.wpforms-field.wpforms-field-readonly.wpforms-field-net_promoter_score table.modern > tbody > tr > td,
.wpforms-field.wpforms-field-readonly .wpforms-camera-link {
  cursor: default !important;
  opacity: 0.35 !important;
  pointer-events: none !important;
}

.wpforms-field.wpf-disable-field input[type=radio],
.wpforms-field.wpf-disable-field input[type=checkbox], .wpforms-field.wpforms-field-readonly input[type=radio],
.wpforms-field.wpforms-field-readonly input[type=checkbox] {
  cursor: default !important;
  pointer-events: none !important;
}

.wpforms-field.wpf-disable-field label, .wpforms-field.wpforms-field-readonly label {
  pointer-events: none !important;
}

.wpforms-field.wpf-disable-field .iti__country-container, .wpforms-field.wpforms-field-readonly .iti__country-container {
  cursor: default !important;
}

.wpforms-field.wpf-disable-field .iti__country-container button, .wpforms-field.wpforms-field-readonly .iti__country-container button {
  cursor: default !important;
}
