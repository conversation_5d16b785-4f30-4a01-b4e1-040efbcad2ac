<?php

namespace WPForms\DevTools\FormTemplates;

use WPForms_Template;

/**
 * WPForms DevTools template: Checkboxes and Multiple Choices.
 *
 * @since 0.24
 */
class CheckboxesAndMultipleChoices extends WPForms_Template {

	/**
	 * Primary class constructor.
	 *
	 * @since 0.24
	 */
	public function init() {

		// Template name.
		$this->name = 'Checkboxes and Multiple Choices';

		// Template slug.
		$this->slug = 'checkboxes_and_multiple_choices';

		// Template description.
		$this->description = '';

		// Template field and settings.
		// phpcs:disable WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned
		$this->data = [
			'fields'     => [
				8  => [
					'id'              => '8',
					'type'            => 'pagebreak',
					'position'        => 'top',
					'indicator'       => 'progress',
					'indicator_color' => '#066aab',
					'title'           => 'Multiple Choices',
					'nav_align'       => 'left',
				],
				1  => [
					'id'                   => '1',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: One Column',
					'choices'              => [
						1 => [
							'label'      => 'Red',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Yellow',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Green',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Blue',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Pink',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
				],
				4  => [
					'id'                   => '4',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Two Columns',
					'choices'              => [
						1 => [
							'label'      => 'Red',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Yellow',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Green',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Blue',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Pink',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
				],
				6  => [
					'id'                   => '6',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Three Columns',
					'choices'              => [
						1 => [
							'label'      => 'Red',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Yellow',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Green',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Blue',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Pink',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '3',
				],
				5  => [
					'id'                   => '5',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Inline',
					'choices'              => [
						1 => [
							'label'      => 'Red',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Yellow',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Green',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Blue',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Pink',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
				],
				16 => [
					'id'                   => '16',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Inline - Icon Choices (Large, Default Style)',
					'choices'              => [
						1 => [
							'label'      => 'Up',
							'icon'       => 'arrow-up',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => 'Down',
							'icon'       => 'arrow-down',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Right',
							'icon'       => 'arrow-right',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => 'Left',
							'icon'       => 'arrow-left',
							'icon_style' => 'solid',
						],
						6 => [
							'label'      => 'Right-Left',
							'icon'       => 'arrow-right-arrow-left',
							'icon_style' => 'solid',
						],
						7 => [
							'label'      => 'Up-Down',
							'icon'       => 'arrows-up-down',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#a505ab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
				],
				24 => [
					'id'                   => '24',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: One Column - Icon Choices (Medium, Modern Style)',
					'choices'              => [
						1 => [
							'label'      => 'Up',
							'icon'       => 'arrow-up',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => 'Down',
							'icon'       => 'arrow-down',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Right',
							'icon'       => 'arrow-right',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => 'Left',
							'icon'       => 'arrow-left',
							'icon_style' => 'solid',
						],
						6 => [
							'label'      => 'Right-Left',
							'icon'       => 'arrow-right-arrow-left',
							'icon_style' => 'solid',
						],
						7 => [
							'label'      => 'Up-Down',
							'icon'       => 'arrows-up-down',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#3b03ab',
					'choices_icons_size'   => 'medium',
					'choices_icons_style'  => 'modern',
					'input_columns'        => 'inline',
				],
				25 => [
					'id'                   => '25',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: One Column - Icon Choices (Small, Classic Style)',
					'choices'              => [
						1 => [
							'label'      => 'Up',
							'icon'       => 'arrow-up',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => 'Down',
							'icon'       => 'arrow-down',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Right',
							'icon'       => 'arrow-right',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => 'Left',
							'icon'       => 'arrow-left',
							'icon_style' => 'solid',
						],
						6 => [
							'label'      => 'Right-Left',
							'icon'       => 'arrow-right-arrow-left',
							'icon_style' => 'solid',
						],
						7 => [
							'label'      => 'Up-Down',
							'icon'       => 'arrows-up-down',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#02ab19',
					'choices_icons_size'   => 'small',
					'choices_icons_style'  => 'classic',
					'input_columns'        => 'inline',
				],
				26 => [
					'id'                   => '26',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: One Column - Icon Choices (Large, None Style)',
					'choices'              => [
						1 => [
							'label'      => 'Up',
							'icon'       => 'arrow-up',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => 'Down',
							'icon'       => 'arrow-down',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Right',
							'icon'       => 'arrow-right',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => 'Left',
							'icon'       => 'arrow-left',
							'icon_style' => 'solid',
						],
						6 => [
							'label'      => 'Right-Left',
							'icon'       => 'arrow-right-arrow-left',
							'icon_style' => 'solid',
						],
						7 => [
							'label'      => 'Up-Down',
							'icon'       => 'arrows-up-down',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#c9f005',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
					'input_columns'        => 'inline',
				],
				27 => [
					'id'                   => '27',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: One Column - Icon Choices (Large, Default Style)',
					'choices'              => [
						1 => [
							'label'      => 'Bell',
							'icon'       => 'bell',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Star',
							'icon'       => 'star',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Heart',
							'icon'       => 'heart',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Calendar',
							'icon'       => 'calendar',
							'icon_style' => 'regular',
						],
						8 => [
							'label'      => 'Atom',
							'icon'       => 'atom',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#a505ab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
				],
				28 => [
					'id'                   => '28',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: One Column - Icon Choices (Medium, Modern Style)',
					'choices'              => [
						1 => [
							'label'      => 'Bell',
							'icon'       => 'bell',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Star',
							'icon'       => 'star',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Heart',
							'icon'       => 'heart',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Calendar',
							'icon'       => 'calendar',
							'icon_style' => 'regular',
						],
						8 => [
							'label'      => 'Atom',
							'icon'       => 'atom',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#0c03ab',
					'choices_icons_size'   => 'medium',
					'choices_icons_style'  => 'modern',
				],
				29 => [
					'id'                   => '29',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: One Column - Icon Choices (Small, Classic Style)',
					'choices'              => [
						1 => [
							'label'      => 'Bell',
							'icon'       => 'bell',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Star',
							'icon'       => 'star',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Heart',
							'icon'       => 'heart',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Calendar',
							'icon'       => 'calendar',
							'icon_style' => 'regular',
						],
						8 => [
							'label'      => 'Atom',
							'icon'       => 'atom',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#02ab2c',
					'choices_icons_size'   => 'small',
					'choices_icons_style'  => 'classic',
				],
				30 => [
					'id'                   => '30',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: One Column - Icon Choices (Large, None Style)',
					'choices'              => [
						1 => [
							'label'      => 'Bell',
							'icon'       => 'bell',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Star',
							'icon'       => 'star',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Heart',
							'icon'       => 'heart',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Calendar',
							'icon'       => 'calendar',
							'icon_style' => 'regular',
						],
						8 => [
							'label'      => 'Atom',
							'icon'       => 'atom',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#02ab2c',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
				],
				31 => [
					'id'                   => '31',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Two Columns - Icon Choices (Large, Default Style)',
					'choices'              => [
						1 => [
							'label'      => 'Apple-Pay',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						2 => [
							'label'      => 'Google-Pay',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Amazon-Pay',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'PayPal',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						8 => [
							'label'      => 'CC-PayPal',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#a505ab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
				],
				32 => [
					'id'                   => '32',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Two Columns - Icon Choices (Medium, Modern Style)',
					'choices'              => [
						1 => [
							'label'      => 'Apple-Pay',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						2 => [
							'label'      => 'Google-Pay',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Amazon-Pay',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'PayPal',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						8 => [
							'label'      => 'CC-PayPal',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#0c03ab',
					'choices_icons_size'   => 'medium',
					'choices_icons_style'  => 'modern',
					'input_columns'        => '2',
				],
				33 => [
					'id'                   => '33',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Two Columns - Icon Choices (Small, Classic Style)',
					'choices'              => [
						1 => [
							'label'      => 'Apple-Pay',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						2 => [
							'label'      => 'Google-Pay',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Amazon-Pay',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'PayPal',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						8 => [
							'label'      => 'CC-PayPal',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#2fab02',
					'choices_icons_size'   => 'small',
					'choices_icons_style'  => 'classic',
					'input_columns'        => '2',
				],
				34 => [
					'id'                   => '34',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Two Columns - Icon Choices (Large, None Style)',
					'choices'              => [
						1 => [
							'label'      => 'Apple-Pay',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						2 => [
							'label'      => 'Google-Pay',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Amazon-Pay',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'PayPal',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						8 => [
							'label'      => 'CC-PayPal',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#e5f01a',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
					'input_columns'        => '2',
				],
				52 => [
					'id'                   => '52',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Three Columns - Icon Choices (Large, Default Style)',
					'choices'              => [
						1 => [
							'label'      => '1',
							'icon'       => '1',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => '2',
							'icon'       => '2',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => '3',
							'icon'       => '3',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => '4',
							'icon'       => '4',
							'icon_style' => 'solid',
						],
						8 => [
							'label'      => '5',
							'icon'       => '5',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#b618f0',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '3',
				],
				53 => [
					'id'                   => '53',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Three Columns - Icon Choices (Medium, Modern Style)',
					'choices'              => [
						1 => [
							'label'      => '1',
							'icon'       => '1',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => '2',
							'icon'       => '2',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => '3',
							'icon'       => '3',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => '4',
							'icon'       => '4',
							'icon_style' => 'solid',
						],
						8 => [
							'label'      => '5',
							'icon'       => '5',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#21a35b',
					'choices_icons_size'   => 'medium',
					'choices_icons_style'  => 'modern',
					'input_columns'        => '3',
				],
				54 => [
					'id'                   => '54',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Three Columns - Icon Choices (Small, Classic Style)',
					'choices'              => [
						1 => [
							'label'      => '1',
							'icon'       => '1',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => '2',
							'icon'       => '2',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => '3',
							'icon'       => '3',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => '4',
							'icon'       => '4',
							'icon_style' => 'solid',
						],
						8 => [
							'label'      => '5',
							'icon'       => '5',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#a8d915',
					'choices_icons_size'   => 'small',
					'choices_icons_style'  => 'classic',
					'input_columns'        => '3',
				],
				55 => [
					'id'                   => '55',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Three Columns - Icon Choices (Large, None Style)',
					'choices'              => [
						1 => [
							'label'      => '1',
							'icon'       => '1',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => '2',
							'icon'       => '2',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => '3',
							'icon'       => '3',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => '4',
							'icon'       => '4',
							'icon_style' => 'solid',
						],
						8 => [
							'label'      => '5',
							'icon'       => '5',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#1452d9',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
					'input_columns'        => '3',
				],
				36 => [
					'id'                   => '36',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: One Column - Image Choices (Modern Style)',
					'choices'              => [
						2 => [
							'label'      => 'Apple',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/apple.png',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Car',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/car.png',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						1 => [
							'label'      => 'Heart',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/heart.png',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'Star',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/star.png',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						8 => [
							'label'      => 'Paint',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/paint.png',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
						9 => [
							'label'      => 'Alarm',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/alarm.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#e5f01a',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
				],
				37 => [
					'id'                   => '37',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Two Columns - Image Choices (Classic Style)',
					'choices'              => [
						2 => [
							'label'      => 'Apple',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/apple.png',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Car',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/car.png',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						1 => [
							'label'      => 'Heart',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/heart.png',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'Star',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/star.png',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						8 => [
							'label'      => 'Paint',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/paint.png',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
						9 => [
							'label'      => 'Alarm',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/alarm.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'classic',
					'choices_icons_color'  => '#e5f01a',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
					'random'               => '1',
					'input_columns'        => '2',
				],
				38 => [
					'id'                   => '38',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Three Columns - Image Choices (None Style)',
					'choices'              => [
						2 => [
							'label'      => 'Apple',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/apple.png',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Car',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/car.png',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						1 => [
							'label'      => 'Heart',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/heart.png',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'Star',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/star.png',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						8 => [
							'label'      => 'Paint',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/paint.png',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
						9 => [
							'label'      => 'Alarm',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/alarm.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'none',
					'choices_icons_color'  => '#e5f01a',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
					'input_columns'        => '2',
				],
				39 => [
					'id'                   => '39',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Inline - Image Choices (Modern Style)',
					'choices'              => [
						2 => [
							'label'      => 'Apple',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/apple.png',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Car',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/car.png',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						1 => [
							'label'      => 'Heart',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/heart.png',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'Star',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/star.png',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						8 => [
							'label'      => 'Paint',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/paint.png',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
						9 => [
							'label'      => 'Alarm',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/alarm.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#e5f01a',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
					'input_columns'        => 'inline',
				],
				7  => [
					'id'    => '7',
					'type'  => 'pagebreak',
					'title' => 'Checkboxes',
					'next'  => 'Next',
				],
				10 => [
					'id'                   => '10',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: One Column',
					'choices'              => [
						1 => [
							'label'      => 'Red',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Yellow',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Green',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Blue',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Pink',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
				],
				13 => [
					'id'                   => '13',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Two Columns',
					'choices'              => [
						1 => [
							'label'      => 'Red',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Yellow',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Green',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Blue',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Pink',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
				],
				14 => [
					'id'                   => '14',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Three Columns',
					'choices'              => [
						1 => [
							'label'      => 'Red',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Yellow',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Green',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Blue',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Pink',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '3',
				],
				15 => [
					'id'                   => '15',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Inline',
					'choices'              => [
						1 => [
							'label'      => 'Red',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Yellow',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Green',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Blue',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Pink',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
				],
				40 => [
					'id'                   => '40',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Inline - Icon Choices (Large, Default Style)',
					'choices'              => [
						1 => [
							'label'      => 'Up',
							'icon'       => 'angle-up',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => 'Down',
							'icon'       => 'angle-down',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Right',
							'icon'       => 'angle-right',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => 'Left',
							'icon'       => 'angle-left',
							'icon_style' => 'solid',
						],
						5 => [
							'label'      => 'Right-Left',
							'icon'       => 'arrows-left-right',
							'icon_style' => 'solid',
						],
						6 => [
							'label'      => 'Up-Down',
							'icon'       => 'arrows-up-down',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#6905ab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
				],
				44 => [
					'id'                   => '44',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Inline - Icon Choices (Large, Default Style) (copy)',
					'choices'              => [
						1 => [
							'label'      => 'Up',
							'icon'       => 'angle-up',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => 'Down',
							'icon'       => 'angle-down',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Right',
							'icon'       => 'angle-right',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => 'Left',
							'icon'       => 'angle-left',
							'icon_style' => 'solid',
						],
						5 => [
							'label'      => 'Right-Left',
							'icon'       => 'arrows-left-right',
							'icon_style' => 'solid',
						],
						6 => [
							'label'      => 'Up-Down',
							'icon'       => 'arrows-up-down',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#6905ab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
				],
				41 => [
					'id'                   => '41',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: One Column - Icon Choices (Medium, Modern Style)',
					'choices'              => [
						1 => [
							'label'      => 'Up',
							'icon'       => 'angle-up',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => 'Down',
							'icon'       => 'angle-down',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Right',
							'icon'       => 'angle-right',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => 'Left',
							'icon'       => 'angle-left',
							'icon_style' => 'solid',
						],
						5 => [
							'label'      => 'Right-Left',
							'icon'       => 'arrows-left-right',
							'icon_style' => 'solid',
						],
						6 => [
							'label'      => 'Up-Down',
							'icon'       => 'arrows-up-down',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#03ab2d',
					'choices_icons_size'   => 'medium',
					'choices_icons_style'  => 'modern',
					'input_columns'        => 'inline',
				],
				42 => [
					'id'                   => '42',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes:	Inline - Icon Choices (Small, Classic Style)',
					'choices'              => [
						1 => [
							'label'      => 'Up',
							'icon'       => 'angle-up',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => 'Down',
							'icon'       => 'angle-down',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Right',
							'icon'       => 'angle-right',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => 'Left',
							'icon'       => 'angle-left',
							'icon_style' => 'solid',
						],
						5 => [
							'label'      => 'Right-Left',
							'icon'       => 'arrows-left-right',
							'icon_style' => 'solid',
						],
						6 => [
							'label'      => 'Up-Down',
							'icon'       => 'arrows-up-down',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#6905ab',
					'choices_icons_size'   => 'small',
					'choices_icons_style'  => 'classic',
					'input_columns'        => 'inline',
				],
				43 => [
					'id'                   => '43',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: One Column - Icon Choices (Large, Default Style)',
					'choices'              => [
						1 => [
							'label'      => 'Bell',
							'icon'       => 'bell',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Star',
							'icon'       => 'star',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Heart',
							'icon'       => 'heart',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Calendar',
							'icon'       => 'calendar',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Atom',
							'icon'       => 'atom',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#d000f5',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
				],
				45 => [
					'id'                   => '45',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: One Column - Icon Choices (Medium, Modern Style)',
					'choices'              => [
						1 => [
							'label'      => 'Bell',
							'icon'       => 'bell',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Star',
							'icon'       => 'star',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Heart',
							'icon'       => 'heart',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Calendar',
							'icon'       => 'calendar',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Atom',
							'icon'       => 'atom',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#4b994b',
					'choices_icons_size'   => 'medium',
					'choices_icons_style'  => 'modern',
				],
				46 => [
					'id'                   => '46',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: One Column - Icon Choices (Small, Classic Style)',
					'choices'              => [
						1 => [
							'label'      => 'Bell',
							'icon'       => 'bell',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Star',
							'icon'       => 'star',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Heart',
							'icon'       => 'heart',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Calendar',
							'icon'       => 'calendar',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Atom',
							'icon'       => 'atom',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#494999',
					'choices_icons_size'   => 'small',
					'choices_icons_style'  => 'classic',
				],
				47 => [
					'id'                   => '47',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: One Column - Icon Choices (Large, None Style)',
					'choices'              => [
						1 => [
							'label'      => 'Bell',
							'icon'       => 'bell',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Star',
							'icon'       => 'star',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Heart',
							'icon'       => 'heart',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Calendar',
							'icon'       => 'calendar',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Atom',
							'icon'       => 'atom',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#e6e617',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
				],
				48 => [
					'id'                   => '48',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Two Columns - Icon Choices (Large, Default Style)',
					'choices'              => [
						1 => [
							'label'      => 'Apple-Pay',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						2 => [
							'label'      => 'Google-Pay',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Amazon-Pay',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'PayPal',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						5 => [
							'label'      => 'CC-PayPal',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#b617e6',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
				],
				49 => [
					'id'                   => '49',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Two Columns - Icon Choices (Medium, Modern Style)',
					'choices'              => [
						1 => [
							'label'      => 'Apple-Pay',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						2 => [
							'label'      => 'Google-Pay',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Amazon-Pay',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'PayPal',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						5 => [
							'label'      => 'CC-PayPal',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#31c26b',
					'choices_icons_size'   => 'medium',
					'choices_icons_style'  => 'modern',
					'input_columns'        => '2',
				],
				50 => [
					'id'                   => '50',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Two Columns - Icon Choices (Small, Classic Style)',
					'choices'              => [
						1 => [
							'label'      => 'Apple-Pay',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						2 => [
							'label'      => 'Google-Pay',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Amazon-Pay',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'PayPal',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						5 => [
							'label'      => 'CC-PayPal',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#d5e617',
					'choices_icons_size'   => 'small',
					'choices_icons_style'  => 'classic',
					'input_columns'        => '2',
				],
				51 => [
					'id'                   => '51',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Two Columns - Icon Choices (Large, None Style)',
					'choices'              => [
						1 => [
							'label'      => 'Apple-Pay',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						2 => [
							'label'      => 'Google-Pay',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Amazon-Pay',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'PayPal',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						5 => [
							'label'      => 'CC-PayPal',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#362ec2',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
					'input_columns'        => '2',
				],
				57 => [
					'id'                   => '57',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: One Column - Image Choices (Modern Style)',
					'choices'              => [
						1 => [
							'label'      => 'Banana',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/banana.png',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						2 => [
							'label'      => 'Apple',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/apple.png',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Avocado',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/avocado.png',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'Mango',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/mango.png',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						5 => [
							'label'      => 'Pomegranate',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/pomegranate.png',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
						6 => [
							'label'      => 'Lemon',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/lemon.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#362ec2',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
				],
				59 => [
					'id'                   => '59',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Three Columns - Image Choices (None Style)',
					'choices'              => [
						1 => [
							'label'      => 'Banana',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/banana.png',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						2 => [
							'label'      => 'Apple',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/apple.png',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Avocado',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/avocado.png',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'Mango',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/mango.png',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						5 => [
							'label'      => 'Pomegranate',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/pomegranate.png',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
						6 => [
							'label'      => 'Lemon',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/lemon.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'none',
					'choices_icons_color'  => '#362ec2',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
					'input_columns'        => '3',
				],
				61 => [
					'id'                   => '61',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Inline - Image Choices (Modern Style)',
					'choices'              => [
						1 => [
							'label'      => 'Banana',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/banana.png',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						2 => [
							'label'      => 'Apple',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/apple.png',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Avocado',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/avocado.png',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'Mango',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/mango.png',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						5 => [
							'label'      => 'Pomegranate',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/pomegranate.png',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
						6 => [
							'label'      => 'Lemon',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/lemon.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#362ec2',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
					'random'               => '1',
					'input_columns'        => 'inline',
				],
				60 => [
					'id'                   => '60',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Two Columns - Image Choices (Classic Style) (copy)',
					'choices'              => [
						1 => [
							'label'      => 'Banana',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/banana.png',
							'icon'       => 'apple-pay',
							'icon_style' => 'brands',
						],
						2 => [
							'label'      => 'Apple',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/apple.png',
							'icon'       => 'google-pay',
							'icon_style' => 'brands',
						],
						3 => [
							'label'      => 'Avocado',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/avocado.png',
							'icon'       => 'amazon-pay',
							'icon_style' => 'brands',
						],
						4 => [
							'label'      => 'Mango',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/mango.png',
							'icon'       => 'paypal',
							'icon_style' => 'brands',
						],
						5 => [
							'label'      => 'Pomegranate',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/pomegranate.png',
							'icon'       => 'cc-paypal',
							'icon_style' => 'brands',
						],
						6 => [
							'label'      => 'Lemon',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/lemon.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'classic',
					'choices_icons_color'  => '#362ec2',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'none',
					'input_columns'        => '2',
				],
				62 => [
					'id'    => '62',
					'type'  => 'pagebreak',
					'title' => 'Checkbox items',
					'next'  => 'Next',
				],
				63 => [
					'id'                   => '63',
					'type'                 => 'payment-checkbox',
					'label'                => 'Checkbox Items: One Column without Prices after item labels',
					'choices'              => [
						1 => [
							'label'      => 'First Product',
							'value'      => '10.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Product',
							'value'      => '25.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Product',
							'value'      => '50.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Product',
							'value'      => '100.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Product',
							'value'      => '200.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
				],
				69 => [
					'id'                      => '69',
					'type'                    => 'payment-checkbox',
					'label'                   => 'Checkbox Items: One Column with Prices after item labels',
					'choices'                 => [
						1 => [
							'label'      => 'First Product',
							'value'      => '10.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Product',
							'value'      => '25.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Product',
							'value'      => '50.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Product',
							'value'      => '100.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Product',
							'value'      => '200.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'modern',
					'choices_icons_color'     => '#066aab',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
				],
				64 => [
					'id'                      => '64',
					'type'                    => 'payment-checkbox',
					'label'                   => 'Checkbox Items: Two Columns',
					'choices'                 => [
						1 => [
							'label'      => 'First Product',
							'value'      => '10.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Product',
							'value'      => '25.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Product',
							'value'      => '50.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Product',
							'value'      => '100.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Product',
							'value'      => '200.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'modern',
					'choices_icons_color'     => '#066aab',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
					'input_columns'           => '2',
				],
				65 => [
					'id'                      => '65',
					'type'                    => 'payment-checkbox',
					'label'                   => 'Checkbox Items: Three Columns',
					'choices'                 => [
						1 => [
							'label'      => 'First Product',
							'value'      => '10.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Product',
							'value'      => '25.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Product',
							'value'      => '50.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Product',
							'value'      => '100.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Product',
							'value'      => '200.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'modern',
					'choices_icons_color'     => '#066aab',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
					'input_columns'           => '3',
				],
				66 => [
					'id'                      => '66',
					'type'                    => 'payment-checkbox',
					'label'                   => 'Checkbox Items: Inline',
					'choices'                 => [
						1 => [
							'label'      => 'First Product',
							'value'      => '10.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Product',
							'value'      => '25.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Product',
							'value'      => '50.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Product',
							'value'      => '100.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Product',
							'value'      => '200.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'modern',
					'choices_icons_color'     => '#066aab',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
					'input_columns'           => 'inline',
				],
				67 => [
					'id'                      => '67',
					'type'                    => 'payment-checkbox',
					'label'                   => 'Checkbox Items: inline - Image Choices (Modern Style)',
					'choices'                 => [
						2 => [
							'label'      => 'Apple',
							'value'      => '25.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/apple.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Star',
							'value'      => '50.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/star.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Car',
							'value'      => '100.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/car.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images'          => '1',
					'choices_images_style'    => 'modern',
					'choices_icons_color'     => '#066aab',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
					'input_columns'           => 'inline',
				],
				68 => [
					'id'                      => '68',
					'type'                    => 'payment-checkbox',
					'label'                   => 'Checkbox Items: One Column - Image Choices (Classic Style)',
					'choices'                 => [
						2 => [
							'label'      => 'Apple',
							'value'      => '25.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/apple.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Star',
							'value'      => '50.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/star.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Car',
							'value'      => '100.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/car.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images'          => '1',
					'choices_images_style'    => 'classic',
					'choices_icons_color'     => '#066aab',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
				],
				76 => [
					'id'                      => '76',
					'type'                    => 'payment-checkbox',
					'label'                   => 'Checkbox Items: One Column - Icon Choices (Large, Default Style)',
					'choices'                 => [
						2 => [
							'label'      => 'Pencil',
							'value'      => '25.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/pencil.png',
							'icon'       => 'pencil',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Clipboard',
							'value'      => '50.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/clipboard.png',
							'icon'       => 'clipboard',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Envelope',
							'value'      => '100.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/envelope.png',
							'icon'       => 'envelope',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'classic',
					'choices_icons'           => '1',
					'choices_icons_color'     => '#65ad07',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
				],
				77 => [
					'id'                      => '77',
					'type'                    => 'payment-checkbox',
					'label'                   => 'Checkbox Items: Three Columns - Icon Choices (Medium, Modern Style)',
					'choices'                 => [
						2 => [
							'label'      => 'Pencil',
							'value'      => '25.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/pencil.png',
							'icon'       => 'pencil',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Clipboard',
							'value'      => '50.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/clipboard.png',
							'icon'       => 'clipboard',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Envelope',
							'value'      => '100.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/envelope.png',
							'icon'       => 'envelope',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'classic',
					'choices_icons'           => '1',
					'choices_icons_color'     => '#c5eb1a',
					'choices_icons_size'      => 'medium',
					'choices_icons_style'     => 'modern',
					'input_columns'           => '3',
				],
				71 => [
					'id'    => '71',
					'type'  => 'pagebreak',
					'title' => 'Multiple Items',
					'next'  => 'Next',
				],
				70 => [
					'id'                   => '70',
					'type'                 => 'payment-multiple',
					'label'                => 'Multiple Items: One Column without Prices after item labels',
					'choices'              => [
						1 => [
							'label'      => 'First Product',
							'value'      => '10.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Product',
							'value'      => '25.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Product',
							'value'      => '50.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Product',
							'value'      => '100.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Product',
							'value'      => '200.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
				],
				72 => [
					'id'                      => '72',
					'type'                    => 'payment-multiple',
					'label'                   => 'Multiple Items: One Column with Prices after item labels',
					'choices'                 => [
						1 => [
							'label'      => 'First Product',
							'value'      => '10.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Product',
							'value'      => '25.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Product',
							'value'      => '50.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Product',
							'value'      => '100.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Product',
							'value'      => '200.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'modern',
					'choices_icons_color'     => '#066aab',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
				],
				73 => [
					'id'                      => '73',
					'type'                    => 'payment-multiple',
					'label'                   => 'Multiple Items: Two Columns',
					'choices'                 => [
						1 => [
							'label'      => 'First Product',
							'value'      => '10.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Product',
							'value'      => '25.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Product',
							'value'      => '50.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Product',
							'value'      => '100.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Product',
							'value'      => '200.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'modern',
					'choices_icons_color'     => '#066aab',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
					'input_columns'           => '2',
				],
				74 => [
					'id'                      => '74',
					'type'                    => 'payment-multiple',
					'label'                   => 'Multiple Items: Three Columns',
					'choices'                 => [
						1 => [
							'label'      => 'First Product',
							'value'      => '10.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Product',
							'value'      => '25.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Product',
							'value'      => '50.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Product',
							'value'      => '100.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Product',
							'value'      => '200.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'modern',
					'choices_icons_color'     => '#066aab',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
					'input_columns'           => '3',
				],
				75 => [
					'id'                      => '75',
					'type'                    => 'payment-multiple',
					'label'                   => 'Multiple Items: Inline',
					'choices'                 => [
						1 => [
							'label'      => 'First Product',
							'value'      => '10.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Product',
							'value'      => '25.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Product',
							'value'      => '50.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Product',
							'value'      => '100.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Product',
							'value'      => '200.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'modern',
					'choices_icons_color'     => '#066aab',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
					'input_columns'           => 'inline',
				],
				78 => [
					'id'                      => '78',
					'type'                    => 'payment-multiple',
					'label'                   => 'Multiple Items: Inline - Image Choices (Modern Style)',
					'choices'                 => [
						1 => [
							'label'      => 'Apple',
							'value'      => '10.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/apple.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Banana',
							'value'      => '25.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/banana.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Pomegranate',
							'value'      => '50.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/pomegranate.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Lemon',
							'value'      => '100.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/lemon.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Avocado',
							'value'      => '200.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/avocado.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images'          => '1',
					'choices_images_style'    => 'modern',
					'choices_icons_color'     => '#066aab',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
					'input_columns'           => 'inline',
				],
				79 => [
					'id'                      => '79',
					'type'                    => 'payment-multiple',
					'label'                   => 'Multiple Items: Three Columns - Image Choices (Classic Style)',
					'choices'                 => [
						1 => [
							'label'      => 'Apple',
							'value'      => '10.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/apple.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Banana',
							'value'      => '25.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/banana.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Pomegranate',
							'value'      => '50.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/pomegranate.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Lemon',
							'value'      => '100.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/lemon.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Avocado',
							'value'      => '200.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/avocado.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images'          => '1',
					'choices_images_style'    => 'classic',
					'choices_icons_color'     => '#066aab',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
					'input_columns'           => '3',
				],
				80 => [
					'id'                      => '80',
					'type'                    => 'payment-multiple',
					'label'                   => 'Multiple Items: Two Columns - Icon Choices (Small, Classic Style)',
					'choices'                 => [
						1 => [
							'label'      => 'One',
							'value'      => '10.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/number-one-icon.png',
							'icon'       => '1',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => 'Two',
							'value'      => '25.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/number-two-icon.png',
							'icon'       => '2',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Three',
							'value'      => '50.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/number-three-icon.png',
							'icon'       => '3',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => 'Four',
							'value'      => '100.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/number-four-icon.png',
							'icon'       => '4',
							'icon_style' => 'solid',
						],
						5 => [
							'label'      => 'Five',
							'value'      => '200.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/number-five-icon.png',
							'icon'       => '5',
							'icon_style' => 'solid',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'classic',
					'choices_icons'           => '1',
					'choices_icons_color'     => '#aa07ad',
					'choices_icons_size'      => 'small',
					'choices_icons_style'     => 'classic',
					'input_columns'           => '2',
				],
				81 => [
					'id'                      => '81',
					'type'                    => 'payment-multiple',
					'label'                   => 'Multiple Items: Three Columns - Icon Choices (Large, Default Style) ',
					'choices'                 => [
						1 => [
							'label'      => 'One',
							'value'      => '10.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/number-one-icon.png',
							'icon'       => '1',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => 'Two',
							'value'      => '25.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/number-two-icon.png',
							'icon'       => '2',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Three',
							'value'      => '50.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/number-three-icon.png',
							'icon'       => '3',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => 'Four',
							'value'      => '100.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/number-four-icon.png',
							'icon'       => '4',
							'icon_style' => 'solid',
						],
						5 => [
							'label'      => 'Five',
							'value'      => '200.00',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/number-five-icon.png',
							'icon'       => '5',
							'icon_style' => 'solid',
						],
					],
					'show_price_after_labels' => '1',
					'choices_images_style'    => 'classic',
					'choices_icons'           => '1',
					'choices_icons_color'     => '#7873bf',
					'choices_icons_size'      => 'large',
					'choices_icons_style'     => 'default',
					'input_columns'           => '3',
				],
				9  => [
					'id'       => '9',
					'type'     => 'pagebreak',
					'position' => 'bottom',
				],
			],
			'field_id'   => 82,
			'settings'   => [
				'form_title'                             => 'Checkboxes and Multiple Choices',
				'submit_text'                            => 'Submit',
				'submit_text_processing'                 => 'Sending...',
				'ajax_submit'                            => '1',
				'notification_enable'                    => '1',
				'notifications'                          => [
					1 => [
						'notification_name'                      => 'Default Notification',
						'email'                                  => '{admin_email}',
						'subject'                                => 'New Entry: Blank Form (ID #3555)',
						'sender_name'                            => '10TEST',
						'sender_address'                         => '{admin_email}',
						'message'                                => '{all_fields}',
						'file_upload_attachment_fields'          => [],
						'entry_csv_attachment_entry_information' => [],
						'entry_csv_attachment_file_name'         => 'entry-details',
					],
				],
				'confirmations'                          => [
					1 => [
						'name'                        => 'Default Confirmation',
						'type'                        => 'message',
						'message'                     => '<p>Thanks for contacting us! We will be in touch with you shortly.</p>',
						'message_scroll'              => '1',
						'page'                        => '666',
						'message_entry_preview_style' => 'basic',
					],
				],
				'lead_forms'                             => [
					'accent_color'   => '#0299ed',
					'field_borders'  => '#cccccc',
					'primary_text'   => '#444444',
					'secondary_text' => '#777777',
				],
				'antispam_v3'                            => '1',
				'anti_spam'                              => [
					'time_limit'     => [
						'duration' => '3',
					],
					'country_filter' => [
						'action'        => 'allow',
						'country_codes' => [],
						'message'       => 'Sorry, this form does not accept submissions from your country.',
					],
					'keyword_filter' => [
						'message' => 'Sorry, your message can\'t be submitted because it contains prohibited words.',
					],
				],
				'form_pages_title'                       => 'Blank Form',
				'form_pages_footer'                      => 'This content is neither created nor endorsed by WPForms.',
				'form_pages_color_scheme'                => '#448ccb',
				'form_pages_style'                       => 'modern',
				'conversational_forms_title'             => 'Blank Form',
				'conversational_forms_color_scheme'      => '#448ccb',
				'conversational_forms_progress_bar'      => 'percentage',
				'save_resume_link_text'                  => 'Save and Resume Later',
				'save_resume_disclaimer_message'         => 'Heads up! Saving your progress now will store a copy of your entry on this server and the site owner may have access to it. For security reasons, sensitive information such as credit cards and mailing addresses, along with file uploads will have to be re-entered when you resume.',
				'save_resume_confirmation_message'       => 'Your form entry has been saved and a unique link has been created which you can access to resume this form.

Enter your email address to receive the link via email. Alternatively, you can copy and save the link below.

Please note, this link should not be shared and will expire in 30 days, afterwards your form entry will be deleted.',
				'save_resume_enable_resume_link'         => '1',
				'save_resume_enable_email_notification'  => '1',
				'save_resume_email_notification_message' => 'Thank you for saving {form_name}. Click the link below to resume the form from any device.

{resume_link}

Remember, the link should not be shared and will expire in 30 days.',
				'save_resume_email_settings_message'     => 'A link to resume this form has been sent to the email address provided.

Please remember, the link should not be shared and will expire in 30 days.',
				'form_tags'                              => [],
			],
			'lead_forms' => [
				'iif_id_ref' => '',
			],
			'payments'   => [
				'stripe'          => [
					'payment_description' => '',
					'receipt_email'       => '',
					'customer_email'      => '',
					'customer_name'       => '',
					'customer_address'    => '',
					'shipping_address'    => '',
					'recurring'           => [
						0 => [
							'name'             => 'Plan Name',
							'period'           => 'yearly',
							'email'            => '',
							'customer_name'    => '',
							'customer_address' => '',
						],
					],
				],
				'paypal_commerce' => [
					'name'                => '',
					'billing_email'       => '',
					'billing_address'     => '',
					'shipping_address'    => '',
					'payment_description' => '',
					'recurring'           => [
						0 => [
							'pp_product_id'    => '',
							'pp_plan_id'       => '',
							'name'             => 'Plan Name #1',
							'product_type'     => 'digital',
							'recurring_times'  => 'yearly',
							'total_cycles'     => '0',
							'shipping_address' => '',
						],
					],
				],
				'authorize_net'   => [
					'payment_description'      => '',
					'receipt_email'            => '',
					'customer_name'            => '',
					'customer_billing_address' => '',
					'recurring'                => [
						'name'                     => '',
						'period'                   => 'yearly',
						'email'                    => '',
						'customer_name'            => '',
						'customer_billing_address' => '',
					],
				],
			],
			'meta'       => [
				'template' => 'checkboxes_and_multiple_choices',
			],
			'providers'  => [
				'google-sheets' => [],
			],
		];
		// phpcs:enable WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned
	}
}
