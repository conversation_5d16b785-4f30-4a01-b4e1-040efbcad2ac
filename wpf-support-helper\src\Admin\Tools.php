<?php

namespace WPForms\SupportHelper\Admin;

use WP_Admin_Bar;

/**
 * Tools class for registering WPForms tools integration.
 *
 * @since {VERSION}
 */
class Tools {

	/**
	 * Settings page instance.
	 *
	 * @since {VERSION}
	 *
	 * @var SettingsPage
	 */
	private $settings_page;

	/**
	 * Constructor method for initializing the class with the provided SettingsPage instance.
	 *
	 * @since {VERSION}
	 *
	 * @param SettingsPage $settings_page An instance of the SettingsPage class.
	 */
	public function __construct( SettingsPage $settings_page ) {

		$this->settings_page = $settings_page;

		$this->hooks();
	}

	/**
	 * Hook into WordPress.
	 *
	 * @since {VERSION}
	 */
	private function hooks(): void {

		add_action( 'wpforms_admin_bar_menu_register_tools_submenu_wpforms-tools-wpcode_after', [ $this, 'register_tools_view' ] );
		add_filter( 'wpforms_tools_views', [ $this, 'register_view' ] );
		add_filter( 'plugin_action_links_' . WPF_SUPPORT_HELPER_PLUGIN_BASENAME, [ $this, 'add_plugin_action_links' ] );
	}

	/**
	 * Register view with WPForms Tools system.
	 *
	 * @since {VERSION}
	 *
	 * @param array $views Existing views array.
	 *
	 * @return array Modified views array.
	 */
	public function register_view( array $views ): array {

		$views['support-helper'] = $this->settings_page;

		return $views;
	}

	/**
	 * Register tools view after wpcode submenu item.
	 *
	 * @since {VERSION}
	 *
	 * @param WP_Admin_Bar $wp_admin_bar WordPress Admin Bar object.
	 */
	public function register_tools_view( WP_Admin_Bar $wp_admin_bar ): void {

		$wp_admin_bar->add_menu(
			[
				'parent' => 'wpforms-tools',
				'id'     => sanitize_key( 'support-helper' ),
				'title'  => esc_html( $this->settings_page->get_label() ),
				'href'   => $this->settings_page->get_link(),
			]
		);
	}

	/**
	 * Add plugin action links.
	 *
	 * @since {VERSION}
	 *
	 * @param array $links Existing plugin action links.
	 *
	 * @return array Modified plugin action links.
	 */
	public function add_plugin_action_links( array $links ): array {

		$settings_link = sprintf(
			'<a href="%s">%s</a>',
			esc_url( $this->settings_page->get_link() ),
			esc_html__( 'Settings', 'wpf-support-helper' )
		);

		array_unshift( $links, $settings_link );

		return $links;
	}
}
