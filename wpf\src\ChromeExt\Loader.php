<?php

namespace WPForms\DevTools\ChromeExt;

/**
 * Chrome extension backend loader class.
 *
 * @since 0.40
 */
final class Loader {

	/**
	 * Have the only available instance of the class.
	 *
	 * @since 0.40
	 *
	 * @var Loader
	 */
	private static $instance;

	/**
	 * Get instance.
	 *
	 * @since 0.40
	 *
	 * @return Loader
	 */
	public static function instance(): Loader {

		if ( ! isset( self::$instance ) && ! ( self::$instance instanceof self ) ) {
			self::$instance = new self();
		}

		return self::$instance;
	}

	/**
	 * Constructor.
	 *
	 * @since 0.40
	 */
	public function __construct() {

		new Ajax();

		$this->hooks();
	}

	/**
	 * Register hooks.
	 *
	 * @since 0.40
	 */
	private function hooks(): void {

		add_action( 'admin_head', [ $this, 'output_meta' ] );
		add_action( 'wp_head', [ $this, 'output_meta' ] );
	}

	/**
	 * Output meta tag.
	 *
	 * @since 0.40
	 */
	public function output_meta(): void {

		printf(
			'<meta name="wpf-chrome-ext" content="%1$s">',
			esc_html( wp_json_encode( $this->get_meta_data() ) )
		);
	}

	/**
	 * Get meta data.
	 *
	 * @since 0.40
	 *
	 * @return array
	 */
	private function get_meta_data(): array {

		return [
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce'    => wp_create_nonce( 'wpf-chrome-ext-nonce' ),
		];
	}
}
