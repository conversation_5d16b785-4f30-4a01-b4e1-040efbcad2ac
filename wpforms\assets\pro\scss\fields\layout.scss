// Layout field frontend styles.
//
// @since 1.7.7
@import '../../../scss/frontend/modern/base/variables';

div.wpforms-container .wpforms-form {

	.wpforms-field-repeater {
		.wpforms-layout-column {
			// Override field size in columns - always full width.
			input[type=text],
			input[type=range],
			input[type=email],
			input[type=url],
			input[type=tel],
			input[type=number],
			input[type=password],
			input[type=file],
			select,
			textarea,
			.wpforms-field-row,
			.choices,
			.wpforms-order-summary-container {
				max-width: 100%;
			}
		}
	}

	.wpforms-field-layout,
	.wpforms-field-repeater {

		.wpforms-layout-row {
			display: flex;
			flex-wrap: nowrap;
			justify-content: space-between;
			margin-right: -10px;
			margin-left: -10px;
		}

		// Columns layout.
		.wpforms-field-layout-columns {
			display: flex;
			flex-wrap: nowrap;
			justify-content: space-between;
			margin-right: -10px;
			margin-left: -10px;
		}

		.wpforms-layout-column {
			padding: 0 10px;
			word-break: break-word;

			// Preset column sizes.
			&-20 {
				width: 20%;
			}

			&-25 {
				width: 25%;
			}

			&-30 {
				width: 30%;
			}

			&-33 {
				width: 33.33333%;
			}

			&-40 {
				width: 40%;
			}

			&-50 {
				width: 50%;
			}

			&-60 {
				width: 60%;
			}

			&-67 {
				width: 66.66666%;
			}

			&-70 {
				width: 70%;
			}

			&-100 {
				width: 100%;
			}

			&:not(.wpforms-layout-column-100) {
				// Override field size in columns - always full width.
				input[type=text],
				input[type=range],
				input[type=email],
				input[type=url],
				input[type=tel],
				input[type=number],
				input[type=password],
				input[type=file],
				select,
				textarea,
				.wpforms-field-row,
				.choices,
				.wpforms-order-summary-container {
					max-width: 100%;
				}
			}
		}

		// Disable field row columns inside columns.
		.wpforms-layout-column {
			.wpforms-field-row {

				.wpforms-five-sixths,
				.wpforms-four-sixths,
				.wpforms-four-fifths,
				.wpforms-one-fifth,
				.wpforms-one-fourth,
				.wpforms-one-half,
				.wpforms-one-sixth,
				.wpforms-one-third,
				.wpforms-three-fourths,
				.wpforms-three-fifths,
				.wpforms-three-sixths,
				.wpforms-two-fourths,
				.wpforms-two-fifths,
				.wpforms-two-sixths,
				.wpforms-two-thirds {
					float: none;
					margin: 0 0 8px 0;
					width: 100%;

					&:last-child {
						margin-bottom: 0;
					}
				}
			}
		}

		// Small columns adjustments for different fields.
		.wpforms-layout-column {

			// Date / Time field.
			.wpforms-field-date-time {
				.wpforms-field-row {
					flex-direction: row;
				}

				.wpforms-field-row-block {
					padding: 0 10px;

					&:first-child {
						padding-inline-start: 0;
					}

					&:last-child {
						padding-inline-end: 0;
					}
				}

				.wpforms-datepicker-wrap {
					.wpforms-field-small,
					.wpforms-field-medium {
						& + .wpforms-datepicker-clear {
							right: 10px;
						}
					}
				}
			}

			&-20,
			&-25,
			&-30 {
				// Password field.
				.wpforms-field-password {
					.wpforms-field-row-block {
						width: 100%;
						margin-bottom: 10px;
						margin-left: 0;
					}
				}
			}

			&-20,
			&-25,
			&-30,
			&-33 {
				// Authorize.NET.
				.wpforms-field-authorize_net {
					.wpforms-field-row {
						display: flex;
						flex-direction: column;

						& > div {
							position: relative;
							margin-bottom: 10px;
							width: 100%;
						}
					}
				}

				// Date Time field.
				.wpforms-field-date-time {
					.wpforms-field-row {
						flex-direction: column;

						.wpforms-field-row-block {
							width: 100%;
							padding: 0;

							&:first-child:not(:only-child) {
								margin-bottom: 15px;
							}
						}
					}
				}

				// Choices (checkboxes, radio) fields.
				.wpforms-list-inline,
				.wpforms-list-2-columns,
				.wpforms-list-3-columns {
					ul {
						flex-direction: column;

						li {
							width: 100%;
							max-width: 100%;
							margin: 0 0 5px 0 !important;
							padding-right: 0 !important;
						}
					}
				}

				.wpforms-summary-enabled {
					.wpforms-order-summary-container {
						display: none;
					}

					.wpforms-payment-total {
						display: block !important;
					}
				}
			}

			// Icon Choices: 50% and smaller columns (Checkboxes, Multiple Choice, Checkbox Items, Multiple Items).
			&-20,
			&-25,
			&-30,
			&-33,
			&-50 {

				// All layouts except Inline should become single column.
				.wpforms-field:not(.wpforms-list-inline) {

					ul.wpforms-icon-choices {
						flex-direction: column;

						li {
							width: 100%;
							max-width: 100%;
						}
					}
				}

				// Icon Choices items use larger spacing.
				ul.wpforms-icon-choices {
					li {
						margin-bottom: 20px !important;
					}
				}
			}
		}

		// Adjustments for different fields in columns.
		.wpforms-layout-column {

			// Rich Text field.
			.wpforms-field-richtext {
				label.wpforms-field-label {
					margin-top: 0;
					margin-bottom: 4px;
				}
			}

			// Square CC field.
			.wpforms-field-square {
				.wpforms-field-square-number {
					.sq-card-wrapper {
						min-width: auto;
					}
				}
			}

			// NPS and LS fields.
			.wpforms-field-net_promoter_score,
			.wpforms-field-likert_scale {
				overflow-x: auto;

				table {
					min-width: 250px;
				}
			}
		}

		.wpforms-layout-column:not(.wpforms-layout-column-100) {
			// Payment Quantity.
			.wpforms-payment-quantities-enabled {
				&.wpforms-field-select-style-modern {
					.wpforms-field-row {
						width: calc(100% - 85px);
						max-width: 100%;
					}

					.wpforms-payment-quantity {
						max-width: 70px;
						margin-left: 0;
					}
				}

				select.wpforms-payment-price {
					width: calc(100% - 85px);
					max-width: 100%;
				}

				.wpforms-single-item-price-content .wpforms-single-item-price {
					width: calc(100% - 70px);
				}
			}

			// Date / Time field.
			.wpforms-field-date-time {
				.wpforms-field-date-dropdown-wrap {
					width: auto;
					margin: 0 -5px 0 -5px;
					max-width: calc(100% + 10px);
				}
			}
		}
	}

	// Small layout column adjustments for different fields.
	.wpforms-field-layout {
		.wpforms-layout-column {
			&-100 {
				// Date / Time field.
				.wpforms-field-date-time {
					.wpforms-datepicker-wrap {
						.wpforms-field-small {
							& + .wpforms-datepicker-clear {
								right: calc(75% + 10px); // Max width small field is 25%.
							}
						}

						.wpforms-field-medium {
							& + .wpforms-datepicker-clear {
								right: calc(40% + 10px); // Max width medium field is 60%.
							}
						}
					}
				}
			}
		}
	}

	.wpforms-field-layout {
		& > .wpforms-field-label {
			font-style: normal;
			font-weight: 700;
			font-size: 22px;
			line-height: 22px;
			margin: 30px 0 15px 0;
			padding: 45px 0 0 0;
			border-top: 1px solid #dddddd;
		}

		& > .wpforms-field-description {
			margin: -$spacing_xs 0 $spacing_m 0;

			&:first-child {
				margin-top: $spacing_m;
			}
		}
	}

	.wpforms-field {
		// Section Divider.
		&.wpforms-field-divider {

			// Layout Blocks after Section Divider field.
			& + .wpforms-field-layout {
				& > .wpforms-field-label {
					margin-top: 0;
				}
			}
		}
	}

	.wpforms-field-container {
		.wpforms-field-layout {

			// The first field in the form is layout `blocks`.
			&:first-child {
				& > .wpforms-field-label {
					border-top: none;
					margin-top: 0;
					padding-top: 0;
				}
			}
		}

		// The last field in the form is layout `blocks`.
		.wpforms-page:last-child {
			.wpforms-field-layout {
				&:has( + .wpforms-field-pagebreak ) {
					padding-bottom: $spacing_m;
				}
			}
		}
	}
}

@media only screen and (max-width: 600px) {
	div.wpforms-container .wpforms-form {
		// Small layout column adjustments for different fields.
		.wpforms-field-layout {
			.wpforms-layout-column {
				&-100 {
					// Date / Time field.
					.wpforms-field-date-time {
						.wpforms-datepicker-wrap {
							.wpforms-field-small,
							.wpforms-field-medium {
								& + .wpforms-datepicker-clear {
									right: 10px;
								}
							}
						}
					}
				}
			}
		}
	}
}
