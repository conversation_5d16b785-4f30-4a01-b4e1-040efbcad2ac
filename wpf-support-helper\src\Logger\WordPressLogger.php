<?php
/**
 * WordPress Logger Implementation.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper\Logger;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * WordPress-compatible logger implementation.
 *
 * @since {VERSION}
 */
class WordPressLogger implements LoggerInterface {

	/**
	 * Current log level.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private $level;

	/**
	 * Log level priorities (lower number = higher priority).
	 *
	 * @since {VERSION}
	 *
	 * @var array
	 */
	private $level_priorities = [
		self::EMERGENCY => 0,
		self::ALERT     => 1,
		self::CRITICAL  => 2,
		self::ERROR     => 3,
		self::WARNING   => 4,
		self::NOTICE    => 5,
		self::INFO      => 6,
		self::DEBUG     => 7,
	];

	/**
	 * Log prefix.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private $prefix;

	/**
	 * Constructor.
	 *
	 * @since {VERSION}
	 *
	 * @param string $level  Initial log level.
	 * @param string $prefix Log message prefix.
	 */
	public function __construct( string $level = self::ERROR, string $prefix = '[WPForms Support Helper]' ) {

		$this->level  = $level;
		$this->prefix = $prefix;
	}

	/**
	 * Log an emergency message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function emergency( string $message, array $context = [] ): void {

		$this->log( self::EMERGENCY, $message, $context );
	}

	/**
	 * Log an alert message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function alert( string $message, array $context = [] ): void {

		$this->log( self::ALERT, $message, $context );
	}

	/**
	 * Log a critical message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function critical( string $message, array $context = [] ): void {

		$this->log( self::CRITICAL, $message, $context );
	}

	/**
	 * Log an error message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function error( string $message, array $context = [] ): void {

		$this->log( self::ERROR, $message, $context );
	}

	/**
	 * Log a warning message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function warning( string $message, array $context = [] ): void {

		$this->log( self::WARNING, $message, $context );
	}

	/**
	 * Log a notice message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function notice( string $message, array $context = [] ): void {

		$this->log( self::NOTICE, $message, $context );
	}

	/**
	 * Log an info message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function info( string $message, array $context = [] ): void {

		$this->log( self::INFO, $message, $context );
	}

	/**
	 * Log a debug message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function debug( string $message, array $context = [] ): void {

		$this->log( self::DEBUG, $message, $context );
	}

	/**
	 * Log a message with arbitrary level.
	 *
	 * @since {VERSION}
	 *
	 * @param string $level   Log level.
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return void
	 */
	public function log( string $level, string $message, array $context = [] ): void {

		// Check if logging is enabled and level is allowed.
		if ( ! $this->should_log( $level ) ) {
			return;
		}

		// Format the log message.
		$formatted_message = $this->format_message( $level, $message, $context );

		// Log using WordPress error_log.
		// phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_error_log
		error_log( $formatted_message );
	}

	/**
	 * Set the minimum log level.
	 *
	 * @since {VERSION}
	 *
	 * @param string $level Minimum log level.
	 *
	 * @return void
	 */
	public function set_level( string $level ): void {

		if ( isset( $this->level_priorities[ $level ] ) ) {
			$this->level = $level;
		}
	}

	/**
	 * Get the current log level.
	 *
	 * @since {VERSION}
	 *
	 * @return string Current log level.
	 */
	public function get_level(): string {

		return $this->level;
	}

	/**
	 * Check if a log level is enabled.
	 *
	 * @since {VERSION}
	 *
	 * @param string $level Log level to check.
	 *
	 * @return bool True if level is enabled, false otherwise.
	 */
	public function is_level_enabled( string $level ): bool {

		if ( ! isset( $this->level_priorities[ $level ] ) ) {
			return false;
		}

		return $this->level_priorities[ $level ] <= $this->level_priorities[ $this->level ];
	}

	/**
	 * Get all available log levels.
	 *
	 * @since {VERSION}
	 *
	 * @return array Array of log levels.
	 */
	public function get_levels(): array {

		return array_keys( $this->level_priorities );
	}

	/**
	 * Check if we should log a message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $level Log level.
	 *
	 * @return bool True if should log, false otherwise.
	 */
	private function should_log( string $level ): bool {

		// Check if WordPress debug logging is enabled.
		if ( ! defined( 'WP_DEBUG_LOG' ) || ! WP_DEBUG_LOG ) {
			return false;
		}

		// Check if level is enabled.
		return $this->is_level_enabled( $level );
	}

	/**
	 * Format log message.
	 *
	 * @since {VERSION}
	 *
	 * @param string $level   Log level.
	 * @param string $message Log message.
	 * @param array  $context Additional context data.
	 *
	 * @return string Formatted log message.
	 */
	private function format_message( string $level, string $message, array $context = [] ): string {

		// Start with prefix and level.
		$formatted = sprintf( '%s [%s] %s', $this->prefix, strtoupper( $level ), $message );

		// Add context if provided.
		if ( ! empty( $context ) ) {
			$formatted .= ' | Context: ' . wp_json_encode( $context );
		}

		return sprintf( '[%s] %s', current_time( 'Y-m-d H:i:s' ), $formatted );
	}

	/**
	 * Create logger with debug level enabled.
	 *
	 * @since {VERSION}
	 *
	 * @param string $prefix Log message prefix.
	 *
	 * @return WordPressLogger Debug logger instance.
	 */
	public static function debug_logger( string $prefix = '[WPForms Support Helper]' ): WordPressLogger {

		return new self( self::DEBUG, $prefix );
	}

	/**
	 * Create logger with error level enabled.
	 *
	 * @since {VERSION}
	 *
	 * @param string $prefix Log message prefix.
	 *
	 * @return WordPressLogger Error logger instance.
	 */
	public static function error_logger( string $prefix = '[WPForms Support Helper]' ): WordPressLogger {

		return new self( self::ERROR, $prefix );
	}
}
