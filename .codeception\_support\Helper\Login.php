<?php

namespace Helper;

use Codeception\Module;
use Codeception\Exception\ModuleException;

/**
 * Login helper for handling authentication in acceptance tests.
 * 
 * Provides robust login handling with proper state management.
 */
class Login extends Module
{
    /**
     * Ensure user is logged in as admin
     * 
     * @throws ModuleException
     */
    public function ensureLoggedIn()
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        // Check if already logged in
        $webDriver->amOnPage('/wp-admin/');
        
        try {
            $webDriver->waitForElementVisible('body.login', 3);
            // Need to login
            $this->loginAsAdmin();
        } catch (\Exception $e) {
            // Already logged in, check for dashboard
            $webDriver->waitForElementVisible('#wpadminbar', 5);
        }
    }

    /**
     * Login as admin user
     * 
     * @throws ModuleException
     */
    public function loginAsAdmin()
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        $webDriver->amOnPage('/wp-login.php');
        $webDriver->waitForElementVisible('body.login', 10);
        
        // Get credentials from configuration
        $username = $this->_getConfig('adminUsername') ?: 'admin';
        $password = $this->_getConfig('adminPassword') ?: 'password';
        
        $webDriver->fillField('#user_login', $username);
        $webDriver->fillField('#user_pass', $password);
        $webDriver->click('#wp-submit');
        
        // Wait for successful login
        $webDriver->waitForElementVisible('#wpadminbar', 10);
        
        // Dismiss any welcome modals or notices
        $this->dismissWelcomeModals();
    }

    /**
     * Logout current user
     * 
     * @throws ModuleException
     */
    public function logout()
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        try {
            // Try to logout via admin bar
            $webDriver->moveMouseOver('#wp-admin-bar-my-account');
            $webDriver->waitForElementVisible('#wp-admin-bar-logout', 5);
            $webDriver->click('#wp-admin-bar-logout');
            
            // Wait for login page
            $webDriver->waitForElementVisible('body.login', 10);
        } catch (\Exception $e) {
            // Fallback: go directly to logout URL
            $webDriver->amOnPage('/wp-login.php?action=logout');
            $webDriver->click('a'); // Confirm logout
            $webDriver->waitForElementVisible('body.login', 10);
        }
    }

    /**
     * Check if user is currently logged in
     * 
     * @return bool
     */
    public function isLoggedIn()
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        try {
            return count($webDriver->_findElements('#wpadminbar')) > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Login with specific credentials
     * 
     * @param string $username Username
     * @param string $password Password
     * @throws ModuleException
     */
    public function loginAs($username, $password)
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        $webDriver->amOnPage('/wp-login.php');
        $webDriver->waitForElementVisible('body.login', 10);
        
        $webDriver->fillField('#user_login', $username);
        $webDriver->fillField('#user_pass', $password);
        $webDriver->click('#wp-submit');
        
        // Wait for successful login
        $webDriver->waitForElementVisible('#wpadminbar', 10);
        
        // Dismiss any welcome modals or notices
        $this->dismissWelcomeModals();
    }

    /**
     * Dismiss welcome modals and notices after login
     * 
     * @throws ModuleException
     */
    private function dismissWelcomeModals()
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        // Handle WordPress welcome panel
        try {
            if ($webDriver->_findElements('#welcome-panel .welcome-panel-close')) {
                $webDriver->click('#welcome-panel .welcome-panel-close');
            }
        } catch (\Exception $e) {
            // Panel might not exist, continue
        }
        
        // Handle WPForms welcome modal
        try {
            if ($webDriver->_findElements('#wpforms-splash-modal .wpforms-splash-modal-close')) {
                $webDriver->click('#wpforms-splash-modal .wpforms-splash-modal-close');
                $webDriver->waitForElementNotVisible('#wpforms-splash-modal', 5);
            }
        } catch (\Exception $e) {
            // Modal might not exist, continue
        }
        
        // Handle admin notices
        try {
            if ($webDriver->_findElements('.notice-dismiss')) {
                $webDriver->click('.notice-dismiss');
            }
        } catch (\Exception $e) {
            // Notice might not exist, continue
        }
        
        // Wait for any animations to complete
        $webDriver->wait(1);
    }

    /**
     * Force login by clearing cookies and logging in fresh
     * 
     * @throws ModuleException
     */
    public function forceLogin()
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        // Clear all cookies to ensure clean state
        $webDriver->resetCookie();
        
        // Now login
        $this->loginAsAdmin();
    }

    /**
     * Check if on login page
     * 
     * @return bool
     */
    public function isOnLoginPage()
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        try {
            return count($webDriver->_findElements('body.login')) > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Wait for login to complete
     * 
     * @param int $timeout Maximum time to wait
     * @throws ModuleException
     */
    public function waitForLoginComplete($timeout = 15)
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        // Wait for admin bar to appear (indicates successful login)
        $webDriver->waitForElementVisible('#wpadminbar', $timeout);
        
        // Wait for page to fully load
        $webDriver->waitForJS('return document.readyState === "complete"', $timeout);
        
        // Additional wait for any post-login scripts
        $webDriver->wait(1);
    }
}
