<?php

namespace WPForms\SupportHelper;

/**
 * Main WPForms Support Helper Plugin class.
 *
 * @since {VERSION}
 */
class Plugin {

	/**
	 * Plugin instance.
	 *
	 * @since {VERSION}
	 *
	 * @var Plugin
	 */
	private static $instance;

	/**
	 * Admin notices instance.
	 *
	 * @since {VERSION}
	 *
	 * @var ActivationInterface[]
	 */
	private $activation_instances;

	/**
	 * Get plugin instance.
	 *
	 * @since {VERSION}
	 *
	 * @return Plugin
	 */
	public static function instance(): Plugin {

		if ( ! ( self::$instance instanceof self ) ) {
			self::$instance = new self();

			self::$instance->init();
		}

		return self::$instance;
	}

	/**
	 * Initialize the plugin.
	 *
	 * @since {VERSION}
	 */
	private function init(): void {

		// Check if we meet minimum requirements.
		if ( ! $this->meets_requirements() ) {
			return;
		}

		// Hook into WordPress.
		$this->hooks();
	}

	/**
	 * Check if plugin meets minimum requirements.
	 *
	 * @since {VERSION}
	 *
	 * @return bool
	 */
	private function meets_requirements(): bool { // phpcs:ignore WPForms.PHP.HooksMethod.InvalidPlaceForAddingHooks

		global $wp_version;

		// Check WordPress version.
		if ( version_compare( $wp_version, '5.5', '<' ) ) {
			add_action( 'admin_notices', [ $this, 'requirements_notice' ] );

			return false;
		}

		// Check PHP version.
		if ( PHP_VERSION_ID < 70200 ) {
			add_action( 'admin_notices', [ $this, 'requirements_notice' ] );

			return false;
		}

		return true;
	}

	/**
	 * Display requirements notice.
	 *
	 * @since {VERSION}
	 */
	public function requirements_notice(): void {

		printf(
			'<div class="notice notice-error"><p>%s</p></div>',
			esc_html__( 'WPForms Support Helper requires WordPress 5.5+ and PHP 7.2+. Please update your environment.', 'wpf-support-helper' )
		);
	}

	/**
	 * Initialize plugin components.
	 *
	 * @since {VERSION}
	 */
	public function init_components(): void {

		// Initialize service provider for dependency injection.
		$service_provider = new ServiceProvider();

		// Configure for environment.
		if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
			$service_provider->configure_for_testing();
		} else {
			$service_provider->configure_for_production();
		}

		$service_provider->register_all();

		// Get services from container.
		$this->activation_instances = [
			$service_provider->get( 'admin_notices' ),
			$service_provider->get( 'modsecurity' ),
		];
	}

	/**
	 * Hook into WordPress.
	 *
	 * @since {VERSION}
	 */
	private function hooks(): void {

		// Plugin activation/deactivation hooks.
		register_activation_hook( WPF_SUPPORT_HELPER_PLUGIN_FILE, [ $this, 'activate' ] );
		register_deactivation_hook( WPF_SUPPORT_HELPER_PLUGIN_FILE, [ $this, 'deactivate' ] );

		// Load text domain.
		add_action( 'plugins_loaded', [ $this, 'load_textdomain' ] );
		add_action( 'wpforms_loaded', [ $this, 'init_components' ] );
	}

	/**
	 * Plugin activation.
	 *
	 * @since {VERSION}
	 */
	public function activate(): void {

		$this->init_components();

		foreach ( $this->activation_instances as $activation_instance ) {
			$activation_instance->activate_plugin();
		}
	}

	/**
	 * Plugin deactivation.
	 *
	 * @since {VERSION}
	 */
	public function deactivate(): void {

		$this->init_components();

		foreach ( $this->activation_instances as $activation_instance ) {
			$activation_instance->deactivate_plugin();
		}
	}

	/**
	 * Load plugin text domain.
	 *
	 * @since {VERSION}
	 */
	public function load_textdomain(): void {

		load_plugin_textdomain(
			'wpf-support-helper',
			false,
			dirname( WPF_SUPPORT_HELPER_PLUGIN_BASENAME ) . '/languages'
		);
	}
}
