# Codeception Test Suite Configuration
#
# Suite for acceptance tests.
# Perform tests in browser using the WPWebDriver or WPBrowser.
# Use WPDb to set up your initial database fixture.
# If you need both WPWebDriver and WPBrowser tests - create a separate suite.

actor: AcceptanceTester
extensions:
    enabled:
        - Codeception\Extension\RunProcess:
              0: chromedriver --url-base=/wd/hub --port=9515
              sleep: 5 # wait 5 seconds for processes to boot
    commands:
        - Codeception\Command\GenerateWPUnit
        - Codeception\Command\GenerateWPRestApi
        - Codeception\Command\GenerateWPRestController
        - Codeception\Command\GenerateWPRestPostTypeController
        - Codeception\Command\GenerateWPAjax
        - Codeception\Command\GenerateWPCanonical
        - Codeception\Command\GenerateWPXMLRPC
modules:
    enabled:
        - Asserts
        - WPForms\Module
        - Cli
        - REST
        - WPCLI
        - Filesystem
        - \Helper\CliHelper
        - \Helper\CheckDebugLog
        - \Helper\WPFormsHelpers
        - \Helper\StepFailureLogger
        - VisualCeption
        - \Helper\Mailchimp
        - \Helper\CampaignMonitor
        - \Helper\GetResponse
        - \Helper\Sendinblue
        - \Helper\Mailerlite
        - \Helper\ActiveCampaign
        - \Helper\Drip
        - \Helper\CliDb
        - \Helper\DbHelpers
        - \Helper\RecordFailed
        - \Helper\StripeApi
        - \Helper\PayPal
        - \Helper\GoogleApiClient
        - \Helper\Crowdin
        - \Helper\ConvertKit
        - \Helper\SlackAPI
        - \Helper\ConstantContact
    config:
        REST:
          depends: PhpBrowser
          url: '%WP_URL%/wp-json/'
          part: Json
        WPCLI:
            path: '%WP_ABSOLUTE_PATH%'
            allow-root: true
        \Helper\DbHelpers:
            dsn: '%DB_DSN%'
            user: '%DB_USER%'
            password: '%DB_PASSWORD%'
            populate: false
            cleanup: false
            url: '%WP_URL%'
            tablePrefix: '%DB_TABLE_PREFIX%'
        \Helper\WPFormsHelpers:
            url: '%WP_URL%'
            port: 9515
            window_size: 1980x1050
            browser: chrome
            host: localhost
            clear_cookies: true
            restart: true
            adminUsername: '%WP_ADMIN_USERNAME%'
            adminPassword: '%WP_ADMIN_PASSWORD%'
            adminPath: '%WP_ADMIN_PATH%'
            eliteLicenseKey: '%WPFORMS_ELITE_LICENSE_KEY%'
            capabilities:
                acceptInsecureCerts: true
                chromeOptions:
                    binary: "/Applications/Google Chrome for Testing.app/Contents/MacOS/Google Chrome for Testing"
                    prefs:
                        download.default_directory: '%WP_ABSOLUTE_PATH%'
                "goog:chromeOptions":
                    args: []
        VisualCeption:
            maximumDeviation: 1
            saveCurrentImageIfFailure: true
            fullScreenShot: true
        \Helper\CliDb:
            dump: '.codeception/_data/testDumps/proDump.sql'
            liteDump: '.codeception/_data/testDumps/liteDump.sql'
            URLInDumps: '%URL_IN_DUMPS%'
            currentUrl: '%WP_URL%'
        \Helper\CheckDebugLog:
            debugLog: true
        \Helper\Mailchimp:
            apiKey: '%MAILCHIMP_API_KEY%'
            datacenter: '%MAILCHIMP_DATACENTER%'
            listId: '%MAILCHIMP_LIST_ID%'
        \Helper\CampaignMonitor:
            apiKey: '%CAMPAIGN_MONITOR_API_KEY%'
            clientId: '%CAMPAIGN_MONITOR_CLIENT_ID%'
            listId: '%CAMPAIGN_MONITOR_LIST_ID%'
        \Helper\Drip:
            apiKey: '%DRIP_API_KEY%'
            accountId: '%DRIP_ACCOUNT_ID%'
            campaignId: '%DRIP_CAMPAIGN_ID%'
        \Helper\GetResponse:
            apiKey: '%GETRESPONSE_API_KEY%'
            campaignId: '%GETRESPONSE_CAMPAIGN_ID%'
        \Helper\Sendinblue:
            apiKey: '%SENDINBLUE_API_KEY%'
        \Helper\Mailerlite:
            apiKey: '%MAILERLITE_API_KEY%'
        \Helper\ActiveCampaign:
            url: '%ACTIVECAMPAIGN_URL%'
            key: '%ACTIVECAMPAIGN_KEY%'
            email: '%ACTIVECAMPAIGN_EMAIL%'
            list: '%ACTIVECAMPAIGN_LIST%'
        \Helper\StripeApi:
            key: '%STRIPE_API_KEY%'
            password: '%STRIPE_API_PASSWORD%'
            stripeApiSubscriptionEndpoint: 'https://api.stripe.com/v1/subscriptions'
        \Helper\PayPal:
            email: '%PAYPAL_EMAIL%'
            password: '%PAYPAL_PASSWORD%'
        \Helper\Crowdin:
            apiKey: '%CROWDIN_API_KEY%'
        \Helper\ConvertKit:
            url: '%CONVERTKIT_URL%'
            key: '%CONVERTKIT_KEY%'
            secret: '%CONVERTKIT_SECRET%'
            email: '%CONVERTKIT_EMAIL%'
        \Helper\SlackAPI:
            token: '%SLACK_API_TOKEN%'
        \Helper\ConstantContact:
            clientId: '%CONSTANT_CONTACT_CLIENT_ID%'
            clientSecret: '%CONSTANT_CONTACT_CLIENT_SECRET%'
            refreshToken: '%CONSTANT_CONTACT_REFRESH_TOKEN%'
            accessToken: '%CONSTANT_CONTACT_ACCESS_TOKEN%'

step_decorators:
    - \Codeception\Step\TryTo

env:
    github-actions:
        extensions:
            enabled:
                - Codeception\Extension\RunProcess:
        modules:
            config:
                \Helper\WPFormsHelpers:
                    capabilities:
                        clear_cookies: true
                        restart: true
                        "goog:chromeOptions":
                            args: ["--headless", "--ignore-certificate-errors", "--no-sandbox", "--disable-dev-shm-usage", "--allow-http-background-page", "--enable-background-thread-pool" ]
    localHeadless:
        modules:
            config:
                \Helper\WPFormsHelpers:
                    capabilities:
                        "goog:chromeOptions":
                            args: ["--headless", "--log-level=1"]

    runFailed:
        extensions:
            enabled:
                - Codeception\Extension\Logger
                - Codeception\Extension\RunProcess:
        modules:
            disabled:
                - \Helper\RecordFailed
            config:
                \Helper\CheckDebugLog:
                    debugLog: true
                    failSuiteIfDebugLogAltered: true
                \Helper\WPFormsHelpers:
                    capabilities:
                        "goog:chromeOptions":
                            args: ["--headless", "--ignore-certificate-errors", "--no-sandbox", "--disable-dev-shm-usage", "--process-per-tab", "--disable-hang-monitor", "--allow-http-background-page", "--enable-background-thread-pool" ]

