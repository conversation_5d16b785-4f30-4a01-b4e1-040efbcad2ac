<?php

namespace WPForms\DevTools;

/**
 * Hooks parser.
 *
 * @since {VERSION}
 */
class Hooks {

	/**
	 * Base dir for path.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private $json_base_dir;

	/**
	 * Hooks constructor.
	 *
	 * @since {VERSION}
	 */
	public function __construct() {

		$uploads_dir = wp_get_upload_dir();

		$this->json_base_dir = $uploads_dir['basedir'] . '/wpf';
	}

	/**
	 * Get output data.
	 *
	 * @since {VERSION}
	 *
	 * @return array
	 */
	public function get_output() {

		return [
			'plugins' => $this->get_plugins(),
			'hooks'   => $this->get_hooks(),
			'type'    => isset( $_GET['type'] ) ? sanitize_key( $_GET['type'] ) : 'actions', // phpcs:ignore WordPress.Security.NonceVerification.Recommended
		];
	}

	/**
	 * Get current hooks json file path.
	 *
	 * @since {VERSION}
	 *
	 * @return string
	 */
	public function get_json_path() {

		return sprintf(
			'%1$s/%2$s/%3$s.json',
			$this->json_base_dir,
			! empty( $_GET['plugin'] ) ? sanitize_key( $_GET['plugin'] ) : 'wpforms', // phpcs:ignore WordPress.Security.NonceVerification.Recommended
			! empty( $_GET['type'] ) ? sanitize_key( $_GET['type'] ) : 'actions' // phpcs:ignore WordPress.Security.NonceVerification.Recommended
		);
	}

	/**
	 * Get decoded json file content.
	 *
	 * @since {VERSION}
	 *
	 * @param string $path Absolute path to json file.
	 *
	 * @return array
	 */
	public function get_json_content( $path ) {

		$json = [];

		if ( is_file( $path ) && is_readable( $path ) ) {
			$contents = file_get_contents( $path );
		}

		if ( ! empty( $contents ) ) {
			$data = json_decode( $contents, true );
		}

		if ( ! empty( $data['hooks'] ) ) {
			$json = $data['hooks'];
		}

		return $json;
	}

	/**
	 * Get ready to use data.
	 *
	 * Filters out only WPForms hooks and drops unnecessary properties.
	 *
	 * @since {VERSION}
	 *
	 * @param array $raw_data Raw hooks data retrieved from JSON file.
	 *
	 * @return array
	 */
	public function get_hooks_data( $raw_data ) {

		$hooks = [];

		foreach ( $raw_data as $raw_hook ) {
			// Skip on non-WPForms hooks.
			if ( strncmp( $raw_hook['name'], 'wpforms_', 8 ) !== 0 ) {
				continue;
			}

			$hook = [];

			$hook['name'] = $raw_hook['name'];
			$hook['file'] = $raw_hook['file'];
			$hook['type'] = $raw_hook['type'];
			$hook['doc']  = [];

			// Filter out empty docs (undocumented hooks).
			$raw_hook['doc'] = array_filter( $raw_hook['doc'] );

			// Prepare documented properties and usage example.
			if ( ! empty( $raw_hook['doc'] ) ) {
				$hook['doc'] = $this->prepare_properties( $raw_hook['doc'] );
			}

			$hooks[] = $hook;
		}

		return $hooks;
	}

	/**
	 * Get list of plugins.
	 *
	 * @since {VERSION}
	 *
	 * @return array
	 */
	private function get_plugins() {

		$plugins = wp_list_pluck( Main::wpforms_obj( 'addons' )->get_all(), 'title', 'slug' );

		return array_merge( [ 'wpforms' => 'WPForms' ], $plugins );
	}

	/**
	 * Get list of hooks.
	 *
	 * @since {VERSION}
	 *
	 * @return array
	 */
	private function get_hooks() {

		$raw_hooks = $this->get_json_content( $this->get_json_path() );

		return $this->get_hooks_data( $raw_hooks );
	}

	/**
	 * Prepare documented properties.
	 *
	 * @since {VERSION}
	 *
	 * @param array $raw_data Raw properties data parsed from inline docs.
	 *
	 * @return array
	 */
	private function prepare_properties( $raw_data ) {

		$data['description']      = isset( $raw_data['description'] ) ? $raw_data['description'] : '';
		$data['long_description'] = isset( $raw_data['long_description'] ) ? $raw_data['long_description'] : '';

		$data['tags'] = [
			'since' => [],
			'param' => [],
		];

		if ( isset( $raw_data['tags'] ) ) {
			// Fill in @since and @param tags separately.
			foreach ( $raw_data['tags'] as $tag ) {
				$data['tags'][ $tag['name'] ][] = $tag;
			}
		}

		return $data;
	}
}
