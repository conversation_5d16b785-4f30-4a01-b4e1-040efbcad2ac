<?php

namespace WPForms\DevTools\Abstracts;

// phpcs:ignore WPForms.PHP.UseStatement.UnusedUseStatement
use WP_Filesystem_Base;

/**
 * Abstract class MUPlugin.
 *
 * @since 0.34
 */
abstract class MUPlugin {

	/**
	 * MU-plugin source path.
	 *
	 * @since 0.34
	 *
	 * @var string
	 */
	protected $mu_plugin_source;

	/**
	 * MU-plugin destination path.
	 *
	 * @since 0.34
	 *
	 * @var string
	 */
	protected $mu_plugin_destination;

	/**
	 * Class constructor.
	 *
	 * @since 0.34
	 */
	public function __construct() {

		$this->mu_plugin_source      = static::MU_DIR . '/' . static::MU_FILENAME;
		$this->mu_plugin_destination = WPMU_PLUGIN_DIR . '/' . static::MU_FILENAME;

		$this->hooks();
	}


	/**
	 * Hooks.
	 *
	 * @since 0.34
	 *
	 * @return void
	 */
	protected function hooks() {

		register_activation_hook( WPFORMS_DEV_PLUGIN_FILE, [ $this, 'activation_hook' ] );
		register_deactivation_hook( WPFORMS_DEV_PLUGIN_FILE, [ $this, 'deactivation_hook' ] );
	}

	/**
	 * Activation hook.
	 *
	 * @since 0.34
	 *
	 * @return void
	 */
	public function activation_hook() {

		$this->copy_mu_plugin();
	}

	/**
	 * Deactivation hook.
	 *
	 * @since 0.34
	 *
	 * @return void
	 */
	public function deactivation_hook() {

		$this->delete_mu_plugin();
	}

	/**
	 * Copy a mu-plugin file to the mu-plugins dir.
	 *
	 * @since 0.34
	 *
	 * @return bool
	 */
	protected function copy_mu_plugin(): bool {

		$filesystem = $this->get_filesystem_direct();

		if ( ! $filesystem ) {
			return false;
		}

		if ( ! $filesystem->is_dir( WPMU_PLUGIN_DIR ) && ! $filesystem->mkdir( WPMU_PLUGIN_DIR ) ) {
			return false;
		}

		$result = $filesystem->copy(
			$this->mu_plugin_source,
			$this->mu_plugin_destination,
			true
		);

		if ( ! $result ) {
			$mu_filename = static::MU_FILENAME;

			// phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_error_log
			error_log( "Cannot install '$mu_filename' mu-plugin." );
		}

		return $result;
	}

	/**
	 * Delete a mu-plugin file.
	 *
	 * @since 0.34
	 *
	 * @return bool
	 */
	protected function delete_mu_plugin(): bool {

		$filesystem = $this->get_filesystem_direct();

		if ( ! $filesystem ) {
			return false;
		}

		$result = $filesystem->delete( $this->mu_plugin_destination );

		if ( ! $result ) {
			$mu_filename = static::MU_FILENAME;

			// phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_error_log
			error_log( "Cannot delete '$mu_filename' mu-plugin." );
		}

		return $result;
	}

	/**
	 * Get direct filesystem.
	 *
	 * @since 0.34
	 *
	 * @return WP_Filesystem_Base|null
	 * @todo  Add support for other filesystems.
	 */
	private function get_filesystem_direct() {

		global $wp_filesystem;

		if ( ! $wp_filesystem && ! WP_Filesystem() ) {
			return null;
		}

		if ( $wp_filesystem->method !== 'direct' ) {
			return null;
		}

		return $wp_filesystem;
	}
}
