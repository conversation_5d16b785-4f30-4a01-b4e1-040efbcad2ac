var WPFormsEducation=window.WPFormsEducation||{};WPFormsEducation.proCore=window.WPFormsEducation.proCore||((e,d,l)=>{var s={init:function(){l(s.ready)},ready:function(){s.events()},events:function(){s.openModalButtonClick(),s.activateButtonClick()},openModalButtonClick(){l(e).on("click",".education-modal",function(e){var t=l(this),o=t.data("action");if(o&&!["activate","install"].includes(o))switch(e.preventDefault(),e.stopImmediatePropagation(),o){case"upgrade":s.upgradeModal(t.data("name"),t.data("field-name"),WPFormsEducation.core.getUTMContentValue(t),t.data("license"),t.data("video"));break;case"license":s.licenseModal(t.data("name"),t.data("field-name"),WPFormsEducation.core.getUTMContentValue(t),t.data("redirect-url"))}})},activateButtonClick:function(){l(".wpforms-education-toggle-plugin-btn").on("click",function(e){var i=l(this);if(e.preventDefault(),e.stopImmediatePropagation(),!i.hasClass("inactive")){i.addClass("inactive");let t=i.closest(".wpforms-addon-form, .wpforms-education-page"),o=i.text(),e=i.data("plugin"),a=i.data("action"),n=i.data("type");i.html(WPFormsAdmin.settings.iconSpinner+o),WPFormsAdmin.setAddonState(e,a,n,function(e){e.success?location.reload():(e="object"==typeof e.data?wpforms_admin[n+"_error"]:e.data,t.append('<div class="msg error" style="display: none;">'+e+"</div>"),t.find(".msg").slideDown()),i.text(o),setTimeout(function(){i.removeClass("inactive"),t.find(".msg").slideUp("",function(){l(this).remove()})},5e3)},function(e){console.log(e.responseText)})}})},upgradeModal:function(e,t,o,a,n){var i,c,r,s;void 0!==a&&0!==a.length||(a="pro"),l.inArray(a,["pro","elite"])<0||(i=e+" "+wpforms_education.upgrade[a].title,c=Boolean(n),r=WPFormsEducation.core.getUpgradeModalWidth(c),void 0!==t&&0<t.length&&(i=t+" "+wpforms_education.upgrade[a].title),s=l.alert({backgroundDismiss:!0,title:i,icon:"fa fa-lock",content:wpforms_education.upgrade[a].message.replace(/%name%/g,e),boxWidth:r,theme:"modern,wpforms-education",closeIcon:!0,onOpenBefore:function(){c&&(this.$el.addClass("upgrade-modal has-video"),this.$btnc.after('<iframe src="'+n+'" class="pro-feature-video" frameborder="0" allowfullscreen="" width="475" height="267"></iframe>')),this.$body.find(".jconfirm-content").addClass("lite-upgrade")},buttons:{confirm:{text:wpforms_education.upgrade[a].button,btnClass:"btn-confirm",keys:["enter"],action:function(){d.open(WPFormsEducation.core.getUpgradeURL(o,a),"_blank")}}}}),l(d).on("resize",function(){r=WPFormsEducation.core.getUpgradeModalWidth(c),s.isOpen()&&s.setBoxWidth(r)}))},licenseModal(e,t,o,a=void 0){let n=t||e,i=wpforms_education.license.is_empty&&"undefined"!=typeof WPFormsBuilder,c=wpforms_education.license.prompt,r=wpforms_education.license.button;i&&(c=`
					<p>${wpforms_education.activate_license.prompt_part1}</p>
					<p>${wpforms_education.activate_license.prompt_part2}</p>
					<input type="password" id="wpforms-edu-modal-license-key" value="" placeholder="${wpforms_education.activate_license.placeholder}">
				`,r=wpforms_education.activate_license.button),l.alert({title:wpforms_education.license.title,content:c.replace(/%name%/g,`<strong>${n}</strong>`).replace(/~utm-content~/g,o),icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:r,btnClass:"btn-confirm",keys:["enter"],action(){if(i)return this.$$confirm.prop("disabled",!0).html(WPFormsEducation.core.getSpinner()+wpforms_education.activating),s.activateLicense(this,a),!1;d.open(wpforms_education.license.url.replace(/~utm-content~/g,o),"_blank")}},cancel:{text:wpforms_education.cancel}}})},activateLicense(o,a=void 0){var e=l("#wpforms-edu-modal-license-key").val();0===e.length?(o.close(),WPFormsEducation.core.errorModal(!1,wpforms_education.activate_license.enter_key)):l.post(wpforms_education.ajax_url,{action:"wpforms_verify_license",nonce:"undefined"!=typeof wpforms_builder?wpforms_builder.admin_nonce:wpforms_admin.nonce,license:e},function(e){var t;o.close(),e.success?WPFormsEducation.core.saveModal(wpforms_education.activate_license.success_title,`<p>${wpforms_education.activate_license.success_part1} ${wpforms_education.activate_license.success_part2}</p>`,{redirectUrl:a,saveConfirm:wpforms_education.activate_license.save_confirm}):(t=e.data.header??!1,e=e.data.msg??e.data,WPFormsEducation.core.errorModal(t,e))})}};return s})(document,window,jQuery),WPFormsEducation.proCore.init();