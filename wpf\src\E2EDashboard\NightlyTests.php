<?php

namespace WPF\E2EDashboard;

/**
 * Nightly Tests Dashboard.
 *
 * @since 1.0
 *
 * @codingStandardsIgnoreFile
 */
class NightlyTests {

	/**
	 * The Firestore instance.
	 *
	 * @since 1.0
	 * @var GoogleFireStore
	 *
	 */
	private $firestore;

	/**
	 * Initialize the Nightly Tests.
	 *
	 * @since 1.0
	 */
	public function __construct() {

		$this->firestore = new GoogleFireStore();
	}

	/**
	 * Initialize hooks.
	 *
	 * @since 1.0
	 */
	public function init() {

		add_action( 'admin_menu', [ $this, 'register_menu' ], 100 );
		add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_scripts' ] );
	}

	/**
	 * Enqueue scripts and styles for the admin page.
	 *
	 * @since 1.0
	 */
	public function enqueue_scripts() {

		$screen = get_current_screen();

		if ( $screen->id !== 'e2e-dashboard_page_wpforms-nightly-tests' ) {
			return;
		}

		wp_enqueue_style( 'dashicons' );
		wp_enqueue_style( 'admin-bar' );
		wp_enqueue_style( 'common' );

		wp_enqueue_style(
			'nightly-tests-styles',
			WPFORMS_DEV_TOOLS_URL . '/assets/e2e-dashboard/css/nightly-tests.css',
			[ 'dashicons', 'admin-bar', 'common' ],
			'1.0.0'
		);

		wp_enqueue_script(
			'cbp-ntaccordion-js',
			WPFORMS_DEV_TOOLS_URL . '/assets/e2e-dashboard/js/cbpNTAccordion.js',
			[ 'jquery' ],
			'1.0.0',
			true
		);

		wp_enqueue_script(
			'nightly-tests-js',
			WPFORMS_DEV_TOOLS_URL . '/assets/e2e-dashboard/js/nightly-tests.js',
			[ 'jquery', 'cbp-ntaccordion-js' ],
			'1.0',
			true
		);
	}

	/**
	 * Register the admin menu.
	 *
	 * @since 1.0
	 */
	public function register_menu() {

		add_submenu_page(
			'e2e-dashboard',
			'Nightly Tests',
			'Nightly Tests',
			'manage_options',
			'wpforms-nightly-tests',
			[ $this, 'render_page' ]
		);
	}

	/**
	 * Group failures from a set of failure entries to return tests failing in multiple PHP versions.
	 *
	 * @param array $failures Array of failures from the most recent run.
	 *
	 * @return array Grouped failures.
	 */
	private function getMultiVersionFailures( $failures ) {

		$tests = [];

		foreach ( $failures as $failure ) {

			// Each $failure should contain a 'failures' array with test data.
			$test_name     = $failure['failures']['testName'] ?? 'Unknown Test';
			$test_method   = $failure['failures']['testMethod'] ?? 'Unknown Method';
			$data_provider = $failure['failures']['dataProvider'] ?? '';
			$test_type     = $failure['testType'] ?? 'core'; // Get test type (core or addon)
			$key           = $test_name . '::' . $test_method . ( $data_provider ? '::' . $data_provider : '' ) . '::' . $test_type;

			if ( ! isset( $tests[ $key ] ) ) {
				$tests[ $key ] = [
					'test_name'       => $test_name,
					'test_method'     => $test_method,
					'data_provider'   => $data_provider,
					'test_type'       => $test_type,
					'php_versions'    => [],
					'failure_details' => [],
				];
			}

			$php_version = $failure['phpVersion'] ?? 'unknown';

			if ( ! in_array( $php_version, $tests[ $key ]['php_versions'], true ) ) {
				$tests[ $key ]['php_versions'][] = $php_version;
			}

			$message = $failure['failures']['message'] ?? 'No message provided';

			if ( strpos( $message, '(Session info:' ) !== false ) {
				$message = trim( explode( '(Session info:', $message )[0] );
			}

			$tests[ $key ]['failure_details'][] = [
				'php_version'   => $php_version,
				'error_type'    => $failure['failures']['exception'] ?? 'Unknown',
				'message'       => $message,
				'failed_step'   => 'Unable to locate element: ' . ( $failure['failures']['message'] ?? 'Unknown step' ),
				'test_location' => $failure['failures']['testFile'] ?? 'Unknown location',
			];
		}

		// Filter to keep only tests failing in more than one PHP version.
		$multiVersion = [];

		foreach ( $tests as $test ) {
			if ( count( $test['php_versions'] ) > 1 ) {

				$multiVersion[] = $test;
			}
		}

		return $multiVersion;
	}

	/**
	 * Render the admin page.
	 *
	 * @since 1.0
	 */
	public function render_page() {

		// Check if Firebase is properly configured.
		if ( ! $this->firestore->isConfigured() ) {
			$error_message = $this->firestore->getErrorMessage();
			?>
			<div class="wrap">
				<h1>Nightly Tests Dashboard</h1>
				<div class="notice notice-error">
					<p><strong>Firebase Configuration Error:</strong> <?php echo esc_html( $error_message ); ?></p>
					<p>Please add the required Firebase configuration constants to your wp-config.php file to use this
						feature.</p>
					<p>Example:</p>
					<pre style="background: #f0f0f0; padding: 10px; overflow: auto;">
						define('FIREBASE_PROJECT_ID', 'your-project-id');
						define('FIREBASE_PRIVATE_KEY_ID', 'your-private-key-id');
						define('FIREBASE_PRIVATE_KEY', '-----BEGIN PRIVATE KEY-----\nYour-Private-Key\n-----END PRIVATE KEY-----\n');
						define('FIREBASE_CLIENT_EMAIL', '<EMAIL>');
						define('FIREBASE_CLIENT_ID', 'your-client-id');
						define('FIREBASE_CLIENT_CERT_URL', 'https://www.googleapis.com/robot/v1/metadata/x509/your-service-account');
					</pre>
				</div>
			</div>
			<?php
			return;
		}

		$connection_status = $this->firestore->testConnection();
		?>
		<div class="wrap">
			<h1>Nightly Tests Dashboard</h1>
			<?php if ( $connection_status === true ): ?>
				<div class="notice notice-success">
					<p>Firebase connection successful.</p>
				</div>
			<?php else: ?>
				<div class="notice notice-error">
					<p>Firebase connection failed: <?php echo esc_html( $connection_status ); ?></p>
				</div>
			<?php endif; ?>

			<?php
			// Get failures from the most recent run.
			$recent_failures_most_recent = $this->firestore->getMostRecentRunFailures();
			$multiVersionFailures        = $this->getMultiVersionFailures( $recent_failures_most_recent );

			if ( ! empty( $multiVersionFailures ) ) : ?>
				<div class="notice notice-warning">
					<p>
						<strong>Warning:</strong> The following tests are failing in multiple PHP versions in the most
						recent run.
						This may indicate a regression or a test that needs updating.
					</p>
					<ul style="margin-left:20px;">
						<?php foreach ( $multiVersionFailures as $test ) : ?>
							<li>
								<strong><?php echo esc_html( $test['test_name'] . '::' . $test['test_method'] ); ?></strong>
								( PHP Versions: <?php echo esc_html( implode( ', ', $test['php_versions'] ) ); ?> )
							</li>
						<?php endforeach; ?>
					</ul>
				</div>
			<?php endif; ?>

			<div class="test-failures-container">
				<?php
				// Existing logic: get recent failures from multiple documents.
				$recent_failures = $this->firestore->getRecentFailures();

				if ( empty( $recent_failures ) ) {
					echo '<div class="notice notice-info"><p>No test failures found.</p></div>';
				} else {
					// Group failures by run ID.
					$grouped_failures = [];

					foreach ( $recent_failures as $failure ) {

						$run_id = $failure['documentId'] ?? 'unknown';

						if ( ! isset( $grouped_failures[ $run_id ] ) ) {
							$grouped_failures[ $run_id ] = [];
						}
						$grouped_failures[ $run_id ][] = $failure;
					}

					// Sort runs descending.
					uksort( $grouped_failures, function ( $a, $b ) {
						if ( $a === 'unknown' ) {

							return 1;
						}
						if ( $b === 'unknown' ) {

							return -1;
						}

						return strcmp( $b, $a );
					} );

					echo '<div class="cbp-ntaccordion-container">';
					echo '<ul id="cbp-ntaccordion" class="cbp-ntaccordion">';
					foreach ( $grouped_failures as $run_id => $failures ) {
						// For each run, group by PHP version and test type
						$grouped_by_php = [];

						foreach ( $failures as $failure ) {
							$phpVersion = $failure['phpVersion'] ?? 'unknown';
							$testType = $failure['testType'] ?? 'core';
							$key = $phpVersion . ($testType !== 'core' ? ' ' . ucfirst($testType) : '');

							if ( ! isset( $grouped_by_php[ $key ] ) ) {
								$grouped_by_php[ $key ] = [];
							}
							$grouped_by_php[ $key ][] = $failure;
						}
						?>
						<li id="test-<?php echo esc_attr( $run_id ); ?>"
							data-workflow-run-id="<?php echo esc_attr( $run_id ); ?>">
							<h3 class="cbp-nttrigger">
								<span class="workflow-name"><?php echo esc_html( $run_id ); ?></span>
								<span class="workflow-status status-failure">✕ <?php echo count( $failures ); ?> Failures</span>
							</h3>
							<div class="cbp-ntcontent">
								<!-- Nested accordion for PHP versions and test types -->
								<ul class="cbp-ntsubaccordion">
									<?php foreach ( $grouped_by_php as $phpVersion => $failures_php ) : ?>
										<li id="php-<?php echo esc_attr( $phpVersion ); ?>"
											data-php-version="<?php echo esc_attr( $phpVersion ); ?>">
											<h4 class="cbp-ntsubtrigger-php">
												<span class="php-version-label">PHP <?php echo esc_html( $phpVersion ); ?></span>
												<span class="failure-count">✕ <?php echo count( $failures_php ); ?> Failures</span>
											</h4>
											<div class="cbp-ntcontent">
												<!-- Individual test failures -->
												<ul class="cbp-ntsubsubaccordion">
													<?php foreach ( $failures_php as $failure ) :
														$failure_data = $failure['failures'] ?? [];
														$test_name = $failure_data['testName'] ?? 'Unknown Test';
														$test_method = $failure_data['testMethod'] ?? 'Unknown Method';
														$data_provider = $failure_data['dataProvider'] ?? '';
														$message = $failure_data['message'] ?? 'No message provided';
														if ( strpos( $message, '(Session info:' ) !== false ) {
															$message = trim( explode( '(Session info:', $message )[0] );
														}
														?>
														<li id="failure-<?php echo esc_attr( $failure['documentId'] ?? '' ); ?>">
															<h5 class="cbp-ntsubtrigger-test">
																<?php echo esc_html( $test_name . '::' . $test_method ); ?>
																<?php if ( $data_provider ) : ?>
																	<span class="data-provider">(<?php echo esc_html( $data_provider ); ?>)</span>
																<?php endif; ?>
															</h5>
															<div class="cbp-ntcontent">
																<div class="failure-detail">
																	<div class="info-row">
																		<span class="label">PHP Version:</span>
																		<span class="value"><?php echo esc_html( $failure['phpVersion'] ?? 'unknown' ); ?></span>
																	</div>
																	<div class="info-row">
																		<span class="label">Test Type:</span>
																		<span class="value"><?php echo esc_html( ucfirst( $failure['testType'] ?? 'core' ) ); ?></span>
																	</div>
																	<div class="info-row">
																		<span class="label">Error Type:</span>
																		<span class="value"><?php echo esc_html( $failure_data['exception'] ?? 'Unknown' ); ?></span>
																	</div>
																	<div class="info-row">
																		<span class="label">Message:</span>
																		<span class="value"><?php echo esc_html( $message ); ?></span>
																	</div>
																	<div class="info-row">
																		<span class="label">Location:</span>
																		<span class="value"><?php echo esc_html( $failure_data['testFile'] ?? 'Unknown location' ); ?></span>
																	</div>
																</div>
															</div>
														</li>
													<?php endforeach; ?>
												</ul>
											</div>
										</li>
									<?php endforeach; ?>
								</ul>
							</div>
						</li>
						<?php
					}
					echo '</ul>';
					echo '</div>';
				}
				?>
			</div>
		</div>
		<?php
	}
}
