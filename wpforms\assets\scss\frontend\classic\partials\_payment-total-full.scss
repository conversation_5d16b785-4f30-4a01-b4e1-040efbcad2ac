// Payment total field: Order Summary view.
//
// These styles are used on a form preview.
//
// @since 1.8.7

@import '../../../partials/fields/payments/order-summary';

// Basic styles.
.wpforms-container .wpforms-form,
.wpforms-confirmation-container-order-summary,
.wpforms-confirmation-container-full {
	@include order-summary-common;
	@include order-summary-sizes;

	.wpforms-order-summary-container {
		@include order-summary-fancy;
	}
}

// Color customizations.
.wpforms-container .wpforms-form,
.wpforms-confirmation-container-order-summary,
.wpforms-confirmation-container-full {
	.wpforms-order-summary-container {
		font-size: 16px;

		table.wpforms-order-summary-preview {
			border-color: color-mix( in srgb, currentColor 25%, transparent );

			tr {
				td:not(.wpforms-order-summary-preview-total) {
					color: color-mix( in srgb, currentColor 50%, transparent );
					border-color: color-mix( in srgb, currentColor 50%, transparent );
				}

				&.wpforms-order-summary-preview-coupon-total td.wpforms-order-summary-item-price {
					color: #990000;
					border-color: color-mix( in srgb, rgb(0, 0, 0) 25%, transparent )
				}

				&.wpforms-order-summary-preview-total td,
				&.wpforms-order-summary-preview-subtotal td {
					color: currentColor;
					border-color: color-mix( in srgb, currentColor 25%, transparent );
				}
			}
		}
	}
}

// Adjustments for confirmation messages.
.wpforms-confirmation-container-order-summary,
.wpforms-confirmation-container-full {
	.wpforms-order-summary-container {
		max-width: 100%;
		margin-bottom: 24px;

		tr td {
			color: color-mix( in srgb, currentColor 75%, transparent );
		}
	}
}
