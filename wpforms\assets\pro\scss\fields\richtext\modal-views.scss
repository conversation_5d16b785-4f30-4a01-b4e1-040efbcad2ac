// Rich Text field styles.
//
// Modals.
//
// @since 1.7.0

@import "vars";

// `Add Media` modal parent container.
.supports-drag-drop[id^="__wp-uploader-id-"] {
	z-index: 10000000000; // Bigger than the z-index of the Popup Maker's popup container.
}

// `Add Media` modal.
.media-modal,
.media-frame {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
	font-size: 12px;
	-webkit-overflow-scrolling: touch;
}

.media-modal {
	a, div {
		outline: 0;
	}

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		display: block;
		font-weight: 600;
		letter-spacing: inherit;
	}

	h1:not(.site-title), h2 {

		&:before {
			content: none;
		}
	}

	.setting {
		input,
		select {
			margin-left: 0;
			margin-right: 0;
			overflow: hidden;
		}
	}

	button:not(:hover):not(:active):not(.has-background):not(.check):not(.media-button-insert) {
		color: $dark_text;
		background-color: initial;

		&.clear-selection,
		&.delete-attachment {
			color: $red;
			font-weight: 400;
		}
	}

	button.media-button {
		text-shadow: none;
		box-shadow: none;
	}

	button.clear-selection,
	button.delete-attachment {
		background-color: transparent;
	}

	button {
		text-transform: none;
		font-family: inherit;
		font-size: 14px;
		font-weight: 400;
		line-height: 2;
		height: auto;
		padding-top: 0;
		padding-bottom: 0;
		letter-spacing: inherit;

		&.delete-attachment {
			font-size: 12px;
		}

		&.active:not(.media-menu-item),
		&.active:not(.media-menu-item):hover {
			font-weight: 600;
			text-transform: none;
		}
	}

	select {
		text-transform: none;
		font-family: inherit;
		font-size: 14px;
		font-weight: 400;
		line-height: 20px;
		height: auto;
		min-height: 30px;
		padding: 4px 2px 4px 6px;
		letter-spacing: inherit;
		background: $white;
		appearance: auto;
	}

	.media-modal-close {
		height: 50px;
	}

	button.media-button-insert:disabled:hover {
		font-weight: 400;
		text-transform: none;
		border: 1px solid $bd_color_hover;
	}

	button.media-modal-close:hover {
		background-color: initial;
		text-decoration: none;
		border: 1px solid transparent;
	}

	.media-attachments-filter-heading,
	.media-frame-menu-heading {
		padding-top: 0;
	}

	.media-menu-item {
		border-radius: 0;
	}

	.media-router {
		.media-menu-item {
			background-color: transparent;

			&:hover {
				background-color: transparent;
			}

			&.active {
				background-color: $white !important;
			}
		}
	}

	.attachment-details .setting.has-description {
		margin: 0;

		label {
			margin-bottom: 0;
		}
	}

	.attachments-browser .media-toolbar-secondary {
		max-width: 70%;

		.spinner {
			margin-top: 32px;
		}

		// Hide filters.
		.attachment-filters {
			display: none;
		}
	}

	.media-sidebar .description {
		font-size: 12px;
		line-height: 18px;
	}

	.attachment-details .setting,
	.media-sidebar .setting {
		&[data-setting="title"],
		&[data-setting="caption"],
		&[data-setting="description"] {
			display: none;
		}
	}

	.media-embed .setting span {
		font-weight: 400;
	}

	// Simplify media modal.
	// Hide left sidebar.
	.media-frame-menu,
	.media-frame-menu-heading {
		display: none;
	}

	.media-modal-content {
		.media-frame-title,
		.media-frame-router,
		.media-frame-content,
		.media-frame-toolbar {
			left: 0;
		}
	}

	.attachments-browser .search {
		height: 30px;
	}

	.copy-to-clipboard-container {
		clear: both;
	}
}

// `Insert link` modal.
.mce-container {
	.mce-grid .mce-custom-color-btn .mce-widget button {
		background-color: inherit;
		border-radius: inherit;
		color: inherit;
		transition: none;
		text-transform: none;
		letter-spacing: normal;
		outline: none;
	}

	//
	// `Keyboard Shortcuts` modal.
	//
	.wp-editor-help {
		padding-bottom: 10px !important;

		h1,
		h2 {
			&:before {
				content: none;
			}
		}

		td,
		th {
			vertical-align: top;
		}
	}

	//
	// `Color choose` modal.
	//
	table.mce-colorbutton-grid {
		min-width: inherit;
	}

	.mce-colorbtn-trans div {
		font-size: 14px;
		line-height: 14px;
	}

	//
	// `Insert link` modal.
	//

	// Input fields.
	.mce-combobox input,
	.mce-checkbox i.mce-i-checkbox,
	.mce-textbox,
	.mce-menubtn {
		border-color: $bd_color;
		border-radius: 0;
		background-color: $white;
		box-shadow: none;

		&:hover {
			border-color: $bd_color_hover;
			background-color: $white;
		}

		&.mce-opened,
		&.mce-active.mce-opened,
		&:focus,
		&:hover:focus {
			background-color: $white;
			border-color: $default_button_bd;
			box-shadow: $default_button_shadow;
		}
	}

	&.mce-panel {
		.mce-widget.mce-btn {

			// Target select.
			&.mce-listbox,
			&.mce-btn-flat {
				box-shadow: none;
				border-radius: 0;
				background-color: $white;
				transform: none;
			}

			// Common styles for action buttons (OK / Cancel).
			button {
				color: inherit;
				text-decoration: none;
				text-transform: inherit;
				letter-spacing: normal;
				background-color: initial;
			}

			// Color adjustments for action buttons.
			&.mce-primary {
				border-color: $primary_button_bd $primary_button_bd2 $primary_button_bd2;

				&:hover {
					box-shadow: 0 1px 0 $primary_button_bd2;
				}

				&:active {
					background-color: $primary_button_bd2;
					box-shadow: inset 0 2px 0 $primary_button_bd2;
				}
			}
		}

		// Close icon in top panel.
		.mce-window-head .mce-close,
		.mce-window-head .mce-close:hover {
			background-color: initial;
		}
	}
}

// Some themes might not have these styles.
// Also, WPForms Conversational Forms and Form Pages don't have them.
.screen-reader-text {
	border: 0 !important;
	clip: rect(0, 0, 0, 0) !important;
	height: 1px !important;
	margin: 0 !important;
	overflow: hidden !important;
	padding: 0 !important;
	position: absolute !important;
	width: 1px !important;
	word-wrap: normal !important;
}
