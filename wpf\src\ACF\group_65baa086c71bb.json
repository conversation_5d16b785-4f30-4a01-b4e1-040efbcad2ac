{"key": "group_65baa086c71bb", "title": "Whats New Details", "fields": [{"key": "field_65bac31cb94d3", "label": "Version", "name": "version", "type": "taxonomy", "instructions": "Version of WPForms required to use.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "taxonomy": "wpf_version", "field_type": "select", "allow_null": 0, "add_term": 1, "save_terms": 1, "load_terms": 0, "return_format": "object", "multiple": 0}, {"key": "field_65bac2fab94d2", "label": "Featured", "name": "featured", "type": "true_false", "instructions": "Display above.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 1, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_65bac2786f04c", "label": "New", "name": "new", "type": "true_false", "instructions": "Display \"New Feature\" badge.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 1, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_65baa086dc526", "label": "Primary Button", "name": "btn", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "Get Started", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_65baa086dc572", "label": "URL", "name": "btn_url", "type": "text", "instructions": "Available Merge Tags: {admin_url}, {license_key}", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_65baa086dc5ba", "label": "Secondary Button", "name": "btn_alt", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "Learn More", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_65baa086dc602", "label": "URL", "name": "btn_alt_url", "type": "text", "instructions": "Available Merge Tags: {admin_url}, {license_key}", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_65bac58378d65", "label": "Image Type", "name": "img", "type": "button_group", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"icon": "Icon", "illustration": "Illustration", "hero": "Hero"}, "allow_null": 0, "default_value": "icon", "layout": "horizontal", "return_format": "value"}, {"key": "field_65bad01e88d3d", "label": "Hero Image", "name": "img_hero", "type": "image", "instructions": "Width: 1480px, Height: >= 860px", "required": 1, "conditional_logic": [[{"field": "field_65bac58378d65", "operator": "==", "value": "hero"}]], "wrapper": {"width": "100", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": 1480, "min_height": 860, "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "jpg,png"}, {"key": "field_65bad09288d3e", "label": "Icon Image", "name": "img_icon", "type": "image", "instructions": "Width: 440px, Height: >= 440px", "required": 1, "conditional_logic": [[{"field": "field_65bac58378d65", "operator": "==", "value": "icon"}]], "wrapper": {"width": "100", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": 440, "min_height": 440, "min_size": "", "max_width": 440, "max_height": "", "max_size": "", "mime_types": "jpg,png"}, {"key": "field_65bad0ee88d3f", "label": "Illustration Image", "name": "img_illustration", "type": "image", "instructions": "Width: 900px, Height: >= 900px", "required": 1, "conditional_logic": [[{"field": "field_65bac58378d65", "operator": "==", "value": "illustration"}]], "wrapper": {"width": "100", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": 900, "min_height": 900, "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "jpg,png"}, {"key": "field_65bad16588d40", "label": "Image Shadow", "name": "img_shadow", "type": "button_group", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_65bac58378d65", "operator": "==", "value": "illustration"}]], "wrapper": {"width": "100", "class": "", "id": ""}, "choices": {"yes": "<PERSON>", "no": "No Shadow", "apply": "Apply Shadow"}, "allow_null": 0, "default_value": "yes", "layout": "horizontal", "return_format": "value"}], "location": [[{"param": "post_type", "operator": "==", "value": "wpf_whats_new"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "field", "hide_on_screen": ["permalink", "excerpt", "discussion", "comments", "revisions", "slug", "author", "format", "page_attributes", "featured_image", "categories", "tags", "send-trackbacks"], "active": true, "description": "", "modified": 1706843033}