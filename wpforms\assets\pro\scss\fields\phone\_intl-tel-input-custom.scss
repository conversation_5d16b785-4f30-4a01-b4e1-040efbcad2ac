// Custom CSS for the International Telephone Input field.
//
// @since 1.8.6

@import '../../../../scss/frontend/modern/base/colors';
@import '../../../../scss/frontend/modern/base/variables';
@import '../../../../scss/frontend/modern/base/mixins';
@import '../../../../scss/frontend/modern/full/mixins';

// TODO: Refactor this file to combine and remove duplicate styles.

// Override the default styles.
body,
div.wpforms-container .wpforms-form,
div.wpforms-container-full .wpforms-form,
#wpforms-conversational-form-page {
	.iti {

		// Fixes for update to v.16.0.8
		&--allow-dropdown {
			width: 100%;
		}

		&__country-list {
			// in case any container has text-align:center
			text-align: left;
			background-color: white !important;

			// Limit maximum width on mobiles (small screens) to avoid horizontal scrolling.
			@media (max-width: 600px) {
				max-width: 90vw;
			}
		}

		&__country {
			padding: 5px 10px !important;
			margin: 0 !important;
			color: #333333;
		}

		&__dial-code {
			vertical-align: baseline;
			line-height: 1;
		}

		&__country.iti__highlight {
			background-color: rgba(0, 0, 0, 0.05) !important;
		}

		&__flag-box, &__country-name, &__dial-code {
			line-height: 1;
		}

		// Fixes for update to v.19.2.16
		&.iti--inline-dropdown {
			.iti__dropdown-content {
				margin-top: 0;
				margin-bottom: 0;
				border: none;
				z-index: 101;

				.iti__country-list {
					max-height: 200px;
					border-width: var( --wpforms-field-border-size );
					border-style: var( --wpforms-field-border-style );
					border-color: var( --wpforms-field-border-color );
				}
			}
		}
	}
}

// Optinmonster fixes.
div[class*="wpfe-wrapper"] {
	.iti__flag.iti__in {
		margin: 10px !important;
	}

	.iti__arrow {
		border-left: var(--iti-triangle-border) solid transparent !important;
		border-right: var(--iti-triangle-border) solid transparent !important;
		border-top: var(--iti-arrow-height) solid var(--iti-arrow-color) !important;
		margin-inline-end: 3px !important;
	}
}

// Customize the styles for RTL.
body.rtl,
body.rtl div.wpforms-container .wpforms-form,
body.rtl div.wpforms-container-full .wpforms-form,
body.rtl #wpforms-conversational-form-page {

	.iti {

		input,
		input[type=text],
		input[type=tel] {
			padding-right: 0;
			padding-left: 36px;
		}

		&__flag-container {
			right: auto;
			left: 0;
		}

		.iti__country-container {
			right: 0;
			left: auto;
		}

		&__selected-flag {
			padding-right: 8px;
			padding-left: 6px;
		}

		&__arrow {
			margin-right: 6px;
			margin-left: 0;
		}

		&__country-list {
			text-align: right;
			margin-right: -1px;
			margin-left: 0;
		}

		&__flag-box,
		&__country-name {
			margin-right: 0;
			margin-left: 6px;
		}

		&__country-name {
			unicode-bidi: embed;
			direction: rtl;
		}

		&--allow-dropdown,
		&--separate-dial-code {

			input,
			input[type=text],
			input[type=tel] {
				padding-right: 52px !important;
				padding-left: 6px;
			}

			.iti__flag-container {
				right: 0;
				left: auto;
			}
		}
	}
}

body,
div.wpforms-container .wpforms-form,
div.wpforms-container-full .wpforms-form,
#wpforms-conversational-form-page {
	.iti--allow-dropdown {
		.iti__country-container {
			padding: 0;
			position: absolute;
			height: 100%;

			.iti__selected-country {
				border-top-left-radius: var(--wpforms-field-border-radius);
				border-bottom-left-radius: var(--wpforms-field-border-radius);
				border-width: var(--wpforms-field-border-size, 0);
				border-style: var(--wpforms-field-border-style);
				border-color: transparent;
				background-color: transparent;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: row;
				height: 100%;
				width: 100%;

				&:focus {
					@include wpforms-input-focus();
				}
			}

			.iti__dropdown-content {
				border-radius: var(--wpforms-field-border-radius);

				.iti__country-list {
					border-radius: var(--wpforms-field-border-radius);
					background: var(--wpforms-field-menu-color) !important;
					border-width: var(--wpforms-field-border-size);
					border-style: var(--wpforms-field-border-style);
					border-color: var(--wpforms-field-border-color);
					font-size: var(--wpforms-field-size-font-size);
					scrollbar-color: #ccc transparent;

					@include scrollbar(4px, transparent, var(--wpforms-field-text-color), 4px);

					.iti__country-name {
						color: var(--wpforms-field-text-color);
					}

					.iti__dial-code {
						color: var(--wpforms-field-text-color);
						font-size: calc(var(--wpforms-field-size-font-size) - 2px);
						opacity: 0.6;
					}
				}
			}
		}
	}
}
