var WPFormsEditEntry=window.WPFormsEditEntry||((e,i,s)=>{let l={$editForm:s("#wpforms-edit-entry-form"),$submitButton:s("#wpforms-edit-entry-update")},r={},a={init(){s(a.ready)},ready(){r.nonce=l.$editForm.find('input[name="nonce"]').val(),r.entryId=l.$editForm.find('input[name="wpforms[entry_id]"]').val(),a.initSavedFormData(),a.events(),wpf.initTooltips()},events(){l.$submitButton.on("click",a.clickUpdateButton),l.$editForm.on("wpformsAjaxBeforeSubmit",a.validateFields).on("wpformsAjaxSubmitFailed",a.submitFailed).on("wpformsAjaxSubmitSuccess",a.submitSuccess),s(i).on("beforeunload",a.beforeUnload),s(e).on("click",".wpforms-edit-entry-field-file-upload .delete",a.fileDelete),s(e).on("click",".wpforms-edit-entry-field-camera .delete",a.fileDelete)},initSavedFormData(){r.savedFormData=l.$editForm.serialize()},beforeUnload(e){if(l.$editForm.serialize()!==r.savedFormData)return e.returnValue="Leave site?",e.returnValue},clickUpdateButton(e){e.preventDefault(),l.$submitButton.prop("disabled",!0),a.preSubmitActions(),a.hideErrors(),l.$editForm.append(s("<input>",{type:"hidden",name:"_wp_http_referer",value:wpf.updateQueryString("_wp_http_referer",null)})),wpforms.formSubmitAjax(l.$editForm)},preSubmitActions(){let r=l.$editForm.data("formid");s(".wpforms-smart-phone-field").trigger("input"),s(".wpforms-edit-entry-field-file-upload a.disabled,.wpforms-edit-entry-field-camera a.disabled").each(function(){s(this).parent().remove()}),s(".wpforms-field-file-upload,.wpforms-field-camera").each(function(){var e=s(this);e.is(":empty")&&(e.closest(".wpforms-edit-entry-field-file-upload, .wpforms-edit-entry-field-camera").addClass("empty"),e.html(s("<span>",{class:"wpforms-entry-field-value",text:wpforms_admin_edit_entry.strings.entry_empty_file})))}),s(".wpforms-field-richtext").each(function(){var e=s(this).data("field-id"),e=tinyMCE.get("wpforms-"+r+"-field_"+e);e&&e.save()})},submitFailed(e,r){a.displayErrors(r),s.alert({title:wpforms_admin.heads_up,content:r.data.errors.general,icon:"fa fa-info-circle",type:"orange",buttons:{confirm:{text:wpforms_admin_edit_entry.strings.continue_editing,btnClass:"btn-confirm",keys:["enter"]},cancel:{text:wpforms_admin_edit_entry.strings.view_entry,action(){i.location.href=wpforms_admin_edit_entry.strings.view_entry_url}}}})},submitSuccess(e,r){a.initSavedFormData(),void 0!==r.data&&(s("#wpforms-entry-details .wpforms-entry-modified .date-time").text(r.data.modified),s.alert({title:wpforms_admin_edit_entry.strings.success,content:wpforms_admin_edit_entry.strings.msg_saved,icon:"fa fa-info-circle",type:"green",buttons:{confirm:{text:wpforms_admin_edit_entry.strings.continue_editing,btnClass:"btn-confirm",keys:["enter"]},cancel:{text:wpforms_admin_edit_entry.strings.view_entry,action(){i.location.href=wpforms_admin_edit_entry.strings.view_entry_url}}}}))},hideErrors(){l.$editForm.find(".wpforms-field.wpforms-has-error").removeClass("wpforms-has-error"),l.$editForm.find(".wpforms-error:not(label)").removeClass("wpforms-error"),l.$editForm.find("label.wpforms-error, em.wpforms-error").addClass("wpforms-hidden")},displayErrors(e){let r=e.data&&"errors"in e.data?e.data.errors:null;wpf.empty(r)||wpf.empty(r.field)||(r=r.field,Object.keys(r).forEach(function(e){a.displayFieldError(e,r[e]),a.displaySubfieldsErrors(e,r[e])}))},displayFieldError(e,r){var t,i,a;"string"!=typeof r||wpf.empty(e)&&"0"!==e||wpf.empty(r)||(t=(e="wpforms-"+l.$editForm.data("formid")+"-field_"+e)+"-error",i=l.$editForm.find("#"+e+"-container"),a=l.$editForm.find("#"+t),i.addClass("wpforms-has-error"),s("#"+e).addClass("wpforms-error"),0<a.length?a.html(r).removeClass("wpforms-hidden"):i.append('<label id="'+t+'" class="wpforms-error">'+r+"</label>"))},displaySubfieldsErrors(d,o){if("object"==typeof o&&!wpf.empty(o)&&!wpf.empty(d)){let s=l.$editForm.data("formid"),e="wpforms-"+s+"-field_"+d,n=l.$editForm.find("#"+e+"-container");Object.keys(o).forEach(function(e){var r,t,i,a=o[e];"string"==typeof a&&""!==a&&(r="wpforms[fields]["+d+"]["+e+"]",t="wpforms-"+s+"-field_"+d+"-"+e+"-error",i=l.$editForm.find("#"+t),n.hasClass("wpforms-has-error")||n.addClass("wpforms-has-error"),0<i.length?(n.find('[name="'+r+'"]').addClass("wpforms-error"),i.html(a).removeClass("wpforms-hidden")):(i='<label id="'+t+'" class="wpforms-error">'+a+"</label>",(n.hasClass("wpforms-field-likert_scale")?n.find("tr").eq(e.replace(/r/,"")):n.find('[name="'+r+'"]').addClass("wpforms-error")).after(i)))})}},fileDelete(e){e.preventDefault();let r=s(this),t=r.parent().find("a").first();s.confirm({title:!1,content:wpforms_admin_edit_entry.strings.entry_delete_file.replace("{file_name}",t.html()),icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_admin.ok,btnClass:"btn-confirm",keys:["enter"],action(){t.html(t.text().strike()),t.addClass("disabled"),r.parent().find('input[type="hidden"]').remove(),r.remove()}},cancel:{text:wpforms_admin.cancel,keys:["esc"]}}})},validateFields(e){a.validateSmartPhoneFields(e),a.validateNumbersFields(e)},validateSmartPhoneFields(t){s(".wpforms-smart-phone-field").each(function(){var e,r;s(this).val()&&(e=s(this).closest(".wpforms-field").data("field-id"),r=i.intlTelInput?.getInstance(this),s(this).triggerHandler("validate")||r?.isValidNumberPrecise()||(t.preventDefault(),a.displayFieldError(e,wpforms_settings.val_phone)))})},validateNumbersFields(t){s(".wpforms-field-number").each(function(){var e=s(this),r=e.find('input[type="number"]')[0];r.required=!1,r.checkValidity()||(e=e.data("field-id"),r=r.validity.badInput?wpforms_settings.val_number:r.validationMessage,a.displayFieldError(e,r),t.preventDefault())})}};return a})(document,window,jQuery);WPFormsEditEntry.init();