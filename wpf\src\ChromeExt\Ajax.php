<?php

namespace WPForms\DevTools\ChromeExt;

/**
 * Handle AJAX actions.
 *
 * @since 0.40
 */
class Ajax {

	/**
	 * Constructor.
	 *
	 * @since 0.40
	 */
	public function __construct() {

		$this->hooks();
	}

	/**
	 * Register hooks.
	 *
	 * @since 0.40
	 */
	private function hooks(): void {

		add_action( 'wp_ajax_wpf_chrome_ext_form_data', [ $this, 'form_data' ] );
	}

	/**
	 * Get form_data AJAX action.
	 *
	 * @since 0.40
	 */
	public function form_data(): void {

		$this->validate_nonce();

		$input   = $this->get_input_vars();
		$form_id = absint( $input['post']['form_id'] ?? '' );

		if ( empty( $form_id ) ) {
			wp_send_json_error( 'Empty form ID.' );
		}

		$form_obj = wpforms()->obj( 'form' );

		if ( ! $form_obj ) {
			wp_send_json_error( 'Cannot get form.' );
		}

		$form_data = $form_obj->get( $form_id, [ 'content_only' => true ] );

		wp_send_json_success( $form_data );
	}

	/**
	 * Get action input vars.
	 *
	 * @since 0.40
	 *
	 * @return array
	 */
	private function get_input_vars(): array {

		// phpcs:disable WordPress.Security.NonceVerification
		$inputs = [
			'get'  => $_GET,
			'post' => $_POST,
		];
		// phpcs:enable WordPress.Security.NonceVerification

		$result = [];

		foreach ( $inputs as $i => $arr ) {
			foreach ( $arr as $key => $value ) {
				$result[ $i ][ $key ] = sanitize_text_field( wp_unslash( $value ) );
			}
		}

		return $result;
	}

	/**
	 * Validate nonce.
	 *
	 * @since 0.40
	 */
	private function validate_nonce(): void {

		if ( ! check_ajax_referer( 'wpf-chrome-ext-nonce', 'nonce', false ) ) {
			wp_send_json_error( 'You do not have permission.' );
		}
	}
}
