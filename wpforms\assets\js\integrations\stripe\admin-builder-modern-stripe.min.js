var WPFormsStripeModernBuilder=window.WPFormsStripeModernBuilder||((e,l)=>{let s={},i={init(){l(i.ready)},ready(){var e,t,n;i.customMetadataActions(),i.isLegacySettings()||(s={$alert:l("#wpforms-stripe-credit-card-alert"),$panelContent:l("#wpforms-panel-content-section-payment-stripe"),$feeNotice:l(".wpforms-stripe-notice-info")},i.bindUIActions(),i.bindPlanUIActions(),wpforms_builder_stripe.is_pro)||(n=(e=".wpforms-panel-content-section-stripe")+" .wpforms-panel-content-section-payment-plan-name input",l(t=e+" .wpforms-panel-content-section-payment-toggle input").each(WPFormsBuilderPaymentsUtils.toggleContent),l(n).each(WPFormsBuilderPaymentsUtils.checkPlanName),l("#wpforms-panel-payments").on("click",t,WPFormsBuilderPaymentsUtils.toggleContent).on("click",e+" .wpforms-panel-content-section-payment-plan-head-buttons-toggle",WPFormsBuilderPaymentsUtils.togglePlan).on("click",e+" .wpforms-panel-content-section-payment-plan-head-buttons-delete",WPFormsBuilderPaymentsUtils.deletePlan).on("input",n,WPFormsBuilderPaymentsUtils.renamePlan).on("focusout",n,WPFormsBuilderPaymentsUtils.checkPlanName))},customMetadataActions(){l("#wpforms-panel-payments").on("focusout",".wpforms-panel-field-stripe-custom-metadata-meta-key",function(){l(this).val(l(this).val().replace(/[^\p{L}\p{N}_-]/gu,""))}).on("click",".wpforms-panel-content-section-stripe-custom-metadata-add",function(e){e.preventDefault();var e=l(this).parents(".wpforms-panel-content-section-stripe-custom-metadata-table"),t=e.find("tr").last(),n=t.clone(!0),t=t.data("key"),s=t+1;n.attr("data-key",s),n.html(n.html().replaceAll("["+t+"]","["+s+"]").replaceAll("-"+t+"-","-"+s+"-")),n.find("select, input").val(""),n.find(".wpforms-panel-content-section-stripe-custom-metadata-delete").removeClass("hidden"),e.find("tbody").append(n.get(0))}).on("click",".wpforms-panel-content-section-stripe-custom-metadata-delete",function(e){e.preventDefault(),l(this).parents(".wpforms-panel-content-section-stripe-custom-metadata-table tr").remove()})},bindUIActions(){l("#wpforms-builder").on("wpformsFieldDelete",i.disableNotifications).on("wpformsSaved",i.requiredFieldsCheck).on("wpformsFieldAdd",i.fieldAdded).on("wpformsFieldDelete",i.fieldDeleted).on("wpformsPaymentsPlanCreated",i.toggleMultiplePlansWarning).on("wpformsPaymentsPlanCreated",i.bindPlanUIActions).on("wpformsPaymentsPlanDeleted",i.toggleMultiplePlansWarning)},bindPlanUIActions(){s.$panelContent.find('.wpforms-panel-content-section-payment-plan-body .wpforms-panel-field-select select[name*="email"]').on("change",i.resetEmailAlertErrorClass),s.$panelContent.find(".wpforms-panel-content-section-payment-plan-period select").on("change",i.resetCyclesValues)},requiredFieldsCheck(){if(l("#wpforms-panel-field-stripe-enable_recurring").is(":checked")&&!s.$panelContent.hasClass("wpforms-hidden")){let t=!1;s.$panelContent.find(".wpforms-panel-content-section-payment-plan").each(function(){var e=l(this).data("plan-id"),e=l(`#wpforms-panel-field-stripe-recurring-${e}-email`);e.val()||(e.addClass("wpforms-required-field-error"),t=!0)}),t&&i.recurringEmailAlert()}},resetEmailAlertErrorClass(){l(this).toggleClass("wpforms-required-field-error",!l(this).val())},recurringEmailAlert(){let e=wpforms_builder.stripe_recurring_email;l(".wpforms-panel-content-section-stripe").is(":visible")||(e+=" "+wpforms_builder.stripe_recurring_settings),l.alert({title:wpforms_builder.stripe_recurring_heading,content:e,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}},onOpen(){l(".wpforms-stripe-settings-redirect").on("click",i.settingsRedirect)}})},settingsRedirect(){l(".wpforms-panel-payments-button").trigger("click"),l(".wpforms-panel-sidebar-section-stripe").trigger("click"),e.location.href=e.location.pathname+e.location.search+"#wpforms-panel-field-stripe-enable_recurring-wrap",l(this).closest(".jconfirm-box").find(".btn-confirm").trigger("click")},disableNotifications(e,t,n){i.isStripeField(n)&&!i.hasStripeCreditCardFieldInBuilder()&&((n=l('.wpforms-panel-content-section-notifications [id*="-stripe-wrap"]')).find('input[id*="-stripe"]').prop("checked",!1),n.addClass("wpforms-hidden"))},isLegacySettings(){return l("#wpforms-panel-field-stripe-enable").length},fieldAdded(e,t,n){i.isStripeField(n)&&i.hasStripeCreditCardFieldInBuilder()&&(i.settingsToggle(!0),s.$feeNotice.toggleClass("wpforms-hidden"))},fieldDeleted(e,t,n){!i.isStripeField(n)||i.hasStripeCreditCardFieldInBuilder()||(i.settingsToggle(!1),i.disablePayments(),s.$feeNotice.toggleClass("wpforms-hidden"))},isStripeField(e){return e===wpforms_builder_stripe.field_slug},hasStripeCreditCardFieldInBuilder(){return 0<l(".wpforms-field.wpforms-field-"+wpforms_builder_stripe.field_slug).length},toggleMultiplePlansWarning(){s.$panelContent.find(".wpforms-stripe-multiple-plans-warning").toggleClass("wpforms-hidden",1===s.$panelContent.find(".wpforms-panel-content-section-payment-plan").length)},settingsToggle(e){(s.$alert.length||s.$panelContent.length)&&(s.$alert.toggleClass("wpforms-hidden",e),s.$panelContent.toggleClass("wpforms-hidden",!e))},toggleContent(){console.warn('WARNING! Function "WPFormsStripeModernBuilder.toggleContent()" has been deprecated, please use the new "WPFormsPaymentsUtils.toggleContent()" function instead!'),WPFormsBuilderPaymentsUtils.toggleContent()},togglePlan(){console.warn('WARNING! Function "WPFormsStripeModernBuilder.togglePlan()" has been deprecated, please use the new "WPFormsPaymentsUtils.togglePlan()" function instead!'),WPFormsBuilderPaymentsUtils.togglePlan()},deletePlan(){console.warn('WARNING! Function "WPFormsStripeModernBuilder.checkPlanName()" has been deprecated, please use the new "WPFormsPaymentsUtils.deletePlan()" function instead!'),WPFormsBuilderPaymentsUtils.deletePlan()},checkPlanName(){console.warn('WARNING! Function "WPFormsStripeModernBuilder.checkPlanName()" has been deprecated, please use the new "WPFormsPaymentsUtils.checkPlanName()" function instead!'),WPFormsBuilderPaymentsUtils.checkPlanName()},renamePlan(){console.warn('WARNING! Function "WPFormsStripeModernBuilder.renamePlan()" has been deprecated, please use the new "WPFormsPaymentsUtils.renamePlan()" function instead!'),WPFormsBuilderPaymentsUtils.renamePlan()},disablePayments(){l("#wpforms-panel-field-stripe-enable_one_time, #wpforms-panel-field-stripe-enable_recurring").prop("checked",!1).trigger("change").each(WPFormsBuilderPaymentsUtils.toggleContent)},resetCyclesValues(){var e=l(this),t=e.closest(".wpforms-panel-content-section-payment-plan-body").find(".wpforms-panel-content-section-payment-plan-cycles select"),n=t.val();let s;switch(e.val()){case"yearly":s=20;break;case"semiyearly":s=40;break;case"quarterly":s=80;break;default:s=wpforms_builder_stripe.cycles_max}var i=[l("<option>",{value:"unlimited",text:wpforms_builder_stripe.i18n.cycles_default})];for(let e=1;e<=s;e++)i.push(l("<option>",{value:e,text:e}));t.empty().append(i).val(n),t.val()!==n&&t.val("unlimited")}};return i})((document,window),jQuery);WPFormsStripeModernBuilder.init();