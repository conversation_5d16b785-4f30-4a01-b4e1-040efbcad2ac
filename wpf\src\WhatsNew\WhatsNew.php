<?php

namespace WPForms\DevTools\WhatsNew;

use WP_Post;
use WPForms\Helpers\File;
use WPForms\Admin\Splash\SplashCache;
use const WPForms\DevTools\WPFORMS_DEV_TOOLS_VERSION;

/**
 * Registration and functionality related to What's New custom post type.
 *
 * @since 0.39
 */
class WhatsNew {

	/**
	 * Draft .json file name.
	 *
	 * @since 0.39
	 */
	private const DATA_JSON_FILE_DRAFT = 'splash-draft.json';

	/**
	 * Core constructor.
	 *
	 * @since 0.39
	 */
	public function __construct() {

		if ( ! class_exists( 'ACF' ) ) {
			return;
		}

		$this->hooks();
	}

	/**
	 * All the actions and filters are registered here.
	 *
	 * @since 0.39
	 */
	public function hooks(): void {

		add_action( 'init', [ $this, 'register' ], 0 );
		add_action( 'save_post', [ $this, 'save' ], 100, 3 );
		add_action( 'delete_post', [ $this, 'save' ], 100, 2 );
		add_filter( 'manage_wpf_whats_new_posts_columns', [ $this, 'post_columns' ] );
		add_action( 'manage_wpf_whats_new_posts_custom_column', [ $this, 'post_columns_content' ], 10, 2 );
		add_filter( 'page_row_actions', [ $this, 'remove_quick_edit' ], 10, 2 );
		add_action( 'admin_enqueue_scripts', [ $this, 'admin_enqueue_scripts' ] );
		add_filter( 'pre_get_posts', [ $this, 'admin_post_order' ] );
		add_action( 'restrict_manage_posts', [ $this, 'add_whats_new_filters' ] );
	}

	/**
	 * Register custom post type and related taxonomies.
	 *
	 * @since 0.39
	 */
	public function register(): void {

		register_post_type(
			'wpf_whats_new',
			[
				'labels'       => [
					'name'               => 'What\'s New',
					'singular_name'      => 'What\'s New',
					'add_new'            => 'Add New',
					'add_new_item'       => 'Add New Item',
					'edit_item'          => 'Edit Item',
					'new_item'           => 'New Item',
					'view_item'          => 'View Item',
					'search_items'       => 'Search What\'s New Items',
					'not_found'          => 'No What\'s New Items found',
					'not_found_in_trash' => 'No What\'s New Items found in trash',
					'parent_item_colon'  => '',
					'menu_name'          => 'What\'s New',
				],
				'public'       => true,
				'show_ui'      => true,
				'show_in_menu' => true,
				'query_var'    => false,
				'rewrite'      => false,
				'hierarchical' => true,
				'supports'     => [ 'title', 'editor', 'revisions' ],
				'menu_icon'    => 'dashicons-megaphone',
			]
		);
	}

	/**
	 * The What's New item has been saved.
	 *
	 * @since        0.39
	 *
	 * @param int|mixed $post_id Post ID.
	 * @param WP_Post   $post    Post object.
	 * @param bool      $update  If it's an update.
	 *
	 * @noinspection PhpUnusedParameterInspection
	 */
	public function save( $post_id, WP_Post $post, bool $update = false ): void { // phpcs:ignore Generic.CodeAnalysis.UnusedFunctionParameter.FoundAfterLastUsed

		if (
			( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) ||
			( defined( 'DOING_AJAX' ) && DOING_AJAX ) ||
			( $post->post_status !== 'publish' && $post->post_status !== 'trash' && $post->post_status !== 'draft' ) ||
			$post->post_type !== 'wpf_whats_new'
		) {
			return;
		}

		$path  = wp_normalize_path( WPFORMS_PLUGIN_DIR . SplashCache::LOCAL_SOURCE_DIR );
		$file  = SplashCache::LOCAL_SOURCE_FILE;
		$draft = false;

		if ( $post->post_status === 'draft' ) {
			$file  = self::DATA_JSON_FILE_DRAFT;
			$draft = true;
		}

		$this->copy_image_to_wpforms_data( $post );

		$whats_new = $this->generate_whats_new_obj( $draft );
		$json      = wp_json_encode( $whats_new );

		File::put_contents( $path . $file, $json );
	}

	/**
	 * Copy image to the plugin directory.
	 *
	 * @since 0.39
	 *
	 * @param WP_Post $post Post object.
	 *
	 * @noinspection PhpUndefinedFunctionInspection
	 */
	private function copy_image_to_wpforms_data( WP_Post $post ): void {

		$image_type   = get_field( 'img', $post->ID );
		$image        = get_field( 'img_' . $image_type, $post->ID );
		$version_term = get_field( 'version', $post->ID );

		// The version should be set.
		if ( empty( $version_term->name ) ) {
			return;
		}

		$upload_dir = wp_upload_dir();
		$image_meta = wp_get_attachment_metadata( $image['id'] );

		$uploads_image_path = ! empty( $image_meta['file'] )
			? wp_normalize_path( $upload_dir['basedir'] . '/' . $image_meta['file'] )
			: '';

		if ( empty( $uploads_image_path ) || ! file_exists( $uploads_image_path ) ) {
			return;
		}

		$wpforms_image_dir = sprintf(
			'%1$s%2$s/',
			SplashCache::LOCAL_SOURCE_DIR,
			$version_term->name
		);

		$file_name        = $image['filename'];
		$wpforms_file_dir = wp_normalize_path( WPFORMS_PLUGIN_DIR . $wpforms_image_dir );

		$dirs_created =
			File::mkdir( WPFORMS_PLUGIN_DIR . 'assets/data/' ) &&
			File::mkdir( WPFORMS_PLUGIN_DIR . SplashCache::LOCAL_SOURCE_DIR ) &&
			File::mkdir( $wpforms_file_dir );

		if ( ! $dirs_created ) {
			return;
		}

		File::copy( $uploads_image_path, $wpforms_file_dir . $file_name );

		// Store the image file path in the attachment metadata.
		add_post_meta(
			$image['id'],
			'wpf_whats_new_image_wpforms_file_path',
			$wpforms_image_dir . $file_name,
			true
		);
	}

	/**
	 * Generates What's New Array object.
	 *
	 * @since 0.39
	 *
	 * @param bool $draft If it's a draft.
	 *
	 * @return array The "What's New" object.
	 * @noinspection PhpUndefinedFunctionInspection
	 */
	public function generate_whats_new_obj( bool $draft = false ): array { // phpcs:ignore Generic.Metrics.CyclomaticComplexity

		$whats_new = [];
		$data      = get_posts(
			[
				'posts_per_page' => -1,
				'post_type'      => 'wpf_whats_new',
				'no_found_rows'  => true,
				'post_status'    => $draft ? 'any' : 'publish',
			]
		);

		foreach ( $data as $item ) {
			$licenses      = get_the_terms( $item->ID, 'wpf_license' );
			$version_term  = get_field( 'version', $item->ID );
			$btn_main_text = get_field( 'btn', $item->ID );
			$btn_main_url  = get_field( 'btn_url', $item->ID );
			$btn_alt_text  = get_field( 'btn_alt', $item->ID );
			$btn_alt_url   = get_field( 'btn_alt_url', $item->ID );
			$image_type    = get_field( 'img', $item->ID );
			$image         = get_field( 'img_' . $image_type, $item->ID );
			$img_url       = ! empty( $image['ID'] )
				? get_post_meta( $image['ID'], 'wpf_whats_new_image_wpforms_file_path', true )
				: '';

			$n = [
				'title'    => $item->post_title,
				'content'  => $item->post_content,
				'img'      => [
					'url'  => $img_url,
					'type' => $image_type,
				],
				'featured' => get_field( 'featured', $item->ID ),
				'new'      => get_field( 'new', $item->ID ),
				'date'     => $item->post_date,
				'version'  => '',
				'type'     => [],
			];

			if ( $image_type === 'illustration' ) {
				$n['img']['shadow'] = get_field( 'img_shadow', $item->ID );
			}

			if ( ! empty( $btn_main_url && ! empty( $btn_main_text ) ) ) {
				$n['btns']['main'] = [
					'url'  => $btn_main_url,
					'text' => $btn_main_text,
				];
			}

			if ( ! empty( $btn_alt_url && ! empty( $btn_alt_text ) ) ) {
				$n['btns']['alt'] = [
					'url'  => $btn_alt_url,
					'text' => $btn_alt_text,
				];
			}

			if ( ! empty( $version_term->name ) ) {
				$n['version'] = $version_term->name;
			}

			if ( ! empty( $licenses ) ) {
				$n['type'] = wp_list_pluck( $licenses, 'slug' );
			}

			$whats_new[] = $n;
		}

		usort(
			$whats_new,
			static function ( $a, $b ) {

				return (
					[ $b['featured'], $b['version'], strtotime( $b['date'] ) ] <=>
					[ $a['featured'], $a['version'], strtotime( $a['date'] ) ]
				);
			}
		);

		foreach ( $whats_new as $key => $wn_item ) {
			unset( $whats_new[ $key ]['date'] );
		}

		return $whats_new;
	}

	/**
	 * Add Content to What's New post list view.
	 *
	 * @since 0.39
	 *
	 * @param array|mixed $columns Post columns.
	 *
	 * @return array
	 */
	public function post_columns( $columns ): array {

		$columns = (array) $columns;

		unset( $columns['title'], $columns['taxonomy-wpf_license'] );

		$col = [ 'whats_new_preview' => 'What\'s New' ];

		return (
			array_slice( $columns, 0, 1, true ) +
			$col +
			array_slice( $columns, 1, count( $columns ) - 1, true )
		);
	}

	/**
	 * Add Content to What's New post list view.
	 *
	 * @since 0.39
	 *
	 * @param string|mixed $column  Column slug.
	 * @param int|mixed    $post_id Post ID.
	 *
	 * @noinspection PhpUndefinedFunctionInspection
	 */
	public function post_columns_content( $column, $post_id ): void { // phpcs:ignore Generic.Metrics.CyclomaticComplexity.TooHigh

		$column  = (string) $column;
		$post_id = (int) $post_id;

		if ( $column !== 'whats_new_preview' ) {
			return;
		}

		$licenses      = get_the_terms( $post_id, 'wpf_license' );
		$licenses      = ! empty( $licenses ) ? wp_list_pluck( $licenses, 'name' ) : false;
		$version_term  = get_field( 'version', $post_id );
		$btn_main_text = get_field( 'btn', $post_id );
		$btn_main_url  = get_field( 'btn_url', $post_id );
		$btn_alt_text  = get_field( 'btn_alt', $post_id );
		$btn_alt_url   = get_field( 'btn_alt_url', $post_id );
		$image_type    = get_field( 'img', $post_id );
		$image         = get_field( 'img_' . $image_type, $post_id );
		$title         = get_the_title( $post_id );

		switch ( $image_type ) {
			case 'hero':
				$layout = 'full-width';
				break;

			case 'illustration':
				$layout = 'fifty-fifty';
				break;

			case 'icon':
			default:
				$layout = 'one-third-two-thirds';
				break;
		}

		$layout_classes = $layout === 'full-width' ? $layout : "$layout $image_type";

		?>
		<div class="whats-new-preview <?php echo esc_attr( $layout_classes ); ?>">
			<div class="whats-new-content">
				<?php if ( get_field( 'new', $post_id ) ) { ?>
					<div class="whats-new-new-feature">New Feature</div>
				<?php } ?>
				<div class="whats-new-title"><?php echo esc_html( $title ); ?></div>
				<div class="whats-new-description"><?php echo esc_html( get_the_content( null, false, $post_id ) ); ?></div>

				<div class="whats-new-buttons">
					<?php if ( ! empty( $btn_main_url ) && ! empty( $btn_main_text ) ) { ?>
						<a href="<?php echo esc_url( $btn_main_url ); ?> " class="button button-primary"><?php echo esc_html( $btn_main_text ); ?></a>
					<?php } ?>
					<?php if ( ! empty( $btn_alt_url ) && ! empty( $btn_alt_text ) ) { ?>
						<a href="<?php echo esc_url( $btn_alt_url ); ?>" class="button button-secondary"><?php echo esc_html( $btn_alt_text ); ?></a>
					<?php } ?>
				</div>
			</div>

			<div class="whats-new-image">
				<?php if ( ! empty( $image ) ) : ?>
					<img class="<?php echo $image_type === 'illustration' ? esc_attr( get_field( 'img_shadow', $post_id ) ) : ''; ?>" src="<?php echo esc_url( $image['url'] ); ?>" alt="<?php echo esc_attr( $title ); ?>" />
				<?php endif; ?>
			</div>
		</div>

		<div class="whats-new-meta">
			<div>
				Featured: <span><?php echo get_field( 'featured', $post_id ) ? 'Yes' : 'No'; ?></span>
			</div>

			<div>
				<?php if ( ! empty( $version_term ) ) { ?>
					WPForms Version: <span><?php echo esc_html( $version_term->name ); ?></span>
				<?php
				} else {
					echo 'No WPForms versions selected';
				}
				?>
			</div>

			<div>
				<?php if ( ! empty( $licenses ) ) { ?>
					Licenses:
					<?php foreach ( $licenses as $license ) : ?>
						<span><?php echo esc_html( $license ); ?></span>
					<?php
						endforeach;
					} else {
						echo 'No licenses selected';
					}
				?>
			</div>
		</div>
		<?php
	}

	/**
	 * Remove the quick edit link for the wpf_whats_new post type.
	 *
	 * @since 0.39
	 *
	 * @param array|mixed $actions An array of row action links.
	 *                             Defaults are 'Edit', 'Quick Edit', 'Restore', 'Trash',
	 *                             'Delete Permanently', 'Preview', and 'View'.
	 * @param WP_Post     $post    The post object.
	 *
	 * @return array
	 */
	public function remove_quick_edit( $actions, WP_Post $post ): array {

		$actions = (array) $actions;

		if ( $post->post_type === 'wpf_whats_new' ) {
			unset( $actions['inline hide-if-no-js'] );
		}

		return $actions;
	}

	/**
	 * Enqueue admin scripts.
	 *
	 * @since 0.39
	 */
	public function admin_enqueue_scripts(): void {

		$current_screen = get_current_screen();

		if ( $current_screen && $current_screen->id !== 'edit-wpf_whats_new' ) {
			return;
		}

		wp_enqueue_style(
			'whats_new_admin_css',
			WPFORMS_DEV_TOOLS_URL . '/assets/css/whats-new-admin.css',
			false,
			WPFORMS_DEV_TOOLS_VERSION
		);
	}

	/**
	 * Set What's New order to DESC on post edit screen.
	 *
	 * @param object $wp_query WP Query object.
	 *
	 * @since 0.39
	 */
	public function admin_post_order( $wp_query ): void {

		// phpcs:disable WordPress.Security.NonceVerification.Recommended
		if (
			isset( $_GET['post_type'] ) &&
			( $_GET['post_type'] === 'wpf_whats_new' ) &&
			is_admin() &&
			$wp_query->is_main_query()
		) {
			$wp_query->set( 'orderby', 'date' );
			$wp_query->set( 'order', 'DESC' );
			$wp_query->set( 'posts_per_page', 999 );
		}
		// phpcs:enable WordPress.Security.NonceVerification.Recommended
	}

	/**
	 * Add filters to the What's New listing page.
	 *
	 * @since 0.39
	 */
	public function add_whats_new_filters(): void {

		$current_screen = get_current_screen();

		if ( ! $current_screen || $current_screen->id !== 'edit-wpf_whats_new' ) {
			return;
		}

		$license = empty( $_GET['wpf_license'] ) ? 0 : sanitize_text_field( wp_unslash( $_GET['wpf_license'] ) ); // phpcs:ignore WordPress.Security.NonceVerification.Recommended

		wp_dropdown_categories(
			[
				'hide_empty'      => false,
				'name'            => 'wpf_license',
				'orderby'         => 'name',
				'selected'        => $license,
				'show_option_all' => 'All Licenses',
				'taxonomy'        => 'wpf_license',
				'value_field'     => 'slug',
			]
		);

		$version = empty( $_GET['wpf_version'] ) ? 0 : sanitize_text_field( wp_unslash( $_GET['wpf_version'] ) ); // phpcs:ignore WordPress.Security.NonceVerification.Recommended

		wp_dropdown_categories(
			[
				'hide_empty'      => false,
				'name'            => 'wpf_version',
				'orderby'         => 'name',
				'selected'        => $version,
				'show_option_all' => 'All WPForms Versions',
				'taxonomy'        => 'wpf_version',
				'value_field'     => 'slug',
			]
		);
	}
}
