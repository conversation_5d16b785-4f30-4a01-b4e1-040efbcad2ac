<?php

namespace WPForms\DevTools\FormTemplates;

use WPF<PERSON>_Template;

/**
 * WPForms DevTools template: Fancy Fields.
 *
 * @since 0.24
 */
class StandardFields extends WPForms_Template {

	/**
	 * Primary class constructor.
	 *
	 * @since 0.24
	 *
	 * @noinspection HtmlUnknownTarget
	 */
	public function init() {

		// Template name.
		$this->name = 'Standard Fields';

		// Template slug.
		$this->slug = 'standard_fields';

		// Template description.
		$this->description = '';

		// Template field and settings.
		// phpcs:disable WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned
		$this->data = [
			'fields'     => [
				13 => [
					'id'              => '13',
					'type'            => 'pagebreak',
					'position'        => 'top',
					'indicator'       => 'progress',
					'indicator_color' => '#066aab',
					'title'           => 'SINGLE LINE FIELD',
					'nav_align'       => 'left',
				],
				2  => [
					'id'           => '2',
					'type'         => 'text',
					'label'        => 'Single Line Text: Required with Description',
					'description'  => 'This is description for the Single Line Text field',
					'required'     => '1',
					'size'         => 'medium',
					'limit_count'  => '1',
					'limit_mode'   => 'characters',
					'map_position' => 'above',
				],
				7  => [
					'id'           => '7',
					'type'         => 'text',
					'label'        => 'Single Line Text: Small with Placeholder',
					'size'         => 'small',
					'placeholder'  => 'This is Placeholder for the Single Line Text Field',
					'limit_count'  => '1',
					'limit_mode'   => 'characters',
					'map_position' => 'above',
				],
				8  => [
					'id'            => '8',
					'type'          => 'text',
					'label'         => 'Single Line Text: Medium with Default Value',
					'size'          => 'medium',
					'limit_count'   => '1',
					'limit_mode'    => 'characters',
					'default_value' => 'This is Default Value for the Single Line Text Field',
					'map_position'  => 'above',
				],
				93 => [
					'id'           => '93',
					'type'         => 'text',
					'label'        => 'Single Line Text: Large',
					'size'         => 'large',
					'limit_count'  => '1',
					'limit_mode'   => 'characters',
					'map_position' => 'above',
				],
				9  => [
					'id'           => '9',
					'type'         => 'text',
					'label'        => 'Single Line Text: With Input Mask',
					'size'         => 'medium',
					'limit_count'  => '1',
					'limit_mode'   => 'characters',
					'input_mask'   => 'AAA-999',
					'map_position' => 'above',
				],
				10 => [
					'id'            => '10',
					'type'          => 'text',
					'label'         => 'Single Line Text: With Limited Length',
					'size'          => 'medium',
					'limit_enabled' => '1',
					'limit_count'   => '9',
					'limit_mode'    => 'characters',
					'input_mask'    => 'AAA-999',
					'map_position'  => 'above',
				],
				11 => [
					'id'                          => '11',
					'type'                        => 'text',
					'label'                       => 'Single Line Text: With Enabled Autocomplete',
					'size'                        => 'medium',
					'limit_count'                 => '9',
					'limit_mode'                  => 'characters',
					'enable_address_autocomplete' => '1',
					'display_map'                 => '1',
					'map_position'                => 'above',
				],
				12 => [
					'id'    => '12',
					'type'  => 'pagebreak',
					'title' => 'PARAGRAPH TEXT FIELD',
					'next'  => 'Next',
				],
				16 => [
					'id'          => '16',
					'type'        => 'textarea',
					'label'       => 'Paragraph Text: Required with Description',
					'description' => 'This is description for the Paragraph Text field',
					'required'    => '1',
					'size'        => 'medium',
					'limit_count' => '1',
					'limit_mode'  => 'characters',
				],
				21 => [
					'id'          => '21',
					'type'        => 'textarea',
					'label'       => 'Paragraph Text: Small with Placeholder',
					'size'        => 'small',
					'placeholder' => 'This is placeholder for the Paragraph Text',
					'limit_count' => '1',
					'limit_mode'  => 'characters',
				],
				22 => [
					'id'            => '22',
					'type'          => 'textarea',
					'label'         => 'Paragraph Text: Medium with Default value',
					'size'          => 'medium',
					'limit_count'   => '1',
					'limit_mode'    => 'characters',
					'default_value' => 'This is default value for the Paragraph Text',
				],
				20 => [
					'id'          => '20',
					'type'        => 'textarea',
					'label'       => 'Paragraph Text: Large',
					'size'        => 'large',
					'limit_count' => '1',
					'limit_mode'  => 'characters',
				],
				23 => [
					'id'    => '23',
					'type'  => 'pagebreak',
					'title' => 'DROPDOWN FIELD',
					'next'  => 'Next',
				],
				25 => [
					'id'          => '25',
					'type'        => 'select',
					'label'       => 'Dropdown: Required with Description',
					'choices'     => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'description' => 'This is description for the Dropdown field',
					'required'    => '1',
					'style'       => 'classic',
					'size'        => 'medium',
				],
				26 => [
					'id'      => '26',
					'type'    => 'select',
					'label'   => 'Dropdown: Classic Style, Small Size',
					'choices' => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'style'   => 'classic',
					'size'    => 'small',
				],
				27 => [
					'id'      => '27',
					'type'    => 'select',
					'label'   => 'Dropdown: Classic Style, Medium Size',
					'choices' => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'style'   => 'classic',
					'size'    => 'medium',
				],
				28 => [
					'id'      => '28',
					'type'    => 'select',
					'label'   => 'Dropdown: Classic Style, Large Size',
					'choices' => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'style'   => 'classic',
					'size'    => 'large',
				],
				34 => [
					'id'      => '34',
					'type'    => 'select',
					'label'   => 'Dropdown: Modern Style, Small Size',
					'choices' => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'style'   => 'modern',
					'size'    => 'small',
				],
				35 => [
					'id'      => '35',
					'type'    => 'select',
					'label'   => 'Dropdown: Modern Style, Medium Size',
					'choices' => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'style'   => 'modern',
					'size'    => 'medium',
				],
				36 => [
					'id'      => '36',
					'type'    => 'select',
					'label'   => 'Dropdown: Modern Style, Large Size',
					'choices' => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'style'   => 'modern',
					'size'    => 'large',
				],
				30 => [
					'id'          => '30',
					'type'        => 'select',
					'label'       => 'Dropdown: Required with Placeholder',
					'choices'     => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'style'       => 'classic',
					'size'        => 'medium',
					'placeholder' => 'This is placeholder for the Dropdown field',
				],
				32 => [
					'id'       => '32',
					'type'     => 'select',
					'label'    => 'Dropdown: Modern Style, Multiple Options',
					'choices'  => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'multiple' => '1',
					'style'    => 'modern',
					'size'     => 'medium',
				],
				33 => [
					'id'       => '33',
					'type'     => 'select',
					'label'    => 'Dropdown: Classic Style, Multiple Options',
					'choices'  => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'multiple' => '1',
					'style'    => 'classic',
					'size'     => 'medium',
				],
				37 => [
					'id'    => '37',
					'type'  => 'pagebreak',
					'title' => 'MULTIPLE CHOICES FIELD',
					'next'  => 'Next',
				],
				38 => [
					'id'                   => '38',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Required with Description',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'description'          => 'This is description for the Multiple Choice field',
					'required'             => '1',
				],
				39 => [
					'id'                   => '39',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: One Column',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
				],
				40 => [
					'id'                   => '40',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Two Columns',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
				],
				41 => [
					'id'                   => '41',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Three Columns',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '3',
				],
				42 => [
					'id'                   => '42',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Inline',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
				],
				43 => [
					'id'                   => '43',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Image Choices',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Apple.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Car.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Star.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Choice',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Note.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Choice',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Heart.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
				],
				44 => [
					'id'                   => '44',
					'type'                 => 'radio',
					'label'                => 'Multiple Choice: Icon Choices',
					'choices'              => [
						1 => [
							'label'      => 'Heart',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Heart.png',
							'icon'       => 'heart',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Star',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Star.png',
							'icon'       => 'star',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Bell',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Bell.png',
							'icon'       => 'bell',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Circle',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Circle.png',
							'icon'       => 'circle',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Square',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Square.png',
							'icon'       => 'square',
							'icon_style' => 'regular',
						],
						6 => [
							'label'      => 'Bookmark',
							'icon'       => 'bookmark',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#7705ab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
				],
				45 => [
					'id'    => '45',
					'type'  => 'pagebreak',
					'title' => 'CHECKBOXES FIELD',
					'next'  => 'Next',
				],
				46 => [
					'id'                   => '46',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Required with Description',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'description'          => 'This is description for the Checkboxes field',
					'required'             => '1',
				],
				47 => [
					'id'                   => '47',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: One Column',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
				],
				48 => [
					'id'                   => '48',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Two Columns',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '2',
				],
				49 => [
					'id'                   => '49',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Three Columns',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '3',
				],
				50 => [
					'id'                   => '50',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Inline',
					'choices'              => [
						1 => [
							'label'      => 'First Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Second Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Third Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Fourth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Fifth Choice',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => 'inline',
				],
				51 => [
					'id'                   => '51',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Image Choices',
					'choices'              => [
						1 => [
							'label'      => 'Lemon',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Lemon.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Apple',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Apple.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Pomegranate',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Pomegranate.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'Banana',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Banana.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						5 => [
							'label'      => 'Avocado',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/Avocado.png',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images'       => '1',
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
					'input_columns'        => '3',
				],
				52 => [
					'id'                   => '52',
					'type'                 => 'checkbox',
					'label'                => 'Checkboxes: Icon Choices',
					'choices'              => [
						1 => [
							'label'      => 'Up',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/up.png',
							'icon'       => 'angle-up',
							'icon_style' => 'solid',
						],
						2 => [
							'label'      => 'Down',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/down.png',
							'icon'       => 'angle-down',
							'icon_style' => 'solid',
						],
						3 => [
							'label'      => 'Right',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/right.png',
							'icon'       => 'angle-right',
							'icon_style' => 'solid',
						],
						4 => [
							'label'      => 'Left',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/left.png',
							'icon'       => 'angle-left',
							'icon_style' => 'solid',
						],
						5 => [
							'label'      => 'Up-Down',
							'image'      => WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/up-down.png',
							'icon'       => 'arrows-up-down',
							'icon_style' => 'solid',
						],
						6 => [
							'label'      => 'Left-Right',
							'icon'       => 'arrows-left-right',
							'icon_style' => 'solid',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons'        => '1',
					'choices_icons_color'  => '#05ab21',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'modern',
					'input_columns'        => '2',
				],
				53 => [
					'id'    => '53',
					'type'  => 'pagebreak',
					'title' => 'NUMBERS FIELD',
					'next'  => 'Next',
				],
				54 => [
					'id'          => '54',
					'type'        => 'number',
					'label'       => 'Numbers: Required with Description',
					'description' => 'This is description for the Numbers field',
					'required'    => '1',
					'size'        => 'medium',
				],
				61 => [
					'id'          => '61',
					'type'        => 'number',
					'label'       => 'Numbers: Small with Placeholder',
					'size'        => 'small',
					'placeholder' => '0000000000',
				],
				62 => [
					'id'            => '62',
					'type'          => 'number',
					'label'         => 'Numbers: Medium with Default value',
					'size'          => 'medium',
					'default_value' => '1234567890',
				],
				58 => [
					'id'    => '58',
					'type'  => 'number',
					'label' => 'Numbers: Large',
					'size'  => 'large',
				],
				63 => [
					'id'    => '63',
					'type'  => 'pagebreak',
					'title' => 'NAME FIELD',
					'next'  => 'Next',
				],
				64 => [
					'id'          => '64',
					'type'        => 'name',
					'label'       => 'Name: Required with Description',
					'format'      => 'first-last',
					'description' => 'This is description for the Name field',
					'required'    => '1',
					'size'        => 'medium',
				],
				66 => [
					'id'     => '66',
					'type'   => 'name',
					'label'  => 'Name Required: First Last Format',
					'format' => 'first-last',
					'size'   => 'medium',
				],
				69 => [
					'id'     => '69',
					'type'   => 'name',
					'label'  => 'Name Required: First Middle Last Format',
					'format' => 'first-middle-last',
					'size'   => 'medium',
				],
				67 => [
					'id'     => '67',
					'type'   => 'name',
					'label'  => 'Name Required: Simple Format',
					'format' => 'simple',
					'size'   => 'medium',
				],
				71 => [
					'id'                => '71',
					'type'              => 'name',
					'label'             => 'Name: Small with Placeholders',
					'format'            => 'first-last',
					'size'              => 'small',
					'first_placeholder' => 'First Name',
					'last_placeholder'  => 'Last Name',
				],
				72 => [
					'id'            => '72',
					'type'          => 'name',
					'label'         => 'Name: Medium with Default Values',
					'format'        => 'first-last',
					'size'          => 'medium',
					'first_default' => 'First Name',
					'last_default'  => 'Last Name',
				],
				70 => [
					'id'     => '70',
					'type'   => 'name',
					'label'  => 'Name: Large',
					'format' => 'first-last',
					'size'   => 'large',
				],
				73 => [
					'id'            => '73',
					'type'          => 'name',
					'label'         => 'Name: without Sublabels with unique answer required',
					'format'        => 'first-last',
					'size'          => 'large',
					'sublabel_hide' => '1',
					'unique_answer' => '1',
				],
				74 => [
					'id'    => '74',
					'type'  => 'pagebreak',
					'title' => 'EMAIL FIELD',
					'next'  => 'Next',
				],
				75 => [
					'id'            => '75',
					'type'          => 'email',
					'label'         => 'Email: Required with Description',
					'description'   => 'This is description for the Email field',
					'required'      => '1',
					'size'          => 'medium',
					'default_value' => false,
				],
				77 => [
					'id'            => '77',
					'type'          => 'email',
					'label'         => 'Email: With the Confirmation',
					'confirmation'  => '1',
					'size'          => 'medium',
					'default_value' => false,
				],
				81 => [
					'id'                       => '81',
					'type'                     => 'email',
					'label'                    => 'Email: Small with Placeholder',
					'confirmation'             => '1',
					'size'                     => 'small',
					'placeholder'              => '<EMAIL>',
					'confirmation_placeholder' => '<EMAIL>',
					'default_value'            => false,
				],
				82 => [
					'id'            => '82',
					'type'          => 'email',
					'label'         => 'Email: Medium with Default Value',
					'size'          => 'medium',
					'default_value' => '<EMAIL>',
					'sublabel_hide' => '1',
				],
				80 => [
					'id'            => '80',
					'type'          => 'email',
					'label'         => 'Email: Large',
					'size'          => 'large',
					'default_value' => false,
				],
				83 => [
					'id'            => '83',
					'type'          => 'email',
					'label'         => 'Email: Without Sublabels with Unique Answer Required',
					'confirmation'  => '1',
					'size'          => 'medium',
					'default_value' => false,
					'sublabel_hide' => '1',
					'unique_answer' => '1',
				],
				84 => [
					'id'    => '84',
					'type'  => 'pagebreak',
					'title' => 'NUMBER SLIDER FIELD',
					'next'  => 'Next',
				],
				85 => [
					'id'            => '85',
					'type'          => 'number-slider',
					'label'         => 'Number Slider: With Description',
					'description'   => 'This is description for the Number Slider field',
					'min'           => '0',
					'max'           => '10',
					'default_value' => '0',
					'step'          => '1',
					'size'          => 'medium',
					'value_display' => 'Selected Value: {value}',
				],
				87 => [
					'id'            => '87',
					'type'          => 'number-slider',
					'label'         => 'Number Slider: Small',
					'min'           => '0',
					'max'           => '10',
					'default_value' => '7',
					'step'          => '1',
					'size'          => 'small',
					'value_display' => 'Selected Value: {value}',
				],
				86 => [
					'id'            => '86',
					'type'          => 'number-slider',
					'label'         => 'Number Slider: Medium with the Default Value',
					'min'           => '0',
					'max'           => '30',
					'default_value' => '10',
					'step'          => '5',
					'size'          => 'medium',
					'value_display' => 'Selected Value: {value}',
				],
				89 => [
					'id'            => '89',
					'type'          => 'number-slider',
					'label'         => 'Number Slider: Large',
					'min'           => '0',
					'max'           => '10',
					'default_value' => '7',
					'step'          => '1',
					'size'          => 'large',
					'value_display' => 'Selected Value: {value}',
				],
				91 => [
					'id'   => '91',
					'type' => 'pagebreak',
					'next' => 'Next',
				],
				90 => [
					'id'        => '90',
					'type'      => 'internal-information',
					'label'     => 'Internal Information',
					'cta-label' => 'Learn More',
				],
				92 => [
					'id'          => '92',
					'type'        => 'internal-information',
					'label'       => 'Internal Information with the Description ',
					'description' => 'This is the description for the Internal Information field',
					'cta-label'   => 'Learn More',
				],
				14 => [
					'id'       => '14',
					'type'     => 'pagebreak',
					'position' => 'bottom',
				],
			],
			'field_id'   => 94,
			'settings'   => [
				'form_title'                                       => 'Standard Fields',
				'submit_text'                                      => 'Submit',
				'submit_text_processing'                           => 'Sending...',
				'ajax_submit'                                      => '1',
				'notification_enable'                              => '1',
				'notifications'                                    => [
					1 => [
						'notification_name'                      => 'Default Notification',
						'email'                                  => '{admin_email}',
						'subject'                                => 'New Entry: Blank Form (ID #3617)',
						'sender_name'                            => '10TEST',
						'sender_address'                         => '{admin_email}',
						'message'                                => '{all_fields}',
						'file_upload_attachment_fields'          => [],
						'entry_csv_attachment_entry_information' => [],
						'entry_csv_attachment_file_name'         => 'entry-details',
					],
				],
				'confirmations'                                    => [
					1 => [
						'name'                        => 'Default Confirmation',
						'type'                        => 'message',
						'message'                     => '<p>Thanks for contacting us! We will be in touch with you shortly.</p>',
						'message_scroll'              => '1',
						'page'                        => '666',
						'message_entry_preview_style' => 'basic',
					],
				],
				'lead_forms'                                       => [
					'accent_color'   => '#0299ed',
					'field_borders'  => '#cccccc',
					'primary_text'   => '#444444',
					'secondary_text' => '#777777',
				],
				'antispam_v3'                                      => '1',
				'anti_spam'                                        => [
					'time_limit'     => [
						'duration' => '3',
					],
					'country_filter' => [
						'action'        => 'allow',
						'country_codes' => [],
						'message'       => 'Sorry, this form does not accept submissions from your country.',
					],
					'keyword_filter' => [
						'message' => 'Sorry, your message can\'t be submitted because it contains prohibited words.',
					],
				],
				'post_submissions_type'                            => 'post',
				'post_submissions_status'                          => 'pending',
				'registration_role'                                => 'subscriber',
				'registration_activation_method'                   => 'user',
				'registration_email_user_activation_subject'       => '{site_name} Activation Required',
				'registration_email_user_activation_message'       => 'IMPORTANT: You must activate your account before you can log in.
Please visit the link below.

{url_user_activation}',
				'registration_hide_message'                        => 'Hi {user_first_name}, you’re already logged in. <a href="{url_logout}">Log out</a>.',
				'registration_email_admin_subject'                 => '{site_name} New User Registration',
				'registration_email_admin_message'                 => 'New user registration on your site {site_name}:

Username: {user_registration_login}
Email: {user_registration_email}',
				'registration_email_user_subject'                  => '{site_name} Your username and password info',
				'registration_email_user_message'                  => 'Username: {user_registration_login}
Password: {user_registration_password}
{url_login}

',
				'registration_email_user_after_activation_subject' => '{site_name} Your account was successfully activated',
				'registration_email_user_after_activation_message' => 'You can log in with your credentials now.

{url_login}',
				'form_pages_title'                                 => 'Blank Form',
				'form_pages_footer'                                => 'This content is neither created nor endorsed by WPForms.',
				'form_pages_color_scheme'                          => '#448ccb',
				'form_pages_style'                                 => 'modern',
				'conversational_forms_title'                       => 'Blank Form',
				'conversational_forms_color_scheme'                => '#448ccb',
				'conversational_forms_progress_bar'                => 'percentage',
				'form_locker_verification_type'                    => 'password',
				'form_locker_age'                                  => '18',
				'form_locker_age_criteria'                         => '>=',
				'form_locker_user_entry_email_duration'            => 'day_start',
				'save_resume_link_text'                            => 'Save and Resume Later',
				'save_resume_disclaimer_message'                   => 'Heads up! Saving your progress now will store a copy of your entry on this server and the site owner may have access to it. For security reasons, sensitive information such as credit cards and mailing addresses, along with file uploads will have to be re-entered when you resume.',
				'save_resume_confirmation_message'                 => 'Your form entry has been saved and a unique link has been created which you can access to resume this form.

Enter your email address to receive the link via email. Alternatively, you can copy and save the link below.

Please note, this link should not be shared and will expire in 30 days, afterwards your form entry will be deleted.',
				'save_resume_enable_resume_link'                   => '1',
				'save_resume_enable_email_notification'            => '1',
				'save_resume_email_notification_message'           => 'Thank you for saving {form_name}. Click the link below to resume the form from any device.

{resume_link}

Remember, the link should not be shared and will expire in 30 days.',
				'save_resume_email_settings_message'               => 'A link to resume this form has been sent to the email address provided.

Please remember, the link should not be shared and will expire in 30 days.',
				'form_tags'                                        => [],
			],
			'lead_forms' => [
				'iif_id_ref' => '',
			],
			'payments'   => [
				'stripe'          => [
					'payment_description' => '',
					'receipt_email'       => '',
					'customer_email'      => '',
					'customer_name'       => '',
					'recurring'           => [
						0 => [
							'name'          => 'Plan Name #1',
							'period'        => 'yearly',
							'email'         => '',
							'customer_name' => '',
						],
					],
				],
				'paypal_commerce' => [
					'name'                => '',
					'billing_email'       => '',
					'billing_address'     => '',
					'shipping_address'    => '',
					'payment_description' => '',
					'recurring'           => [
						0 => [
							'pp_product_id'    => '',
							'pp_plan_id'       => '',
							'name'             => 'Plan Name #1',
							'product_type'     => 'digital',
							'recurring_times'  => 'yearly',
							'total_cycles'     => '0',
							'shipping_address' => '',
						],
					],
				],
				'paypal_standard' => [
					'production_email' => '',
					'sandbox_email'    => '',
					'mode'             => 'production',
					'transaction'      => 'product',
					'cancel_url'       => '',
					'shipping'         => '0',
				],
				'square'          => [
					'payment_description' => '',
					'buyer_email'         => '',
					'billing_name'        => '',
					'billing_address'     => '',
				],
				'authorize_net'   => [
					'payment_description'      => '',
					'receipt_email'            => '',
					'customer_name'            => '',
					'customer_billing_address' => '',
					'recurring'                => [
						'name'                     => '',
						'period'                   => 'yearly',
						'email'                    => '',
						'customer_name'            => '',
						'customer_billing_address' => '',
					],
				],
			],
			'meta'       => [
				'template' => 'standard_fields',
			],
			'providers'  => [
				'google-sheets' => [],
			],
		];
		// phpcs:enable WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned
	}
}
