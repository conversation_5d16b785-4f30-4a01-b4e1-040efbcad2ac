<?php

namespace WPForms\DevTools;

/**
 * Dashboard Widget.
 * Keep track of addons that have updates ready to be rolled out.
 *
 * @since 0.6.0
 */
class DashboardWidget {

	/**
	 * Which sections do we have inside the CHANGELOG.md for each plugin.
	 *
	 * @since 0.7.1
	 *
	 * @var array
	 */
	private static $changelog_sections = [ 'important', 'added', 'changed', 'fixed' ];

	/**
	 * Constructor.
	 *
	 * @since 0.6.0
	 */
	private function __construct() {

		$this->init();
	}

	/**
	 * Instantiate class.
	 *
	 * @since 0.6.0
	 */
	public static function instance() {

		new DashboardWidget();
	}

	/**
	 * Init class.
	 *
	 * @since 0.6.0
	 */
	private function init() {

		if ( ! wpforms_current_user_can() ) {
			return;
		}

		if ( ! apply_filters( 'wpf_admin_dashboard_widget', '__return_true' ) ) {
			return;
		}

		$this->hooks();
	}

	/**
	 * Widget hooks.
	 *
	 * @since 0.6.0
	 */
	private function hooks() {

		add_action( 'admin_enqueue_scripts', [ $this, 'widget_scripts' ] );
		add_action( 'wp_dashboard_setup', [ $this, 'widget_register' ] );
	}

	/**
	 * Load widget-specific scripts and styles.
	 *
	 * @since 0.6.0
	 */
	public function widget_scripts() {

		$screen = get_current_screen();

		if ( ! isset( $screen->id ) || $screen->id !== 'dashboard' ) {
			return;
		}

		wp_enqueue_style(
			'wpf-dash-widget',
			WPFORMS_DEV_TOOLS_URL . '/assets/css/dash-widget.css',
			[],
			WPFORMS_DEV_TOOLS_VERSION
		);

		wp_enqueue_script( 'jquery-ui' );
		wp_enqueue_script( 'jquery-ui-accordion' );

		wp_add_inline_script(
			'jquery-ui-accordion',
			'jQuery( function( $ ) {
				$( ".wpf-dash-widget" )
					.css( "display", "block" )
					.accordion( {
						collapsible: true,
						heightStyle: "content",
						active: false,
					} );
			} );'
		);
	}

	/**
	 * Register the widget.
	 *
	 * @since 0.6.0
	 */
	public function widget_register() {

		global $wp_meta_boxes;

		$widget_key = 'wpf_plugin_status';

		wp_add_dashboard_widget(
			$widget_key,
			esc_html__( 'WPForms & Addons Release Status', 'wpf' ),
			[ $this, 'widget_content' ]
		);

		// Attempt to place the widget at the top.
		$normal_dashboard = $wp_meta_boxes['dashboard']['normal']['core'];
		$widget_instance  = [ $widget_key => $normal_dashboard[ $widget_key ] ];

		unset( $normal_dashboard[ $widget_key ] );
		$sorted_dashboard = array_merge( $widget_instance, $normal_dashboard );

		$wp_meta_boxes['dashboard']['normal']['core'] = $sorted_dashboard; // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited
	}

	/**
	 * Load widget content.
	 *
	 * @since 0.6.0
	 */
	public function widget_content() {

		$plugins = $this->get_wpforms_plugins();

		echo '<div class="wpf-dash-widget" style="display: none;">';

		foreach ( $plugins as $plugin ) {
			$this->output_plugin_item( $plugin );
		}

		echo '</div><!-- .wpf-dash-widget -->';
	}

	/**
	 * Set the default state of the widget to hidden.
	 *
	 * @since 0.30
	 */
	public static function set_default_widget_state() {

		$widget_key = 'wpf_plugin_status';
		$meta_key   = 'metaboxhidden_dashboard';
		$user_id    = get_current_user_id();
		$hidden     = get_user_meta( $user_id, $meta_key, true );
		$hidden     = is_array( $hidden ) ? $hidden : [];

		$hidden[] = $widget_key;
		$hidden   = array_unique( $hidden );

		update_user_meta( $user_id, $meta_key, $hidden );
	}

	/**
	 * Generate and output plugin item HTML.
	 *
	 * @since 0.6.0
	 *
	 * @param array $plugin Plugin data.
	 */
	private function output_plugin_item( $plugin ) {

		if ( empty( $plugin['Name'] ) || empty( $plugin['release_info']['version'] ) ) {
			return;
		}

		$plugin['release_info']['status'] = stripos( $plugin['release_info']['version'], 'unreleased' ) ? 'unreleased' : 'latest';

		if ( $plugin['release_info']['status'] === 'unreleased' ) {
			$release_status = esc_html__( 'Unreleased', 'wpf' );
			$release_class  = 'wpf-unreleased';
		} else {
			$release_status = esc_html__( 'Latest', 'wpf' );
			$release_class  = 'wpf-latest';
		}

		if ( $plugin['release_info']['status'] === 'latest' ) {
			$release_status .= '&nbsp;<span class="version">' . $plugin['release_info']['version'] . '</span>';
		}

		printf(
			'<h3>
				%1$s
				<span class="%2$s">%3$s</span>
			</h3>
			<div class="wpf-release-info">%4$s</div>',
			esc_html( $plugin['Name'] !== 'WPForms' ? str_replace( 'WPForms ', '', $plugin['Name'] ) : $plugin['Name'] ),
			sanitize_html_class( $release_class ),
			wp_kses( $release_status, [ 'span' => [ 'class' => true ] ] ),
			$this->get_plugin_item_release_info_html( $plugin ) // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
		);
	}

	/**
	 * Generate plugin item release info HTML.
	 *
	 * @since 0.6.0
	 *
	 * @param array $plugin Plugin data.
	 *
	 * @return string Plugin release info HTML.
	 */
	private function get_plugin_item_release_info_html( $plugin ) {

		$release_info = '';

		if ( $plugin['release_info']['status'] === 'latest' ) {
			$release_info .= sprintf(
				'<h4 class="wpf-release-info-version">%1$s</h4>',
				esc_html( $plugin['release_info']['version'] )
			);
		}

		foreach ( self::$changelog_sections as $key ) {
			if ( empty( $plugin['release_info'][ $key ] ) ) {
				continue;
			}
			$release_info .= sprintf(
				'<h4>%1$s</h4>%2$s',
				esc_html( ucfirst( $key ) ),
				$this->text_to_ul( $plugin['release_info'][ $key ] ) // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
			);
		}

		return $release_info;
	}

	/**
	 * Get all WPForms plugins.
	 *
	 * @since 0.6.0
	 *
	 * @return array Plugins.
	 */
	private function get_wpforms_plugins() {

		$wp_plugins_dir = trailingslashit( wp_normalize_path( WP_PLUGIN_DIR ) );
		$dirs           = glob( $wp_plugins_dir . 'wpforms-*/', GLOB_BRACE );

		if ( empty( $dirs ) ) {
			return [];
		}

		$dirs = array_filter(
			$dirs,
			static function( $dir ) {

				return strpos( $dir, '/wpforms-upgrade/' ) === false;
			}
		);

		sort( $dirs );
		array_unshift( $dirs, $wp_plugins_dir . 'wpforms/' );

		$plugins = [];

		foreach ( $dirs as $plugin_path ) {
			$release_info = $this->get_release_info( $plugin_path );
			$plugin_dir   = basename( $plugin_path );
			$plugin_file  = untrailingslashit( $plugin_dir ) . '.php';

			if ( ! is_readable( $plugin_path . $plugin_file ) ) {
				continue;
			}

			$plugin_dir_file        = $plugin_dir . untrailingslashit( $plugin_dir ) . '.php';
			$plugin                 = get_plugin_data( $plugin_path . $plugin_file, false );
			$plugin['release_info'] = $release_info;

			$plugins[ $plugin_dir_file ] = $plugin;
		}

		return $plugins;
	}

	/**
	 * Get latest release information from CHANGELOG.md file.
	 *
	 * @since 0.6.0
	 *
	 * @param string $dir Plugin directory full path.
	 *
	 * @return bool|array Release info array or false.
	 */
	private function get_release_info( $dir ) {

		$changelog_file = trailingslashit( $dir ) . 'CHANGELOG.md';

		if ( ! is_readable( $changelog_file ) ) {
			return false;
		}

		$changelog = file_get_contents( $changelog_file );

		if ( empty( $changelog ) ) {
			return false;
		}

		$chunks = preg_split( '/## (\[.*\].*)/m', $changelog, 3, PREG_SPLIT_DELIM_CAPTURE );

		if ( empty( $chunks[1] ) && empty( $chunks[2] ) ) {
			return false;
		}

		$release = [
			'version'  => trim( $chunks[1] ),
			'raw_info' => trim( $chunks[2] ),
		];

		$chunks = preg_split( '/### (.*)/m', $release['raw_info'], 6, PREG_SPLIT_DELIM_CAPTURE );

		if ( empty( $chunks ) ) {
			return false;
		}

		foreach ( $chunks as $i => $chunk ) {
			$section = strtolower( trim( $chunk ) );

			if ( ! in_array( $section, self::$changelog_sections, true ) ) {
				continue;
			}
			$release[ $section ] = trim( $chunks[ $i + 1 ] );
		}

		return $release;
	}

	/**
	 * Convert plain text to unordered list (HTML).
	 *
	 * @since 0.6.0
	 *
	 * @param string $text Plain text.
	 *
	 * @return string Unordered list (HTML).
	 */
	private function text_to_ul( $text ) {

		$lines = preg_split( "/\r\n|\n|\r/", $text );

		if ( empty( $lines ) ) {
			return '';
		}

		$ul = '';

		foreach ( $lines as $line ) {
			$line = preg_replace( '/^- |^-/', '', $line );
			$ul  .= empty( $line ) ? '' : '<li>' . $line . '</li>';
		}
		$ul = empty( $ul ) ? '' : '<ul>' . $ul . '</ul>';

		return $ul;
	}
}
