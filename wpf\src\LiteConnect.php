<?php

namespace WPForms\DevTools;

/**
 * Lite Connect service.
 *
 * @since {VERSION}
 */
class LiteConnect {

	/**
	 * Lite Connect query.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private $query;

	/**
	 * Lite Connect API URL.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private $api_url;

	/**
	 * Lite Connect production API URL.
	 *
	 * @since {VERSION}
	 */
	const PRODUCTION_API_URL = 'https://wpformsliteconnect.com/utils/get_sites';

	/**
	 * Lite Connect staging API URL.
	 *
	 * @since {VERSION}
	 */
	const STAGING_API_URL = 'https://staging.wpformsliteconnect.com/utils/get_sites';

	/**
	 * Lite Connect support token.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private $support_token;

	/**
	 * Lite Connect constructor.
	 *
	 * @since {VERSION}
	 *
	 * @param string $query Lite Connect query.
	 */
	public function __construct( $query ) {

		$this->query = $query;

		$this->api_url = self::PRODUCTION_API_URL;

		if ( defined( 'WPFORMS_LITE_CONNECT_STAGING' ) && WPFORMS_LITE_CONNECT_STAGING ) {
			$this->api_url = self::STAGING_API_URL;
		}

		if ( defined( 'WPFORMS_LITE_CONNECT_SUPPORT_TOKEN' ) && WPFORMS_LITE_CONNECT_SUPPORT_TOKEN ) {
			$this->support_token = WPFORMS_LITE_CONNECT_SUPPORT_TOKEN;
		}
	}

	/**
	 * Get sites from Lite Connect API.
	 *
	 * @since {VERSION}
	 *
	 * @return array
	 */
	private function get_sites() {

		if ( ! $this->support_token ) {
			return [
				'error' => 'Support token is not defined.',
			];
		}

		$param = null;
		$query = trim( $this->query );

		if ( wpforms_is_email( $query ) ) {
			$param = 'admin_email';
		}

		// Check if query is a URL.
		if ( wpforms_is_url( $query ) ) {
			$query = wp_parse_url( $query, PHP_URL_HOST );
			// Remove www. from the beginning of the domain.
			$query = preg_replace( '/^www\./', '', $query );
			$param = 'domain';
		}

		// Check if query is not a URL but a domain.
		if ( ! $param && preg_match( '/^([a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,}$/', $query ) ) {
			$param = 'domain';
		}

		if ( ! $param ) {
			return [
				'error' => 'Invalid query.',
			];
		}

		$api_url = add_query_arg(
			[
				$param => $query,
			],
			$this->api_url
		);

		$request_args = [
			'headers'    => [
				'X-WPForms-Lite-Connect-Support-Token' => $this->support_token,
			],
			'timeout'    => 3000,
			'user-agent' => 'WPForms/curl',
		];

		$response = wp_remote_post( $api_url, $request_args );

		if ( is_wp_error( $response ) ) {
			return [
				'error' => $response->get_error_message(),
			];
		}

		$response = json_decode( wp_remote_retrieve_body( $response ), true );

		if ( empty( $response['sites'] ) ) {
			return [
				'error' => 'No sites found.',
			];
		}

		return $response['sites'];
	}

	/**
	 * Run Lite Connect.
	 *
	 * @since {VERSION}
	 *
	 * @return array
	 */
	public function run() {

		return [
			'sites' => $this->get_sites(),
		];
	}
}
