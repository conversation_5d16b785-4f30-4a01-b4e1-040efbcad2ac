let WPFormsBuilderSquare=window.WPFormsBuilderSquare||((e,n,r)=>{let s={},a={init(){r(a.ready)},ready(){var e,n,t;s.$singlePaymentControl=r("#wpforms-panel-field-square-enable_one_time"),s.$recurringPaymentControl=r("#wpforms-panel-field-square-enable_recurring"),s.$panelContent=r("#wpforms-panel-content-section-payment-square"),s.$AJAXSubmitOption=r("#wpforms-panel-field-settings-ajax_submit"),s.$cardButton=r("#wpforms-add-fields-square"),s.$alert=r("#wpforms-square-credit-card-alert"),s.$feeNotice=r(".wpforms-square-notice-info"),a.bindUIActions(),a.bindPlanUIActions(),wpforms_builder.square_is_pro||(t=(e=".wpforms-panel-content-section-square")+" .wpforms-panel-content-section-payment-plan-name input",r(n=e+" .wpforms-panel-content-section-payment-toggle input").each(WPFormsBuilderPaymentsUtils.toggleContent),r(t).each(WPFormsBuilderPaymentsUtils.checkPlanName),r("#wpforms-panel-payments").on("click",n,WPFormsBuilderPaymentsUtils.toggleContent).on("click",e+" .wpforms-panel-content-section-payment-plan-head-buttons-toggle",WPFormsBuilderPaymentsUtils.togglePlan).on("click",e+" .wpforms-panel-content-section-payment-plan-head-buttons-delete",WPFormsBuilderPaymentsUtils.deletePlan).on("input",t,WPFormsBuilderPaymentsUtils.renamePlan).on("focusout",t,WPFormsBuilderPaymentsUtils.checkPlanName))},bindUIActions(){r(e).on("wpformsSaved",a.ajaxRequiredCheck).on("wpformsSaved",a.paymentsEnabledCheck).on("wpformsSaved",a.requiredFieldsCheck).on("wpformsFieldAdd",a.fieldAdded).on("wpformsFieldDelete",a.fieldDeleted).on("wpformsPaymentsPlanCreated",a.toggleMultiplePlansWarning).on("wpformsPaymentsPlanCreated",a.bindPlanUIActions).on("wpformsPaymentsPlanDeleted",a.toggleMultiplePlansWarning),s.$cardButton.on("click",a.connectionCheck)},bindPlanUIActions(){s.$panelContent.find(".wpforms-panel-content-section-payment-plan-body .wpforms-panel-field-select select").on("change",a.resetRequiredPlanFieldError)},ajaxRequiredCheck(){!r("#wpforms-panel-fields .wpforms-field.wpforms-field-square").length||a.isAJAXSubmitEnabled()||r.alert({title:wpforms_builder.heads_up,content:wpforms_builder.square_ajax_required,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},paymentsEnabledCheck(){!r("#wpforms-panel-fields .wpforms-field.wpforms-field-square").length||a.isPaymentsEnabled()||r.alert({title:wpforms_builder.heads_up,content:wpforms_builder.square_payments_enabled_required,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}})},requiredFieldsCheck(){if(s.$recurringPaymentControl.is(":checked")&&!s.$panelContent.hasClass("wpforms-hidden")){let t=!1;if(s.$panelContent.find(".wpforms-panel-content-section-payment-plan").each(function(){var e=r(this).data("plan-id"),n=r(`#wpforms-panel-field-square-recurring-${e}-customer_email`),e=r(`#wpforms-panel-field-square-recurring-${e}-customer_name`);n.val()||(n.addClass("wpforms-required-field-error"),t=!0),e.val()||(e.addClass("wpforms-required-field-error"),t=!0)}),t){let e=wpforms_builder.square_recurring_payments_fields_required;r(".wpforms-panel-content-section-square").is(":visible")||(e+=" "+wpforms_builder.square_recurring_payments_fields_settings),r.alert({title:wpforms_builder.square_recurring_payments_fields_heading,content:e,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}},onOpen(){r(".wpforms-square-settings-redirect").on("click",a.settingsRedirect)}})}}},settingsRedirect(){r(".wpforms-panel-payments-button").trigger("click"),r(".wpforms-panel-sidebar-section-square").trigger("click"),n.location.href=n.location.pathname+n.location.search+"#wpforms-panel-field-square-enable_recurring-wrap",r(this).closest(".jconfirm-box").find(".btn-confirm").trigger("click")},resetRequiredPlanFieldError(){var e=r(this).attr("name");(e.includes("customer_email")||e.includes("customer_name"))&&r(this).toggleClass("wpforms-required-field-error",!r(this).val())},connectionCheck(){return!r(this).hasClass("wpforms-add-fields-button-disabled")&&(!r(this).hasClass("square-connection-required")||void r.alert({title:wpforms_builder.heads_up,content:wpforms_builder.square_connection_required,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"]}}}))},fieldAdded(e,n,t){"square"===t&&(a.cardButtonToggle(!0),a.settingsToggle(!0),a.paymentsEnabledCheck(),s.$feeNotice.toggleClass("wpforms-hidden"))},fieldDeleted(e,n,t){"square"===t&&(a.cardButtonToggle(!1),a.settingsToggle(!1),a.disablePayments(),a.disableNotifications(),s.$feeNotice.toggleClass("wpforms-hidden"))},toggleMultiplePlansWarning(){s.$panelContent.find(".wpforms-square-multiple-plans-warning").toggleClass("wpforms-hidden",1===s.$panelContent.find(".wpforms-panel-content-section-payment-plan").length)},cardButtonToggle(e){s.$cardButton.prop("disabled",e).toggleClass("wpforms-add-fields-button-disabled",e)},settingsToggle(e){s.$alert.length&&(s.$alert.toggleClass("wpforms-hidden",e),r("#wpforms-panel-content-section-payment-square").toggleClass("wpforms-hidden",!e),e||(s.$singlePaymentControl.prop("checked",!1).trigger("change"),s.$recurringPaymentControl.prop("checked",!1).trigger("change")))},disablePayments(){r("#wpforms-panel-field-square-enable_one_time, #wpforms-panel-field-square-enable_recurring").prop("checked",!1).trigger("change").each(WPFormsBuilderPaymentsUtils.toggleContent)},disableNotifications(){var e=r('.wpforms-panel-content-section-notifications [id*="-square-wrap"]');e.find('input[id*="-square"]').prop("checked",!1),e.addClass("wpforms-hidden")},isPaymentsEnabled(){return s.$singlePaymentControl.is(":checked")||s.$recurringPaymentControl.is(":checked")},isAJAXSubmitEnabled(){return s.$AJAXSubmitOption.is(":checked")}};return a})(document,window,jQuery);WPFormsBuilderSquare.init();