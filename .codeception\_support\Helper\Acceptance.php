<?php

namespace Helper;

use Codeception\Module;
use Codeception\Exception\ModuleException;

/**
 * Acceptance test helper with robust wait strategies and safe interaction methods.
 * 
 * This helper addresses common acceptance test issues:
 * - Overlay interception problems
 * - Element not found issues
 * - Timing problems with dynamic content
 * - Modal/dialog handling
 */
class Acceptance extends Module
{
    /**
     * Wait for overlay to disappear before interactions
     * 
     * @param int $timeout Maximum time to wait in seconds
     * @throws ModuleException
     */
    public function waitForOverlayToDisappear($timeout = 10)
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        try {
            $webDriver->waitForElementNotVisible('#wpforms-builder-overlay', $timeout);
        } catch (\Exception $e) {
            // Overlay might not exist, continue
        }
        
        // Additional buffer time for any remaining animations
        $webDriver->wait(0.5);
    }

    /**
     * Wait for element to be clickable and not intercepted
     * 
     * @param string $selector CSS selector or XPath
     * @param int $timeout Maximum time to wait in seconds
     * @throws ModuleException
     */
    public function waitForClickableElement($selector, $timeout = 10)
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        $webDriver->waitForElementVisible($selector, $timeout);
        $webDriver->waitForElementClickable($selector, $timeout);
        $this->waitForOverlayToDisappear();
    }

    /**
     * Safe click that handles overlays and dialogs
     * 
     * @param string $selector CSS selector or XPath
     * @param int $timeout Maximum time to wait in seconds
     * @throws ModuleException
     */
    public function safeClick($selector, $timeout = 10)
    {
        $this->dismissAnyModals();
        $this->waitForClickableElement($selector, $timeout);
        
        $webDriver = $this->getModule('WPWebDriver');
        $webDriver->click($selector);
    }

    /**
     * Dismiss any blocking modals or dialogs
     * 
     * @throws ModuleException
     */
    public function dismissAnyModals()
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        // Handle jConfirm dialogs
        try {
            if ($webDriver->_findElements('.jconfirm-cell')) {
                $webDriver->click('.jconfirm-buttons .btn-confirm');
                $webDriver->waitForElementNotVisible('.jconfirm-cell', 5);
            }
        } catch (\Exception $e) {
            // Modal might not exist, continue
        }
        
        // Handle WordPress admin notices
        try {
            if ($webDriver->_findElements('.notice-dismiss')) {
                $webDriver->click('.notice-dismiss');
            }
        } catch (\Exception $e) {
            // Notice might not exist, continue
        }
        
        // Handle WPForms specific modals
        try {
            if ($webDriver->_findElements('#wpforms-splash-modal .wpforms-splash-modal-close')) {
                $webDriver->click('#wpforms-splash-modal .wpforms-splash-modal-close');
                $webDriver->waitForElementNotVisible('#wpforms-splash-modal', 5);
            }
        } catch (\Exception $e) {
            // Modal might not exist, continue
        }
    }

    /**
     * Wait for element with retry logic
     * 
     * @param string $selector CSS selector or XPath
     * @param int $timeout Maximum time to wait in seconds
     * @param int $retries Number of retries
     * @throws ModuleException
     */
    public function waitForElementWithRetry($selector, $timeout = 10, $retries = 3)
    {
        $webDriver = $this->getModule('WPWebDriver');
        $attempt = 0;
        
        while ($attempt < $retries) {
            try {
                $webDriver->waitForElementVisible($selector, $timeout);
                return;
            } catch (\Exception $e) {
                $attempt++;
                if ($attempt >= $retries) {
                    throw $e;
                }
                $webDriver->wait(1);
            }
        }
    }

    /**
     * Safe fill field that waits for element to be ready
     * 
     * @param string $selector CSS selector or XPath
     * @param string $value Value to fill
     * @param int $timeout Maximum time to wait in seconds
     * @throws ModuleException
     */
    public function safeFillField($selector, $value, $timeout = 10)
    {
        $this->waitForClickableElement($selector, $timeout);
        
        $webDriver = $this->getModule('WPWebDriver');
        $webDriver->clearField($selector);
        $webDriver->fillField($selector, $value);
    }

    /**
     * Wait for text to appear with retry logic
     * 
     * @param string $text Text to wait for
     * @param int $timeout Maximum time to wait in seconds
     * @param string $selector Optional selector to limit search
     * @throws ModuleException
     */
    public function waitForTextWithRetry($text, $timeout = 10, $selector = null)
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        try {
            $webDriver->waitForText($text, $timeout, $selector);
        } catch (\Exception $e) {
            // Retry once after a brief pause
            $webDriver->wait(1);
            $webDriver->waitForText($text, $timeout, $selector);
        }
    }

    /**
     * Ensure page is fully loaded before proceeding
     * 
     * @param int $timeout Maximum time to wait in seconds
     * @throws ModuleException
     */
    public function waitForPageLoad($timeout = 30)
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        // Wait for jQuery to be loaded and ready
        $webDriver->waitForJS('return typeof jQuery !== "undefined" && jQuery.active == 0', $timeout);
        
        // Wait for document ready state
        $webDriver->waitForJS('return document.readyState === "complete"', $timeout);
        
        // Additional wait for any remaining animations
        $webDriver->wait(0.5);
    }

    /**
     * Safe select option that waits for dropdown to be ready
     * 
     * @param string $selector CSS selector or XPath
     * @param string $option Option to select
     * @param int $timeout Maximum time to wait in seconds
     * @throws ModuleException
     */
    public function safeSelectOption($selector, $option, $timeout = 10)
    {
        $this->waitForClickableElement($selector, $timeout);
        
        $webDriver = $this->getModule('WPWebDriver');
        $webDriver->selectOption($selector, $option);
    }

    /**
     * Wait for AJAX requests to complete
     * 
     * @param int $timeout Maximum time to wait in seconds
     * @throws ModuleException
     */
    public function waitForAjax($timeout = 30)
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        // Wait for jQuery AJAX to complete
        $webDriver->waitForJS('return typeof jQuery !== "undefined" && jQuery.active == 0', $timeout);
        
        // Wait for any WPForms specific AJAX
        $webDriver->waitForJS('return typeof wpforms === "undefined" || !wpforms.ajaxInProgress', $timeout);
    }

    /**
     * Scroll element into view before interaction
     * 
     * @param string $selector CSS selector or XPath
     * @throws ModuleException
     */
    public function scrollIntoView($selector)
    {
        $webDriver = $this->getModule('WPWebDriver');
        $webDriver->scrollTo($selector, 0, -100);
    }

    /**
     * Check if element exists without throwing exception
     * 
     * @param string $selector CSS selector or XPath
     * @return bool
     */
    public function elementExists($selector)
    {
        $webDriver = $this->getModule('WPWebDriver');
        
        try {
            return count($webDriver->_findElements($selector)) > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Wait for element to be stable (not moving/changing)
     * 
     * @param string $selector CSS selector or XPath
     * @param int $timeout Maximum time to wait in seconds
     * @throws ModuleException
     */
    public function waitForElementStable($selector, $timeout = 10)
    {
        $webDriver = $this->getModule('WPWebDriver');
        $webDriver->waitForElementVisible($selector, $timeout);
        
        // Wait a bit for any animations to settle
        $webDriver->wait(0.3);
        
        // Verify element is still visible and stable
        $webDriver->seeElement($selector);
    }
}
