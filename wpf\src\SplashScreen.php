<?php

namespace WPForms\DevTools;

/**
 * Splash screen.
 *
 * @since {VERSION}
 */
class SplashScreen {

	/**
	 * Constructor.
	 *
	 * @since {VERSION}
	 */
	public function __construct() {

		$this->init();
	}

	/**
	 * Instantiate class.
	 *
	 * @since {VERSION}
	 */
	public static function instance() {

		new self();
	}

	/**
	 * Init class.
	 *
	 * @since {VERSION}
	 */
	public function init() {

		if ( ! function_exists( 'wpforms' ) || ! wpforms_current_user_can() ) {
			return;
		}

		if ( ! apply_filters( 'wpf_admin_devtools', '__return_true' ) ) { // phpcs:ignore WPForms.PHP.ValidateHooks.InvalidHookName, WPForms.Comments.PHPDocHooks.RequiredHookDocumentation
			return;
		}

		$this->hooks();
	}

	/**
	 * Hooks.
	 *
	 * @since {VERSION}
	 */
	public function hooks() {

		add_action( 'admin_init', [ $this, 'splash_screen_preview_process' ] );
		add_filter( 'wpforms_admin_splash_splashtrait_get_user_license', [ $this, 'reset_splash_screen_preview_license' ] );
		add_filter( 'wpforms_admin_splash_splashtrait_get_user_version', [ $this, 'reset_core_version' ] );
	}

	/**
	 * Process splash screen form submit.
	 *
	 * @since {VERSION}
	 */
	public function splash_screen_preview_process() {

		$nonce = filter_input( INPUT_POST, 'splash_screen_preview_nonce', FILTER_SANITIZE_FULL_SPECIAL_CHARS );

		if ( ! wp_verify_nonce( $nonce, 'splash_screen_preview' ) ) {
			return;
		}

		$license = filter_input( INPUT_POST, 'license', FILTER_SANITIZE_FULL_SPECIAL_CHARS );

		if ( ! $license ) {
			return;
		}

		$splash_screen_preview = [
			'license' => $license,
		];

		Utils::update_option( 'splash_screen_preview', $splash_screen_preview );

		// Unset splash screen versions to show splash screen.
		update_option( 'wpforms_splash_version', WPFORMS_VERSION );
		delete_option( 'wpforms_splash_data_version' );

		// Unset Hide announcements option.
		$wpforms_settings = get_option( 'wpforms_settings', [] );

		unset( $wpforms_settings['hide-announcements'] );
		update_option( 'wpforms_settings', $wpforms_settings );

		// Force update cache to check is content exists.
		Main::wpforms_obj( 'splash_cache' )->update( true );

		$cached_data = Main::wpforms_obj( 'splash_cache' )->get();

		// If no data found, reset splash versions.
		if ( empty( $cached_data ) ) {
			update_option( 'wpforms_splash_data_version', WPFORMS_VERSION );
			update_option( 'wpforms_splash_version', WPFORMS_VERSION );

			return;
		}

		// Show the welcome message on dashboard widget for user.
		update_user_meta( get_current_user_id(), 'wpforms_dash_widget_hide_welcome_block', 0 );
		wp_safe_redirect( admin_url( 'admin.php?page=wpforms-overview&wpforms_action=preview-splash-screen' ) );
		exit;
	}

	/**
	 * Reset license type used to display splash screen.
	 *
	 * @since {VERSION}
	 *
	 * @param string $license License type.
	 *
	 * @return string
	 */
	public function reset_splash_screen_preview_license( $license ) {

		$splash_screen_preview = Utils::get_option( 'splash_screen_preview' );

		if ( $splash_screen_preview && $splash_screen_preview['license'] ) {
			return $splash_screen_preview['license'];
		}

		return $license;
	}

	/**
	 * Reset core version used to display splash screen.
	 *
	 * @since {VERSION}
	 *
	 * @param string $version Core version.
	 *
	 * @return string
	 */
	public function reset_core_version( $version ) {

		$splash_screen_preview = Utils::get_option( 'splash_screen_preview' );

		if ( $splash_screen_preview && $splash_screen_preview['version'] ) {
			return $splash_screen_preview['version'];
		}

		return $version;
	}
}
