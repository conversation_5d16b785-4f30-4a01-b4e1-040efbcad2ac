// Entries overview page (Table) - admin.php?page=wpforms-entries

.wpforms-entries-overview {

	// Overview Table
	table.wpforms-table-list {

		> tbody {

			> tr {
				height: 49px;
			}
		}

		.column-graph {

			&::before {
				top: $spacing_ss;
			}

			@include media ( "<=1024px" ) {
				min-height: 17px;

				&::before {
					line-height: 25px;
				}
			}

			@include media ( '>desktop' ) {
				width: 46px;
				text-align: center;
			}

			.dashicons {
				height: 25px;
				width: 32px;
				transition: none;
				text-align: center;
				padding: 0;

				&-dismiss {
					height: 18px;
					width: 18px;
					line-height: 18px;
				}
			}
		}

		@include media ( ">tablet" ) {

			.column-name {
				width: auto;
			}

			.column-created,
			.column-last_entry {
				min-width: 136px;
			}

			.column-all_time,
			.column-timespan {
				min-width: 76px;
				text-align: center;
				white-space: nowrap;

				a {
					justify-content: center;
				}
			}
		}
	}
}

// Show chart button
.wpforms-show-chart {

	&.dashicons {
		background-color: $color_white;
		border: 1px solid currentcolor;
		border-radius: $border_radius_xs;
		color: #a0a5aa;
		font-size: 17px;

		&:hover {
			color: $color_overview_button_hover;
			border-color: $color_overview_button_hover;
		}
	}
}

// Reset chart button
.wpforms-reset-chart {
	background-color: transparent;
	border-style: hidden;
	position: relative;

	&::before {
		color: $color_red;
		font-size: $font_size_l;
		position: relative;
		z-index: 1;
	}

	&:hover::before {
		color: $color_dark_red;
	}

	td & {
		&::after {
			background-color: $color_white;
			border-radius: $spacing_ms;
			content: "";
			left: calc(50% - 7px);
			position: absolute;
			height: 14px;
			top: calc(50% - 6px);
			width: 14px;
		}

		@include media ( "<=tablet" ) {

			margin-left: $spacing_s/-1;
		}
	}
}
