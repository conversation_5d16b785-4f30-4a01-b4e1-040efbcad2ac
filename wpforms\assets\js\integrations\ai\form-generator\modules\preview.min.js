export default function(s,i){let n=wpforms_ai_form_generator,r={el:{},mouse:{},init(){r.el.$contentWrap=s.main.el.$generatorPanel.find(".wpforms-panel-content-wrap"),r.el.$content=r.el.$contentWrap.find(".wpforms-panel-content"),r.el.$emptyState=r.el.$content.find(".wpforms-panel-empty-state"),r.events()},events(){i(document).on("mousemove",e=>{r.mouse.x=e.pageX,r.mouse.y=e.pageY}),r.el.$contentWrap.on("scroll",r.closeTooltips)},update(){var e=s.state.aiResponse;if(e&&e.fields){for(var t in s.state.isPreviewUpdate=!0,s.state.previewFields=[],r.clear(!1),r.displayHeader(e),e.fieldsOrder){var o=e.fieldsOrder[t];r.field(e.fields[o],t)}e.fieldsOrder?.length?r.displaySubmit(e.settings?.submit_text||n.panel.submitButton):(r.el.$emptyState.removeClass("wpforms-hidden-strict"),s.state.isPreviewUpdate=!1)}},async field(t,e){var o=`
				<div id="wpforms-generator-field-${t.id??""}" class="wpforms-ai-form-generator-preview-field">
					<div class="placeholder"></div>
					<div class="wpforms-field wpforms-field-${t.type??""}"></div>
				</div>
			`,o=(r.el.$content.append(o),{action:"wpforms_get_ai_form_field_preview",nonce:n.nonce,field:t});await r.delay(300*e),i.post(n.ajaxUrl,o).done(function(e){e.success?r.displayField(e.data??"",t):wpf.debug("Form Generator AJAX error:",e.data.error??e.data)}).fail(function(e){wpf.debug("Form Generator AJAX error:",e.responseText??e.statusText)})},displayField(e,t){var o,i;!t.id&&0!==t.id||(i=(o=r.el.$content.find("#wpforms-generator-field-"+t.id)).find(".wpforms-field"),o.find(".placeholder").addClass("fade-out"),i.html(e??"").addClass("fade-in").toggleClass("wpforms-hidden",!e).toggleClass("required","1"===t.required).toggleClass("label_empty",!t.label),r.initTooltip(i),r.initPageBreak(i,t),s.state.previewFields.push(t.id),s.state.previewFields.length!==Object.keys(s.state.aiResponse?.fields).length)||(s.state.isPreviewUpdate=!1)},getAddonsUsedInResponse(){var e=s.state.aiResponse;if(!e||!e.fields)return"";var t,o,i=[];for(t in e.fields){var r=wpforms_ai_form_generator.addonFields[e.fields[t].type];r&&(r=wpforms_addons["wpforms-"+r]?.title.replace(n.addons.addon,"").trim())&&!i.includes(r)&&i.push(r)}return i.length?(o=i.pop(),o+=" "+n.addons.addon,i.length?i.join(", ")+", "+n.addons.and+" "+o:o):""},initPageBreak(e,t){"pagebreak"!==t.type||["top","bottom"].includes(t.position)||e.addClass("wpforms-pagebreak-normal")},initTooltip(e){var t={content:n.panel.tooltipTitle+"<br>"+n.panel.tooltipText,trigger:"manual",interactive:!0,animationDuration:100,delay:0,side:["top"],contentAsHTML:!0,functionPosition:(e,t,o)=>(o.coord.top=r.mouse.y-57,o.coord.left=r.mouse.x-130,o)};e.tooltipster(t),r.toggleTooltipOnClick(e)},toggleTooltipOnClick(t){t.on("click",()=>{r.closeTooltips();var e=t.tooltipster("status");t.tooltipster("closed"===e.state?"open":"close"),"closed"===e.state&&((e=t.tooltipster("instance"))._$tooltip.css({height:"auto"}),e._$tooltip.find(".tooltipster-arrow").css({left:"50%"}),setTimeout(function(){r.closeTooltips()},5e3))})},closeTooltips(){r.el.$content.find(".wpforms-field").each(function(){var e=i(this);e.hasClass("tooltipstered")&&e.parent().length&&e.tooltipster("close")})},displayHeader(e){e=`<h2 class="wpforms-ai-form-generator-preview-title">${e.form_title??""}</h2>`;r.el.$content.prepend(e)},displaySubmit(e){r.el.$content.append(`<button type="button" value="${e}" class="wpforms-ai-form-generator-preview-submit">${e}</button>`)},clear(e=!0){r.el.$content.find(".wpforms-ai-form-generator-preview-field").remove(),r.el.$content.find(".wpforms-ai-form-generator-preview-placeholder").remove(),r.el.$content.find(".wpforms-ai-form-generator-preview-title").remove(),r.el.$content.find(".wpforms-ai-form-generator-preview-addons-notice").remove(),r.el.$content.find(".wpforms-ai-form-generator-preview-submit").remove(),r.el.$emptyState.toggleClass("wpforms-hidden-strict",!e)},delay(t){return new Promise(e=>{setTimeout(e,t)})}};return r}