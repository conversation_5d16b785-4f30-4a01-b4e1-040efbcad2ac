$primary: #333333;
$secondary: #777777;
$error: #d63638;
$orange: #e27730;
$blue: #036aab;
$linkColor: #e27730;
$linkColorHover: #e27730;
$backgroundColor: #f8f8f8;
$backgroundContent: #ffffff;
$fontColor: #444444;
$tableBorder: #dddddd;
$orangeBackground: #f7f0ed;
$blueBackground: #edf3f7;

@import 'resets';
@import 'text';

// WPForms Corrupted Data Report Email Styles.

/* Base */
table.body,
body {
	background-color: $backgroundColor;
	text-align: center;
}

.wrapper {
	max-width: 700px;
}

.body-inner {
	box-sizing: border-box;
	padding-bottom: 40px;
}

.container {
	margin: 0 auto 0 auto;
}

.header {
	line-height: 1;
	padding: 30px;
	text-align: center;

	.header-image {
		display: inline-block;
		margin: 0 auto 0 auto;
		max-width: 260px;
		vertical-align: middle;
	}

	img {
		display: inline-block !important;
		max-height: 180px;
		vertical-align: middle;
	}
}

// Hide the dark variation by default.
.header-wrapper {
	&.dark-mode {
		display: none;
	}
}

/* Typography */
p, td {
	-webkit-hyphens: none;
	-moz-hyphens: none;
	hyphens: none;
}

a, p, pre {
	-ms-word-break: break-word;
	word-break: break-word;
}

.content {
	p, li {
		font-size: 16px;
		line-height: 24px;
	}

	p {
		margin-bottom: 0;

		+ p {
			margin-top: 24px;
		}
	}

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		color: $fontColor;
	}
}

/* Content */
.content {
	.corrupted-data-report-container {
		> tbody {
			> tr:last-child {
				.corrupted-data-report-content {
					border-bottom-left-radius: 6px;
					border-bottom-right-radius: 6px;
				}
			}
		}
	}

	.corrupted-data-report-content {
		background-color: $backgroundContent;
		border-top-left-radius: 6px;
		border-top-right-radius: 6px;
		padding: 50px 30px;

		.corrupted-data-report-content-inner {

			div {
				max-width: 600px;
				margin-left: auto;
				margin-right: auto;
			}
		}
	}

}

.corrupted-data-report-header {
	width: 100%;

	p {
		margin-bottom: 15px;
	}
}

.corrupted-data-report-table {
	width: 100%;

	table {
		border: 1px solid $tableBorder;

		th, td {
			border: 1px solid $tableBorder;
			font-size: 16px;
		}

		th {
			background-color: $backgroundColor;
			font-weight: bold;
			padding: 15px 20px;
			vertical-align: middle;
		}

		td {
			padding: 12px 20px;
		}
	}
}

.corrupted-data-reports-count {
	color: $error;
	font-size: 18px;
	font-weight: bold;
}

.corrupted-data-reports-urls {
	.corrupted-data-report-url-wrapper {
		padding: 8px 0;
		border-bottom: 1px solid #f0f0f0;

		&:first-child {
			padding-top: 0;
		}
	}

	& > div:last-child {
		border-bottom: 0;
		padding-bottom: 0;
	}

	.corrupted-data-report-urls-more,
	.corrupted-data-report-urls-empty {
		font-style: italic;
		color: $secondary;
	}

	.corrupted-data-report-urls-more {
		margin-top: 8px;
		font-size: 12px;
	}

	.corrupted-data-report-urls-empty {
		font-size: 14px;
	}
}



.corrupted-data-report-url-count {
	color: $secondary;
	font-size: 12px;
	margin-top: 2px;
}

.corrupted-data-report-recommendations {
	width: 100%;
	margin-top: 30px;

	h6 {
		font-size: 20px;
		line-height: 24px;
		margin-bottom: 24px;
	}

	p {
		margin-bottom: 15px;
	}

	ol {
		li {
			margin-bottom: 20px;
		}
	}
}

/* Footer */
.footer {
	color: $secondary;
	font-size: 13px;
	line-height: 20px;
	padding: 20px 30px 20px 30px;
	text-align: center;

	a {
		color: $secondary;
		text-decoration: underline;

		&:hover {
			color: $secondary;
		}
	}
}
