<?php

namespace WPForms\DevTools;

use WP_Error;
use WPForms\Admin\Notice;
use WPForms\Pro\Admin\PluginList;

/**
 * Upgrades.
 *
 * @since 0.32
 */
class Upgrades {

	/**
	 * Upgrade files available.
	 *
	 * @since 0.32
	 */
	public const UPGRADES = 'wpf_admin_devtools_upgrades';

	/**
	 * Errors.
	 *
	 * @var array
	 *
	 * @since 0.32
	 */
	private $errors = [];

	/**
	 * Successes.
	 *
	 * @var array
	 *
	 * @since 0.32
	 */
	private $successes = [];

	/**
	 * Current upgrades.
	 *
	 * @since 0.32
	 *
	 * @var array
	 */
	private $upgrades;

	/**
	 * Hooks constructor.
	 *
	 * @since 0.32
	 */
	public function __construct() {

		$this->hooks();

		$this->upgrades = get_option( self::UPGRADES, [] );
	}

	/**
	 * Get upgrades.
	 *
	 * @since 0.32
	 *
	 * @return array
	 */
	public function get_upgrades(): array {

		$this->clean_upgrade_versions();

		return $this->upgrades;
	}

	/**
	 * Clean all version strings in an upgrade array.
	 *
	 * @since 0.44
	 *
	 * @return void
	 */
	private function clean_upgrade_versions(): void {

		if ( empty( $this->upgrades ) ) {
			return;
		}

		foreach ( $this->upgrades as $key => $upgrade ) {
			if ( isset( $upgrade['version'] ) ) {
				$this->upgrades[ $key ]['version'] = preg_replace( '/-RC\d*/', '', $upgrade['version'] );
			}
		}
	}

	/**
	 * Hooks.
	 *
	 * @since 0.7
	 */
	private function hooks(): void {

		add_action( 'admin_init', [ $this, 'save_upgrade' ] );
		add_action( 'admin_init', [ $this, 'delete_upgrade' ] );
		add_filter( 'wpforms_updater_remote_request_response_body', [ $this, 'change_remote_data' ] );
		add_filter( 'site_transient_update_plugins', [ $this, 'prevent_updating_plugins_under_git' ], 20 );
		add_filter( 'site_transient_update_plugins', [ $this, 'change_core_cache' ], 30 );
		add_filter( 'pre_set_site_transient_update_plugins', [ $this, 'change_default_remote_data' ], 20 );
		add_filter( 'wpforms_pro_admin_list_addons_cache', [ $this, 'change_addons_cache' ] );
		add_filter( 'plugins_api_result', [ $this, 'change_lite_plugins_api_result_data' ], 10, 3 );
	}

	/**
	 * Change WPForms Lite plugin versions for plugin details.
	 *
	 * @since 0.33
	 *
	 * @param object $res    Plugin information.
	 * @param string $action Action.
	 * @param object $args   Arguments.
	 *
	 * @return object
	 */
	public function change_lite_plugins_api_result_data( $res, string $action, $args ) {

		if ( $action !== 'plugin_information' || $args->slug !== 'wpforms-lite' ) {
			return $res;
		}

		$upgrades = $this->get_upgrades();

		foreach ( $upgrades as $upgrade_data ) {
			if ( $upgrade_data['slug'] !== 'wpforms-lite' ) {
				continue;
			}

			$upgrade_data = wp_parse_args(
				$upgrade_data,
				[
					'version' => $res->version,
					'wp'      => $res->requires,
					'php'     => $res->requires_php,
				]
			);

			$res->version      = $upgrade_data['version'];
			$res->requires     = $upgrade_data['wp'];
			$res->requires_php = $upgrade_data['php'];
		}

		return $res;
	}

	/**
	 * Change addons cache.
	 *
	 * @since 0.32
	 *
	 * @param array $addons_cache Addons cache.
	 *
	 * @return array
	 */
	public function change_addons_cache( array $addons_cache ): array {

		$upgrades = $this->get_upgrades();

		if ( empty( $upgrades ) ) {
			return $addons_cache;
		}

		foreach ( $upgrades as $upgrade_data ) {
			if ( ! isset( $addons_cache[ $upgrade_data['slug'] ] ) ) {
				continue;
			}

			$addons_cache[ $upgrade_data['slug'] ]['version'] = $upgrade_data['version'];
		}

		return $addons_cache;
	}

	/**
	 * Prevent updating plugins under git.
	 *
	 * @since 0.38
	 *
	 * @param mixed $value Value of site transient.
	 *
	 * @return object
	 */
	public function prevent_updating_plugins_under_git( $value ) {

		if ( ! is_object( $value ) ) {
			return $value;
		}

		/**
		 * Whether to prevent updating plugins under git.
		 *
		 * @since 0.38
		 *
		 * @param bool $prevent True to prevent plugins' updates.
		 */
		if ( ! apply_filters( 'wpforms_dev_tools_upgrades_prevent_updating_plugins_under_git', true ) ) {
			return $value;
		}

		$response = $value->response ?? [];

		foreach ( $response as $plugin_file => $plugin_data ) {
			$this->process_plugin_update( $value, $plugin_file, $plugin_data );
		}

		return $value;
	}

	/**
	 * Process individual plugin update check.
	 *
	 * @since 0.41
	 *
	 * @param mixed  $value       Site transient value.
	 * @param string $plugin_file Plugin file path.
	 * @param object $plugin_data Plugin data.
	 *
	 * @return void
	 */
	private function process_plugin_update( $value, string $plugin_file, $plugin_data ): void {

		$slug = explode( '/', $plugin_file )[0];

		if ( $this->has_simulated_upgrade( $slug ) ) {
			return;
		}

		$has_updatable_plugins_under_git = false;

		if ( $this->is_plugin_under_git( $slug ) ) {
			unset( $value->response[ $plugin_file ] );

			$plugin_data->slug = $slug;

			$value->no_update[ $plugin_file ] = $this->get_no_update( $plugin_data );

			$has_updatable_plugins_under_git = true;
		}

		if ( $has_updatable_plugins_under_git ) {
			$this->updates_prevented_notice();
		}
	}

	/**
	 * Check if the plugin is under git version control.
	 *
	 * @since 0.41
	 *
	 * @param string $slug Plugin slug.
	 *
	 * @return bool
	 */
	private function is_plugin_under_git( string $slug ): bool {

		return (
			is_dir( WP_PLUGIN_DIR . '/' . $slug . '/.git' ) ||
			( strpos( $slug, 'wpforms' ) === 0 && is_dir( WP_PLUGIN_DIR . '/.git' ) )
		);
	}

	/**
	 * Updates are currently disabled notice.
	 *
	 * @since 0.47
	 */
	private function updates_prevented_notice(): void {

		static $notices_shown = false;

		// Show notice only on certain admin pages.
		$pages = [ 'update-core.php', 'plugins.php' ];

		if ( $notices_shown || ! in_array( $GLOBALS['pagenow'], $pages, true ) ) {
			return;
		}

		Notice::warning(
			esc_html__( 'Updates are currently disabled for plugins under git version control.', 'wpf' )
		);

		$notices_shown = true;
	}

	/**
	 * Check if the plugin has a simulated upgrade.
	 *
	 * @since 0.41
	 *
	 * @param string $slug Plugin slug.
	 *
	 * @return bool
	 */
	private function has_simulated_upgrade( string $slug ): bool {

		$upgrades = $this->get_upgrades();

		foreach ( $upgrades as $upgrade ) {
			$upgrade_slug = $upgrade['slug'] ?? '';

			if ( $upgrade_slug === $slug ) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Prepare the "mock" item to the `no_update` property.
	 * Is required for the enable/disable auto-updates links to correctly appear in the UI.
	 *
	 * @since 0.38
	 *
	 * @param object $plugin_data Plugin data.
	 *
	 * @return object
	 */
	public function get_no_update( $plugin_data ) {

		return (object) [
			'id'          => $plugin_data->id,
			'slug'        => $plugin_data->slug,
			'plugin'      => $plugin_data->plugin,
			'new_version' => $plugin_data->version ?? '',
			'url'         => '',
			'package'     => '',
			'icons'       => [],
			'banners'     => [],
			'banners_rtl' => [],
			'requires'    => '',
		];
	}

	/**
	 * Change default remote data.
	 *
	 * @since 0.32
	 *
	 * @param object $update_obj Update object.
	 *
	 * @return object
	 */
	public function change_default_remote_data( $update_obj ) {

		if ( ! is_object( $update_obj ) ) {
			return $update_obj;
		}

		$upgrades = $this->get_upgrades();

		if ( empty( $upgrades ) ) {
			return $update_obj;
		}

		foreach ( $upgrades as $upgrade_data ) {
			$update_obj = $this->process_remote_data_for_upgrade( $update_obj, $upgrade_data );
		}

		return $update_obj;
	}

	/**
	 * Process remote data for upgrade.
	 *
	 * @since 0.32
	 *
	 * @param object $update_obj   Update object.
	 * @param array  $upgrade_data Upgrade data.
	 *
	 * @return object
	 */
	private function process_remote_data_for_upgrade( $update_obj, array $upgrade_data ) {

		if ( $upgrade_data['slug'] !== 'wpforms-lite' ) {
			return $update_obj;
		}

		$remote_data = [];

		if ( isset( $update_obj->response['wpforms-lite/wpforms.php'] ) ) {
			$remote_data = (array) $update_obj->response['wpforms-lite/wpforms.php'];
		} elseif ( isset( $update_obj->no_update['wpforms-lite/wpforms.php'] ) ) {
			$remote_data = (array) $update_obj->no_update['wpforms-lite/wpforms.php'];

			unset( $update_obj->no_update['wpforms-lite/wpforms.php'] );
		}

		if ( $remote_data && version_compare( $upgrade_data['version'], $this->get_current_lite_version(), '>' ) ) {
			$remote_data['new_version']                       = $upgrade_data['version'];
			$remote_data['package']                           = $upgrade_data['file'];
			$update_obj->response['wpforms-lite/wpforms.php'] = (object) $remote_data;
		}

		return $update_obj;
	}

	/**
	 * Change remote data.
	 *
	 * @since 0.32
	 *
	 * @param object $remote_data Remote data.
	 *
	 * @return object
	 */
	public function change_remote_data( $remote_data ) {

		$upgrades = $this->get_upgrades();

		if ( empty( $upgrades ) ) {
			return $remote_data;
		}

		foreach ( $upgrades as $upgrade_data ) {
			$remote_data = (array) $remote_data;

			if ( isset( $upgrade_data['slug'], $remote_data['slug'] ) && $upgrade_data['slug'] === $remote_data['slug'] ) {
				$remote_data['new_version']  = $upgrade_data['version'];
				$remote_data['package']      = $upgrade_data['file'];
				$remote_data['download_url'] = $upgrade_data['file'];
			}
		}

		return (object) $remote_data;
	}

	/**
	 * Delete upgrade.
	 *
	 * @since 0.32
	 */
	public function save_upgrade(): void {

		$nonce = isset( $_POST['nonce'] ) ? sanitize_text_field( wp_unslash( $_POST['nonce'] ) ) : '';

		if ( ! wp_verify_nonce( $nonce, DevTools::NONCE ) ) {
			return;
		}

		if ( ! isset( $_FILES['wpforms_upgrades_file']['name'] ) ) {
			return;
		}

		$filename  = sanitize_file_name( $_FILES['wpforms_upgrades_file']['name'] );
		$file_type = wp_check_filetype( $filename );

		if ( $file_type['ext'] !== 'zip' ) {
			$this->errors[] = 'Only zip files are allowed.';

			return;
		}

		$this->process_plugin_upload_form( $_FILES['wpforms_upgrades_file'] ); // phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized
	}

	/**
	 * Delete upgrade.
	 *
	 * @since 0.32
	 */
	public function delete_upgrade(): void {

		$nonce = isset( $_POST['nonce'] ) ? sanitize_text_field( wp_unslash( $_POST['nonce'] ) ) : '';

		if ( ! wp_verify_nonce( $nonce, DevTools::NONCE ) ) {
			return;
		}

		if ( ! isset( $_POST['wpforms_admin_devtools_delete_upgrade_id'] ) ) {
			return;
		}

		$upgrade_id = absint( $_POST['wpforms_admin_devtools_delete_upgrade_id'] );

		$upgrades = $this->get_upgrades();

		if ( isset( $upgrades[ $upgrade_id ] ) ) {
			wp_delete_file( $upgrades[ $upgrade_id ]['file'] );

			unset( $upgrades[ $upgrade_id ] );
			update_option( self::UPGRADES, $upgrades );

			delete_site_transient( 'update_plugins' );

			$this->successes[] = 'Plugin deleted successfully.';
		}
	}

	/**
	 * Process plugin upload form.
	 *
	 * @since 0.32
	 *
	 * @param array $file File array.
	 */
	public function process_plugin_upload_form( array $file ): void {

		$zip_file = $this->handle_file_upload( $file );

		if ( is_wp_error( $zip_file ) ) {
			$this->errors[] = 'Error uploading: ' . $zip_file->get_error_message();

			return;
		}

		$unzip_path = $this->unzip_plugin_file( $zip_file );

		if ( is_wp_error( $unzip_path ) ) {
			$this->errors[] = 'Error unzipping: ' . $unzip_path->get_error_message();

			return;
		}

		$plugin_info = $this->get_plugin_info( $unzip_path );

		$this->delete_unzipped_dir( $unzip_path );

		if ( is_wp_error( $plugin_info ) ) {
			$this->errors[] = 'Error getting plugin info: ' . $plugin_info->get_error_message();

			return;
		}

		$this->save_plugin_info( $plugin_info, $zip_file );
	}

	/**
	 * Save plugin info.
	 *
	 * @since 0.32
	 *
	 * @param array  $plugin_info Plugin info.
	 * @param string $zip_file    Path to a zip file.
	 */
	private function save_plugin_info( array $plugin_info, string $zip_file ): void {

		$upgrades = $this->get_upgrades();

		$upgrades[] = [
			'name'    => $plugin_info['name'],
			'slug'    => $plugin_info['slug'],
			'version' => $plugin_info['version'],
			'wp'      => $plugin_info['wp'],
			'php'     => $plugin_info['php'],
			'file'    => wp_normalize_path( $zip_file ),
		];

		update_option( self::UPGRADES, $upgrades );

		delete_site_transient( 'update_plugins' );

		$this->successes[] = 'Plugin uploaded successfully.';
	}

	/**
	 * Handle file upload.
	 *
	 * @since 0.32
	 *
	 * @param array $file File array.
	 *
	 * @return string|WP_Error
	 */
	private function handle_file_upload( array $file ) {

		$upload_overrides = [
			'test_form' => false,
			'test_type' => false,
			'test_size' => false,
			'mimes'     => [
				'zip' => 'application/zip',
			],
			'action'    => 'wpforms_upgrade_simulate',
		];
		$move_file        = wp_handle_upload( $file, $upload_overrides );

		if ( $move_file && ! isset( $move_file['error'] ) ) {
			return $move_file['file'];
		}

		return new WP_Error( 'upload_error', $move_file['error'] );
	}

	/**
	 * Unzip plugin file.
	 *
	 * @since 0.32
	 *
	 * @param string $zip_file Path to a zip file.
	 *
	 * @return string|WP_Error
	 */
	private function unzip_plugin_file( string $zip_file ) {

		$unzip_to = WP_CONTENT_DIR . '/uploads/wpf/upgrades/';
		$result   = unzip_file( $zip_file, $unzip_to );

		if ( is_wp_error( $result ) ) {
			return $result;
		}

		return $unzip_to;
	}

	/**
	 * Get plugin info.
	 *
	 * @since 0.32
	 *
	 * @param string $unzip_path Path to unzipped plugin.
	 *
	 * @return array|WP_Error
	 */
	private function get_plugin_info( string $unzip_path ) {

		$plugin_folder = $this->get_plugin_folder_name( $unzip_path );
		$plugin_dir    = $unzip_path . $plugin_folder . '/';
		$plugin_file   = $plugin_dir . $plugin_folder . '.php';

		// Check if the plugin is WPForms Lite.
		if ( $plugin_folder === 'wpforms-lite' ) {
			$plugin_file = $plugin_dir . 'wpforms.php';
		}

		if ( file_exists( $plugin_file ) ) {
			$plugin_data = get_plugin_data( $plugin_file );

			return [
				'name'    => $plugin_data['Name'],
				'slug'    => $plugin_folder,
				'version' => $plugin_data['Version'],
				'wp'      => $plugin_data['RequiresWP'],
				'php'     => $plugin_data['RequiresPHP'],
			];
		}

		return new WP_Error( 'plugin_file_not_found', 'Main plugin file not found.' );
	}

	/**
	 * Get the plugin folder name.
	 *
	 * @since 0.32
	 *
	 * @param string $unzip_path Path to the unzipped directory.
	 *
	 * @return string|WP_Error
	 */
	private function get_plugin_folder_name( string $unzip_path ) {

		$directories = array_filter( glob( $unzip_path . '*' ), 'is_dir' );

		if ( ! empty( $directories ) ) {
			return basename( $directories[0] );
		}

		return new WP_Error( 'folder_not_found', 'Plugin folder not found.' );
	}

	/**
	 * Get notices.
	 *
	 * @since 0.32
	 *
	 * @return string
	 */
	public function get_notices(): string {

		ob_start();

		foreach ( $this->errors as $error ) {
			?>
			<div class="notice notice-error">
				<p><?php echo esc_html( $error ); ?></p>
			</div>
			<?php
		}

		foreach ( $this->successes as $success ) {
			?>
			<div class="notice notice-success">
				<p><?php echo esc_html( $success ); ?></p>
			</div>
			<?php
		}

		return ob_get_clean();
	}

	/**
	 * Delete unzipped directory.
	 *
	 * @since 0.32
	 *
	 * @param string $unzip_path Path to the unzipped directory.
	 */
	private function delete_unzipped_dir( string $unzip_path ): void {

		global $wp_filesystem;

		$wp_filesystem->delete( $unzip_path, true );
	}

	/**
	 * Get current WPForms Lite version.
	 *
	 * @since 0.32
	 *
	 * @return string
	 */
	private function get_current_lite_version(): string {

		$plugins = get_plugins();

		$version = '';

		foreach ( $plugins as $plugin_path => $plugin ) {
			$slug = explode( '/', $plugin_path )[0];

			if ( $slug !== 'wpforms-lite' ) {
				continue;
			}

			$version = $plugin['Version'];
		}

		return $version;
	}

	/**
	 * Change core cache data when the license is not valid.
	 *
	 * @since 0.41
	 *
	 * @param mixed $value Value of site transient.
	 *
	 * @return mixed
	 */
	public function change_core_cache( $value ) {

		$upgrades = $this->get_upgrades();

		if ( empty( $upgrades ) ||
			! is_object( $value ) ||
			! class_exists( PluginList::class ) ||
			( new PluginList() )->is_valid_license()
		) {
			return $value;
		}

		// Process core plugin updates.
		foreach ( $upgrades as $upgrade_data ) {
			if ( ! isset( $upgrade_data['slug'] ) || $upgrade_data['slug'] !== 'wpforms' ) {
				continue;
			}

			// Replace core plugin data with simulated data.
			if ( isset( $value->response['wpforms/wpforms.php'] ) ) {
				$core_data                              = (array) $value->response['wpforms/wpforms.php'];
				$core_data['new_version']               = $upgrade_data['version'];
				$core_data['package']                   = $upgrade_data['file'];
				$value->response['wpforms/wpforms.php'] = (object) $core_data;
			} elseif ( isset( $value->no_update['wpforms/wpforms.php'] ) ) {
				$core_data                              = (array) $value->no_update['wpforms/wpforms.php'];
				$core_data['new_version']               = $upgrade_data['version'];
				$core_data['package']                   = $upgrade_data['file'];
				$value->response['wpforms/wpforms.php'] = (object) $core_data;

				unset( $value->no_update['wpforms/wpforms.php'] );
			}
		}

		return $value;
	}
}
