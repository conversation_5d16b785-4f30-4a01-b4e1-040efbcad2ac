// WPForms Classic styles.
//
// Read-only field.
//
// @since {VERSION}

.wpforms-field {

	&.wpf-disable-field, // Compatibility for snippet from https://wpforms.com/developers/disable-a-form-field-to-prevent-user-input/.
	&.wpforms-field-readonly {
		select,
		textarea,
		button,
		input[type=text],
		input[type=number],
		input[type=email],
		input[type=url],
		input[type=tel],
		input[type=password],
		input[type=radio],
		input[type=checkbox],
		input[type=range],
		input[type=file],
		input.wpforms-field-date-time-date,
		.wpforms-uploader,
		.choices,
		.wpforms-image-choices-image,
		.wpforms-field-rating-wrapper,
		.wpforms-field-password-input-icon,
		.wpforms-disclaimer-description,
		.mce-tinymce,
		.iti__selected-country,
		.StripeElement,
		.wpforms-stripe-element,
		.wpforms-field-square-cardnumber,
		.wpforms-square-cardnumber,
		.wpforms-geolocation-map,
		.wpforms-signature-wrap,
		.wpforms-paypal-commerce-card-fields,
		&.wpforms-field-net_promoter_score table.modern > tbody > tr > td,
		.wpforms-camera-link {
			cursor: default !important;
			opacity: 0.35 !important;
			pointer-events: none !important;
		}

		input[type=radio],
		input[type=checkbox] {
			cursor: default !important;
			pointer-events: none !important;
		}

		label {
			pointer-events: none !important;
		}

		.iti__country-container {
			cursor: default !important;

			button {
				cursor: default !important;
			}
		}
	}
}
