var WPForms=window.WPForms||{};WPForms.Admin=WPForms.Admin||{},WPForms.Admin.Builder=WPForms.Admin.Builder||{},WPForms.Admin.Builder.FieldFileUploadCamera=WPForms.Admin.Builder.FieldFileUploadCamera||((e,s)=>{let r={init(){s(r.ready)},ready(){r.bindEvents(),r.initializeAspectRatioOptions()},bindEvents(){s(e).on("change",".wpforms-file-upload-camera-enabled-toggle",r.toggleCameraOptions),s(e).on("change",".wpforms-file-upload-camera-format-select",r.toggleTimeLimit),s(e).on("change",".wpforms-file-upload-camera-aspect-ratio-select",r.toggleCustomRatio),s(e).on("change",".wpforms-camera-style select",r.updateCameraPreview),s(e).on("input",".wpforms-field-option-row-button_link_text input",r.updateCameraButtonLinkText),s(e).on("blur",".wpforms-file-upload-camera-time-limit-minutes-input",r.validateTimeLimitMinutes),s(e).on("blur",".wpforms-file-upload-camera-time-limit-seconds-input",r.validateTimeLimitSeconds),s(e).on("blur",".wpforms-file-upload-camera-ratio-width-input",r.validateRatioWidth),s(e).on("blur",".wpforms-file-upload-camera-ratio-height-input",r.validateRatioHeight),s(e).on("wpformsBeforeSave",r.validateCameraFields)},initializeAspectRatioOptions(){s(".wpforms-file-upload-camera-aspect-ratio-no-freeform").each(function(){s(this).find('option[value="freeform"]').hide()})},toggleCameraOptions(){var e=s(this),i=e.closest(".wpforms-field-option"),e=e.is(":checked"),a=i.find(".wpforms-file-upload-camera-format"),t=i.find(".wpforms-file-upload-camera-aspect-ratio"),o=i.find(".wpforms-file-upload-camera-time-limit");e?(a.removeClass("wpforms-hidden"),t.removeClass("wpforms-hidden"),r.toggleTimeLimit.call(i.find(".wpforms-file-upload-camera-format-select")[0])):(a.addClass("wpforms-hidden"),t.addClass("wpforms-hidden"),o.addClass("wpforms-hidden"))},toggleTimeLimit(){var e=s(this),i=e.closest(".wpforms-field-option"),a=i.find(".wpforms-file-upload-camera-time-limit"),i=i.find(".wpforms-file-upload-camera-aspect-ratio-select");"video"===e.val()?(a.removeClass("wpforms-hidden"),i.addClass("wpforms-file-upload-camera-aspect-ratio-no-freeform"),i.find('option[value="freeform"]').hide(),"freeform"===i.val()&&i.val("original")):(a.addClass("wpforms-hidden"),i.removeClass("wpforms-file-upload-camera-aspect-ratio-no-freeform"),i.find('option[value="freeform"]').show())},toggleCustomRatio(){var e,i,a=s(this),t=a.closest(".wpforms-field-option"),o=t.find(".wpforms-file-upload-camera-custom-ratio"),r=a.val(),l=a.data("previous-value");"custom"===r?(o.removeClass("wpforms-hidden"),l&&l.includes(":")&&([l,e]=l.split(":"),i=t.find(".wpforms-file-upload-camera-ratio-width-input"),t=t.find(".wpforms-file-upload-camera-ratio-height-input"),i.val(l),t.val(e))):o.addClass("wpforms-hidden"),a.data("previous-value",r)},validateTimeLimitMinutes(){var e=s(this),i=e.closest(".wpforms-field-option").find(".wpforms-file-upload-camera-time-limit-seconds-input");let a=parseInt(e.val(),10);var t=parseInt(i.val(),10);(a=isNaN(a)?0:a)<0&&(a=0),e.val(a),0!==a||!isNaN(t)&&0!==t||i.val(1)},validateTimeLimitSeconds(){var e=s(this),i=e.closest(".wpforms-field-option").find(".wpforms-file-upload-camera-time-limit-minutes-input");let a=parseInt(e.val(),10);i=parseInt(i.val(),10);(a=isNaN(a)?0:a)<0?a=0:59<a&&(a=59),e.val(a),0!==a||!isNaN(i)&&0!==i||e.val(1)},validateRatioWidth(){var e=s(this);let i=parseInt(e.val(),10);(i=isNaN(i)?1:i)<1&&(i=1),e.val(i)},validateRatioHeight(){var e=s(this);let i=parseInt(e.val(),10);(i=isNaN(i)?1:i)<1&&(i=1),e.val(i)},updateCameraPreview(){var e=s(this),i=e.closest(".wpforms-field-option"),a=i.data("field-id"),e=e.val(),a=s("#wpforms-field-"+a),t=a.find(".wpforms-camera-button"),a=a.find(".wpforms-camera-link"),t=("button"===e?(t.removeClass("wpforms-hidden"),a.addClass("wpforms-hidden")):(t.addClass("wpforms-hidden"),a.removeClass("wpforms-hidden")),i.find(".wpforms-field-option-row-button_link_text")),a=t.find("label"),i=a.find("i.fa-question-circle-o");"button"===e?(i.attr("title",wpforms_camera_builder.button_link_text_tooltip),a.html(wpforms_camera_builder.button_link_text_label+i.prop("outerHTML"))):(i.attr("title",wpforms_camera_builder.link_text_tooltip),a.html(wpforms_camera_builder.link_text_label+i.prop("outerHTML"))),"undefined"!=typeof wpf&&"function"==typeof wpf.initTooltips&&wpf.restoreTooltips(t)},updateCameraButtonLinkText(){var e=s(this),i=e.closest(".wpforms-field-option").data("field-id"),e=e.val(),i=s("#wpforms-field-"+i),a=i.find(".wpforms-camera-button"),i=i.find(".wpforms-camera-link"),t=a.find("svg");a.html(t).append(" "+e),i.text(e)},validateCameraFields(l){s(".wpforms-field-option").filter(function(){return 0<s(this).find(".wpforms-field-option-row-button_link_text").length}).each(function(){var e=s(this),i=e.find(".wpforms-camera-style select"),a=e.find(".wpforms-field-option-row-button_link_text input"),t=e.find(".wpforms-file-upload-camera-time-limit-minutes-input"),o=e.find(".wpforms-file-upload-camera-time-limit-seconds-input"),i=i.val(),a=a.val().trim(),t=parseInt(t.val(),10)||0,r=parseInt(o.val(),10)||0;if("link"===i&&""===a)return s.confirm({title:wpforms_camera_builder.error_title,content:wpforms_camera_builder.error_message,type:"red",typeAnimated:!0,icon:"fa fa-exclamation-triangle",buttons:{ok:{text:wpforms_camera_builder.error_ok,btnClass:"btn-confirm",action:()=>{}}}}),l.preventDefault(),!1;"video"===e.find(".wpforms-file-upload-camera-format select").val()&&0===t&&0===r&&o.val(1)})}};return r.init(),r})(document,(window,jQuery));