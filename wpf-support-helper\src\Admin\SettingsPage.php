<?php

namespace WPForms\SupportHelper\Admin;

use WPForms\Admin\Tools\Views\View;

/**
 * Settings page for ModSecurity detection interface.
 *
 * @since {VERSION}
 */
class SettingsPage extends View {

	/**
	 * View slug.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	protected $slug = 'support-helper';

	/**
	 * Nonce key for refreshing the support helper.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private const NONCE_KEY = 'wpf-support-helper-refresh';

	/**
	 * Data handler instance.
	 *
	 * @since {VERSION}
	 *
	 * @var ModSecurityDataHandler
	 */
	private $data_handler;

	/**
	 * Initialize the settings page.
	 *
	 * @since {VERSION}
	 *
	 * @param ModSecurityDataHandler $data_handler Data handler instance.
	 */
	public function __construct( ModSecurityDataHandler $data_handler ) {

		$this->data_handler = $data_handler;
	}

	/**
	 * Init view.
	 *
	 * @since {VERSION}
	 */
	public function init(): void {

		$this->handle_refresh();
		$this->hooks();
	}

	/**
	 * Hook into WordPress.
	 *
	 * @since {VERSION}
	 */
	private function hooks(): void {

		add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_styles' ] );
	}

	/**
	 * Get view label.
	 *
	 * @since {VERSION}
	 *
	 * @return string
	 */
	public function get_label(): string {

		return esc_html__( 'Support Helper', 'wpf-support-helper' );
	}

	/**
	 * Checking user capability to view.
	 *
	 * @since {VERSION}
	 *
	 * @return bool
	 */
	public function check_capability(): bool {

		return current_user_can( 'manage_options' );
	}

	/**
	 * Handles a force refresh request by clearing cached data and redirecting the user.
	 *
	 * @since {VERSION}
	 */
	public function handle_refresh(): void {

		// Handle force refresh request.
		if ( empty( $_GET['force_refresh'] ) ||
			// phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized
			! wp_verify_nonce( wp_unslash( $_GET['_wpnonce'] ?? '' ), self::NONCE_KEY )
		) {
			return;
		}

		// Force refresh the detection data.
		$this->data_handler->clear_cache();

		// Redirect to the same page without the force_refresh parameter.
		$redirect_url = $this->get_link();

		wp_safe_redirect( $redirect_url );
		exit;
	}

	/**
	 * Enqueue admin styles.
	 *
	 * @since {VERSION}
	 */
	public function enqueue_styles(): void {

		$min = wpforms_get_min_suffix();

		wp_enqueue_style(
			'wpf-support-helper-admin',
			WPF_SUPPORT_HELPER_PLUGIN_URL . "assets/css/modsecurity-interface{$min}.css",
			[],
			WPF_SUPPORT_HELPER_VERSION
		);
	}

	/**
	 * Display view content.
	 *
	 * @since {VERSION}
	 */
	public function display(): void {

		// Get detection data.
		$detection_data = $this->data_handler->get_detection_data();

		?>
		<div class="tools wpforms-settings-row-support-helper">
			<div class="wpf-support-helper-content">
				<?php $this->render_detection_summary( $detection_data ); ?>
				<?php $this->render_detection_methods( $detection_data ); ?>
			</div>
		</div>
		<?php
	}

	/**
	 * Render detection summary section.
	 *
	 * @since {VERSION}
	 *
	 * @param array $detection_data Detection data.
	 */
	private function render_detection_summary( array $detection_data ): void {

		?>
		<div class="wpf-support-helper-section">
			<?php $this->render_summary_header(); ?>
			<?php $this->render_summary_content( $detection_data ); ?>
			<?php $this->render_summary_status( $detection_data ); ?>
			<?php $this->render_summary_error( $detection_data ); ?>
		</div>
		<?php
	}

	/**
	 * Render summary header with refresh button.
	 *
	 * @since {VERSION}
	 */
	private function render_summary_header(): void {

		?>
		<div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
			<h2 style="margin: 0;"><?php esc_html_e( 'ModSecurity Detection Status', 'wpf-support-helper' ); ?></h2>
			<form method="get" action="<?php echo esc_url( admin_url( 'admin.php' ) ); ?>" style="margin: 0;">
				<?php wp_nonce_field( self::NONCE_KEY ); ?>
				<input type="hidden" name="page" value="wpforms-tools" />
				<input type="hidden" name="view" value="support-helper" />
				<input type="hidden" name="force_refresh" value="1" />
				<input type="submit" value="<?php esc_attr_e( 'Refresh', 'wpf-support-helper' ); ?>" class="wpforms-btn wpforms-btn-md wpforms-btn-orange" />
			</form>
		</div>
		<?php
	}

	/**
	 * Render summary content with statistics.
	 *
	 * @since {VERSION}
	 *
	 * @param array $detection_data Detection data.
	 */
	private function render_summary_content( array $detection_data ): void {

		$summary = $detection_data['summary'] ?? [];

		?>
		<div class="wpf-detection-summary">
			<h3 class="summary-title"><?php echo esc_html( $summary['detection_status'] ?? __( 'Unknown Status', 'wpf-support-helper' ) ); ?></h3>
			<?php $this->render_summary_stats( $summary ); ?>
		</div>
		<?php
	}

	/**
	 * Render summary statistics.
	 *
	 * @since {VERSION}
	 *
	 * @param array $summary Summary data.
	 */
	private function render_summary_stats( array $summary ): void {

		?>
		<div class="summary-stats">
			<div class="stat-item">
				<span class="stat-number"><?php echo esc_html( $summary['total_methods'] ?? 0 ); ?></span>
				<span class="stat-label"><?php esc_html_e( 'Total Methods', 'wpf-support-helper' ); ?></span>
			</div>
			<div class="stat-item">
				<span class="stat-number"><?php echo esc_html( $summary['successful_methods'] ?? 0 ); ?></span>
				<span class="stat-label"><?php esc_html_e( 'Successful', 'wpf-support-helper' ); ?></span>
			</div>
			<div class="stat-item">
				<span class="stat-number"><?php echo esc_html( $summary['failed_methods'] ?? 0 ); ?></span>
				<span class="stat-label"><?php esc_html_e( 'Failed', 'wpf-support-helper' ); ?></span>
			</div>
			<div class="stat-item">
				<span class="stat-number"><?php echo esc_html( $summary['skipped_methods'] ?? 0 ); ?></span>
				<span class="stat-label"><?php esc_html_e( 'Skipped', 'wpf-support-helper' ); ?></span>
			</div>
		</div>
		<?php
	}

	/**
	 * Render summary status badge and timestamp.
	 *
	 * @since {VERSION}
	 *
	 * @param array $detection_data Detection data.
	 */
	private function render_summary_status( array $detection_data ): void {

		$status_class = $detection_data['detected'] ? 'status-success' : 'status-failed';

		?>
		<p>
			<span class="wpf-status-badge <?php echo esc_attr( $status_class ); ?>">
				<?php echo $detection_data['detected'] ? esc_html__( 'Detected', 'wpf-support-helper' ) : esc_html__( 'Not Detected', 'wpf-support-helper' ); ?>
			</span>
			<?php
			/* translators: %s - Detection timestamp. */
			printf( esc_html__( 'Last checked: %s', 'wpf-support-helper' ), esc_html( $detection_data['timestamp'] ?? __( 'Unknown', 'wpf-support-helper' ) ) );
			?>
		</p>
		<?php
	}

	/**
	 * Render error message if present.
	 *
	 * @since {VERSION}
	 *
	 * @param array $detection_data Detection data.
	 */
	private function render_summary_error( array $detection_data ): void {

		if ( empty( $detection_data['error_message'] ) ) {
			return;
		}

		?>
		<div class="wpf-error-message">
			<div class="error-title"><?php esc_html_e( 'Error', 'wpf-support-helper' ); ?></div>
			<?php echo esc_html( $detection_data['error_message'] ); ?>
		</div>
		<?php
	}

	/**
	 * Render detection methods section.
	 *
	 * @since {VERSION}
	 *
	 * @param array $detection_data Detection data.
	 */
	private function render_detection_methods( array $detection_data ): void {

		$successful_methods = $detection_data['successful_methods'] ?? [];
		$failed_methods     = $detection_data['failed_methods'] ?? [];
		$skipped_methods    = $detection_data['skipped_methods'] ?? [];

		?>
		<div class="wpf-support-helper-section">
			<h2><?php esc_html_e( 'Detection Methods', 'wpf-support-helper' ); ?></h2>
			<?php $this->render_successful_methods( $successful_methods ); ?>
			<?php $this->render_failed_methods( $failed_methods ); ?>
			<?php $this->render_skipped_methods( $skipped_methods ); ?>
			<?php $this->render_empty_methods_message( $successful_methods, $failed_methods, $skipped_methods ); ?>
		</div>
		<?php
	}

	/**
	 * Render successful detection methods.
	 *
	 * @since {VERSION}
	 *
	 * @param array $successful_methods Successful methods data.
	 */
	private function render_successful_methods( array $successful_methods ): void {

		if ( empty( $successful_methods ) ) {
			return;
		}

		?>
		<h3><?php esc_html_e( 'Successful Methods', 'wpf-support-helper' ); ?></h3>
		<div class="wpf-detection-methods">
			<?php foreach ( $successful_methods as $index => $method ) : ?>
				<?php $this->render_detection_method( $method, 'success', $index ); ?>
			<?php endforeach; ?>
		</div>
		<?php
	}

	/**
	 * Render failed detection methods.
	 *
	 * @since {VERSION}
	 *
	 * @param array $failed_methods Failed methods data.
	 */
	private function render_failed_methods( array $failed_methods ): void {

		if ( empty( $failed_methods ) ) {
			return;
		}

		?>
		<h3><?php esc_html_e( 'Failed Methods', 'wpf-support-helper' ); ?></h3>
		<div class="wpf-detection-methods">
			<?php foreach ( $failed_methods as $index => $method ) : ?>
				<?php $this->render_detection_method( $method, 'failed', $index ); ?>
			<?php endforeach; ?>
		</div>
		<?php
	}

	/**
	 * Render skipped detection methods.
	 *
	 * @since {VERSION}
	 *
	 * @param array $skipped_methods Skipped methods data.
	 */
	private function render_skipped_methods( array $skipped_methods ): void {

		if ( empty( $skipped_methods ) ) {
			return;
		}

		?>
		<h3><?php esc_html_e( 'Skipped Methods', 'wpf-support-helper' ); ?></h3>
		<div class="wpf-detection-methods">
			<?php foreach ( $skipped_methods as $index => $method ) : ?>
				<?php $this->render_detection_method( $method, 'skipped', $index ); ?>
			<?php endforeach; ?>
		</div>
		<?php
	}

	/**
	 * Render empty methods message if no methods are available.
	 *
	 * @since {VERSION}
	 *
	 * @param array $successful_methods Successful methods data.
	 * @param array $failed_methods     Failed methods data.
	 * @param array $skipped_methods    Skipped methods data.
	 */
	private function render_empty_methods_message( array $successful_methods, array $failed_methods, array $skipped_methods ): void {

		if ( ! empty( $successful_methods ) || ! empty( $failed_methods ) || ! empty( $skipped_methods ) ) {
			return;
		}

		?>
		<p><?php esc_html_e( 'No detection methods available.', 'wpf-support-helper' ); ?></p>
		<?php
	}

	/**
	 * Render individual detection method.
	 *
	 * @since {VERSION}
	 *
	 * @param array  $method Method data.
	 * @param string $status Method status (success, failed, skipped).
	 * @param int    $index  Method index for unique IDs.
	 */
	private function render_detection_method( array $method, string $status, int $index ): void {

		$method_name         = $method['name'] ?? __( 'Unknown Method', 'wpf-support-helper' );
		$method_reason       = $method['reason'] ?? '';
		$additional_info     = $method['additional_info'] ?? [];
		$has_additional_info = ! empty( $additional_info );

		if ( $has_additional_info ) {
			$this->render_expandable_method( $method_name, $method_reason, $status, $index, $additional_info );
		} else {
			$this->render_simple_method( $method_name, $method_reason, $status );
		}
	}

	/**
	 * Render expandable detection method with additional info.
	 *
	 * @since {VERSION}
	 *
	 * @param string $method_name     Method name.
	 * @param string $method_reason   Method reason.
	 * @param string $status          Method status.
	 * @param int    $index           Method index.
	 * @param array  $additional_info Additional information.
	 */
	private function render_expandable_method( string $method_name, string $method_reason, string $status, int $index, array $additional_info ): void {

		$unique_id = "method-{$status}-{$index}";

		?>
		<label class="method-item expandable" for="<?php echo esc_attr( $unique_id ); ?>">
			<input type="checkbox" id="<?php echo esc_attr( $unique_id ); ?>" class="method-toggle" />
			<div class="method-header">
				<?php $this->render_status_badge( $status ); ?>
				<?php $this->render_method_content( $method_name, $method_reason ); ?>
				<svg class="expand-arrow" width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M3 4.5L6 7.5L9 4.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
			</div>
			<div class="method-additional-info">
				<?php // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r ?>
				<textarea readonly class="additional-info-textarea"><?php echo esc_textarea( print_r( $additional_info, true ) ); ?></textarea>
			</div>
		</label>
		<?php
	}

	/**
	 * Render simple detection method without additional info.
	 *
	 * @since {VERSION}
	 *
	 * @param string $method_name   Method name.
	 * @param string $method_reason Method reason.
	 * @param string $status        Method status.
	 */
	private function render_simple_method( string $method_name, string $method_reason, string $status ): void {

		?>
		<div class="method-item">
			<?php $this->render_status_badge( $status ); ?>
			<?php $this->render_method_content( $method_name, $method_reason ); ?>
		</div>
		<?php
	}

	/**
	 * Render status badge for detection method.
	 *
	 * @since {VERSION}
	 *
	 * @param string $status Method status.
	 */
	private function render_status_badge( string $status ): void {

		?>
		<span class="wpf-status-badge status-<?php echo esc_attr( $status ); ?>">
			<?php echo esc_html( $this->get_status_label( $status ) ); ?>
		</span>
		<?php
	}

	/**
	 * Render method name and reason content.
	 *
	 * @since {VERSION}
	 *
	 * @param string $method_name   Method name.
	 * @param string $method_reason Method reason.
	 */
	private function render_method_content( string $method_name, string $method_reason ): void {

		?>
		<span class="method-name"><?php echo esc_html( $method_name ); ?></span>
		<?php if ( ! empty( $method_reason ) ) : ?>
			<span class="method-reason"><?php echo esc_html( $method_reason ); ?></span>
		<?php endif; ?>
		<?php
	}

	/**
	 * Get localized status label.
	 *
	 * @since {VERSION}
	 *
	 * @param string $status Method status.
	 *
	 * @return string Localized status label.
	 */
	private function get_status_label( string $status ): string {

		switch ( $status ) {
			case 'success':
				$status = __( 'Success', 'wpf-support-helper' );
				break;

			case 'failed':
				$status = __( 'Failed', 'wpf-support-helper' );
				break;

			case 'skipped':
				$status = __( 'Skipped', 'wpf-support-helper' );
				break;

			default:
				$status = __( 'Unknown', 'wpf-support-helper' );
				break;
		}

		return $status;
	}
}
