<?php

namespace WPForms\DevTools;

/**
 * Command palette.
 *
 * @since 0.19
 */
class CommandPalette {

	/**
	 * Assets handle.
	 *
	 * @since 0.19
	 *
	 * @var string Handle.
	 */
	private const HANDLE = 'wpf-command-palette';

	/**
	 * Constructor.
	 *
	 * @since 0.19
	 */
	public function __construct() {

		if ( ! is_user_logged_in() ) {
			return;
		}

		/**
		 * Filter to enable/disable the Command Palette.
		 *
		 * @since 0.40
		 *
		 * @param bool $is_enabled False By default.
		 */
		if ( ! apply_filters( 'wpf_enable_command_palette', false ) ) { // phpcs:ignore WPForms.PHP.ValidateHooks.InvalidHookName
			return;
		}

		$this->hooks();
	}

	/**
	 * Instantiate class.
	 *
	 * @since 0.19
	 */
	public static function instance(): void {

		new self();
	}

	/**
	 * Hook into WordPress lifecycle.
	 *
	 * @since 0.19
	 */
	public function hooks(): void {

		add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_assets' ] );
		add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_assets' ] );

		add_filter( 'script_loader_tag', [ $this, 'set_scripts_type_attribute' ], 10, 3 );

		add_action( 'admin_footer', [ $this, 'output' ] );
		add_action( 'wp_footer', [ $this, 'output' ] );

		add_action( 'wp_ajax_wpf_get_last_entities', [ $this, 'ajax_get_last_entities' ] );
		add_action( 'wp_ajax_nopriv_wpf_get_last_entities', [ $this, 'ajax_get_last_entities' ] );
	}

	/**
	 * Enqueue assets.
	 *
	 * @since 0.19
	 */
	public function enqueue_assets(): void {

		wp_enqueue_script(
			'wpf-ninja-keys',
			WPFORMS_DEV_TOOLS_URL . '/assets/js/vendor/ninja-keys.bundled.js',
			[],
			WPFORMS_DEV_TOOLS_VERSION,
			true
		);

		wp_enqueue_style(
			self::HANDLE,
			WPFORMS_DEV_TOOLS_URL . '/assets/css/command-palette.css',
			[],
			WPFORMS_DEV_TOOLS_VERSION
		);

		wp_enqueue_script(
			self::HANDLE,
			WPFORMS_DEV_TOOLS_URL . '/assets/js/command-palette.js',
			[
				'jquery',
				'wpf-ninja-keys',
			],
			WPFORMS_DEV_TOOLS_VERSION,
			true
		);

		wp_localize_script(
			self::HANDLE,
			str_replace( '-', '_', self::HANDLE ),
			$this->get_data()
		);
	}

	/**
	 * Load script as module.
	 *
	 * @since        0.19
	 *
	 * @param string|mixed $tag    HTML script tag.
	 * @param string       $handle Registered script handle.
	 * @param string       $src    Source file URL.
	 *
	 * @return string
	 * @noinspection PhpUnusedParameterInspection
	 */
	public function set_scripts_type_attribute( $tag, string $handle, string $src ): string { // phpcs:ignore Generic.CodeAnalysis.UnusedFunctionParameter.FoundAfterLastUsed

		$tag = (string) $tag;

		if ( $handle !== 'wpf-ninja-keys' ) {
			return $tag;
		}

		return str_replace( '<script', '<script type="module"', $tag );
	}

	/**
	 * Get data for JS.
	 *
	 * @since 0.19
	 */
	private function get_data(): array {

		$wpf_utils = get_option( 'wpf-utils', [] );

		return [
			'greeting'                => 'This is the way.',
			'admin_url'               => rtrim( admin_url(), '/' ),
			'site_url'                => rtrim( site_url(), '/' ),
			'is_pro'                  => wpforms()->is_pro(),
			'hide_debug'              => ! empty( $wpf_utils['hide-debug-data'] ),
			'forms'                   => $this->get_forms(),
			'is_active_coupons_addon' => function_exists( 'wpforms_is_addon_initialized' ) && wpforms_is_addon_initialized( 'coupons' ),
			'coupons'                 => $this->get_coupons(),
			'last_payment'            => $this->get_last_payment(),
			'last_entry'              => $this->get_last_entry(),
		];
	}

	/**
	 * Output.
	 *
	 * @since 0.19
	 */
	public function output(): void {

		echo '<ninja-keys placeholder="I have spoken..." openHotkey="ctrl+\"> </ninja-keys>'; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
	}

	/**
	 * Get all forms.
	 *
	 * @since 0.19
	 *
	 * @return array
	 */
	private function get_forms(): array {

		$forms = Main::wpforms_obj( 'form' )->get();
		$data  = [];

		if ( ! $forms ) {
			return $data;
		}

		foreach ( $forms as $form ) {
			$data[] = [
				'id'    => $form->ID,
				'title' => $form->post_title,
			];
		}

		return $data;
	}

	/**
	 * Get all coupons.
	 *
	 * @since 0.19
	 *
	 * @return array
	 */
	private function get_coupons(): array {

		// Avoid Coupons addon duplication initialization on the builder page.
		if ( ! function_exists( 'wpforms_is_addon_initialized' ) || ! wpforms_is_addon_initialized( 'coupons' ) ) {
			return [];
		}

		$coupons = wpforms_coupons()->get( 'repository' )->get_coupons(
			[
				'limit'  => -1,
				'fields' => 'no_forms',
			]
		);

		$data = [];

		foreach ( $coupons as $coupon ) {
			$data[] = [
				'id'    => $coupon->get_id(),
				'title' => sprintf( '%1$s (%2$s)', $coupon->get_name(), $coupon->get_code() ),
			];
		}

		return $data;
	}

	/**
	 * Get last payment.
	 *
	 * @since 0.19
	 *
	 * @return int
	 */
	private function get_last_payment(): int {

		$payment = Main::wpforms_obj( 'payment' );

		if ( ! method_exists( $payment, 'get_payments' ) ) {
			return 0;
		}

		$payments_live = $payment->get_payments( [ 'number' => 1 ] );
		$payments_test = $payment->get_payments(
			[
				'number' => 1,
				'mode'   => 'test',
			]
		);

		$live_id = empty( $payments_live[0] ) ? 0 : $payments_live[0]['id'];
		$test_id = empty( $payments_test[0] ) ? 0 : $payments_test[0]['id'];

		return max( $live_id, $test_id );
	}

	/**
	 * Get the last entry.
	 *
	 * @since 0.19
	 *
	 * @return int
	 */
	private function get_last_entry(): int {

		if ( ! wpforms()->is_pro() ) {
			return 0;
		}

		$entries = Main::wpforms_obj( 'entry' )->get_entries( [ 'number' => 1 ] );

		return $entries[0]->entry_id ?? 0;
	}

	/**
	 * Update last entities after AJAX submission.
	 *
	 * @since 0.19
	 */
	public function ajax_get_last_entities(): void {

		wp_send_json_success(
			[
				'last_payment' => $this->get_last_payment(),
				'last_entry'   => $this->get_last_entry(),
			]
		);
	}
}
