<?php

use <PERSON><PERSON><PERSON>\Plugin;

/**
 * SDK for theme developers.
 *
 * @since {VERSION}
 */
class WPFormsThemeDevSDK {

	/**
	 * WPForms Lite plugin basename.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	const PLUGIN_BASENAME = 'wpforms-lite/wpforms.php';

	/**
	 * WPForms Lite plugin file URL.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	const PLUGIN_FILE_URL = 'https://downloads.wordpress.org/plugin/wpforms-lite.zip';

	/**
	 * Constructor.
	 *
	 * @since {VERSION}
	 */
	public function __construct() {

		require_once ABSPATH . 'wp-admin/includes/plugin.php';
		require_once ABSPATH . 'wp-admin/includes/class-wp-upgrader-skin.php';
		require_once __DIR__ . '/wpforms-silent-upgrader-skin.php';
	}

	/**
	 * Do all the voodoo to install and activate the WPForms Lite plugin behind the scene.
	 * No user interaction is needed.
	 *
	 * @since {VERSION}
	 *
	 * @return bool|\WP_Error True on success.
	 */
	public function install() {

		// If already installed & activated - just return true.
		if ( $this->is_activated() ) {
			return true;
		}

		// If installed but not activated - just activate it.
		if ( ! $this->is_activated() && $this->is_installed() ) {
			$result = activate_plugin( self::PLUGIN_BASENAME, '', false, true );

			return $result instanceof \WP_Error ? $result : true;
		}

		// Includes necessary for Plugin_Upgrader and Plugin_Installer_Skin.
		require_once ABSPATH . 'wp-admin/includes/file.php';
		require_once ABSPATH . 'wp-admin/includes/misc.php';
		require_once ABSPATH . 'wp-admin/includes/class-wp-upgrader.php';

		$upgrader = new \Plugin_Upgrader( new WPFormsThemeDevSDK_SilentUpgraderSkin() );
		$result   = $upgrader->install( self::PLUGIN_FILE_URL );

		// Activate plugin if it was installed successfully.
		if ( $result === true ) {
			$result = activate_plugin( self::PLUGIN_BASENAME, '', false, true );
		}

		return $result instanceof \WP_Error ? $result : true;
	}

	/**
	 * Check if WPForms Lite is installed.
	 *
	 * @since {VERSION}
	 *
	 * @return bool
	 */
	public function is_installed() {

		$plugins = get_plugins();

		return isset( $plugins[ self::PLUGIN_BASENAME ] );
	}

	/**
	 * Check if WPForms Lite is activated.
	 *
	 * @since {VERSION}
	 *
	 * @return bool
	 */
	public function is_activated() {

		return is_plugin_active( self::PLUGIN_BASENAME );
	}

	/**
	 * Create a form by given template.
	 *
	 * @since {VERSION}
	 *
	 * @param string $title    Form title.
	 * @param string $template Template slug.
	 *
	 * @return int|bool
	 */
	public function create_form( $title, $template ) {

		$wpforms = $this->load_wpforms();

		if ( ! $wpforms ) {
			return false;
		}

		return $wpforms->obj( 'form' )->add(
			$title,
			[],
			[
				'template' => $template,
				'builder'  => false,
			]
		);
	}

	/**
	 * Load and init WPForms Lite plugin.
	 *
	 * @since {VERSION}
	 *
	 * @return WPForms\WPForms|bool
	 */
	private function load_wpforms() {

		$plugin_file = WP_PLUGIN_DIR . '/' . self::PLUGIN_BASENAME;

		if ( ! file_exists( $plugin_file ) ) {
			return false;
		}

		require_once $plugin_file;

		add_filter( 'wpforms_admin_builder_templates_allow_load', '__return_true' );
		add_filter( 'wpforms_admin_builder_templatescache_allow_load', '__return_true' );
		add_filter( 'wpforms_admin_builder_templatesinglecache_allow_load', '__return_true' );

		$wpforms = wpforms();

		$wpforms->objects();
		$wpforms->register_bulk(
			[
				[
					'name' => 'Tasks\Meta',
					'id'   => 'tasks_meta',
					'hook' => false,
					'run'  => false,
				],
				[
					'name' => 'Tasks\Tasks',
					'id'   => 'tasks',
					'hook' => false,
				],
				[
					'name' => 'Admin\Builder\TemplatesCache',
					'id'   => 'builder_templates_cache',
					'hook' => false,
				],
				[
					'name' => 'Admin\Builder\TemplateSingleCache',
					'id'   => 'builder_template_single',
					'hook' => false,
				],
				[
					'name' => 'Admin\Builder\Templates',
					'id'   => 'builder_templates',
					'hook' => false,
				],
				[
					'name' => 'Access\Capabilities',
					'id'   => 'access',
					'hook' => false,
				],
			]
		);

		return $wpforms;
	}

	/**
	 * Add form to the page.
	 *
	 * Given placeholder string will be replaced with the needed block.
	 *
	 * @since {VERSION}
	 *
	 * @param string $form_id     Form ID.
	 * @param mixed  $page        Page ID or slug.
	 * @param mixed  $page_title  Page title.
	 * @param string $placeholder Is the string we're looking to replace with the form embed code.
	 * @param string $type        Is what type of the content block we're looking to add:
	 *                            - shortcode      WordPress shortcode,
	 *                            - block          Gutenberg block,
	 *                            - elementor      Elementor widget,
	 *                            - divi           Divi block,
	 *                            - visualcomposer WPBakery Visual Composer element.
	 */
	public function add_form_to_page( $form_id, $page, $page_title, $placeholder = '[wpforms]', $type = 'shortcode' ) {

		if ( ! function_exists( 'post_exists' ) ) {
			require_once ABSPATH . 'wp-admin/includes/post.php';
		}

		// Determine page ID.
		$post        = ! empty( $page ) ? get_post( $page ) : false;
		$page_id     = ! empty( $post ) ? $page : post_exists( $page_title );
		$is_new_page = false;

		// If page is not exist - create it.
		if ( empty( $page_id ) ) {

			$is_new_page = true;
			$page_id     = wp_insert_post(
				[
					'post_type'    => 'page',
					'post_title'   => $page_title,
					'post_content' => $placeholder,
					'post_status'  => 'publish',
					'post_author'  => get_current_user_id(),
					'post_slug'    => $page,
				]
			);
		}

		// Get the result post.
		$post         = get_post( $page_id );
		$post_content = $this->update_post_content( $post, $is_new_page, $form_id, $type, $placeholder );

		wp_update_post(
			[
				'ID'           => $page_id,
				'post_content' => $post_content,
			]
		);
	}

	/**
	 * Get updated `post_content` according to the type of content block.
	 *
	 * @since {VERSION}
	 *
	 * @param \WP_Post $post        Post object.
	 * @param bool     $is_new_page True if the page is just created..
	 * @param int      $form_id     Form ID.
	 * @param string   $type        Type of content block.
	 * @param string   $placeholder Placeholder.
	 *
	 * @return string Updated `post_content`.
	 */
	private function update_post_content( $post, $is_new_page, $form_id, $type, $placeholder ) { // phpcs:ignore Generic.Metrics.CyclomaticComplexity.TooHigh

		if ( ! $post instanceof \WP_Post ) {
			return '';
		}

		$post_content = $post->post_content;
		$form_id      = absint( $form_id );

		switch ( $type ) {

			// Gutenberg block.
			case 'block':
				$post_content = $this->update_post_content_gutenberg( $post, $is_new_page, $form_id, $placeholder );

				break;

			// Divi module.
			case 'divi':
				$post_content = $this->update_post_content_divi( $post, $is_new_page, $form_id, $placeholder );

				break;

			// WPBakery Visual Composer.
			case 'visualcomposer':
				$post_content = $this->update_post_content_visualcomposer( $post, $is_new_page, $form_id, $placeholder );

				break;

			// Elementor widget.
			case 'elementor':
				$this->update_post_content_elementor( $post->ID, $form_id, $placeholder );

				break;

			// Shortcode (by default).
			default:
				$post_content = str_replace(
					$placeholder,
					'[wpforms id="' . $form_id . '" title="false"]',
					$post_content
				);
		}

		return $post_content;
	}

	/**
	 * Update Gutenberg page `post_content`.
	 *
	 * @since {VERSION}
	 *
	 * @param \WP_Post $post        Post object.
	 * @param bool     $is_new_page True if the page is just created.
	 * @param int      $form_id     Form ID.
	 * @param string   $placeholder Placeholder.
	 *
	 * @return string
	 */
	private function update_post_content_gutenberg( $post, $is_new_page, $form_id, $placeholder ) {

		$post_content  = $post->post_content;
		$block_content = '<!-- wp:wpforms/form-selector {"formId":"' . $form_id . '"} /-->';

		if ( $is_new_page ) {
			$post_content = $block_content;
		} else {
			// Replace Paragraph text block with WPForms block.
			$esc_placeholder = preg_quote( $placeholder, '|' );
			$post_content    = preg_replace( // phpcs:ignore WPForms.Comments.EmptyLineAfterAssigmentVariables.AddEmptyLine
				"|<!-- wp:paragraph -->\n*\r*.*{$esc_placeholder}.*\n*\r*<!-- /wp:paragraph -->|m",
				$block_content,
				$post_content
			);
		}

		return $post_content;
	}

	/**
	 * Update Divi page `post_content`.
	 *
	 * @since {VERSION}
	 *
	 * @param \WP_Post $post        Post object.
	 * @param bool     $is_new_page True if the page is just created.
	 * @param int      $form_id     Form ID.
	 * @param string   $placeholder Placeholder.
	 *
	 * @return string
	 */
	private function update_post_content_divi( $post, $is_new_page, $form_id, $placeholder ) {

		$post_content = $post->post_content;

		if ( ! function_exists( 'et_core_init' ) ) {
			return $post_content;
		}

		$is_page_divi_enabled = get_post_meta( $post->ID, '_et_pb_use_builder', true ) === 'on';

		if ( ! $is_page_divi_enabled ) {
			update_metadata( 'post', $post->ID, '_et_pb_use_builder', 'on' );
		}

		$block_content = '[wpforms_selector _module_preset="default" form_id="' . $form_id . '" admin_label="WPForms"][/wpforms_selector]';

		if ( $is_new_page ) {
			$post_content = '[et_pb_section][et_pb_row][et_pb_column]' . $block_content . '[/et_pb_column][/et_pb_row][/et_pb_section]';
		} else {
			// Replace Text module with WPForms module.
			$esc_placeholder = preg_quote( $placeholder, '|' );
			$post_content    = preg_replace( // phpcs:ignore WPForms.Comments.EmptyLineAfterAssigmentVariables.AddEmptyLine
				"|\[et_pb_text .*].*\n*\r*.*{$esc_placeholder}.*\n*\r*.*\[/et_pb_text]|m",
				$block_content,
				$post_content
			);
		}

		return $post_content;
	}

	/**
	 * Update WPBakery Visual Composer page `post_content`.
	 *
	 * @since {VERSION}
	 *
	 * @param \WP_Post $post        Post object.
	 * @param bool     $is_new_page True if the page is just created.
	 * @param int      $form_id     Form ID.
	 * @param string   $placeholder Placeholder.
	 *
	 * @return string
	 */
	private function update_post_content_visualcomposer( $post, $is_new_page, $form_id, $placeholder ) {

		$post_content = $post->post_content;

		global $vc_manager;

		if ( ! $vc_manager ) {
			return $post_content;
		}

		$is_page_vc_enabled = get_post_meta( $post->ID, '_wpb_vc_js_status', true ) === 'true';

		if ( ! $is_page_vc_enabled ) {
			update_metadata( 'post', $post->ID, '_wpb_vc_js_status', true );
		}

		$block_content = '[wpforms id="' . $form_id . '" title="false" description="false"]';

		if ( $is_new_page ) {
			$post_content = '[vc_row][vc_column]' . $block_content . '[/vc_column][/vc_row]';
		} else {
			// Replace Text Block element with WPForms element.
			$esc_placeholder = preg_quote( $placeholder, '|' );
			$post_content    = preg_replace( // phpcs:ignore WPForms.Comments.EmptyLineAfterAssigmentVariables.AddEmptyLine
				"|\[vc_column_text.*].*\n*\r*.*{$esc_placeholder}.*\n*\r*.*\[/vc_column_text]|m",
				$block_content,
				$post_content
			);
		}

		return $post_content;
	}

	/**
	 * Update Elementor page data.
	 *
	 * @since {VERSION}
	 *
	 * @param int    $page_id     Page ID.
	 * @param int    $form_id     Form ID.
	 * @param string $placeholder Placeholder.
	 */
	private function update_post_content_elementor( $page_id, $form_id, $placeholder ) {

		if ( ! class_exists( '\Elementor\Plugin' ) ) {
			return;
		}

		$doc = Plugin::$instance->documents->get( $page_id );

		if ( ! $doc->is_built_with_elementor() ) {
			$doc->set_is_built_with_elementor( true );
			$data = $doc->convert_to_elementor();
		} else {
			$data = $doc->get_elements_raw_data( null, true );
		}

		if ( empty( $data ) ) {
			return;
		}

		$esc_placeholder = preg_quote( $placeholder, '/' );
		$data            = $this->update_post_content_elementor_data( $data, $esc_placeholder, $form_id );
		$json_value      = wp_slash( wp_json_encode( $data ) );

		update_metadata( 'post', $page_id, '_elementor_data', $json_value );
	}

	/**
	 * Update Elementor widgets data.
	 *
	 * Replace text editor widget that contains `$placeholder` with the native WPForms widget.
	 *
	 * @since {VERSION}
	 *
	 * @param array  $data        Elementor elements array.
	 * @param string $placeholder Placeholder.
	 * @param int    $form_id     Form ID.
	 *
	 * @return array
	 */
	private function update_post_content_elementor_data( $data, $placeholder, $form_id ) {

		if ( empty( $data ) ) {
			return $data;
		}

		foreach ( $data as $el => $element ) {
			if ( ! empty( $element['elements'] ) && is_array( $element['elements'] ) ) {
				$data[ $el ]['elements'] = $this->update_post_content_elementor_data( $element['elements'], $placeholder, $form_id );
			} elseif (
				! empty( $element['widgetType'] )
				&& $element['widgetType'] === 'text-editor'
				&& ! empty( $element['settings']['editor'] )
				&& preg_match( "/{$placeholder}/m", $element['settings']['editor'] )
			) {
				$data[ $el ]['widgetType']          = 'wpforms';
				$data[ $el ]['settings']['form_id'] = (string) $form_id;

				unset( $data[ $el ]['settings']['editor'] );
			} elseif (
				! empty( $element['widgetType'] )
				&& $element['widgetType'] === 'shortcode'
				&& ! empty( $element['settings']['shortcode'] )
				&& preg_match( "/{$placeholder}/m", $element['settings']['shortcode'] )
			) {
				$data[ $el ]['widgetType']          = 'wpforms';
				$data[ $el ]['settings']['form_id'] = (string) $form_id;

				unset( $data[ $el ]['settings']['editor'] );
			}
		}

		return $data;
	}
}
