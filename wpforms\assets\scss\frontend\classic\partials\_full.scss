/* ==========================================================================
   Theme - standard form styling
   ========================================================================== */

.wpforms-clear:before {
	content: " ";
	display: table;
}

.wpforms-clear:after {
	clear: both;
	content: " ";
	display: table;
}

/* Basic Field properties
----------------------------------------------------------------------------- */

/* Field sizes - medium */
div.wpforms-container-full .wpforms-form input.wpforms-field-medium,
div.wpforms-container-full .wpforms-form select.wpforms-field-medium,
div.wpforms-container-full .wpforms-form .wpforms-field-row.wpforms-field-medium {
	max-width: 60%;
}

div.wpforms-container-full .wpforms-form textarea.wpforms-field-medium {
	height: 120px;
}

/* Field sizes - small */
div.wpforms-container-full .wpforms-form input.wpforms-field-small,
div.wpforms-container-full .wpforms-form select.wpforms-field-small,
div.wpforms-container-full .wpforms-form .wpforms-field-row.wpforms-field-small {
	max-width: 25%;
}

div.wpforms-container-full .wpforms-form textarea.wpforms-field-small {
	height: 70px;
}

/* Field sizes - large */
div.wpforms-container-full .wpforms-form input.wpforms-field-large,
div.wpforms-container-full .wpforms-form select.wpforms-field-large,
div.wpforms-container-full .wpforms-form .wpforms-field-row.wpforms-field-large {
	max-width: 100%;
}

div.wpforms-container-full .wpforms-form textarea.wpforms-field-large {
	height: 220px;
}

/* Field container */
div.wpforms-container-full .wpforms-form .wpforms-field {
	padding: 10px 0;
	clear: both;
}

/* Field Description */
div.wpforms-container-full .wpforms-form .wpforms-field-description,
div.wpforms-container-full .wpforms-form .wpforms-field-limit-text {
	font-size: 13px;
	line-height: 1.3;
	margin: 8px 0 0 0;
	word-break: break-word;
	word-wrap: break-word;
}

div.wpforms-container-full .wpforms-form .wpforms-field-description.wpforms-disclaimer-description {
	background-color: #fff;
	border: 1px solid #ddd;
	color: #444;
	padding: 15px 15px 0;
	margin-top: 15px;
	height: 125px;
	overflow-y: scroll;
	overflow-x: hidden;
	font-size: 12px
}

div.wpforms-container-full .wpforms-form .wpforms-field-description.wpforms-disclaimer-description p {
	color: #444;
	font-size: 12px;
	line-height: 18px;
	margin-bottom: 15px;
}

div.wpforms-container-full .wpforms-form .wpforms-field-description-before,
div.wpforms-container-full .wpforms-form .wpforms-field-description.before {
	margin: 0 0 8px 0;
}

/* Labels and sub-labels */
div.wpforms-container-full .wpforms-form .wpforms-field-label {
	display: block;
	font-weight: 700;
	font-size: 16px;
	float: none;
	line-height: 1.3;
	margin: 0 0 4px 0;
	padding: 0;
	word-break: break-word;
	word-wrap: break-word;
}

div.wpforms-container-full .wpforms-form .wpforms-field-sublabel {
	display: block;
	font-size: 13px;
	float: none;
	font-weight: 400;
	line-height: 1.3;
	margin: 4px 0 0;
	padding: 0;
	word-break: break-word;
    word-wrap: break-word;
}

div.wpforms-container-full .wpforms-form .wpforms-field-sublabel.before {
	margin: 0 0 4px 0;
}

div.wpforms-container-full .wpforms-form .wpforms-field-label-inline {
	display: inline;
	vertical-align: baseline;
	font-size: 16px;
	font-weight: 400;
	line-height: 1.3;
	word-break: break-word;
}

div.wpforms-container-full .wpforms-form .wpforms-field-label.wpforms-label-hide,
div.wpforms-container-full .wpforms-form .wpforms-field-sublabel.wpforms-sublabel-hide {
	position: absolute;
	clip: rect(0 0 0 0);
	width: 1px;
	height: 1px;
	margin: -1px;
	overflow: hidden;
}

div.wpforms-container-full .wpforms-form .wpforms-required-label {
	color: #ff0000;
	font-weight: 400;
}


/* Rows (multi-line fields: address, credit card, etc)
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form .wpforms-field-row {
	margin-bottom: 8px;
	position: relative;
}

div.wpforms-container-full .wpforms-form .wpforms-field .wpforms-field-row:last-of-type {
	margin-bottom: 0;
}

/* Clear each row */
div.wpforms-container-full .wpforms-form .wpforms-field-row:before {
	content: "";
	display: table;
}

div.wpforms-container-full .wpforms-form .wpforms-field-row:after {
	clear: both;
	content: "";
	display: table;
}

div.wpforms-container-full .wpforms-form .wpforms-field-address .wpforms-one-half:only-child {
	margin-left: 0;
}

/* Columns
----------------------------------------------------------------------------- */

/* User column classes (legacy).  */
div.wpforms-container-full .wpforms-form {

	@import "legacy-columns-base";
}

/* User list column classes  */
div.wpforms-container-full .wpforms-form .wpforms-checkbox-2-columns ul,
div.wpforms-container-full .wpforms-form .wpforms-multiplechoice-2-columns ul,
div.wpforms-container-full .wpforms-form .wpforms-list-2-columns ul,
div.wpforms-container-full .wpforms-form .wpforms-checkbox-3-columns ul,
div.wpforms-container-full .wpforms-form .wpforms-multiplechoice-3-columns ul,
div.wpforms-container-full .wpforms-form .wpforms-list-3-columns ul {
	display: -ms-flex;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-wrap: wrap;
	flex-wrap: wrap;
}

div.wpforms-container-full .wpforms-form .wpforms-checkbox-2-columns ul li,
div.wpforms-container-full .wpforms-form .wpforms-multiplechoice-2-columns ul li,
div.wpforms-container-full .wpforms-form .wpforms-list-2-columns ul li {
	width: 50%;
	display: block;
	padding-right: 26px !important;
}

div.wpforms-container-full .wpforms-form .wpforms-checkbox-3-columns ul li,
div.wpforms-container-full .wpforms-form .wpforms-multiplechoice-3-columns ul li,
div.wpforms-container-full .wpforms-form .wpforms-list-3-columns ul li {
	width: 33.3333%;
	display: block;
	padding-right: 26px !important;
}

div.wpforms-container-full .wpforms-form .wpforms-list-inline ul li {
	display: inline-block;
	vertical-align: top;
	margin-right: 20px !important;
}

/* Legacy, for BC */
div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-first-half {
	float: left;
	width: 48%;
	clear: both;
}
div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-last-half {
	float: right;
	width: 48%;
	clear: none;
}
div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-first-third {
	float: left;
	width: 30.666666667%;
	clear: both;
}
div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-middle-third  {
	float: left;
	width: 30.666666667%;
	margin-left: 4%;
	clear: none;
}
div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-last-third {
	float: right;
	width: 30.666666667%;
	clear: none;
}
div.wpforms-container-full .wpforms-form div.wpforms-last {
	float: right !important;
	margin-right: 0 !important;
	clear: none;
}


/* Preset Layouts
----------------------------------------------------------------------------- */

/* Single line */
div.wpforms-container-full.inline-fields {
	overflow: visible;
}

div.wpforms-container-full.inline-fields .wpforms-form {
	display: flex;
	align-items: flex-end;
}

div.wpforms-container-full.inline-fields .wpforms-form .wpforms-field-container  {
	display: table;
	width: calc(100% - 160px);
	float: left;
}

div.wpforms-container-full.inline-fields .wpforms-form .wpforms-field  {
	display: table-cell;
	padding-right: 2%;
	vertical-align: top;
}

div.wpforms-container-full.inline-fields .wpforms-form .wpforms-submit-container {
	float: right;
	width: 160px;
	clear: none;
	padding-bottom: 10px;
}

div.wpforms-container-full.inline-fields .wpforms-form .wpforms-submit {
	display: block;
	width: 100%;
}

div.wpforms-container-full.inline-fields .wpforms-form input.wpforms-field-medium,
div.wpforms-container-full.inline-fields .wpforms-form select.wpforms-field-medium,
div.wpforms-container-full.inline-fields .wpforms-form .wpforms-field-row.wpforms-field-medium {
	max-width: 100%;
}


/* Set Styles
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form input[type=date],
div.wpforms-container-full .wpforms-form input[type=datetime],
div.wpforms-container-full .wpforms-form input[type=datetime-local],
div.wpforms-container-full .wpforms-form input[type=email],
div.wpforms-container-full .wpforms-form input[type=month],
div.wpforms-container-full .wpforms-form input[type=number],
div.wpforms-container-full .wpforms-form input[type=password],
div.wpforms-container-full .wpforms-form input[type=range],
div.wpforms-container-full .wpforms-form input[type=search],
div.wpforms-container-full .wpforms-form input[type=tel],
div.wpforms-container-full .wpforms-form input[type=text],
div.wpforms-container-full .wpforms-form input[type=time],
div.wpforms-container-full .wpforms-form input[type=url],
div.wpforms-container-full .wpforms-form input[type=week],
div.wpforms-container-full .wpforms-form select,
div.wpforms-container-full .wpforms-form textarea {
	background-color: #fff;
	box-sizing: border-box;
	border-radius: 2px;
	color: #333;
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	display: block;
	float: none;
	font-size: 16px;
	font-family: inherit;
	border: 1px solid #ccc;
	padding: 6px 10px;
	height: 38px;
	width: 100%;
	line-height: 1.3;
}

div.wpforms-container-full .wpforms-form textarea {
	min-height: 38px;
	resize: vertical;
}

div.wpforms-container-full .wpforms-form input[type=checkbox],
div.wpforms-container-full .wpforms-form input[type=radio] {
	border: 1px solid #ccc;
	background-color: #fff;
	width: 14px;
	height: 14px;
	margin: 0 10px 0 3px;
	display: inline-block;
	vertical-align: baseline;
}

div.wpforms-container-full .wpforms-form input[type=radio] {
	border-radius: 50%;
}

div.wpforms-container-full .wpforms-form select {
	max-width: 100%;
	text-transform: none;
	white-space: nowrap;
}

div.wpforms-container-full .wpforms-form select[multiple] {
	height: auto;
	overflow-y: scroll;
	background-image: none;
}

// Readonly number input should not display arrows (spin box).
div.wpforms-container-full .wpforms-form {
	input[type=number] {
		&:read-only {
			appearance: textfield;
			-moz-appearance: textfield;
			-webkit-appearance: textfield;

			&::-webkit-inner-spin-button {
				visibility: hidden;
			}
		}
	}
}

div.wpforms-container-full .wpforms-form input[type=submit],
div.wpforms-container-full .wpforms-form button[type=submit],
div.wpforms-container-full .wpforms-form .wpforms-page-button {
	background-color: #eee;
	border: 1px solid #ddd;
	color: #333;
	font-size: 1em;
	font-family: inherit;
	padding: 10px 15px;
}

div.wpforms-container-full .wpforms-form .wpforms-page-button {
	font-size: 0.9em;
	font-weight: 400;
	margin: 0 5px;
	min-width: 90px;
	text-align: center;
}

div.wpforms-container-full .wpforms-form input[type=submit]:hover,
div.wpforms-container-full .wpforms-form input[type=submit]:focus,
div.wpforms-container-full .wpforms-form input[type=submit]:active,
div.wpforms-container-full .wpforms-form button[type=submit]:hover,
div.wpforms-container-full .wpforms-form button[type=submit]:focus,
div.wpforms-container-full .wpforms-form button[type=submit]:active,
div.wpforms-container-full .wpforms-form .wpforms-page-button:hover,
div.wpforms-container-full .wpforms-form .wpforms-page-button:active,
div.wpforms-container-full .wpforms-form .wpforms-page-button:focus {
	background-color: #ddd;
	border: 1px solid #ccc;
	cursor: pointer;
}

div.wpforms-container-full .wpforms-form input[type=submit]:disabled,
div.wpforms-container-full .wpforms-form button[type=submit]:disabled,
div.wpforms-container-full .wpforms-form .wpforms-page-button:disabled {
	background-color: #eee;
	border: 1px solid #ddd;
	cursor: default;
	opacity: 0.5;
}

div.wpforms-container-full .wpforms-form input:focus,
div.wpforms-container-full .wpforms-form textarea:focus,
div.wpforms-container-full .wpforms-form select:focus,
div.wpforms-container-full .wpforms-form .is-focused .choices__inner,
div.wpforms-container-full .wpforms-form .is-open .choices__inner,
div.wpforms-container-full .wpforms-form .is-open .choices__list--dropdown {
	border: 1px solid #999;
	box-shadow: none;
}

div.wpforms-container-full .wpforms-form input:disabled,
div.wpforms-container-full .wpforms-form textarea:disabled,
div.wpforms-container-full .wpforms-form select:disabled {
	background-color: #f9f9f9;
	border-color: #ddd;
	color: #999;
	cursor: not-allowed;
}


/* Errors, Warnings, etc
----------------------------------------------------------------------------- */
div.wpforms-container-full .wpforms-error-container {
	&.wpforms-error-styled-container {
		padding: 10px 0;
		font-size: 15px;

		.wpforms-error {
			padding: 6px;
			border: 1px solid #990000;
			border-left: 5px solid;
		}
	}
}

div.wpforms-container-full .wpforms-form .wpforms-error-container,
div.wpforms-container-full .wpforms-form noscript.wpforms-error-noscript {
	color: #990000;
}

div.wpforms-container-full .wpforms-form label.wpforms-error {
	display: block;
	color: #990000;
	font-size: 12px;
	float: none;
	cursor: default;
	margin-top: 8px;
}

div.wpforms-container-full .wpforms-form .wpforms-field input.wpforms-error,
div.wpforms-container-full .wpforms-form .wpforms-field input.user-invalid,
div.wpforms-container-full .wpforms-form .wpforms-field textarea.wpforms-error,
div.wpforms-container-full .wpforms-form .wpforms-field textarea.user-invalid,
div.wpforms-container-full .wpforms-form .wpforms-field select.wpforms-error,
div.wpforms-container-full .wpforms-form .wpforms-field select.user-invalid,
div.wpforms-container-full .wpforms-form .wpforms-field.wpforms-has-error .choices__inner {
	border: 1px solid #cc0000;
}

div.wpforms-container-full .wpforms-form .wpforms-field-credit-card-expiration label.wpforms-error,
div.wpforms-container-full .wpforms-form .wpforms-field-credit-card-code label.wpforms-error {
	display: none !important;
}

/* Confirmation
----------------------------------------------------------------------------- */

/*
 * Note: The second selector for the following two rules is needed to override the rule with
 * the selector: `div.wpforms-container-full, div.wpforms-container-full .wpforms-form *`.
 * The underlying cause is that the .wpforms-confirmation-container-full is not expected
 * to be located inside of the .wpforms-confirmation-container-full element, since on non-AMP
 * pages the confirmation message is never displayed on the same page as the form.
 */
.wpforms-confirmation-container-full,
div[submit-success] > .wpforms-confirmation-container-full {
	color: #333;
	margin: 0 auto 24px;
	padding: 15px 15px;
	overflow-wrap: break-word;

	iframe {
		width: 100%;
		border: 0;
	}
}

.wpforms-confirmation-container-full,
div[submit-success] > .wpforms-confirmation-container-full:not(.wpforms-redirection-message) {
	background: #e0ffc7;
	border: 1px solid #b4d39b;
	box-sizing: border-box;

	p {
		color: #333333;
	}
}

.wpforms-confirmation-container-full p:last-of-type,
div[submit-success] > .wpforms-confirmation-container-full p:last-of-type {
	margin: 0;
}

/*
 * Hide the form fields upon successful submission. This may not be the best approach.
 * Perhaps more robust: .wpforms-form.amp-form-submit-success > *:not([submit-success]) { display:none }
 */
.amp-form-submit-success .wpforms-field-container,
.amp-form-submit-success .wpforms-submit-container {
	display: none;
}


/* reCAPTCHA Area
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form .wpforms-recaptcha-container {
	padding: 10px 0 20px 0;
	clear: both;

	// Fix for invisible captcha state see #6130.
	&.wpforms-is-turnstile {

		&-invisible {
			padding: 0;
		}

		.g-recaptcha {
			line-height: 0;
			font-size: 0;

			iframe {
				position: relative !important;
			}
		}
	}
}

div.wpforms-container-full .wpforms-form .wpforms-recaptcha-container iframe {
	width: 100%;
	max-width: 100%;
}


/* Form Header area
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form .wpforms-title {
	font-size: 26px;
	margin: 0 0 10px 0;
}

div.wpforms-container-full .wpforms-form .wpforms-description {
	margin: 0 0 10px 0;
}


/* Form Footer area
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form .wpforms-submit-container {
	padding: 10px 0 0 0;
	clear: both;
	position: relative;
}

div.wpforms-container-full .wpforms-form button[type=submit] {
}

div.wpforms-container-full .wpforms-form .wpforms-submit-spinner {
	margin-inline-start: 0.5em;
	display: inline-block;
	vertical-align: middle;
	max-width: 26px;
}

div.wpforms-container-full .wpforms-form .wpforms-pagebreak-center {
	text-align: center;
}

div.wpforms-container-full .wpforms-form .wpforms-pagebreak-left {
	text-align: left;
}

div.wpforms-container-full .wpforms-form .wpforms-pagebreak-left .wpforms-page-button {
	margin: 0 10px 0 0;
}

div.wpforms-container-full .wpforms-form .wpforms-pagebreak-right {
	text-align: right;
}

div.wpforms-container-full .wpforms-form .wpforms-pagebreak-right .wpforms-page-button {
	margin: 0 0 0 10px;
}

div.wpforms-container-full .wpforms-form .wpforms-pagebreak-split .wpforms-page-prev {
	float: left;
	margin: 0;
}

div.wpforms-container-full .wpforms-form .wpforms-pagebreak-split .wpforms-page-next {
	float: right;
	margin: 0;
}


/* Credit Card field
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form .wpforms-field-credit-card-number {
	margin-right: 100px;
	width: calc( 100% - 120px );
}

div.wpforms-container-full .wpforms-form .wpforms-field-credit-card-code {
	position: absolute;
	right: 0;
	top: 0;
	width: 110px;
}

div.wpforms-container-full .wpforms-form .wpforms-field-credit-card-name {
	margin-right: 170px;
}

div.wpforms-container-full .wpforms-form .wpforms-field-credit-card-expiration {
	position: absolute;
	right: 0;
	top: 0;
	width: 160px;
}

div.wpforms-container-full .wpforms-form .wpforms-field-credit-card-expiration select {
	width: 45%;
	float: left;
	display: block;
}

div.wpforms-container-full .wpforms-form .wpforms-field-credit-card-expiration span {
	float: left;
	width: 10%;
	text-align: center;
	line-height: 38px;
}

/* Number slider field
----------------------------------------------------------------------------- */
div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range] {
	-webkit-appearance: none;
	appearance: none;
	height: 10px;
	background: #fff;
	border: 1px solid #CCC;
	border-radius: 5px;
	outline: none;
	padding: 0;
	margin: 10px 0 5px;
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range]::-ms-track {
	color: transparent;
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range]::-webkit-slider-thumb {
	-webkit-appearance: none; /* Override default look */
	appearance: none;
	width: 17px;
	height: 17px;
	background: #B5B5B5;
	cursor: pointer;
	border-radius: 100%;
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range]::-moz-range-thumb {
	width: 17px;
	height: 17px;
	background: #B5B5B5;
	cursor: pointer;
	border-radius: 100%;
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider input[type=range]::-ms-thumb {
	width: 17px;
	height: 17px;
	background: #B5B5B5;
	cursor: pointer;
	border-radius: 100%;
}

div.wpforms-container-full .wpforms-form .wpforms-field-number-slider .wpforms-field-number-slider-hint {
	font-size: 13px;
}

/* Stripe Credit Card field
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form .wpforms-field-stripe-credit-card-cardnumber,
div.wpforms-container-full .wpforms-form input.wpforms-stripe-credit-card-hidden-input {
	background-color: #fff;
	box-sizing: border-box;
	border-radius: 2px;
	color: #333;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	display: block;
	float: none;
	font-size: 16px;
	border: 1px solid #ccc;
	padding: 8px 10px;
	height: 38px;
	width: 100%;
	line-height: 1.3
}

div.wpforms-container-full .wpforms-form .wpforms-stripe-element-invalid {
	border: 1px solid #cc0000!important;
}


/* HTML field
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form .wpforms-field-html ul,
div.wpforms-container-full .wpforms-form .wpforms-field-html ol {
	margin: 0 0 20px !important;
}

div.wpforms-container-full .wpforms-form .wpforms-field-html ul li,
div.wpforms-container-full .wpforms-form .wpforms-field-html ol li {
	margin: 0 0 5px 0 !important;
}

div.wpforms-container-full .wpforms-form .wpforms-field-html ul li {
	list-style: disc !important;
}

div.wpforms-container-full .wpforms-form .wpforms-field-html ol li {
	list-style: decimal !important;
}

div.wpforms-container-full .wpforms-form .wpforms-field-html li > ul,
div.wpforms-container-full .wpforms-form .wpforms-field-html li > ol {
	margin: 6px 0 0 20px !important;
}


/* Date/time field
----------------------------------------------------------------------------- */

div.wpforms-container-full {
	.wpforms-field-date-time {

		.wpforms-field-row {
			display: flex;
			flex-wrap: wrap;
			align-items: start;
			gap: 10px 12px;

			&::before,
			&::after {
				position: absolute;
			}
		}

		.wpforms-date-type-dropdown {
			align-items: center;
			display: flex;
			flex-grow: 1;
			flex-wrap: wrap;
			width: clamp( calc( 50% - 12px ), 100px, 100% );

			+ .wpforms-field-row-block {
				width: clamp( calc( 50% - 12px ), 100px, 100% );
				flex: 1;
				min-width: 30%;
			}

			.wpforms-field-date-dropdown-wrap {
				width: 100%;
			}

			.wpforms-field-sublabel {
				width: 100%;
			}
		}

		.wpforms-field-date-dropdown-wrap {
			align-items: center;
			display: flex;
			flex-grow: 1;
			flex-wrap: wrap;
			margin: 0 -6px 0 -6px;

			&.wpforms-field-small {
				width: calc( 25% + 12px );

				select {
					appearance: none;
					background-image: none;
					padding-left: 5px;
					padding-right: 5px;
				}
			}

			&.wpforms-field-medium {
				width: calc( 60% + 12px );
			}

			&.wpforms-field-large {
				width: calc( 100% + 12px );
			}

			select {
				margin: 0 6px 0 6px;
			}
		}

		.wpforms-field-row-block {
			.wpforms-field-date-dropdown-wrap {
				width: 100%;
			}
		}

		.wpforms-field-date-time-date-day,
		.wpforms-field-date-time-date-month {
			width: calc( 30% - 12px );
		}

		.wpforms-field-date-time-date-year {
			width: calc( 40% - 12px );
		}

		.wpforms-date-type-datepicker {
			width: clamp( calc( 50% - 12px ), 100px, 100% );

			+ .wpforms-field-row-block {
				width: clamp( 50%, 100px, 100% );
			}
		}
	}
}

div.wpforms-container-full .wpforms-datepicker-wrap {
	position: relative;
}

div.wpforms-container-full .wpforms-datepicker-wrap .wpforms-datepicker-clear {
	position: absolute;
	background-image: url("../../../pro/images/times-solid-white.svg") !important;
	background-position: 50% 50% !important;
	background-repeat: no-repeat !important;
	background-color: #cccccc !important;
	background-size: 8px !important;
	width: 16px;
	height: 16px;
	cursor: pointer;
	display: block;
	border-radius: 50%;
	right: 10px;
	top: 50%;
	margin-top: -8px;
	transition: all 0.3s;
}

div.wpforms-container-full .wpforms-datepicker-wrap .wpforms-datepicker-clear:hover,
div.wpforms-container-full .wpforms-datepicker-wrap .wpforms-datepicker-clear:focus {
	background-color: red !important;
}

div.wpforms-container-full .wpforms-datepicker-wrap .wpforms-field-small + .wpforms-datepicker-clear {
	right: calc( 75% + 10px );
}

div.wpforms-container-full .wpforms-datepicker-wrap .wpforms-field-medium + .wpforms-datepicker-clear {
	right: calc( 40% + 10px );
}

/* Custom Captcha field
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form .wpforms-captcha-math input {
	display: inline-block;
	width: 70px;
	vertical-align: inherit;
	margin: 0 0 0 5px;
}

div.wpforms-container-full .wpforms-form .wpforms-captcha-equation {
	font-size: 16px;
}

div.wpforms-container-full .wpforms-form .wpforms-captcha-question {
	margin: 0 0 4px 0;
}

/* Rating field
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form .wpforms-field-rating-item {
	padding-right: 6px;
}

div.wpforms-container-full .wpforms-form .wpforms-field-rating svg {
	cursor: pointer;
	-webkit-transform: perspective(1px) translateZ(0);
	transform: perspective(1px) translateZ(0);
	box-shadow: 0 0 1px transparent;
	opacity: 0.60;
}

div.wpforms-container-full .wpforms-form .wpforms-field-rating-item.selected svg,
div.wpforms-container-full .wpforms-form .wpforms-field-rating-item.hover svg {
	-webkit-transform: scale(1.3);
	transform: scale(1.3);
	opacity: 1;
}

div.wpforms-container-full .wpforms-form .wpforms-field-rating-wrapper {
	display: inline-block;
}

div.wpforms-container-full .wpforms-form .wpforms-field-rating-labels {
    display: flex;
    justify-content: space-between;
	gap: 10px;
}

/* Layout field
----------------------------------------------------------------------------- */
div.wpforms-container-full .wpforms-form {
	.wpforms-field-layout {
		padding: 0;
	}
}

/* File upload field
----------------------------------------------------------------------------- */
div.wpforms-field-file-upload .wpforms-hide{
	display: none !important;
}

div.wpforms-field-file-upload {
	input[type=file] {
		font-style: normal;
		font-weight: 400;
		font-size: 16px;
		line-height: 22px;
		padding: 1px;
		height: auto;
		width: 60%;
		border: none !important;
		box-shadow: none;

		&::-webkit-file-upload-button {
			padding: 4px 12px;
			font-weight: 400;
			font-size: 14px;
			margin-right: 10px;
			cursor: pointer;
			transition: all 0.15s ease-out;
		}

		&:hover {
			&::-webkit-file-upload-button {
				background: #eeeeee;
			}
		}
	}

	p.wpforms-file-upload-capture-camera-classic {
		margin-top: 20px;
		display: block;

		a.camera {
			color: var( --wpforms-field-text-color );
			text-decoration: underline;
		}
	}
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern {
	border: 2px dashed rgba(0, 0, 0, 0.1);
	background: rgba(0, 0, 0, 0.02);
	padding: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	cursor: pointer;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern.wpforms-with-files{
	padding: 5px;
	flex-direction: row;
	align-items: flex-start;
	justify-content: flex-start;
	flex-wrap: wrap;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern svg{
	height: 50px;
	width: 50px;
	color: #e27730;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-modern-title{
	font-weight: bold;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-modern-hint{
	color: rgba(0, 0, 0, 0.1);
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern.wpforms-highlighted{
	border-color: #e27730;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-text{
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-preview-block{
	width: 100px;
	height: 100px;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background: #fff;
	background-size: cover !important;
	margin: 5px;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: default;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-preview-block:hover .wpforms-overlay{
	opacity: 1;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-preview-block .wpforms-overlay{
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.5);
	opacity: 0;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-preview-block .wpforms-overlay .wpforms-close{
	position: absolute;
	right: 5px;
	top: 5px;
	cursor: pointer;
	display: block;
	width: 18px;
	height: 18px;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-preview-block .wpforms-overlay .wpforms-title{
	position: absolute;
	font-size: 12px;
	color: #fff;
	top: 25px;
	bottom: 10px;
	left: 5px;
	right: 5px;
	overflow-y: auto;
	line-height: 16px;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-preview-block .wpforms-overlay .wpforms-close .wpforms-close-left,
div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-preview-block .wpforms-overlay .wpforms-close .wpforms-close-right{
	height: 18px;
	width: 1px;
	background: #fff;
	position: absolute;
	display: block;
	z-index: 1;
	right: 9px;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-preview-block .wpforms-overlay .wpforms-close .wpforms-close-left{
	-webkit-transform: rotate(-45deg);
	transform: rotate(-45deg);
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-preview-block .wpforms-overlay .wpforms-close .wpforms-close-right{
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-preview-block svg{
	height: 25px;
	width: 25px;
	color: rgba(0, 0, 0, 0.1);
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern .wpforms-preview-block .wpforms-progress{
	height: 5px;
	width: 0;
	position: absolute;
	bottom: 0;
	left: 0;
	background: #e27730;
	transition: all 0.5s;
}

div.wpforms-field-file-upload .wpforms-file-upload-builder-modern input[type="file"] {
	display: none;
}

/* Camera field
----------------------------------------------------------------------------- */
body div.wpforms-container-full {

	.wpforms-camera-link {
		color: #036AAB;
		text-decoration: underline;
		font-size: 16px;

		// Hover state.
		&:hover {
			text-decoration: none;
		}

		// Focus state.
		&:focus {
			outline: 2px solid #036AAB;
			outline-offset: 2px;
			text-decoration: none;
		}
	}

	.wpforms-field-camera {
		.wpforms-camera-button {
			display: inline-flex;
			align-items: center;
			gap: 7px;
			line-height: 100%;
			cursor: pointer;
			padding: 0 15px;
			border: 1px solid #ccc;
			height: 41px;
			background-color: #fff;
			color: #333;
			font-size: 16px;

			&:hover, &:focus {
				border-color: #bbb;
			}

			svg {
				fill: #bbb;
				width: 1.1em;
				height: auto;
				flex-shrink: 0;
				vertical-align: middle;
			}
		}

		input[type="file"] {
			display: none;
		}

		.wpforms-camera-selected-file {
			display: none;
			align-items: center;
			gap: 10px;
			color: #777777;
			font-size: 15px;

			svg {
				fill: #D63638;
				cursor: pointer;
				&:hover {
					opacity: .75;
				}
			}

			.wpforms-camera-remove-file {
				height: 15px;
			}

			&.wpforms-camera-selected-file-active {
				display: inline-flex;
			}
		}
	}

	.wpforms-layout-column-33 {
		.wpforms-camera-selected-file {
			&.wpforms-camera-selected-file-active {
				display: block;

				button {
					margin-left: 5px;
				}
			}
		}
	}

	// Camera modal styles.
	div.wpforms-camera-modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.75);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 999999;

		&.wpforms-camera-format-video {
			.wpforms-camera-modal-footer {
				.wpforms-camera-modal-actions {
					justify-content: space-between;
					position: relative;

					.wpforms-camera-video-countdown {
						font-family: monospace;
						font-size: 14px;
						color: #999999;
						flex: 0 0 auto;
						font-weight: 500;
						min-width: 120px;
						text-align: left;
						white-space: nowrap;

						span {
							display: inline-block;
						}

						div {
							display: inline;
						}
					}

					.wpforms-camera-capture, .wpforms-camera-countdown, .wpforms-camera-stop {
						position: absolute;
						left: 50%;
						transform: translateX(-50%);
					}

					.wpforms-camera-capture {
						background-color: #D63638;

						&:hover {
							background-color: #b32d2e;
						}
					}

					.wpforms-camera-stop {
						width: 48px;
						height: 48px;
						border-radius: 50%;
						background-color: #D63638;
						display: flex;
						align-items: center;
						justify-content: center;
						cursor: pointer;

						&:after {
							content: '';
							position: absolute;
							top: 50%;
							left: 50%;
							transform: translate(-50%, -50%);
							width: 18px;
							height: 18px;
							background-color: #fff;
							border-radius: 4px;
						}

						&:hover {
							background-color: #b32d2e;
						}
					}
				}

				.wpforms-camera-modal-buttons {
					button {
						border: none;
					}
					.wpforms-camera-cancel {
						width: 26px;
						background: url(../../../pro/images/camera-video.svg) no-repeat center;
					}
					.wpforms-camera-crop {
						background: url(../../../pro/images/video-cancel.svg) no-repeat center;
					}
				}
			}
		}
	}

	div.wpforms-camera-modal {
		background: #FFFFFF;
		max-width: 100%;
		width: 540px;
		height: 440px;
		display: flex;
		flex-direction: column;
		border-radius: 9px;
		box-shadow: -4px 5px 15px 0 rgba(0, 0, 0, 0.15);
		overflow: hidden;
		position: relative;
		z-index: 1000000;

		.wpforms-camera-modal-header {
			display: flex;
			height: 62px;
			padding: 16px 30px;
			justify-content: space-between;
			align-items: center;

			.wpforms-camera-modal-title {
				font-size: 18px;
				font-weight: 700;
				color: #444444;
			}

			.wpforms-camera-modal-close {
				cursor: pointer;
				width: 12px;
				height: 12px;
				display: flex;
				justify-content: center;
				align-items: center;

				svg {
					fill: #BBBBBB;
					width: 12px;
					height: 12px;
				}

				&:hover {
					opacity: .75;
				}
			}
		}

		.wpforms-camera-modal-content {
			min-height: 300px;
			background-color: #dedede;
			display: flex;
			align-items: center;
			justify-content: center;
			overflow: hidden;
			position: relative;

			.wpforms-camera-preview {
				display: none;
				width: 100%;
				height: auto;
				position: relative;
				overflow: hidden;
				justify-content: center;

				video {
					max-width: 100%;
					height: auto;
					width: auto;
				}

				.wpforms-camera-captured-photo {
					max-width: 100%;
					height: auto;
					width: auto;
				}

				cropper-canvas {
					min-height: 0;
					width: 100%;
					height: 100%;
					display: block;

					cropper-shade {
						outline-style: solid;
						outline-color: rgba(0, 0, 0, .75);
					}

					cropper-image {
						max-width: 100%;
						max-height: 100%;
					}

					cropper-handle::after {
						width: 8px!important;
						height: 8px!important;
					}

					cropper-selection {
						min-width: auto;
					}

					cropper-handle {
						background-color: transparent;
						height: 15px;
						position: absolute;
						width: 15px;

						&[action=move] {
							height: 100%;
							left: 0;
							top: 0;
							width: 100%;
						}
					}

					cropper-grid {
						position: absolute;
						&[bordered] {
							border: 2px solid #fff !important;
						}

					}
				}

			}

			.wpforms-camera-error {
				display: none;
				padding: 20px;
				text-align: center;
				color: #d63637;
				font-size: 16px;
				line-height: 1.5;
			}
		}

		.wpforms-camera-modal-footer {
			height: 78px;
			min-height: 78px;
			padding: 0 30px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-top: 1px solid #e6e9ec;

			.wpforms-camera-modal-text {
				font-size: 14px;
				color: #555;
			}

			.wpforms-camera-modal-actions {
				display: flex;
				width: 100%;
				justify-content: space-between;
				align-items: center;

				button {
					border: none;
					outline: none;
				}

				.wpforms-camera-capture {
					width: 48px;
					height: 48px;
					border-radius: 50%;
					background-color: #036AAB;
					display: flex;
					align-items: center;
					justify-content: center;
					cursor: pointer;

					&:hover,&:focus {
						outline: none;
						background-color: #215d8f;
					}

					&:disabled {
						opacity: .5;
						cursor: not-allowed;
					}
				}

				.wpforms-camera-capture, .wpforms-camera-countdown {
					position: absolute;
					left: 50%;
					transform: translateX(-50%);
				}

				.wpforms-camera-flip {
					display: none;
					position: absolute;
					right: 20px;
					width: 24px;
					height: 24px;
					background: url(../../../pro/images/camera-rotate.svg) no-repeat center;
					cursor: pointer;

					&:hover {
						opacity: .75;
					}
				}

				.wpforms-camera-countdown {
					width: 48px;
					height: 48px;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #999999;
					font-size: 24px;
					font-weight: 500;

					svg {
						position: absolute;
						top: 0;
						left: 0;
						width: 48px;
						height: 48px;
						transform: rotate(-90deg);
					}

					circle {
						cx: 24px;
						cy: 24px;
						r: calc((48px - 4px) / 2);
						stroke-width: 4px;
						fill: none;
					}

					circle.bg {
						stroke: #dedede;
					}

					circle.progress {
						stroke: #036AAB;
						stroke-linecap: round;
						transform-origin: 24px 24px;
					}

					&.animate circle.progress {
						animation: wpforms-camera-countdown 3s linear forwards;
					}
				}

				@keyframes wpforms-camera-countdown {
					0% {
						stroke-dasharray: 0 calc(calc((48px - 4px) / 2) * 3.14159 * 2);
					}
					100% {
						stroke-dasharray: calc(calc((48px - 4px) / 2) * 3.14159 * 2) 0;
					}
				}
			}

			.wpforms-camera-modal-buttons {
				display: flex;
				width: 100%;
				justify-content: space-between;
				align-items: center;

				button {
					border: none;
					outline: none;

					&:focus {
						outline: none;
					}
				}

				.wpforms-camera-cancel {
					width: 24px;
					height: 24px;
					background: url(../../../pro/images/camera.svg) no-repeat center;
					cursor: pointer;

					&:hover {
						opacity: .75;
					}
				}

				.wpforms-camera-accept, .wpforms-camera-accept-crop {
					font-size: 15px;
					padding: 0 15px;
					background-color: #036AAB;
					color: #fff;
					border-radius: 3px;
					height: 41px;
					font-weight: 500;
					line-height: 100%;
					cursor: pointer;

					&:hover, &:focus {
						background-color: #215d8f;
					}
				}

				.wpforms-camera-crop {
					visibility: hidden; // Temporary hidden as we implement the crop feature in the next release.
					width: 24px;
					height: 24px;
					background: url(../../../pro/images/crop.svg) no-repeat center;
					cursor: pointer;

					&:hover {
						opacity: .75;
					}
				}

				.wpforms-camera-crop-cancel {
					width: 24px;
					height: 24px;
					background: url(../../../pro/images/cancel.svg) no-repeat center;
					cursor: pointer;
					&:hover {
						opacity: .75;
					}
				}

				.wpforms-camera-cancel-video {
					width: 24px;
					height: 24px;
					background: url(../../../pro/images/trash.svg) no-repeat center;
					cursor: pointer;
					&:hover {
						opacity: .75;
					}
				}
			}
		}
	}
}

// Prevent body scroll when camera modal is open.
body.wpforms-camera-modal-open {
	overflow: hidden;
	position: fixed;
	width: 100%;
}


// If it is portrait orientation...
@media only screen and (max-width: 767px) and (orientation: portrait) {
	body div.wpforms-container-full {
		div.wpforms-camera-modal {
			height: 75vh;
			max-height: 75vh;
			width: 90vw;
			max-width: 90vw;

			.wpforms-camera-modal-header {
				padding: 20px 16px;
			}

			.wpforms-camera-modal-content {
				min-height: 0;
				height: 100%;

				.wpforms-camera-preview {
					height: auto;
					width: 100%;
				}
			}

			.wpforms-camera-modal-footer {
				padding: 0 20px;
				.wpforms-camera-modal-actions {
					.wpforms-camera-flip {
						&.wpforms-camera-flip-active {
							display: block;
						}
					}
				}
			}
		}
	}

	body div.wpforms-container-full {
		div.wpforms-camera-modal-overlay.wpforms-camera-format-video {
			.wpforms-camera-modal {
				.wpforms-camera-modal-footer {
					.wpforms-camera-modal-actions {
						.wpforms-camera-flip {
							position: static;
						}

						.wpforms-camera-video-countdown {

							span {
								text-align: left;
							}
						}
					}
				}
			}
		}
	}
	// Video modal portrait styles.
}

@media only screen and (max-width: 1024px) and (orientation: landscape ) {

	body div.wpforms-container-full {
		div.wpforms-camera-modal {
			flex-direction: row;
			width: 80vw;
			max-width: 90vw;
			height: 75vh;
			max-height: 75vh;

			.wpforms-camera-modal-content {
				min-height: 0;
				height: 100%;
				flex-grow: 1;

				.wpforms-camera-preview {
					height: auto;
					width: auto;
					.wpforms-camera-captured-photo {
						object-fit: cover;
					}
				}
			}

			.wpforms-camera-modal-header {
				flex-direction: column-reverse;
				height: 100%;
				padding: 20px 16px;

				.wpforms-camera-modal-title {
					writing-mode: vertical-lr;
					transform: rotate(180deg);
					text-align: center;
				}
			}

			.wpforms-camera-modal-footer {
				height: 100%;
				flex-direction: row;
				padding: 20px 0;
				width: 78px;
				min-width: 78px;

				.wpforms-camera-modal-actions {
					justify-content: center;
					display: flex;
					flex-direction: column-reverse;

					.wpforms-camera-capture, .wpforms-camera-countdown, .wpforms-camera-stop {
						position: relative;
						left: auto;
						transform: none;
					}

					.wpforms-camera-flip {
						left: calc(100% - 52px);
						top: 20px;
						&.wpforms-camera-flip-active {
							display: block;
						}
					}
				}

				.wpforms-camera-modal-buttons {
					flex-direction: column-reverse;
					height: 100%;

					.wpforms-camera-accept, .wpforms-camera-accept-crop {
						transform: rotate(-90deg);
						transform-origin: center;
						width: max-content;
					}
				}
			}

		}
	}

	// Video modal landscape styles.
	body div.wpforms-container-full {
		div.wpforms-camera-modal-overlay.wpforms-camera-format-video {
			div.wpforms-camera-modal {

				.wpforms-camera-preview {
					max-height: 100%;
					video {
						max-height: 100%;
						object-fit: cover;
					}

					.wpforms-camera-captured-photo {
						object-fit: cover;
					}
				}

				.wpforms-camera-modal-footer {
					.wpforms-camera-modal-actions {
						height: 100%;

						.wpforms-camera-capture, .wpforms-camera-countdown, .wpforms-camera-stop {
							position: absolute;
							left:auto;
							top: 50%;
							transform: translateY(-50%);
						}

						.wpforms-camera-flip {
							position: static;
						}

						.wpforms-camera-video-countdown {
							min-width: auto;

							span {
								text-align: center;
							}

							div {
								display: none!important;
							}
						}
					}
				}
			}
		}
	}
}

// General mobile styles.
@media only screen and (max-width: 767px) {
	body div.wpforms-container-full {
		div.wpforms-camera-modal {

			.wpforms-camera-modal-footer {
				.wpforms-camera-video-countdown {
					div {
						display: none!important;
					}
				}
			}

		}
	}
}

/* Image choices
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices label:not(.wpforms-error) {
	cursor: pointer;
	position: relative;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices label input {
	top: 50%;
}

/* Modern style */
div.wpforms-container-full .wpforms-form .wpforms-list-inline ul.wpforms-image-choices-modern li {
	margin: 5px 5px 5px 5px !important;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern img {
	display: inline-block;
	margin: 0 auto;
	max-width: 100%;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern label:not(.wpforms-error) {
	background-color: #fff;
	display: block;
	margin: 0 auto;
	border: 1px solid #fff;
	border-radius: 3px;
	padding: 20px 20px 18px 20px;
	transition: all 0.5s;
	text-align: center;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern label:not(.wpforms-error):hover,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern label:not(.wpforms-error):focus {
	border:1px solid #ddd;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern .wpforms-selected label,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern li:has( input:checked ) label {
	box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern .wpforms-image-choices-image:after {
	content: "\2714";
	font-size: 22px;
	line-height: 32px;
	color: #fff;
	background: green;
	opacity: 0;
	position: absolute;
	top: 50%;
	left: 50%;
	margin: -16px 0 0 -16px;
	width: 32px;
	height: 32px;
	border-radius: 50%;
	transition: all 0.5s;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern .wpforms-selected .wpforms-image-choices-image:after,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern li:has( input:checked ) .wpforms-image-choices-image:after {
	opacity: 1;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern .wpforms-image-choices-image {
	display: block;
	position: relative;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern .wpforms-selected .wpforms-image-choices-label,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern li:has( input:checked ) .wpforms-image-choices-label {
	font-weight: 700;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-modern .wpforms-image-choices-label {
	display: block;
	margin-top: 12px;
}

/* Classic */
div.wpforms-container-full .wpforms-form .wpforms-list-inline ul.wpforms-image-choices-classic li {
	margin: 0 10px 10px 0 !important;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic img {
	display: inline-block;
	margin: 0 auto;
	max-width: 100%;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic label:not(.wpforms-error) {
	background-color: #fff;
	display: block;
	margin: 0 auto;
	border: 2px solid #fff;
	padding: 10px;
	text-align: center;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic label:not(.wpforms-error):hover,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic label:not(.wpforms-error):focus {
	border-color: #ddd;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic .wpforms-image-choices-image {
	display: block;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic .wpforms-selected label,
div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic li:has( input:checked ) label {
	border-color: #666 !important;
}

div.wpforms-container-full .wpforms-form ul.wpforms-image-choices-classic .wpforms-image-choices-label {
	display: block;
	margin-top: 8px;
}

/* Icon choices
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form {

	@import "icon-choices-base";
}

/* Page Indicator themes
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form .wpforms-page-indicator {
	margin: 0 0 20px 0;
	overflow: hidden;
}

/** Circles theme **/
div.wpforms-container-full .wpforms-form .wpforms-page-indicator.circles {
	border-top: 1px solid #dfdfdf;
	border-bottom: 1px solid #dfdfdf;
	padding: 15px 10px;
}

div.wpforms-container-full .wpforms-form .wpforms-page-indicator.circles .wpforms-page-indicator-page {
	float: left;
	margin: 0 20px 0 0;
}

div.wpforms-container-full .wpforms-form .wpforms-page-indicator.circles .wpforms-page-indicator-page:last-of-type {
	margin: 0;
}

div.wpforms-container-full .wpforms-form .wpforms-page-indicator.circles .wpforms-page-indicator-page-number {
	height: 40px;
	width: 40px;
	border-radius: 50%;
	display: inline-block;
	margin: 0 10px 0 0;
	line-height: 40px;
	text-align: center;
	background-color: #ddd;
	color: #666;
}

div.wpforms-container-full .wpforms-form .wpforms-page-indicator.circles .active .wpforms-page-indicator-page-number {
	color: #fff;
}

/* Connector theme */
div.wpforms-container-full .wpforms-form .wpforms-page-indicator.connector .wpforms-page-indicator-page {
	float: left;
	text-align: center;
	font-size: 16px;
	line-height: 1.2;
}

div.wpforms-container-full .wpforms-form .wpforms-page-indicator.connector .wpforms-page-indicator-page-number {
	display: block;
	text-indent: -9999px;
	height: 6px;
	background-color: #ddd;
	margin: 0 0 16px 0;
	position: relative;
}

div.wpforms-container-full .wpforms-form .wpforms-page-indicator.connector .wpforms-page-indicator-page-triangle {
	position: absolute;
	top: 100%;
	left: 50%;
	width: 0;
	height: 0;
	margin-left: -5px;
	border-style: solid;
	border-width: 6px 5px 0 5px;
	border-color: transparent transparent transparent transparent;
}

div.wpforms-container-full .wpforms-form .wpforms-page-indicator.connector .wpforms-page-indicator-page-title {
	display: inline-block;
	padding: 0 15px;
	font-size: 16px;
}

/* Progress theme */
div.wpforms-container-full .wpforms-form .wpforms-page-indicator.progress {
	font-size: 18px;
}

div.wpforms-container-full .wpforms-form .wpforms-page-indicator.progress .wpforms-page-indicator-page-progress-wrap {
	display: block;
	width: 100%;
	background-color: #ddd;
	height: 18px;
	border-radius: 10px;
	overflow: hidden;
	position: relative;
	margin: 5px 0 0;
}

div.wpforms-container-full .wpforms-form .wpforms-page-indicator.progress .wpforms-page-indicator-page-progress {
	height: 18px;
	position: absolute;
	left: 0;
	top: 0;
}

/* Dropdown field
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-form .wpforms-field-select select > option {
	color: inherit;
}

div.wpforms-container-full .wpforms-form .wpforms-field-select select > option.placeholder,
div.wpforms-container-full .wpforms-form .wpforms-field-select select > option[disabled] {
	color: inherit;
	opacity: 0.5;
}

/* Classic style */
div.wpforms-container-full .wpforms-field.wpforms-field-select-style-classic select {
	&[multiple] {
		padding: 0;

		@supports (font: -apple-system-body) and (-webkit-appearance: none) and (-webkit-hyphens: none) {
			padding: 10px 8px;
		}

		> option {
			padding: 8px 10px;
			box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1);

			&.placeholder,
			&[disabled] {
				box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.2);
			}

			&:checked {
				background-color: lightgrey;
			}
		}
	}

	@supports (font: -apple-system-body) and (-webkit-appearance: none) and (-webkit-hyphens: none) {
		text-indent: 2px;
	}
}

/* Modern style */
div.wpforms-container-full .wpforms-form .choices {
	font-size: 16px;
	color: #333;
}

div.wpforms-container-full .wpforms-form .choices .choices__list--single {
	font-size: 1em;
	line-height: normal;
}

// Override Choices border-radius on the frontend.
div.wpforms-container-full .wpforms-form .choices.is-open.is-flipped .choices__inner,
div.wpforms-container-full .wpforms-form .choices.is-open .choices__list--dropdown {
	border-radius: 0 0 2px 2px;
}

div.wpforms-container-full .wpforms-form .choices.is-open.is-flipped .choices__list--dropdown,
div.wpforms-container-full .wpforms-form .choices.is-open .choices__inner {
	border-radius: 2px 2px 0 0;
}

div.wpforms-container-full .wpforms-form .choices {
	.choices__inner {
		min-height: 38px;
		line-height: 38px;
		border-radius: 2px;
		padding-top: 0;
	}
}

div.wpforms-container-full .wpforms-form .choices input.choices__input {
	display: inline-block;
	height: auto;
	line-height: 1.3;
}

div.wpforms-container-full .wpforms-form .choices ::-webkit-input-placeholder {
	color: inherit;
	opacity: 0.5;
}

div.wpforms-container-full .wpforms-form .choices ::-moz-placeholder {
	color: inherit;
	opacity: 0.5;
}

div.wpforms-container-full .wpforms-form .choices :-ms-input-placeholder {
	color: inherit;
	opacity: 0.5;
}

div.wpforms-container-full .wpforms-field.wpforms-field-select-style-modern {
	.choices .choices__inner {
		.choices__list--multiple {
			&:empty + .choices__input {
				min-width: 100% !important;
				text-overflow: ellipsis;
				padding-right: 4px !important;
				margin-bottom: 1px !important;
				white-space: nowrap;
			}
		}

		input[disabled] {
			opacity: 1;
		}
	}
}

/* Notices
----------------------------------------------------------------------------- */

div.wpforms-container-full .wpforms-notice {
	background-color: #fff;
	border: 1px solid #ddd;
	border-left-width: 12px;
	color: #333;
	font-size: 16px;
	line-height: 1.5;
	margin-bottom: 30px;
	padding: 20px 36px 20px 26px;
	position: relative;
}

div.wpforms-container-full .wpforms-notice .wpforms-delete {
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-moz-appearance: none;
	-webkit-appearance: none;
	background-color: rgba(10, 10, 10, 0.2);
	border: none;
	border-radius: 290486px;
	cursor: pointer;
	display: inline-block;
	height: 20px;
	margin: 0;
	padding: 0;
	outline: none;
	vertical-align: top;
	width: 20px;
	position: absolute;
	right: 10px;
	top: 10px;
}

div.wpforms-container-full .wpforms-notice .wpforms-delete:before,
div.wpforms-container-full .wpforms-notice .wpforms-delete:after {
	background-color: #fff;
	content: "";
	display: block;
	left: 50%;
	position: absolute;
	top: 50%;
	-webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg);
	transform: translateX(-50%) translateY(-50%) rotate(45deg);
	-webkit-transform-origin: center center;
	transform-origin: center center;
}

div.wpforms-container-full .wpforms-notice .wpforms-delete:before {
	height: 2px;
	width: 50%;
}

div.wpforms-container-full .wpforms-notice .wpforms-delete:after {
	height: 50%;
	width: 2px;
}

div.wpforms-container-full .wpforms-notice .wpforms-delete:hover,
div.wpforms-container-full .wpforms-notice .wpforms-delete:focus  {
	background-color: rgba(10, 10, 10, 0.3);
}

div.wpforms-container-full .wpforms-notice a {
	text-decoration: underline;
}

div.wpforms-container-full .wpforms-notice p {
	margin: 0 0 20px 0;
}

div.wpforms-container-full .wpforms-notice p:last-of-type {
	margin-bottom: 0;
}

div.wpforms-container-full .wpforms-notice.wpforms-info .wpforms-notice-action,
div.wpforms-container-full .wpforms-notice.wpforms-info {
	border-color: #3273dc
}

div.wpforms-container-full .wpforms-notice.wpforms-success .wpforms-notice-action,
div.wpforms-container-full .wpforms-notice.wpforms-success {
	border-color: #23d160
}

div.wpforms-container-full .wpforms-notice.wpforms-warning .wpforms-notice-action,
div.wpforms-container-full .wpforms-notice.wpforms-warning {
	border-color: #ffdd57
}

div.wpforms-container-full .wpforms-notice.wpforms-error .wpforms-notice-action,
div.wpforms-container-full .wpforms-notice.wpforms-error {
	border-color: #ff3860
}

div.wpforms-container-full .wpforms-notice .wpforms-notice-actions {
	margin-top: 20px;
}

div.wpforms-container-full .wpforms-notice .wpforms-notice-action {
	border: 2px solid;
	margin-right: 20px;
	padding: 5px;
	text-decoration: none;
}
div.wpforms-container-full .wpforms-notice .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice .wpforms-notice-action:active {
	color: #fff;
}

div.wpforms-container-full .wpforms-notice.wpforms-info .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice.wpforms-info .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice.wpforms-info .wpforms-notice-action:active {
	background-color: #3273dc;
}

div.wpforms-container-full .wpforms-notice.wpforms-success .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice.wpforms-success .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice.wpforms-success .wpforms-notice-action:active {
	background-color: #23d160;
}

div.wpforms-container-full .wpforms-notice.wpforms-warning .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice.wpforms-warning .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice.wpforms-warning .wpforms-notice-action:active {
	background-color: #ffdd57;
	color: inherit;
}

div.wpforms-container-full .wpforms-notice.wpforms-error .wpforms-notice-action:hover,
div.wpforms-container-full .wpforms-notice.wpforms-error .wpforms-notice-action:focus,
div.wpforms-container-full .wpforms-notice.wpforms-error .wpforms-notice-action:active {
	background-color: #ff3860;
}


div.wpforms-container-full {
	.wpforms-form {
		textarea.wpforms-field-small.wp-editor-area {
			height: 100px;
		}

		textarea.wpforms-field-medium.wp-editor-area {
			height: 250px;
		}

		textarea.wpforms-field-large.wp-editor-area {
			height: 400px;
		}
	}
}

/* Preview notice.
----------------------------------------------------------------------------- */

.wpforms-preview-notice-links {
	line-height: 2.4;
}

.wpforms-preview-notice {
	background-color: #fcf9e8;
	border: 1px solid #f5e6ab;
	padding: 15px;
	box-sizing: border-box;
	font-size: 16px;

	p {
		margin: 0;
	}
}

/* Misc
----------------------------------------------------------------------------- */

div.wpforms-container-full {
	margin: 24px auto;
}

div.wpforms-container-full .wpforms-form h3 {
	font-size: 24px;
}

/* Honeypot Area */
div.wpforms-container-full .wpforms-form .wpforms-field-hp {
	display: none !important;
	position: absolute !important;
	left: -9000px !important;
}

div.wpforms-container-full .wpforms-form .wpforms-field-hidden {
	display: none;
	padding: 0;
}

div.wpforms-container-full .wpforms-form .wpforms-screen-reader-element {
	position: absolute !important;
	clip: rect(0, 0, 0, 0) !important;
	height: 1px !important;
	width: 1px !important;
	border: 0 !important;
	margin: 0 !important;
	padding: 0 !important;
	overflow: hidden !important;
	word-wrap: normal !important;
}

/* Limit options */
div.wpforms-container-full .wpforms-form .wpforms-limit-text{
	font-size: 13px;
	display: block;
}

/* RTL support
----------------------------------------------------------------------------- */

/* Phone US format */
body.rtl .wpforms-field-phone input[type=tel] {
	direction: ltr;
	unicode-bidi: embed;
	text-align: right;
}

body.rtl .wpforms-container-full .wpforms-form .wpforms-first {
	float: right;
}

body.rtl .wpforms-container-full .wpforms-form .wpforms-first + .wpforms-one-half {
	margin-right: 4%;
	margin-left: 0;
}

/* Un-reset styles for form error container */
div.wpforms-container-full .wpforms-form .wpforms-error-container {
	ul li {
		list-style: inside !important;
	}

	ol li {
		list-style: inside decimal !important;
	}

	a {
		text-decoration: underline !important;
	}

	del {
		text-decoration: line-through !important;
	}

	blockquote {
		padding-left: 20px;
		border-left: 4px solid;
		font-style: italic;
	}
}

/* Payment fields.
----------------------------------------------------------------------------- */
@import 'payment-quantities-base';
@import 'payment-total-full';
