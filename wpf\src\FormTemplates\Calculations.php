<?php

namespace WPForms\DevTools\FormTemplates;

use WPF<PERSON>_Template;

/**
 * WPForms DevTools template: Calculations' Test Form.
 *
 * @since 0.24
 */
class Calculations extends WPForms_Template {

	/**
	 * Primary class constructor.
	 *
	 * @since 0.24
	 *
	 * @noinspection HtmlUnknownTarget
	 */
	public function init() {

		// Template name.
		$this->name = 'Calculations Test Form';

		// Template slug.
		$this->slug = 'calculations_test_form';

		// Template description.
		$this->description = '';

		// Template field and settings.
		// phpcs:disable WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned, WordPress.Arrays.MultipleStatementAlignment.LongIndexSpaceBeforeDoubleArrow
		$this->data = [
			'fields'     => [
				22 => [
					'id'              => '22',
					'type'            => 'pagebreak',
					'position'        => 'top',
					'indicator'       => 'progress',
					'indicator_color' => '#066aab',
					'nav_align'       => 'left',
				],
				20 => [
					'id'            => '20',
					'type'          => 'content',
					'content'       => '<h2><span style="color: #000000;">Mortgage Calculator</span></h2>',
					'label_disable' => '1',
					'size'          => 'medium',
				],
				1  => [
					'id'    => '1',
					'type'  => 'number',
					'label' => 'Home price $',
					'size'  => 'medium',
				],
				17 => [
					'id'                     => '17',
					'type'                   => 'text',
					'label'                  => 'Today\'s date',
					'size'                   => 'medium',
					'limit_count'            => '1',
					'limit_mode'             => 'characters',
					'map_position'           => 'above',
					'calculation_is_enabled' => '1',
					'calculation_code'       => 'now( )
',
					'calculation_code_php'   => '<?php

$_RETVAL = $_FUNCTION[\'now\'][\'func\']();',
					'calculation_code_js'    => '

$_RETVAL = $_FUNCTION[\'now\']();',
				],
				4  => [
					'label'      => 'Layout',
					'name'       => 'Layout',
					'label_hide' => '1',
					'size'       => 'large',
					'preset'     => '50-50',
					'columns'    => [
						0 => [
							'width_preset' => '50',
							'fields'       => [
								0 => 2,
								1 => 5,
								2 => 8,
								3 => 7,
								4 => 15,
								5 => 14,
							],
						],
						1 => [
							'width_preset' => '50',
							'fields'       => [
								0 => 3,
								1 => 6,
								2 => 9,
								3 => 16,
							],
						],
					],
					'id'         => '4',
					'type'       => 'layout',
				],
				2  => [
					'id'                     => '2',
					'type'                   => 'number',
					'label'                  => 'Down Payment $',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => '$F1 - $F1	*	$F3 /100 ',
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'minus\']($F1, $_INNER_FUNC[\'div\']($_INNER_FUNC[\'mul\']($F1, $F3), 100));',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'minus\']($F1, $_INNER_FUNC[\'div\']($_INNER_FUNC[\'mul\']($F1, $F3), 100));',
				],
				5  => [
					'id'      => '5',
					'type'    => 'payment-select',
					'label'   => 'Loan Term',
					'choices' => [
						1 => [
							'label'      => '30 years',
							'value'      => '30.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => '20 years',
							'value'      => '20.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => '15 years',
							'value'      => '15.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => '10 years',
							'value'      => '10.00',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'style'   => 'classic',
					'size'    => 'medium',
				],
				8  => [
					'id'                     => '8',
					'type'                   => 'number',
					'label'                  => 'Number of payments over the loan’s lifetime',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F5_amount	 * 12
',
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'mul\']($F5_amount, 12);',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'mul\']($F5_amount, 12);',
				],
				7  => [
					'id'                     => '7',
					'type'                   => 'payment-single',
					'label'                  => 'Mortgage payment per month',
					'format'                 => 'single',
					'min_price'              => '0.00',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F2	* ($F9 * pow(1 + $F9, $F8 ) )	/ (pow(1+ $F9, $F8 ) -1)',
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'div\']($_INNER_FUNC[\'mul\']($F2, $_INNER_FUNC[\'mul\']($F9, $_FUNCTION[\'pow\'][\'func\']($_INNER_FUNC[\'plus\'](1, $F9), $F8))), $_INNER_FUNC[\'minus\']($_FUNCTION[\'pow\'][\'func\']($_INNER_FUNC[\'plus\'](1, $F9), $F8), 1));',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'div\']($_INNER_FUNC[\'mul\']($F2, $_INNER_FUNC[\'mul\']($F9, $_FUNCTION[\'pow\']($_INNER_FUNC[\'plus\'](1, $F9), $F8))), $_INNER_FUNC[\'minus\']($_FUNCTION[\'pow\']($_INNER_FUNC[\'plus\'](1, $F9), $F8), 1));',
				],
				15 => [
					'id'                     => '15',
					'type'                   => 'payment-single',
					'label'                  => 'Homeowner\'s insurance (0.05%)',
					'price'                  => '0.00',
					'format'                 => 'single',
					'min_price'              => '0.00',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F1	* 0.0005',
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'mul\']($F1, 0.0005);',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'mul\']($F1, 0.0005);',
				],
				14 => [
					'id'                     => '14',
					'type'                   => 'payment-single',
					'label'                  => 'Property tax (0.07%)',
					'price'                  => '0.00',
					'format'                 => 'single',
					'min_price'              => '0.00',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F1	* 0.0007',
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'mul\']($F1, 0.0007);',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'mul\']($F1, 0.0007);',
				],
				3  => [
					'id'    => '3',
					'type'  => 'number',
					'label' => 'Down payment % ',
					'size'  => 'small',
				],
				6  => [
					'id'            => '6',
					'type'          => 'number-slider',
					'label'         => 'Interest Rate %',
					'min'           => '0',
					'max'           => '10',
					'default_value' => '1',
					'step'          => '1',
					'size'          => 'medium',
					'value_display' => 'Selected Value: {value} %',
				],
				9  => [
					'id'                     => '9',
					'type'                   => 'hidden',
					'label'                  => 'Monthly interest rate',
					'label_disable'          => '1',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F6	/ 100 / 12',
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'div\']($_INNER_FUNC[\'div\']($F6, 100), 12);',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'div\']($_INNER_FUNC[\'div\']($F6, 100), 12);',
				],
				16 => [
					'id'                     => '16',
					'type'                   => 'number',
					'label'                  => 'Total payment ',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F7_amount	+	$F15_amount	+	$F14_amount ',
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'plus\']($_INNER_FUNC[\'plus\']($F7_amount, $F15_amount), $F14_amount);',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'plus\']($_INNER_FUNC[\'plus\']($F7_amount, $F15_amount), $F14_amount);',
				],
				21 => [
					'id'   => '21',
					'type' => 'pagebreak',
					'next' => 'Next',
				],
				24 => [
					'id'            => '24',
					'type'          => 'content',
					'content'       => '<h2>Travel budget</h2>',
					'label_disable' => '1',
					'size'          => 'medium',
				],
				26 => [
					'label'      => 'Layout',
					'name'       => 'Layout',
					'label_hide' => '1',
					'size'       => 'large',
					'preset'     => '50-50',
					'columns'    => [
						0 => [
							'width_preset' => '50',
							'fields'       => [
								0 => 25,
							],
						],
						1 => [
							'width_preset' => '50',
							'fields'       => [
								0 => 28,
								1 => 29,
								2 => 27,
							],
						],
					],
					'id'         => '26',
					'type'       => 'layout',
				],
				25 => [
					'id'                   => '25',
					'type'                 => 'radio',
					'label'                => 'Destination',
					'choices'              => [
						1 => [
							'label'      => 'Italy',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Tenerife',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'Mexico',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
				],
				28 => [
					'id'                => '28',
					'type'              => 'content',
					'content'           => '<span style="color: #0000ff;"><em><strong>Congrats! You are going to travel to Mexico!</strong></em></span>',
					'label_disable'     => '1',
					'size'              => 'medium',
					'conditional_logic' => '1',
					'conditional_type'  => 'show',
					'conditionals'      => [
						0 => [
							0 => [
								'field'    => '25',
								'operator' => '==',
								'value'    => '3',
							],
						],
					],
				],
				29 => [
					'id'                => '29',
					'type'              => 'content',
					'content'           => '<span style="color: #0000ff;"><em><strong>Congrats! You are going to travel to Tenerife!</strong></em></span>',
					'label_disable'     => '1',
					'size'              => 'medium',
					'conditional_logic' => '1',
					'conditional_type'  => 'show',
					'conditionals'      => [
						0 => [
							0 => [
								'field'    => '25',
								'operator' => '==',
								'value'    => '2',
							],
						],
					],
				],
				27 => [
					'id'                => '27',
					'type'              => 'content',
					'content'           => '<span style="color: #0000ff;"><em><strong>Congrats! You are going to travel to Italy!</strong></em></span>',
					'label_disable'     => '1',
					'size'              => 'medium',
					'conditional_logic' => '1',
					'conditional_type'  => 'show',
					'conditionals'      => [
						0 => [
							0 => [
								'field'    => '25',
								'operator' => '==',
								'value'    => '1',
							],
						],
					],
				],
				30 => [
					'label'      => 'Layout',
					'name'       => 'Layout',
					'label_hide' => '1',
					'size'       => 'large',
					'preset'     => '25-25-25-25',
					'columns'    => [
						0 => [
							'width_preset' => '25',
							'fields'       => [
								0 => 31,
								1 => 33,
								2 => 35,
								3 => 37,
								4 => 38,
								5 => 40,
							],
						],
						1 => [
							'width_preset' => '25',
							'fields'       => [
								0 => 32,
								1 => 34,
								2 => 36,
							],
						],
						2 => [
							'width_preset' => '25',
							'fields'       => [],
						],
						3 => [
							'width_preset' => '25',
							'fields'       => [],
						],
					],
					'id'         => '30',
					'type'       => 'layout',
				],
				31 => [
					'id'                          => '31',
					'type'                        => 'date-time',
					'label'                       => 'Start Date',
					'format'                      => 'date',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				33 => [
					'id'         => '33',
					'type'       => 'rating',
					'label'      => 'Hotel Starts',
					'scale'      => '5',
					'icon'       => 'star',
					'icon_size'  => 'medium',
					'icon_color' => '#066aab',
				],
				35 => [
					'id'                     => '35',
					'type'                   => 'number',
					'label'                  => 'Cost (1 psn / 1 day)',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => 'if	( $F25	== \'Italy\' ):
			$F33 * 50
elseif ( $F25	== \'Tenerife\' ):
		 $F33	* 30
elseif ( $F25	== \'Mexico\' ):
		 $F33 * 80;
else:
		 0
endif;',
					'calculation_code_php'   => '<?php

if ($F25 == \'Italy\') {
		$_RETVAL = $_INNER_FUNC[\'mul\']($F33, 50);
} elseif ($F25 == \'Tenerife\') {
		$_RETVAL = $_INNER_FUNC[\'mul\']($F33, 30);
} elseif ($F25 == \'Mexico\') {
		$_RETVAL = $_INNER_FUNC[\'mul\']($F33, 80);
} else {
		$_RETVAL = 0;
}',
					'calculation_code_js'    => '

if ($F25 == \'Italy\') {
		$_RETVAL = $_INNER_FUNC[\'mul\']($F33, 50);
}
else if ($F25 == \'Tenerife\') {
		$_RETVAL = $_INNER_FUNC[\'mul\']($F33, 30);
}
else if ($F25 == \'Mexico\') {
		$_RETVAL = $_INNER_FUNC[\'mul\']($F33, 80);
} else {
		$_RETVAL = 0;
}',
				],
				37 => [
					'id'                     => '37',
					'type'                   => 'payment-single',
					'label'                  => 'Tour cost',
					'format'                 => 'single',
					'min_price'              => '10.00',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F35	*	$F36	*	$F34 ',
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'mul\']($_INNER_FUNC[\'mul\']($F35, $F36), $F34);',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'mul\']($_INNER_FUNC[\'mul\']($F35, $F36), $F34);',
				],
				38 => [
					'id'                     => '38',
					'type'                   => 'payment-single',
					'label'                  => 'Commission',
					'price'                  => '0.00',
					'format'                 => 'single',
					'min_price'              => '10.00',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F37_amount * 0.15',
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'mul\']($F37_amount, 0.15);',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'mul\']($F37_amount, 0.15);',
				],
				40 => [
					'id'                     => '40',
					'type'                   => 'payment-single',
					'label'                  => 'Total cost',
					'format'                 => 'single',
					'min_price'              => '10.00',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F37_amount	+	$F38_amount ',
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'plus\']($F37_amount, $F38_amount);',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'plus\']($F37_amount, $F38_amount);',
				],
				32 => [
					'id'                          => '32',
					'type'                        => 'date-time',
					'label'                       => 'End Date',
					'format'                      => 'date',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				34 => [
					'id'            => '34',
					'type'          => 'number-slider',
					'label'         => 'Persons',
					'min'           => '0',
					'max'           => '10',
					'default_value' => '0',
					'step'          => '1',
					'size'          => 'medium',
					'value_display' => 'Selected Value: {value}',
				],
				36 => [
					'id'                     => '36',
					'type'                   => 'number',
					'label'                  => 'Days',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => 'days(	$F31 ,	$F32	)',
					'calculation_code_php'   => '<?php

$_RETVAL = $_FUNCTION[\'days\'][\'func\']($F31, $F32);',
					'calculation_code_js'    => '

$_RETVAL = $_FUNCTION[\'days\']($F31, $F32);',
				],
				41 => [
					'id'          => '41',
					'type'        => 'pagebreak',
					'next'        => 'Next',
					'prev_toggle' => '1',
					'prev'        => 'Previous',
				],
				42 => [
					'id'            => '42',
					'type'          => 'content',
					'content'       => '<h2>Car selling</h2>',
					'label_disable' => '1',
					'size'          => 'medium',
				],
				47 => [
					'label'      => 'Layout',
					'name'       => 'Layout',
					'label_hide' => '1',
					'size'       => 'large',
					'preset'     => '33-33-33',
					'columns'    => [
						0 => [
							'width_preset' => '33',
							'fields'       => [
								0 => 43,
							],
						],
						1 => [
							'width_preset' => '33',
							'fields'       => [
								0 => 44,
								1 => 45,
								2 => 46,
								3 => 48,
								4 => 49,
								5 => 51,
								6 => 52,
								7 => 53,
								8 => 54,
							],
						],
						2 => [
							'width_preset' => '33',
							'fields'       => [],
						],
					],
					'id'         => '47',
					'type'       => 'layout',
				],
				43 => [
					'id'                   => '43',
					'type'                 => 'radio',
					'label'                => 'Car model',
					'choices'              => [
						1 => [
							'label'      => 'Audi',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						2 => [
							'label'      => 'Ford',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						3 => [
							'label'      => 'BMW',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
						4 => [
							'label'      => 'VW',
							'icon'       => 'face-smile',
							'icon_style' => 'regular',
						],
					],
					'choices_images_style' => 'modern',
					'choices_icons_color'  => '#066aab',
					'choices_icons_size'   => 'large',
					'choices_icons_style'  => 'default',
				],
				44 => [
					'id'                => '44',
					'type'              => 'number',
					'label'             => 'Price A',
					'size'              => 'medium',
					'default_value'     => '50',
					'conditional_logic' => '1',
					'conditional_type'  => 'show',
					'conditionals'      => [
						0 => [
							0 => [
								'field'    => '43',
								'operator' => '==',
								'value'    => '2',
							],
						],
					],
				],
				45 => [
					'id'                     => '45',
					'type'                   => 'number',
					'label'                  => 'Price B',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => 'if ( $F43 == \'BMW\' ): 125
	elseif ( $F43	== \'VW\' ): 100
	elseif ( $F43	== \'Audi\' ): 75
	elseif ( $F43 == \'Ford\' ):	 $F44
		else: 0
			endif; ',
					'conditional_logic'      => '1',
					'conditional_type'       => 'hide',
					'conditionals'           => [
						0 => [
							0 => [
								'field'    => '43',
								'operator' => '==',
								'value'    => '2',
							],
						],
					],
					'calculation_code_php'   => '<?php

if ($F43 == \'BMW\') {
		$_RETVAL = 125;
} elseif ($F43 == \'VW\') {
		$_RETVAL = 100;
} elseif ($F43 == \'Audi\') {
		$_RETVAL = 75;
} elseif ($F43 == \'Ford\') {
		$_RETVAL = $F44;
} else {
		$_RETVAL = 0;
}',
					'calculation_code_js'    => '

if ($F43 == \'BMW\') {
		$_RETVAL = 125;
}
else if ($F43 == \'VW\') {
		$_RETVAL = 100;
}
else if ($F43 == \'Audi\') {
		$_RETVAL = 75;
}
else if ($F43 == \'Ford\') {
		$_RETVAL = $F44;
} else {
		$_RETVAL = 0;
}',
				],
				46 => [
					'id'                => '46',
					'type'              => 'textarea',
					'label'             => 'Paragraph Text',
					'description'       => 'Triggered by calculation, show if Number Field B price > 99',
					'size'              => 'medium',
					'limit_count'       => '1',
					'limit_mode'        => 'characters',
					'conditional_logic' => '1',
					'conditional_type'  => 'show',
					'conditionals'      => [
						0 => [
							0 => [
								'field'    => '44',
								'operator' => '>',
								'value'    => '99',
							],
							1 => [
								'field'    => '45',
								'operator' => '>',
								'value'    => '99',
							],
						],
					],
				],
				48 => [
					'id'                     => '48',
					'type'                   => 'payment-single',
					'label'                  => 'Price A final ',
					'format'                 => 'single',
					'min_price'              => '10.00',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F44 + $F44	* 0.15',
					'conditional_logic'      => '1',
					'conditional_type'       => 'show',
					'conditionals'           => [
						0 => [
							0 => [
								'field'    => '44',
								'operator' => '!e',
							],
						],
					],
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'plus\']($F44, $_INNER_FUNC[\'mul\']($F44, 0.15));',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'plus\']($F44, $_INNER_FUNC[\'mul\']($F44, 0.15));',
				],
				49 => [
					'id'                     => '49',
					'type'                   => 'payment-single',
					'label'                  => 'Price B final ',
					'price'                  => '0.00',
					'format'                 => 'single',
					'min_price'              => '10.00',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F45	+	$F45 * 0.15',
					'conditional_logic'      => '1',
					'conditional_type'       => 'show',
					'conditionals'           => [
						0 => [
							0 => [
								'field'    => '45',
								'operator' => '!e',
							],
						],
					],
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'plus\']($F45, $_INNER_FUNC[\'mul\']($F45, 0.15));',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'plus\']($F45, $_INNER_FUNC[\'mul\']($F45, 0.15));',
				],
				51 => [
					'id'                     => '51',
					'type'                   => 'number',
					'label'                  => 'VAT Price A',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F48_amount	* 1.24',
					'conditional_logic'      => '1',
					'conditional_type'       => 'show',
					'conditionals'           => [
						0 => [
							0 => [
								'field'    => '44',
								'operator' => '!e',
							],
						],
					],
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'mul\']($F48_amount, 1.24);',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'mul\']($F48_amount, 1.24);',
				],
				52 => [
					'id'                     => '52',
					'type'                   => 'number',
					'label'                  => 'VAT Price B',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => '	$F49_amount	 * 1.24',
					'conditional_logic'      => '1',
					'conditional_type'       => 'show',
					'conditionals'           => [
						0 => [
							0 => [
								'field'    => '45',
								'operator' => '!e',
							],
						],
					],
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'mul\']($F49_amount, 1.24);',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'mul\']($F49_amount, 1.24);',
				],
				53 => [
					'id'                     => '53',
					'type'                   => 'payment-single',
					'label'                  => 'Total price A',
					'format'                 => 'single',
					'min_price'              => '10.00',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => ' $F51	+	$F48_amount ',
					'conditional_logic'      => '1',
					'conditional_type'       => 'show',
					'conditionals'           => [
						0 => [
							0 => [
								'field'    => '43',
								'operator' => '==',
								'value'    => '2',
							],
						],
					],
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'plus\']($F51, $F48_amount);',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'plus\']($F51, $F48_amount);',
				],
				54 => [
					'id'                     => '54',
					'type'                   => 'payment-single',
					'label'                  => 'Total price B',
					'price'                  => '0.00',
					'format'                 => 'single',
					'min_price'              => '10.00',
					'size'                   => 'medium',
					'calculation_is_enabled' => '1',
					'calculation_code'       => '	$F52	 +	$F49_amount ',
					'conditional_logic'      => '1',
					'conditional_type'       => 'hide',
					'conditionals'           => [
						0 => [
							0 => [
								'field'    => '43',
								'operator' => '==',
								'value'    => '2',
							],
						],
					],
					'calculation_code_php'   => '<?php

$_RETVAL = $_INNER_FUNC[\'plus\']($F52, $F49_amount);',
					'calculation_code_js'    => '

$_RETVAL = $_INNER_FUNC[\'plus\']($F52, $F49_amount);',
				],
				23 => [
					'id'       => '23',
					'type'     => 'pagebreak',
					'position' => 'bottom',
				],
			],
			'field_id'   => 55,
			'settings'   => [
				'form_title'                                       => 'Calculations Test Form	',
				'submit_text'                                      => 'Submit',
				'submit_text_processing'                           => 'Sending...',
				'notification_enable'                              => '1',
				'notifications'                                    => [
					1 => [
						'notification_name'                      => 'Default Notification',
						'email'                                  => '{admin_email}',
						'subject'                                => 'New Entry: Blank Form (ID #7697)',
						'sender_name'                            => 'new1',
						'sender_address'                         => '{admin_email}',
						'message'                                => '{all_fields}',
						'file_upload_attachment_fields'          => [],
						'entry_csv_attachment_enable'            => '1',
						'entry_csv_attachment_entry_information' => [
							0 => 'all_fields',
							1 => 'form_name',
							2 => 'form_id',
						],
						'entry_csv_attachment_file_name'         => 'entry-details',
					],
				],
				'confirmations'                                    => [
					1 => [
						'name'                        => 'Default Confirmation',
						'type'                        => 'message',
						'message'                     => '<p>Thanks for contacting us! We will be in touch with you shortly.</p>',
						'message_scroll'              => '1',
						'page'                        => '3938',
						'message_entry_preview'       => '1',
						'message_entry_preview_style' => 'table_compact',
					],
				],
				'lead_forms'                                       => [
					'accent_color'   => '#0299ed',
					'field_borders'  => '#cccccc',
					'primary_text'   => '#444444',
					'secondary_text' => '#777777',
				],
				'antispam_v3'                                      => '1',
				'anti_spam'                                        => [
					'time_limit'     => [
						'duration' => '3',
					],
					'country_filter' => [
						'action'        => 'allow',
						'country_codes' => [],
						'message'       => 'Sorry, this form does not accept submissions from your country.',
					],
					'keyword_filter' => [
						'message' => 'Sorry, your message can\'t be submitted because it contains prohibited words.',
					],
				],
				'registration_role'                                => 'subscriber',
				'registration_activation_method'                   => 'user',
				'registration_email_user_activation_subject'       => '{site_name} Activation Required',
				'registration_email_user_activation_message'       => 'IMPORTANT: You must activate your account before you can log in.
Please visit the link below.

{url_user_activation}',
				'registration_hide_message'                        => 'Hi {user_first_name}, you’re already logged in. <a href="{url_logout}">Log out</a>.',
				'registration_email_admin_subject'                 => '{site_name} New User Registration',
				'registration_email_admin_message'                 => 'New user registration on your site {site_name}:

Username: {user_registration_login}
Email: {user_registration_email}',
				'registration_email_user_subject'                  => '{site_name} Your username and password info',
				'registration_email_user_message'                  => 'Username: {user_registration_login}
Password: {user_registration_password}
{url_login}

',
				'registration_email_user_after_activation_subject' => '{site_name} Your account was successfully activated',
				'registration_email_user_after_activation_message' => 'You can log in with your credentials now.

{url_login}',
				'form_locker_verification_type'                    => 'password',
				'form_locker_age'                                  => '18',
				'form_locker_age_criteria'                         => '>=',
				'form_locker_user_entry_email_duration'            => 'day_start',
				'save_resume_link_text'                            => 'Save and Resume Later',
				'save_resume_disclaimer_enable'                    => '1',
				'save_resume_disclaimer_message'                   => 'Heads up! Saving your progress now will store a copy of your entry on this server and the site owner may have access to it. For security reasons, sensitive information such as credit cards and mailing addresses, along with file uploads will have to be re-entered when you resume.',
				'save_resume_confirmation_message'                 => 'Your form entry has been saved and a unique link has been created which you can access to resume this form.

Enter your email address to receive the link via email. Alternatively, you can copy and save the link below.

Please note, this link should not be shared and will expire in 30 days, afterwards your form entry will be deleted.',
				'save_resume_enable_resume_link'                   => '1',
				'save_resume_enable_email_notification'            => '1',
				'save_resume_email_notification_message'           => 'Thank you for saving {form_name}. Click the link below to resume the form from any device.

{resume_link}

Remember, the link should not be shared and will expire in 30 days.',
				'save_resume_email_settings_message'               => 'A link to resume this form has been sent to the email address provided.

Please remember, the link should not be shared and will expire in 30 days.',
				'form_tags'                                        => [],
			],
			'lead_forms' => [
				'iif_id_ref' => '',
			],
			'providers'  => [
				'google-sheets' => [],
			],
			'payments'   => [
				'stripe'          => [
					'payment_description' => '',
					'receipt_email'       => '',
					'customer_email'      => '',
					'customer_name'       => '',
					'recurring'           => [
						0 => [
							'name'          => 'Plan Name',
							'period'        => 'yearly',
							'email'         => '',
							'customer_name' => '',
						],
					],
				],
				'paypal_commerce' => [
					'name'                => '',
					'billing_email'       => '',
					'billing_address'     => '',
					'shipping_address'    => '',
					'payment_description' => '',
					'recurring'           => [
						0 => [
							'pp_product_id'    => '',
							'pp_plan_id'       => '',
							'name'             => 'Plan Name #1',
							'product_type'     => 'digital',
							'recurring_times'  => 'yearly',
							'total_cycles'     => '0',
							'shipping_address' => '',
						],
					],
				],
				'paypal_standard' => [
					'production_email' => '',
					'sandbox_email'    => '',
					'mode'             => 'production',
					'transaction'      => 'product',
					'cancel_url'       => '',
					'shipping'         => '0',
				],
				'square'          => [
					'payment_description' => '',
					'buyer_email'         => '',
					'billing_name'        => '',
					'billing_address'     => '',
				],
				'authorize_net'   => [
					'payment_description'      => '',
					'receipt_email'            => '',
					'customer_name'            => '',
					'customer_billing_address' => '',
					'recurring'                => [
						'name'                     => '',
						'period'                   => 'yearly',
						'email'                    => '',
						'customer_name'            => '',
						'customer_billing_address' => '',
					],
				],
			],
			'meta'       => [
				'template' => 'calculations_test_form',
			],
		];
		// phpcs:enable WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned, WordPress.Arrays.MultipleStatementAlignment.LongIndexSpaceBeforeDoubleArrow
	}
}
