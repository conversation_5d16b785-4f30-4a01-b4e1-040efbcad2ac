window.WPFormsIframe=window.WPFormsIframe||(o=>{let a={update(t={}){let r=this;if(!r.classList.contains("wpforms-iframe-updated")){r.classList.add("wpforms-iframe-updated");let e=o.createElement("iframe");e.onload=function(){a.iframeStyles(e,t),a.iframeBody(e,r.innerHTML),a.iframeFullHeight(e),r.remove()},r.after(e)}},iframeStyles(e,t={}){var r=e.contentWindow.document,n=r.querySelector("head"),r=r.createElement("style"),i=getComputedStyle(o.body).fontFamily,{color:t="inherit"}=t;r.setAttribute("type","text/css"),r.innerHTML="body.mce-content-body {\tmargin: 0 !important;\tbackground-color: transparent !important;\tfont-family: "+i+";\tcolor: "+t+";}*:first-child {\tmargin-top: 0}*:last-child {\tmargin-bottom: 0}pre {\twhite-space: pre !important;\toverflow-x: auto !important;}a,img {\tdisplay: inline-block;}",n.appendChild(r),a.entryPreviewAddLinkElement(e)},entryPreviewAddLinkElement(i){if(wpforms_settings.entry_preview_iframe_styles){let r=i.contentWindow.document,n=r.querySelector("head");wpforms_settings.entry_preview_iframe_styles.forEach(function(e){var t=r.createElement("link");t.setAttribute("rel","stylesheet"),t.setAttribute("href",e),t.onload=function(){a.iframeFullHeight(i)},n.appendChild(t)})}},iframeBody(e,t){var e=e.contentWindow.document,r=e.querySelector("body"),e=e.createElement("div");e.classList.add("wpforms-iframe-wrapper"),r.append(e),e.innerHTML=t,r.classList.add("mce-content-body"),r.querySelectorAll("a").forEach(function(e){e.setAttribute("rel","noopener"),e.hasAttribute("target")||e.setAttribute("target","_top")}),r.querySelectorAll("table")?.forEach?.(function(e){e.classList.add("mce-item-table")})},iframeFullHeight(e){var t;e.contentWindow&&e.contentWindow.document&&(t=e.contentWindow.document.querySelector(".wpforms-iframe-wrapper"),e.style.height=t.scrollHeight+"px")}};return a})(document);