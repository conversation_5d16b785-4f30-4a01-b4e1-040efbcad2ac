<?php
/**
 * WordPress Object Cache Implementation.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper\Cache;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * WordPress object cache implementation.
 *
 * @since {VERSION}
 */
class ObjectCache implements CacheInterface {

	/**
	 * Cache group.
	 *
	 * @since {VERSION}
	 *
	 * @var string
	 */
	private $group;

	/**
	 * Default expiration time.
	 *
	 * @since {VERSION}
	 *
	 * @var int
	 */
	private $default_expiration;

	/**
	 * Constructor.
	 *
	 * @since {VERSION}
	 *
	 * @param string $group              Cache group.
	 * @param int    $default_expiration Default expiration time in seconds.
	 */
	public function __construct( string $group = 'wpf_support_helper', int $default_expiration = 300 ) {

		$this->group              = $group;
		$this->default_expiration = $default_expiration;
	}

	/**
	 * Get a cached value.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key           Cache key.
	 * @param mixed  $default_value Default value if key doesn't exist.
	 *
	 * @return mixed Cached value or default.
	 */
	public function get( string $key, $default_value = null ) {

		$value = wp_cache_get( $this->get_prefixed_key( $key ), $this->group );

		return $value !== false ? $value : $default_value;
	}

	/**
	 * Set a cached value.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key        Cache key.
	 * @param mixed  $value      Value to cache.
	 * @param int    $expiration Expiration time in seconds (0 = use default).
	 *
	 * @return bool True on success, false on failure.
	 */
	public function set( string $key, $value, int $expiration = 0 ): bool {

		if ( $expiration === 0 ) {
			$expiration = $this->default_expiration;
		}

		return wp_cache_set( $this->get_prefixed_key( $key ), $value, $this->group, $expiration );
	}

	/**
	 * Delete a cached value.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key Cache key.
	 *
	 * @return bool True on success, false on failure.
	 */
	public function delete( string $key ): bool {

		return wp_cache_delete( $this->get_prefixed_key( $key ), $this->group );
	}

	/**
	 * Check if a key exists in cache.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key Cache key.
	 *
	 * @return bool True if key exists, false otherwise.
	 */
	public function has( string $key ): bool {

		return wp_cache_get( $this->get_prefixed_key( $key ), $this->group ) !== false;
	}

	/**
	 * Clear all cached values.
	 *
	 * @since {VERSION}
	 *
	 * @return bool True on success, false on failure.
	 */
	public function clear(): bool {

		// WordPress object cache doesn't have a group flush method by default.
		// We'll use wp_cache_flush() which clears all cache.
		return wp_cache_flush();
	}

	/**
	 * Get prefixed cache key.
	 *
	 * @since {VERSION}
	 *
	 * @param string $key Original cache key.
	 *
	 * @return string Prefixed cache key.
	 */
	private function get_prefixed_key( string $key ): string {

		return self::CACHE_PREFIX . $key;
	}
}
