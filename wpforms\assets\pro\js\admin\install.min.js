let WPFormsAdminInstall=window.WPFormsAdminInstall||((t,e)=>{let i={init(){e(i.ready)},ready(){i.initAddonCheckboxes(),i.events()},events(){i.updateButtonAfterInstall(),i.clickMoreDetailsButton()},initAddonCheckboxes(){e(".wpforms-update-message.notice-error").each(function(){e(this).closest(".plugin-update-tr").prev("tr").find(".check-column").css("pointer-events","none").find('input[type="checkbox"]').prop("disabled",!0)})},updateButtonAfterInstall(){e(t).on("wp-plugin-install-success",function(t,n){"wpforms-lite"===n.slug&&e('.plugin-action-buttons a[data-slug="wpforms-lite"]').replaceWith('<button type="button" class="button button-disabled" disabled="disabled">'+wpforms_install_data.activate+"</button>")})},clickMoreDetailsButton(){e(t).on("thickbox:iframe:loaded","#TB_window",function(){var t=e("#TB_iframeContent"),n=t.attr("src");n.includes("update=disabled")?i.incompatibleAddonDisableUpdateButton(t):n.includes("wpforms-lite")&&(n=t.contents().find("#plugin-information-footer").find('.button[data-slug="wpforms-lite"]')).hasClass("activate-now")&&(n.before('<strong style="display:inline-block;margin-top:10px;">'+wpforms_install_data.lite_version_notice+"</strong>"),n.replaceWith('<button type="button" id="plugin_install_from_iframe" class="right button button-disabled" disabled="disabled">'+wpforms_install_data.activate+"</button>"))})},incompatibleAddonDisableUpdateButton(t){t.contents().find('#plugin-information-footer .button[data-slug^="wpforms-"]').prop("disabled",!0).css({"pointer-events":"none",tabindex:"-1",opacity:"0.5"})}};return i})(document,(window,jQuery));WPFormsAdminInstall.init();