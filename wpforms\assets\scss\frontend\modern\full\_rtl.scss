// WPForms Modern Full styles.
//
// RTL support.
//
// @since 1.8.7

body.rtl {

	div.wpforms-container-full {

		.wpforms-form {

			.wpforms-page-indicator {

				&.progress {

					.wpforms-page-indicator-page-progress-wrap {
						transform: rotate(180deg);
					}
				}

				&.circles {

					.wpforms-page-indicator-page-number {
						margin: 0 0 0 $spacing_s;
					}

					.wpforms-page-indicator-page {
						margin: 0 0 0 $spacing_m;
					}
				}

			}

			em.wpforms-error {
				padding: 0 $spacing_xs 0 0;

				&:before {
					left: $spacing_xs;
					right: auto;
				}
			}

			.wpforms-field.wpforms-field-select-style-modern {

				.choices__list--single .choices__item {
					padding-right: 0;
					padding-left: $spacing_m;
				}

				.choices .choices__inner {
					padding: 0 7px 0 24px;

					.choices__list--single {
						padding: 0 4px 0 16px;
					}
				}

				.choices[data-type*="select-multiple"]:after {
					right: auto;
					left: 12px;
				}

				.choices[data-type*="select-one"] {

					&:after {
						right: auto;
						left: 12px;
					}

					.choices__button {
						right: auto;
						left: 0;
						margin-right: 0;
						margin-left: 25px;
					}
				}

				.choices[data-type*="select-multiple"] {

					.choices__button {
						margin-right: $spacing_xs;
						border-right: none;
					}
				}
			}

			.wpforms-field.wpforms-field-select-style-classic {

				select {
					background-position: calc(0% + 12px) 50%, calc(0% + 17px) 50%;
					padding: 0 12px 0 24px;
				}
			}
		}
	}
}
