<?php
/**
 * View for the Hooks tab.
 *
 * @var string $nonce     WordPress nonce.
 * @var array  $templates List of all registered WPForms form templates.
 */
?>
<div class="generator-progress-bar"></div>
<form action="" id="generate_forms">
	<input type="hidden" name="nonce" value="<?php echo esc_html( $nonce ); ?>">

	<?php if ( ! empty( $templates ) ) : ?>
		<div class="wpforms-setting-row wpforms-setting-row-select">
			<div class="wpforms-setting-label"><label for="wpforms-setting-disable-css">Choose a template</label></div>
			<div class="wpforms-setting-field">
				<span class="choicesjs-select-wrap">
					<select class="choicesjs-select" name="template" id="templates"
						data-sorting="off"
						data-search="1">
						<option value="random" selected>Default: Random per each form</option>
						<option value="blank">Blank</option>
						<?php foreach ( $templates as $template ) : ?>
							<option value="<?php echo esc_attr( $template['slug'] ); ?>">
								<?php echo esc_html( $template['name'] ); ?>
							</option>
						<?php endforeach; ?>
					</select>
				</span>
			</div>
		</div>

		<div class="wpforms-setting-row wpforms-setting-row-text">
			<div class="wpforms-setting-label"><label for="wpforms-setting-disable-css">Count</label></div>
			<div class="wpforms-setting-field">
				<input type="text" name="count" value="100">
				<p class="desc">How many forms you want to generate based on selected templates.</p>
			</div>
		</div>
		<div class="wpforms-setting-row wpforms-setting-row-text">
			<div class="wpforms-setting-label"><label for="wpforms-setting-disable-css">Chunk size</label></div>
			<div class="wpforms-setting-field">
				<input type="text" name="chunk_size" value="10">
				<p class="desc">How many forms will be generated in one iteration.</p>
			</div>
		</div>
		<p class="submit wpforms-admin-page">
			<button id="generate-button" type="submit" class="wpforms-btn wpforms-btn-md wpforms-btn-orange">Generate</button>
		</p>
	<?php else : ?>
		<a href="<?php echo esc_url( admin_url( 'admin.php?page=wpforms-builder' ) ); ?>">Please make sure that you have at least one template.</a>
	<?php endif; ?>
</form>
