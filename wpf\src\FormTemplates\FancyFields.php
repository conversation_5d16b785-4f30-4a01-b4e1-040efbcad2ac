<?php

namespace WPForms\DevTools\FormTemplates;

use WPF<PERSON>_Template;

/**
 * WPForms DevTools template: Fancy Fields.
 *
 * @since 0.24
 */
class FancyFields extends WPForms_Template {

	/**
	 * Primary class constructor.
	 *
	 * @since 0.24
	 */
	public function init() {

		// Template name.
		$this->name = 'Fancy Fields';

		// Template slug.
		$this->slug = 'fancy_fields';

		// Template description.
		$this->description = '';

		// Template field and settings.
		// phpcs:disable WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned
		$this->data = [
			'fields'     => [
				12  => [
					'id'              => '12',
					'type'            => 'pagebreak',
					'position'        => 'top',
					'indicator'       => 'progress',
					'indicator_color' => '#066aab',
					'title'           => 'PHONE FIELD',
					'nav_align'       => 'left',
				],
				4   => [
					'id'          => '4',
					'type'        => 'phone',
					'label'       => 'Phone: Required With Description',
					'format'      => 'smart',
					'description' => 'This is description for the Phone field',
					'required'    => '1',
					'size'        => 'medium',
				],
				9   => [
					'id'          => '9',
					'type'        => 'phone',
					'label'       => 'Phone: Small With Placeholder',
					'format'      => 'smart',
					'size'        => 'small',
					'placeholder' => '000000000',
				],
				10  => [
					'id'            => '10',
					'type'          => 'phone',
					'label'         => 'Phone: Medium with Default Value',
					'format'        => 'smart',
					'size'          => 'medium',
					'default_value' => '000000000',
				],
				8   => [
					'id'     => '8',
					'type'   => 'phone',
					'label'  => 'Phone Smart: Large',
					'format' => 'smart',
					'size'   => 'large',
				],
				2   => [
					'id'     => '2',
					'type'   => 'phone',
					'label'  => 'Phone: US',
					'format' => 'us',
					'size'   => 'medium',
				],
				3   => [
					'id'     => '3',
					'type'   => 'phone',
					'label'  => 'Phone: International',
					'format' => 'international',
					'size'   => 'medium',
				],
				11  => [
					'id'    => '11',
					'type'  => 'pagebreak',
					'title' => 'ADDRESS FIELD',
					'next'  => 'Next',
				],
				14  => [
					'id'           => '14',
					'type'         => 'address',
					'label'        => 'Address: Required with Description',
					'scheme'       => 'us',
					'description'  => 'This is description for the Address field',
					'required'     => '1',
					'size'         => 'medium',
					'map_position' => 'above',
				],
				15  => [
					'id'           => '15',
					'type'         => 'address',
					'label'        => 'Address: International',
					'scheme'       => 'international',
					'size'         => 'medium',
					'map_position' => 'above',
				],
				20  => [
					'id'                   => '20',
					'type'                 => 'address',
					'label'                => 'Address: Small With Placeholders',
					'scheme'               => 'us',
					'size'                 => 'small',
					'address1_placeholder' => 'Adress Line 1',
					'address2_placeholder' => 'Address Line 2',
					'city_placeholder'     => 'City',
					'state_placeholder'    => 'State / Province / Region',
					'postal_placeholder'   => '000000',
					'map_position'         => 'above',
				],
				21  => [
					'id'               => '21',
					'type'             => 'address',
					'label'            => 'Address: Medium With Default Value',
					'scheme'           => 'us',
					'size'             => 'medium',
					'address1_default' => 'Adress Line 1',
					'address2_default' => 'Address Line 2',
					'city_default'     => 'City',
					'state_default'    => 'AZ',
					'postal_default'   => '85142',
					'map_position'     => 'above',
				],
				19  => [
					'id'                          => '19',
					'type'                        => 'address',
					'label'                       => 'Address: Large With Enabled Autocomplete',
					'scheme'                      => 'us',
					'size'                        => 'large',
					'enable_address_autocomplete' => '1',
					'display_map'                 => '1',
					'map_position'                => 'above',
				],
				22  => [
					'id'    => '22',
					'type'  => 'pagebreak',
					'title' => 'DATE / TIME FIELD',
					'next'  => 'Next',
				],
				23  => [
					'id'                          => '23',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time: Required With Description',
					'format'                      => 'date-time',
					'description'                 => 'This is description for the Date / Time field',
					'required'                    => '1',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				26  => [
					'id'                          => '26',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time: Small With Placeholders',
					'format'                      => 'date-time',
					'size'                        => 'small',
					'date_type'                   => 'datepicker',
					'date_placeholder'            => '00/00/0000',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_placeholder'            => '6:00 PM',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				31  => [
					'id'                          => '31',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time: Medium With Disabled Past Dates',
					'format'                      => 'date-time',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'date_disable_past_dates'     => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				28  => [
					'id'                          => '28',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time: Large',
					'format'                      => 'date-time',
					'size'                        => 'large',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				29  => [
					'id'                          => '29',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time: Date Dropdown',
					'format'                      => 'date-time',
					'size'                        => 'medium',
					'date_type'                   => 'dropdown',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				30  => [
					'id'                          => '30',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time: m/d/y format',
					'format'                      => 'date-time',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				33  => [
					'id'                          => '33',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time: d/m/y format',
					'format'                      => 'date-time',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'd/m/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				34  => [
					'id'                          => '34',
					'type'                        => 'date-time',
					'label'                       => 'Date / Time: Month day, year format',
					'format'                      => 'date-time',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'F j, Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				32  => [
					'id'                          => '32',
					'type'                        => 'date-time',
					'label'                       => 'Date: Limited days (Mondays only)',
					'format'                      => 'date',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'm/d/Y',
					'date_limit_days'             => '1',
					'date_limit_days_mon'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '06',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				35  => [
					'id'                          => '35',
					'type'                        => 'date-time',
					'label'                       => 'Time: Limited hours (6-7)',
					'format'                      => 'time',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'date_limit_days_sat'         => '1',
					'date_limit_days_sun'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'g:i A',
					'time_limit_hours'            => '1',
					'time_limit_hours_start_hour' => '06',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '07',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'am',
				],
				36  => [
					'id'                          => '36',
					'type'                        => 'date-time',
					'label'                       => 'Time: 24H Format',
					'format'                      => 'time',
					'size'                        => 'medium',
					'date_type'                   => 'datepicker',
					'date_format'                 => 'm/d/Y',
					'date_limit_days_mon'         => '1',
					'date_limit_days_tue'         => '1',
					'date_limit_days_wed'         => '1',
					'date_limit_days_thu'         => '1',
					'date_limit_days_fri'         => '1',
					'time_interval'               => '30',
					'time_format'                 => 'H:i',
					'time_limit_hours_start_hour' => '09',
					'time_limit_hours_start_min'  => '00',
					'time_limit_hours_start_ampm' => 'am',
					'time_limit_hours_end_hour'   => '18',
					'time_limit_hours_end_min'    => '00',
					'time_limit_hours_end_ampm'   => 'pm',
				],
				37  => [
					'id'    => '37',
					'type'  => 'pagebreak',
					'title' => 'WEBSITE / URL FIELD',
					'next'  => 'Next',
				],
				38  => [
					'id'          => '38',
					'type'        => 'url',
					'label'       => 'Website / URL: Required With Description',
					'description' => 'This is description for the Website / URL field',
					'required'    => '1',
					'size'        => 'medium',
				],
				44  => [
					'id'          => '44',
					'type'        => 'url',
					'label'       => 'Website / URL: Small With PlaceHolder',
					'size'        => 'small',
					'placeholder' => 'https://test.com',
				],
				45  => [
					'id'            => '45',
					'type'          => 'url',
					'label'         => 'Website / URL: Medium With	Default Value',
					'size'          => 'medium',
					'default_value' => 'https://test.com',
				],
				43  => [
					'id'          => '43',
					'type'        => 'url',
					'label'       => 'Website / URL: Large',
					'description' => 'This is description for the Website / URL field',
					'size'        => 'large',
				],
				46  => [
					'id'    => '46',
					'type'  => 'pagebreak',
					'title' => 'FILE UPLOAD FIELD',
					'next'  => 'Next',
				],
				47  => [
					'id'              => '47',
					'type'            => 'file-upload',
					'label'           => 'File Upload: Required	with Description',
					'description'     => 'This is description for the File Upload field',
					'max_file_number' => '1',
					'required'        => '1',
					'style'           => 'modern',
					'media_library'   => '1',
				],
				48  => [
					'id'              => '48',
					'type'            => 'file-upload',
					'label'           => 'File Upload with only .jpg allowed',
					'extensions'      => '.jpg',
					'max_file_number' => '1',
					'style'           => 'modern',
				],
				49  => [
					'id'              => '49',
					'type'            => 'file-upload',
					'label'           => 'File Upload with Max File Size',
					'max_size'        => '2',
					'max_file_number' => '1',
					'style'           => 'modern',
				],
				50  => [
					'id'              => '50',
					'type'            => 'file-upload',
					'label'           => 'File Upload with Max File Uploads',
					'max_file_number' => '5',
					'style'           => 'modern',
				],
				51  => [
					'id'              => '51',
					'type'            => 'file-upload',
					'label'           => 'File Upload: Classic',
					'max_file_number' => '1',
					'style'           => 'classic',
				],
				94  => [
					'id'    => '94',
					'type'  => 'pagebreak',
					'title' => 'ASSWOTRD',
					'next'  => 'Next',
				],
				93  => [
					'id'                      => '93',
					'type'                    => 'password',
					'label'                   => 'Password: Required With Description',
					'description'             => 'This is description for the Password field',
					'required'                => '1',
					'password-strength-level' => '3',
					'size'                    => 'medium',
				],
				95  => [
					'id'                      => '95',
					'type'                    => 'password',
					'label'                   => 'Password: Small With Placeholder',
					'password-strength-level' => '3',
					'size'                    => 'small',
					'placeholder'             => 'This is Placeholder for the Password Field',
				],
				96  => [
					'id'                      => '96',
					'type'                    => 'password',
					'label'                   => 'Password: Medium With Default Value',
					'password-strength-level' => '3',
					'size'                    => 'medium',
					'default_value'           => 'This is Default Value for the Password Field',
				],
				97  => [
					'id'                      => '97',
					'type'                    => 'password',
					'label'                   => 'Password: With Enabled Password Confirmation',
					'confirmation'            => '1',
					'password-strength-level' => '4',
					'size'                    => 'medium',
				],
				98  => [
					'id'                      => '98',
					'type'                    => 'password',
					'label'                   => 'Password: Large With Enabled Password Strength (copy)',
					'description'             => 'This is Default Value for the Password Field',
					'password-strength'       => '1',
					'password-strength-level' => '3',
					'size'                    => 'large',
				],
				102 => [
					'id'    => '102',
					'type'  => 'pagebreak',
					'title' => 'RICH TEXT',
					'next'  => 'Next',
				],
				101 => [
					'id'          => '101',
					'type'        => 'richtext',
					'label'       => 'Rich Text: Required With Description',
					'description' => 'This is the description for the Rich Text field',
					'required'    => '1',
					'style'       => 'full',
					'size'        => 'medium',
				],
				103 => [
					'id'            => '103',
					'type'          => 'richtext',
					'label'         => 'Rich Text: Small With Allow Media Uploads',
					'media_enabled' => '1',
					'style'         => 'full',
					'size'          => 'small',
				],
				104 => [
					'id'            => '104',
					'type'          => 'richtext',
					'label'         => 'Rich Text: Medium With Basic Field Style',
					'media_enabled' => '1',
					'style'         => 'basic',
					'size'          => 'medium',
				],
				105 => [
					'id'            => '105',
					'type'          => 'richtext',
					'label'         => 'Rich Text: Large With Full Style',
					'media_enabled' => '1',
					'style'         => 'full',
					'size'          => 'large',
				],
				54  => [
					'id'    => '54',
					'type'  => 'pagebreak',
					'title' => 'HTML AND CONTENT FIELDS',
					'next'  => 'Next',
				],
				55  => [
					'id'            => '55',
					'type'          => 'html',
					'code'          => '<table>
	<caption>
		Most nutritious fruits
	</caption>
	<thead>
		<tr>
			<th scope="col"> Fruit </th>
			<th scope="col"> Nutrients </th>
			<th scope="col"> Color </th>
		</tr>
	</thead>
	<tbody>
		<tr>
			<th scope="row"> Apple </th>
			<td> Calcium </td>
			<td style="color:green"> Green </td>
		</tr>
		<tr>
			<th scope="row"> Lemon </th>
			<td> Vitamin C </td>
			<td style="color:yellow"> Yellow </td>
		</tr>
		<tr>
			<th scope="row"> Strawberry </th>
			<td> Magnesium </td>
			<td style="color:red"> Red</td>
		</tr>
		<tr>
			<th scope="row"> Orange </th>
			<td> Calcium </td>
			<td style="color:orange"> Orange </td>
		</tr>
	</tbody>
</table>',
					'label_disable' => '1',
				],
				56  => [
					'id'            => '56',
					'type'          => 'divider',
					'label'         => 'Section Divider',
					'label_disable' => '1',
				],
				57  => [
					'id'            => '57',
					'type'          => 'content',
					'content'       => '<h1>CONTENT FIELD</h1>
Add Text and Images to Your Form With Ease
<h1>To get started, replace this text with your own.</h1>
<h2>To get started, replace this text with your own.</h2>
<h3>To get started, replace this text with your own.</h3>
<h4>To get started, replace this text with your own.</h4>
<h5>To get started, replace this text with your own.</h5>
<h6>To get started, replace this text with your own.</h6>
<pre>To get started, replace this text with your own.</pre>
<strong>To get started, replace this text with your own.</strong>

<em>To get started, replace this text with your own.</em>

<span style="text-decoration: underline;">To get started, replace this text with your own.</span>

<span style="text-decoration: line-through;">To get started, replace this text with your own</span>

<span style="color: #ff00ff;">To get started, replace this text with your own.</span>

<span style="color: #cc99ff;">To get started, replace this text with your own.</span>
<ul>
 	<li><span style="color: #cc99ff;"><strong>To get started, replace this text with your own.</strong></span></li>
 	<li><span style="color: #cc99ff;"><strong>To get started, replace this text with your own.</strong></span></li>
</ul>
<ol>
 	<li><span style="color: #cc99ff;"><strong>To get started, replace this text with your own.</strong></span></li>
 	<li><span style="color: #cc99ff;"><strong>To get started, replace this text with your own.</strong></span></li>
</ol>
<p style="text-align: right;"><span style="color: #cc99ff;"><strong>To get started, replace this text with your own.</strong></span></p>
<p style="text-align: center;"><span style="color: #cc99ff;"><strong>To get started, replace this text with your own.</strong></span></p>
<p style="text-align: left;"><span style="color: #cc99ff;"><strong>To get started, replace this text with your own.</strong></span></p>

<blockquote>To get started, replace this text with your own.</blockquote>
<img height="150" width="150" alt="" src="' . WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/content-field.png" class="wp-image-2813 size-thumbnail alignleft">

<img height="200" width="300" alt="" src="' . WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/content-field.png" class="wp-image-2813 size-medium aligncenter">

<img height="667" width="1000" alt="" src="' . WPFORMS_DEV_TOOLS_URL . '/assets/images/templates/content-field.png" class="wp-image-2813 size-full alignright">

&nbsp;',
					'label_disable' => '1',
					'size'          => 'medium',
				],
				58  => [
					'id'    => '58',
					'type'  => 'pagebreak',
					'title' => 'RATING FIELD',
					'next'  => 'Next',
				],
				59  => [
					'id'          => '59',
					'type'        => 'rating',
					'label'       => 'Rating: Required With Description',
					'description' => 'This is description for the Rating field',
					'scale'       => '5',
					'required'    => '1',
					'icon'        => 'star',
					'icon_size'   => 'medium',
					'icon_color'  => '#066aab',
				],
				60  => [
					'id'         => '60',
					'type'       => 'rating',
					'label'      => 'Rating: Star Small',
					'scale'      => '3',
					'icon'       => 'star',
					'icon_size'  => 'small',
					'icon_color' => '#2bad07',
				],
				61  => [
					'id'         => '61',
					'type'       => 'rating',
					'label'      => 'Rating: Heart Medium',
					'scale'      => '8',
					'icon'       => 'heart',
					'icon_size'  => 'medium',
					'icon_color' => '#ad0722',
				],
				62  => [
					'id'         => '62',
					'type'       => 'rating',
					'label'      => 'Rating: Thumb Large',
					'scale'      => '10',
					'icon'       => 'thumb',
					'icon_size'  => 'large',
					'icon_color' => '#066aab',
				],
				63  => [
					'id'         => '63',
					'type'       => 'rating',
					'label'      => 'Rating: Smiley Face Large',
					'scale'      => '10',
					'icon'       => 'smiley',
					'icon_size'  => 'large',
					'icon_color' => '#e4eb0e',
				],
				67  => [
					'id'    => '67',
					'type'  => 'pagebreak',
					'title' => 'SIGNATURE FIELD',
					'next'  => 'Next',
				],
				66  => [
					'id'          => '66',
					'type'        => 'signature',
					'label'       => 'Signature: Required with Description',
					'description' => 'This is description for the Signature field',
					'required'    => '1',
					'ink_color'   => '#000000',
					'size'        => 'large',
				],
				70  => [
					'id'        => '70',
					'type'      => 'signature',
					'label'     => 'Signature: Small',
					'ink_color' => '#eb0e0e',
					'size'      => 'small',
				],
				71  => [
					'id'        => '71',
					'type'      => 'signature',
					'label'     => 'Signature: Medium',
					'ink_color' => '#0e85ed',
					'size'      => 'medium',
				],
				72  => [
					'id'        => '72',
					'type'      => 'signature',
					'label'     => 'Signature: Large',
					'ink_color' => '#0eed24',
					'size'      => 'large',
				],
				68  => [
					'id'    => '68',
					'type'  => 'pagebreak',
					'title' => 'LIKERT SCALE FIELD',
					'next'  => 'Next',
				],
				73  => [
					'id'          => '73',
					'type'        => 'likert_scale',
					'label'       => 'Likert Scale: Required With Description ',
					'rows'        => [
						1 => '1',
						2 => '2',
						3 => '3',
						4 => '4',
						5 => '5',
					],
					'columns'     => [
						1 => 'Strongly Disagree',
						2 => 'Disagree',
						3 => 'Neutral',
						4 => 'Agree',
						5 => 'Strongly Agree',
					],
					'description' => 'This is description for the Likert Scale field',
					'required'    => '1',
					'survey'      => '1',
					'style'       => 'modern',
					'size'        => 'large',
				],
				75  => [
					'id'         => '75',
					'type'       => 'likert_scale',
					'label'      => 'Likert Scale: Single Row',
					'rows'       => [
						1 => 'Item #1',
						2 => 'Item #2',
						3 => 'Item #3',
					],
					'single_row' => '1',
					'columns'    => [
						1 => 'Strongly Disagree',
						2 => 'Disagree',
						3 => 'Neutral',
						4 => 'Agree',
						5 => 'Strongly Agree',
					],
					'survey'     => '1',
					'style'      => 'modern',
					'size'       => 'large',
				],
				77  => [
					'id'      => '77',
					'type'    => 'likert_scale',
					'label'   => 'Likert Scale: Classic',
					'rows'    => [
						1 => 'Item #1',
						2 => 'Item #2',
						3 => 'Item #3',
					],
					'columns' => [
						1 => 'Strongly Disagree',
						2 => 'Disagree',
						3 => 'Neutral',
						4 => 'Agree',
						5 => 'Strongly Agree',
					],
					'survey'  => '1',
					'style'   => 'classic',
					'size'    => 'large',
				],
				78  => [
					'id'      => '78',
					'type'    => 'likert_scale',
					'label'   => 'Likert Scale: Small Modern',
					'rows'    => [
						1 => 'Item #1',
						2 => 'Item #2',
						3 => 'Item #3',
					],
					'columns' => [
						1 => 'Strongly Disagree',
						2 => 'Disagree',
						3 => 'Neutral',
						4 => 'Agree',
						5 => 'Strongly Agree',
					],
					'survey'  => '1',
					'style'   => 'modern',
					'size'    => 'small',
				],
				79  => [
					'id'                 => '79',
					'type'               => 'likert_scale',
					'label'              => 'Likert Scale: Medium Modern With Multiple Responses Per Row',
					'rows'               => [
						1 => 'Item #1',
						2 => 'Item #2',
						3 => 'Item #3',
					],
					'multiple_responses' => '1',
					'columns'            => [
						1 => 'Strongly Disagree',
						2 => 'Disagree',
						3 => 'Neutral',
						4 => 'Agree',
						5 => 'Strongly Agree',
					],
					'survey'             => '1',
					'style'              => 'modern',
					'size'               => 'medium',
				],
				80  => [
					'id'                 => '80',
					'type'               => 'likert_scale',
					'label'              => 'Likert Scale: Large Modern',
					'rows'               => [
						1 => 'Item #1',
						2 => 'Item #2',
						3 => 'Item #3',
					],
					'multiple_responses' => '1',
					'columns'            => [
						1 => 'Strongly Disagree',
						2 => 'Disagree',
						3 => 'Neutral',
						4 => 'Agree',
						5 => 'Strongly Agree',
					],
					'survey'             => '1',
					'style'              => 'modern',
					'size'               => 'large',
				],
				81  => [
					'id'                 => '81',
					'type'               => 'likert_scale',
					'label'              => 'Likert Scale: Small Classic',
					'rows'               => [
						1 => 'Item #1',
						2 => 'Item #2',
						3 => 'Item #3',
					],
					'multiple_responses' => '1',
					'columns'            => [
						1 => 'Strongly Disagree',
						2 => 'Disagree',
						3 => 'Neutral',
						4 => 'Agree',
						5 => 'Strongly Agree',
					],
					'survey'             => '1',
					'style'              => 'classic',
					'size'               => 'small',
				],
				83  => [
					'id'                 => '83',
					'type'               => 'likert_scale',
					'label'              => 'Likert Scale: Medium Classic',
					'rows'               => [
						1 => 'Item #1',
						2 => 'Item #2',
						3 => 'Item #3',
					],
					'multiple_responses' => '1',
					'columns'            => [
						1 => 'Strongly Disagree',
						2 => 'Disagree',
						3 => 'Neutral',
						4 => 'Agree',
						5 => 'Strongly Agree',
					],
					'survey'             => '1',
					'style'              => 'classic',
					'size'               => 'medium',
				],
				82  => [
					'id'                 => '82',
					'type'               => 'likert_scale',
					'label'              => 'Likert Scale: Large Classic',
					'rows'               => [
						1 => 'Item #1',
						2 => 'Item #2',
						3 => 'Item #3',
					],
					'multiple_responses' => '1',
					'columns'            => [
						1 => 'Strongly Disagree',
						2 => 'Disagree',
						3 => 'Neutral',
						4 => 'Agree',
						5 => 'Strongly Agree',
					],
					'survey'             => '1',
					'style'              => 'classic',
					'size'               => 'large',
				],
				74  => [
					'id'    => '74',
					'type'  => 'pagebreak',
					'title' => 'NET PROMOTER SCORE FIELD',
					'next'  => 'Next',
				],
				84  => [
					'id'            => '84',
					'type'          => 'net_promoter_score',
					'label'         => 'Net Promoter Score: Required with Description',
					'description'   => 'This is description for the Net Promoter Score field',
					'required'      => '1',
					'survey'        => '1',
					'style'         => 'modern',
					'size'          => 'large',
					'lowest_label'  => 'Not at all Likely',
					'highest_label' => 'Extremely Likely',
				],
				86  => [
					'id'            => '86',
					'type'          => 'net_promoter_score',
					'label'         => 'Net Promoter Score: Small Modern',
					'survey'        => '1',
					'style'         => 'modern',
					'size'          => 'small',
					'lowest_label'  => 'Not at all Likely',
					'highest_label' => 'Extremely Likely',
				],
				87  => [
					'id'            => '87',
					'type'          => 'net_promoter_score',
					'label'         => 'Net Promoter Score: Medium Modern',
					'survey'        => '1',
					'style'         => 'modern',
					'size'          => 'medium',
					'lowest_label'  => 'Not at all Likely',
					'highest_label' => 'Extremely Likely',
				],
				88  => [
					'id'            => '88',
					'type'          => 'net_promoter_score',
					'label'         => 'Net Promoter Score: Large Modern',
					'survey'        => '1',
					'style'         => 'modern',
					'size'          => 'large',
					'lowest_label'  => 'Not at all Likely',
					'highest_label' => 'Extremely Likely',
				],
				89  => [
					'id'            => '89',
					'type'          => 'net_promoter_score',
					'label'         => 'Net Promoter Score: Small Classic',
					'survey'        => '1',
					'style'         => 'classic',
					'size'          => 'small',
					'lowest_label'  => 'Not at all Likely',
					'highest_label' => 'Extremely Likely',
				],
				91  => [
					'id'            => '91',
					'type'          => 'net_promoter_score',
					'label'         => 'Net Promoter Score: Medium Classic',
					'survey'        => '1',
					'style'         => 'classic',
					'size'          => 'medium',
					'lowest_label'  => 'Not at all Likely',
					'highest_label' => 'Extremely Likely',
				],
				90  => [
					'id'            => '90',
					'type'          => 'net_promoter_score',
					'label'         => 'Net Promoter Score: Large Classic',
					'survey'        => '1',
					'style'         => 'classic',
					'size'          => 'large',
					'lowest_label'  => 'Not at all Likely',
					'highest_label' => 'Extremely Likely',
				],
				85  => [
					'id'    => '85',
					'type'  => 'pagebreak',
					'title' => 'OTHER FIELDS',
					'next'  => 'Next',
				],
				64  => [
					'id'            => '64',
					'type'          => 'hidden',
					'label'         => 'Hidden Field',
					'label_disable' => '1',
					'default_value' => 'Default Value for Hidden Field',
					'label_hide'    => '1',
				],
				65  => [
					'id'          => '65',
					'type'        => 'captcha',
					'required'    => '1',
					'label'       => 'Custom Captcha: Math with Description',
					'format'      => 'math',
					'questions'   => [
						1 => [
							'question' => 'What is 7+4?',
							'answer'   => '11',
						],
					],
					'description' => 'This is description for the Custom Captcha field',
					'size'        => 'medium',
				],
				92  => [
					'id'          => '92',
					'type'        => 'captcha',
					'required'    => '1',
					'label'       => 'Custom Captcha: Question and Answer with Placeholder',
					'format'      => 'qa',
					'questions'   => [
						1 => [
							'question' => 'What is 7+4?',
							'answer'   => '11',
						],
					],
					'size'        => 'medium',
					'placeholder' => 'Custom Captcha Placeholder',
				],
				69  => [
					'id'                    => '69',
					'type'                  => 'entry-preview',
					'preview-notice-enable' => '1',
					'preview-notice'        => '<strong>This is a preview of your submission. It has not been submitted yet!</strong>
Please take a moment to verify your information. You can also go back to make changes.',
					'style'                 => 'basic',
				],
				13  => [
					'id'          => '13',
					'type'        => 'pagebreak',
					'position'    => 'bottom',
					'prev_toggle' => '1',
					'prev'        => 'Previous',
				],
			],
			'field_id'   => 106,
			'settings'   => [
				'form_title'                             => 'Fancy Fields',
				'submit_text'                            => 'Submit',
				'submit_text_processing'                 => 'Sending...',
				'ajax_submit'                            => '1',
				'notification_enable'                    => '1',
				'notifications'                          => [
					1 => [
						'notification_name'                      => 'Default Notification',
						'email'                                  => '{admin_email}',
						'subject'                                => 'New Entry: Blank Form (ID #3786)',
						'sender_name'                            => '10TEST',
						'sender_address'                         => '{admin_email}',
						'message'                                => '{all_fields}',
						'file_upload_attachment_fields'          => [],
						'entry_csv_attachment_entry_information' => [],
						'entry_csv_attachment_file_name'         => 'entry-details',
					],
				],
				'confirmations'                          => [
					1 => [
						'name'                        => 'Default Confirmation',
						'type'                        => 'message',
						'message'                     => '<p>Thanks for contacting us! We will be in touch with you shortly.</p>',
						'message_scroll'              => '1',
						'page'                        => '666',
						'message_entry_preview_style' => 'basic',
					],
				],
				'lead_forms'                             => [
					'accent_color'   => '#0299ed',
					'field_borders'  => '#cccccc',
					'primary_text'   => '#444444',
					'secondary_text' => '#777777',
				],
				'antispam_v3'                            => '1',
				'anti_spam'                              => [
					'time_limit'     => [
						'duration' => '3',
					],
					'country_filter' => [
						'action'        => 'allow',
						'country_codes' => [],
						'message'       => 'Sorry, this form does not accept submissions from your country.',
					],
					'keyword_filter' => [
						'message' => 'Sorry, your message can\'t be submitted because it contains prohibited words.',
					],
				],
				'form_pages_title'                       => 'Blank Form',
				'form_pages_footer'                      => 'This content is neither created nor endorsed by WPForms.',
				'form_pages_color_scheme'                => '#448ccb',
				'form_pages_style'                       => 'modern',
				'conversational_forms_title'             => 'Blank Form',
				'conversational_forms_color_scheme'      => '#448ccb',
				'conversational_forms_progress_bar'      => 'percentage',
				'save_resume_link_text'                  => 'Save and Resume Later',
				'save_resume_disclaimer_message'         => 'Heads up! Saving your progress now will store a copy of your entry on this server and the site owner may have access to it. For security reasons, sensitive information such as credit cards and mailing addresses, along with file uploads will have to be re-entered when you resume.',
				'save_resume_confirmation_message'       => 'Your form entry has been saved and a unique link has been created which you can access to resume this form.

Enter your email address to receive the link via email. Alternatively, you can copy and save the link below.

Please note, this link should not be shared and will expire in 30 days, afterwards your form entry will be deleted.',
				'save_resume_enable_resume_link'         => '1',
				'save_resume_enable_email_notification'  => '1',
				'save_resume_email_notification_message' => 'Thank you for saving {form_name}. Click the link below to resume the form from any device.

{resume_link}

Remember, the link should not be shared and will expire in 30 days.',
				'save_resume_email_settings_message'     => 'A link to resume this form has been sent to the email address provided.

Please remember, the link should not be shared and will expire in 30 days.',
				'form_tags'                              => [],
			],
			'lead_forms' => [
				'iif_id_ref' => '',
			],
			'payments'   => [
				'stripe'          => [
					'payment_description' => '',
					'receipt_email'       => '',
					'customer_email'      => '',
					'customer_name'       => '',
					'customer_address'    => '',
					'shipping_address'    => '',
					'recurring'           => [
						0 => [
							'name'             => 'Plan Name',
							'period'           => 'yearly',
							'email'            => '',
							'customer_name'    => '',
							'customer_address' => '',
						],
					],
				],
				'paypal_commerce' => [
					'name'                => '',
					'billing_email'       => '',
					'billing_address'     => '',
					'shipping_address'    => '',
					'payment_description' => '',
					'recurring'           => [
						0 => [
							'pp_product_id'    => '',
							'pp_plan_id'       => '',
							'name'             => 'Plan Name #1',
							'product_type'     => 'digital',
							'recurring_times'  => 'yearly',
							'total_cycles'     => '0',
							'shipping_address' => '',
						],
					],
				],
				'authorize_net'   => [
					'payment_description'      => '',
					'receipt_email'            => '',
					'customer_name'            => '',
					'customer_billing_address' => '',
					'recurring'                => [
						'name'                     => '',
						'period'                   => 'yearly',
						'email'                    => '',
						'customer_name'            => '',
						'customer_billing_address' => '',
					],
				],
			],
			'meta'       => [
				'template' => 'fancy_fields',
			],
			'providers'  => [
				'google-sheets' => [],
			],
		];
		// phpcs:enable WordPress.Arrays.MultipleStatementAlignment.DoubleArrowNotAligned
	}
}
