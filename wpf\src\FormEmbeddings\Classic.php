<?php

namespace WPForms\DevTools\FormEmbeddings;

/**
 * Class Classic.
 *
 * Embed Form into Classic Editor.
 *
 * @since 0.27
 */
class Classic extends Embeddings {

	/**
	 * Get post content.
	 *
	 * @since 0.27
	 *
	 * @param int $form_id Form ID.
	 *
	 * @return string
	 */
	protected function get_post_content( int $form_id ): string {

		return sprintf( '[wpforms id="%d"]', $form_id );
	}

	/**
	 * Get post meta.
	 *
	 * @since 0.27
	 *
	 * @param int $form_id Form ID.
	 *
	 * @return array
	 */
	protected function get_post_meta( int $form_id ): array {

		return [
			'_wp_page_template' => 'default',
		];
	}

	/**
	 * Get embed name.
	 *
	 * @since 0.27
	 *
	 * @return string
	 */
	protected function get_name(): string {

		return 'Classic Editor';
	}

	/**
	 * Get edit link.
	 *
	 * @since 0.27
	 *
	 * @param int $post_id Post ID.
	 *
	 * @return string
	 */
	public function get_edit_link( int $post_id ): string {

		return get_edit_post_link( $post_id );
	}
}
