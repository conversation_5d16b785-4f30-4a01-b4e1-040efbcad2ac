<?php
/**
 * Detector Interface.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper\Detectors\Base;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Interface for detection methods.
 *
 * @since {VERSION}
 */
interface DetectorInterface {

	/**
	 * Run the detection method.
	 *
	 * @since {VERSION}
	 *
	 * @return array Detection result with keys: detected, reason, additional_info.
	 */
	public function detect(): array;

	/**
	 * Get the detector name.
	 *
	 * @since {VERSION}
	 *
	 * @return string Detector name.
	 */
	public function get_name(): string;

	/**
	 * Get the detector description.
	 *
	 * @since {VERSION}
	 *
	 * @return string Detector description.
	 */
	public function get_description(): string;

	/**
	 * Check if detector can run in current environment.
	 *
	 * @since {VERSION}
	 *
	 * @return bool True if detector can run, false otherwise.
	 */
	public function supports(): bool;

	/**
	 * Get detector execution priority.
	 *
	 * Lower numbers indicate higher priority (executed first).
	 *
	 * @since {VERSION}
	 *
	 * @return int Priority level (1-100, default: 50).
	 */
	public function get_priority(): int;

	/**
	 * Get detector requirements.
	 *
	 * @since {VERSION}
	 *
	 * @return array Array of requirements with keys: functions, extensions, files, etc.
	 */
	public function get_requirements(): array;
}
