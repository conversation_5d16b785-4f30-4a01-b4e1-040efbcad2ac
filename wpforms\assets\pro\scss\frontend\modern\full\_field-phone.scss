// WPForms Modern Base styles.
//
// Phone field.
//
// @since 1.8.1

div.wpforms-container-full {

	.wpforms-field-phone {
		&-input-container {
			position: relative;

			&[data-format="smart"] {
				.wpforms-field-phone-country-container {
					display: flex;
				}

				input.primary-input {
					padding-inline-start: 52px;
				}
			}
		}

		&-country-container {
			position: absolute;
			height: 100%;
			top: 0;
			padding: 0 6px 0 8px;
			align-items: center;
			gap: 6px;
			// Overrides by "[data-format]" selector.
			display: none;
		}

		&-flag {
			height: 11px;
			width: 20px;
			box-shadow: 0 0 1px 0 #888;
			// USA flag.
			background: url( "../../../../images/phone/us-flag.png" ) no-repeat;
			background-size: 100%;
		}

		&-arrow {
			border-inline: calc( 5px / 2 ) solid transparent;
			border-top: 4px solid #555;
		}
	}
}
