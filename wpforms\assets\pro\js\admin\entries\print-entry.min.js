let WPFormsPrintEntryPage=window.WPFormsPrintEntryPage||((e,t,a)=>{let s={vars:{},cache:{getCookie:function(){return JSON.parse(wpCookies.get("wpforms_entry_print"))||{}},saveCookie:function(e,t){var o=s.cache.getCookie();o[e]=t,wpCookies.set("wpforms_entry_print",JSON.stringify(o))}},init:function(){a(t).on("load",function(){"function"==typeof a.ready.then?a.ready.then(s.load):s.load()})},load:function(){s.vars={$body:a("body"),$page:a(".print-preview"),$modeToggles:a(".toggle-mode"),$richTextFields:a(".wpforms-entry-field-value-richtext"),printButtonSelector:".print",closeButtonSelector:".close-window",settingsButtonSelector:".button-settings",settingsMenuSelector:".actions",toggleModeSelector:".toggle-mode",toggleSelector:".switch",activeClass:"active"},s.vars.$modeToggles.each(s.presetSettingsValues),s.vars.$richTextFields.each(s.loadRichText),s.bindEvents()},bindEvents:function(){a(e).on("click",s.vars.printButtonSelector,s.print).on("click",s.vars.closeButtonSelector,s.close).on("click",s.vars.settingsButtonSelector,s.toggleSettings).on("click",s.vars.toggleModeSelector,s.toggleMode)},toggleMode:function(){var e=a(this),t=e.data("mode"),e=e.find(s.vars.toggleSelector),o=!e.hasClass(s.vars.activeClass);e.toggleClass(s.vars.activeClass),"compact"===t&&s.disableMode("maintain-layout"),"maintain-layout"===t&&s.disableMode("compact"),s.vars.$page.toggleClass("wpforms-preview-mode-"+t),s.cache.saveCookie(t,o)},disableMode:function(e){a(s.vars.toggleModeSelector+'[data-mode="'+e+'"]').find(s.vars.toggleSelector).removeClass(s.vars.activeClass),s.vars.$page.removeClass(s.prepareModeClass(e)),s.cache.saveCookie(e,!1)},presetSettingsValues:function(){var e=a(this),t=e.data("mode"),e=e.find(s.vars.toggleSelector),o=s.cache.getCookie();Object.prototype.hasOwnProperty.call(o,t)&&o[t]&&(e.addClass(s.vars.activeClass),s.vars.$page.addClass(s.prepareModeClass(t)))},prepareModeClass:function(e){return"wpforms-preview-mode-"+e},print:function(e){e.preventDefault(),t.print()},close:function(e){e.preventDefault(),t.close()},loadRichText:function(){let e=this;var t=a(this);t.on("load",function(){s.iframeStyles(e),s.modifyRichTextLinks(e),s.updateRichTextTableClasses(e),s.updateRichTextIframeSize(e)}),t.attr("src",t.data("src"))},updateRichTextIframeSize:function(e){var t;e&&e.contentWindow&&(t=e.contentWindow.document.documentElement||!1)&&(t=t.querySelector(".mce-content-body").scrollHeight,e.style.height=t+"px")},modifyRichTextLinks:function(e){a(e).contents().find("a").attr({target:"_blank",rel:"noopener"})},updateRichTextTableClasses(e){a(e).contents().find("table").addClass("mce-item-table")},iframeStyles:function(e){var e=e.contentWindow.document,t=e.querySelector("head"),e=e.createElement("style"),o=s.vars.$body.css("font-family"),a=s.vars.$body.css("font-size"),n=s.vars.$body.css("line-height");e.setAttribute("type","text/css"),e.innerHTML="body.mce-content-body {\tmargin: 0 !important;\tbackground-color: transparent !important;\tfont-family: "+o+";\tfont-size: "+a+";\tline-height: "+n+";}*:first-child {\tmargin-top: 0}*:last-child {\tmargin-bottom: 0}ul, ol {\tpadding-inline-start: 30px;}li {\tlist-style-position: outside;}pre {\twhite-space: pre !important;\toverflow-x: auto !important;}a,img {\tdisplay: inline-block;}",t.appendChild(e)},toggleSettings:function(e){e.preventDefault(),a(this).toggleClass(s.vars.activeClass),a(s.vars.settingsMenuSelector).toggleClass(s.vars.activeClass)}};return s})(document,window,jQuery);WPFormsPrintEntryPage.init();