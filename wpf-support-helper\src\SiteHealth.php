<?php
/**
 * Site Health Integration Class.
 *
 * @since {VERSION}
 */

namespace WPForms\SupportHelper;

use WPForms\SupportHelper\Admin\SettingsPage;
use WPForms\SupportHelper\Detectors\ModSecurity\DetectionEngine;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Site Health integration functionality.
 *
 * @since {VERSION}
 */
class SiteHealth {

	/**
	 * Detection engine instance.
	 *
	 * @since {VERSION}
	 *
	 * @var DetectionEngine
	 */
	private $detector;

	/**
	 * Represents the settings page configuration or instance within the application.
	 *
	 * @since {VERSION}
	 *
	 * @var SettingsPage
	 */
	private $settings_page;

	/**
	 * Constructor for initializing dependencies and setting up hooks.
	 *
	 * @since {VERSION}
	 *
	 * @param DetectionEngine $detector      Detection engine instance.
	 * @param SettingsPage    $settings_page Settings page instance.
	 */
	public function __construct( DetectionEngine $detector, SettingsPage $settings_page ) {

		$this->detector      = $detector;
		$this->settings_page = $settings_page;

		$this->hooks();
	}

	/**
	 * Initialize Site Health integration.
	 *
	 * @since {VERSION}
	 */
	private function hooks(): void {

		add_filter( 'debug_information', [ $this, 'add_info_section' ] );
	}

	/**
	 * Add security detection section to Site Health Info.
	 *
	 * @since {VERSION}
	 *
	 * @param array $debug_info Array of all debug information.
	 *
	 * @return array Array with added security detection info section.
	 */
	public function add_info_section( array $debug_info ): array {

		// Run security detection.
		$detection_results = $this->detector->detect();

		// Create separate ModSecurity section.
		$debug_info['wpf_support_helper_modsecurity'] = [
			'label'       => __( 'ModSecurity Detection', 'wpf-support-helper' ),
			'description' => wp_kses(
				sprintf(
					/* translators: %s - URL to settings page. */
					__( 'Information about ModSecurity detection on this server. <a href="%1$s" target="_blank">See more details</a>.', 'wpf-support-helper' ),
					$this->settings_page->get_link()
				),
				[
					'a' => [
						'href'   => [],
						'target' => [],
					],
				]
			),
			'fields'      => $this->get_detection_fields( $detection_results ),
		];

		return $debug_info;
	}

	/**
	 * Get detection fields for Site Health display.
	 *
	 * @since {VERSION}
	 *
	 * @param array $detection_results Detection results from detector.
	 *
	 * @return array Fields array for Site Health.
	 */
	private function get_detection_fields( array $detection_results ): array {

		$fields = [];

		// Main detection status.
		$fields['modsecurity_detected'] = [
			'label' => __( 'ModSecurity Detected', 'wpf-support-helper' ),
			'value' => $detection_results['detected'] ? __( 'Yes', 'wpf-support-helper' ) : __( 'No', 'wpf-support-helper' ),
		];

		// Detection timestamp.
		$fields['detection_timestamp'] = [
			'label' => __( 'Last Detection Check', 'wpf-support-helper' ),
			'value' => $detection_results['timestamp'],
		];

		// Plugin version.
		$fields['plugin_version'] = [
			'label' => __( 'WPForms Support Helper Version', 'wpf-support-helper' ),
			'value' => WPF_SUPPORT_HELPER_VERSION,
		];

		return $fields;
	}
}
