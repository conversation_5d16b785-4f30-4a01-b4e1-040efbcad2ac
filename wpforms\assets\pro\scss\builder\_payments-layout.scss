// Fix for the layout field compatibility with the payment coupon field in the 100% column.
.wpforms-panel-fields .wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field-payment-coupon,
.wpforms-panel-fields .wpforms-field.wpforms-field-payment-coupon.wpforms-field-drag-to-column.wpforms-layout-column-100 {
	.wpforms-field-payment-coupon-wrapper {
		max-width: 60%;
	}

	@media screen and (max-width: 1280px) {
		.wpforms-field-payment-coupon-wrapper {
			max-width: 60%;
		}
	}
}

.wpforms-field-layout .wpforms-field-layout-columns .wpforms-layout-column .wpforms-field.wpforms-field-payment-coupon {
	.wpforms-field-payment-coupon-wrapper {
		input[type=text].wpforms-field-payment-coupon-input {
			min-width: 80px !important;
		}
	}
}

// Fix for the layout field compatibility with the payment square field in the 100% column.
.wpforms-panel-fields {
	.wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field,
	.wpforms-field.wpforms-field-drag-to-column.wpforms-field-drag-to-column-100 {
		.wpforms-square-cardnumber-wrapper {
			width: 60% !important;
			min-width: auto !important;
			flex-direction: column !important;
		}

		&.size-large .wpforms-square-cardnumber-wrapper {
			width: 100% !important;
			min-width: auto !important;
			flex-direction: row !important;

			.card-number {
				border: none !important;
			}
		}

		&.size-medium .wpforms-square-cardnumber-wrapper {
			width: 60% !important;
			min-width: auto !important;
			flex-direction: column !important;
		}

		&.size-small .wpforms-square-cardnumber-wrapper {
			width: 25% !important;
			min-width: 250px !important;
			flex-direction: column !important;
		}
	}
}

// Fix for the layout field compatibility with the PPC field in the 100% column.
#wpforms-panel-fields,
#wpforms-panel-revisions {
	.wpforms-field-wrap {
		.wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field,
		.wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 {
			.wpforms-paypal-commerce-credit-card-fields {
				width: $field_size_medium !important;
			}

			&.size-small .wpforms-paypal-commerce-credit-card-fields {
				width: $field_size_small !important;
			}

			&.size-large .wpforms-paypal-commerce-credit-card-fields {
				width: $field_size_large !important;
			}
		}
	}
}

// Fix for the layout field compatibility with the Authorize field in the 100% column.
.wpforms-panel-fields {
	.wpforms-field.wpforms-field-authorize_net {

		input[type=text],
		select {
			width: 100% !important;
			min-width: initial;
		}
	}
}

// Fix for the layout field compatibility with the signature field in the 100% column.
.wpforms-panel-fields {
	.wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field,
	.wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 {
		&.wpforms-field-signature {
			&.size-medium .wpforms-signature-wrap {
				width: 65%;
			}

			&.size-small .wpforms-signature-wrap {
				width: 25%;
			}
		}
	}
}

// Fix for the layout field compatibility with the geolocation field in the 100% column.
.wpforms-field-layout .wpforms-layout-column.wpforms-layout-column-100 .wpforms-field,
.wpforms-field.wpforms-field-drag-to-column.wpforms-layout-column-100 {
	&.size-small > .wpforms-geolocation-map {
		max-width: $field_size_small;
		min-width: $field_min_width;
	}

	&.size-medium > .wpforms-geolocation-map {
		max-width: $field_size_medium;
	}

	&.size-large > .wpforms-geolocation-map {
		max-width: $field_size_large;
	}
}
