export default function(a,r){let l=wpforms_ai_form_generator,i={el:{},ajaxError:"Form Generator AJAX error:",init(){i.el.$doc=r(document),i.el.$templateCard=r("#wpforms-template-generate"),i.events()},events(){i.el.$doc.on("change",".wpforms-ai-forms-install-addons-modal-dismiss",i.dismissAddonsModal)},openAddonsModal(n){n?.preventDefault();let o="install"===l.addonsAction;n=o?l.addons.installContent:l.addons.activateContent,n={title:l.addons.installTitle,content:n,type:"purple",icon:"fa fa-info-circle",buttons:{confirm:{text:o?l.addons.installConfirmButton:l.addons.activateConfirmButton,btnClass:"btn-confirm",keys:["enter"],action(){var n=o?l.addons.installing:l.addons.activating;return this.$$confirm.prop("disabled",!0).html('<i class="wpforms-loading-spinner wpforms-loading-white wpforms-loading-inline"></i>'+n),this.$$cancel.prop("disabled",!0),i.installAddonsAjax(this),!1}},cancel:{text:l.addons.cancelButton,keys:["esc"],btnClass:"btn-cancel",action(){i.updateGenerateFormButton(!1),setTimeout(()=>{a.state.panelOpen=!0},250)}}},onOpenBefore(){var n=`
						<label class="jconfirm-checkbox">
							<input type="checkbox" class="jconfirm-checkbox-input wpforms-ai-forms-install-addons-modal-dismiss">
							${l.addons.dontShow}
						</label>
					`;this.$body.addClass("wpforms-ai-forms-install-addons-modal").find(".jconfirm-buttons").after(n)}};r.confirm(n)},installAddonsAjax(n){function o(n){n.success||wpf.debug(i.ajaxError,n.data.error??n.data),n.success||t||(t=!0,i.openErrorModal({title:"install"===l.addonsAction?l.addons.addonsInstallErrorTitle:l.addons.addonsActivateErrorTitle,content:l.addons.addonsInstallError}))}let a=null,t=!1;function e(n){var o;t||(n=n.responseText||l.addons.addonsInstallErrorNetwork,o=l.addons.addonsInstallError,o+=n&&"error"!==n?"<br>"+n:"",wpf.debug(i.ajaxError,o),i.openErrorModal({title:"install"===l.addonsAction?l.addons.addonsInstallErrorTitle:l.addons.addonsActivateErrorTitle,content:o}),t=!0)}for(var s in WPFormsBuilder.setCloseConfirmation(!1),l.addonsData){var d=l.addonsData[s]?.url;let n={action:d?"wpforms_install_addon":"wpforms_activate_addon",nonce:l.adminNonce,plugin:d||l.addonsData[s]?.path,type:"addon"};(a=null===a?r.post(l.ajaxUrl,n,o):a.then(()=>r.post(l.ajaxUrl,n,o))).fail(e)}a.then(()=>{t||i.openAddonsInstalledModal()}).always(()=>{n.close(),i.updateGenerateFormButton(!1)})},dismissAddonsModal(){var n=r(this).prop("checked"),o={action:"wpforms_dismiss_ai_form",nonce:l.nonce,element:"install-addons-modal",dismiss:n};i.updateGenerateFormButton(!n),r.post(l.ajaxUrl,o).done(function(n){n.success||(i.openErrorModal({title:l.addons.dismissErrorTitle,content:l.addons.dismissError}),wpf.debug(i.ajaxError,n.data.error??n.data))}).fail(function(n){i.openErrorModal({title:l.addons.dismissErrorTitle,content:l.addons.dismissError+"<br>"+l.addons.addonsInstallErrorNetwork}),wpf.debug(i.ajaxError,n.responseText??n.statusText)})},updateGenerateFormButton(n){n?r(".wpforms-template-generate").removeClass("wpforms-template-generate").addClass("wpforms-template-generate-install-addons"):r(".wpforms-template-generate-install-addons").removeClass("wpforms-template-generate-install-addons").addClass("wpforms-template-generate")},openAddonsInstalledModal(){var n={title:"install"===l.addonsAction?l.addons.addonsInstalledTitle:l.addons.addonsActivatedTitle,content:l.addons.addonsInstalledContent,icon:"fa fa-check-circle",type:"green",buttons:{confirm:{text:l.addons.okay,btnClass:"btn-confirm",keys:["enter"],action(){WPFormsBuilder.showLoadingOverlay(),window.location=window.location+"&ai-form"}}},onOpenBefore(){this.$body.addClass("wpforms-ai-forms-addons-installed-modal")}};r.confirm(n)},openExistingFormModal(n){r.confirm({title:wpforms_builder.heads_up,content:l.misc.warningExistingForm,icon:"fa fa-exclamation-circle",type:"orange",buttons:{confirm:{text:wpforms_builder.ok,btnClass:"btn-confirm",keys:["enter"],action(){a.main.useFormAjax(n)}},cancel:{text:wpforms_builder.cancel}}})},openErrorModal(n){n={title:n.title??!1,content:n.content??!1,icon:"fa fa-exclamation-circle",type:"red",buttons:{confirm:{text:l.addons.okay,btnClass:"btn-confirm",keys:["enter"]}}};r.confirm(n)}};return i}