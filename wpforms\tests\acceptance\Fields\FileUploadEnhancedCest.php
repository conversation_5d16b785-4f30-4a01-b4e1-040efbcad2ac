<?php

namespace WPFormsTests\Fields;

use Exception;
use WPF<PERSON>\Cest;
use AcceptanceTester;
use WPF<PERSON>\PageObjects\Builder;
use WPForms\PageObjects\FormsOverview;
use WPForms\PageObjects\FrontEndForm;

/**
 * Enhanced File Upload field test demonstrating the improved wait strategies.
 * 
 * This test shows how to use the new helper methods to avoid common test failures.
 */
class FileUploadEnhancedCest extends Cest
{
    /**
     * The Actor.
     *
     * @var AcceptanceTester
     */
    private $i;

    /**
     * The Forms Overview Page.
     *
     * @var FormsOverview
     */
    private $formsOverview;

    /**
     * The Builder.
     *
     * @var Builder
     */
    private $builder;

    /**
     * The Frontend Form.
     *
     * @var FrontEndForm
     */
    private $frontEnd;

    /**
     * Form ID.
     *
     * @var int
     */
    private $formId;

    /**
     * Field IDs for different file upload fields.
     *
     * @var array
     */
    private $fileUploadFieldIds = [
        'modern' => null,
        'classic' => null,
    ];

    /**
     * Text field ID for form completion.
     *
     * @var int
     */
    private $textFieldId;

    /**
     * Enhanced file upload field test using new wait strategies
     *
     * @param AcceptanceTester $i Actor
     * @param FormsOverview $formsOverview Forms Overview Page
     * @param Builder $builder Builder Page
     * @param FrontEndForm $frontEnd Frontend Form Page
     * @throws Exception
     */
    public function fileUploadFieldEnhanced(AcceptanceTester $i, FormsOverview $formsOverview, Builder $builder, FrontEndForm $frontEnd)
    {
        $this->i = $i;
        $this->formsOverview = $formsOverview;
        $this->builder = $builder;
        $this->frontEnd = $frontEnd;

        $i->skipDebugCheck();
        
        // Use enhanced login method
        $i->ensureLoggedIn();
        
        $this->createFormWithEnhancedMethods();
        $this->testFileUploadFunctionality();
    }

    /**
     * Create form using enhanced methods with proper wait strategies
     *
     * @throws Exception
     */
    private function createFormWithEnhancedMethods()
    {
        // Navigate to forms overview
        $this->formsOverview->visitFormsOverviewPage();
        $this->formsOverview->addNewForm();
        $this->builder->chooseTemplate();
        
        // Wait for builder to be fully ready using enhanced method
        $this->i->waitForBuilderReady(15);
        
        // Add text field using enhanced method
        $this->textFieldId = $this->i->addFieldSafely('text');
        
        // Add modern file upload field with enhanced configuration
        $this->fileUploadFieldIds['modern'] = $this->i->addFieldSafely('file-upload', [
            'style' => 'modern',
            'max_file_number' => 2,
            'extensions' => 'png',
            'label' => 'Modern File Upload'
        ]);
        
        // Brief pause between field additions to avoid conflicts
        $this->i->wait(1);
        
        // Add classic file upload field
        $this->fileUploadFieldIds['classic'] = $this->i->addFieldSafely('file-upload', [
            'style' => 'classic',
            'max_file_number' => 1,
            'extensions' => 'jpg',
            'label' => 'Classic File Upload'
        ]);
        
        // Save form using enhanced method
        $this->i->saveSafely();
        
        // Verify fields were created successfully
        $this->verifyFileUploadFields();
        
        // Get form ID for later use
        $this->formId = $this->builder->getFormId();
    }

    /**
     * Verify file upload fields were created correctly
     *
     * @throws Exception
     */
    private function verifyFileUploadFields()
    {
        foreach ($this->fileUploadFieldIds as $style => $fieldId) {
            $this->i->verifyFieldCreated('file-upload', $fieldId);
        }
    }

    /**
     * Test file upload functionality on frontend
     *
     * @throws Exception
     */
    private function testFileUploadFunctionality()
    {
        // Navigate to frontend form
        $this->i->amOnPage("/?wpforms_form_preview={$this->formId}");
        
        // Wait for form to load completely
        $this->i->waitForPageLoad();
        
        // Fill text field
        $this->i->safeFillField("[data-field-id='{$this->textFieldId}'] input", 'Test submission');
        
        // Test modern file upload
        $this->testModernFileUpload();
        
        // Test classic file upload
        $this->testClassicFileUpload();
        
        // Submit form
        $this->i->safeClick('.wpforms-submit');
        
        // Wait for submission confirmation
        $this->i->waitForTextWithRetry('Thanks for contacting us!', 15);
    }

    /**
     * Test modern file upload field
     *
     * @throws Exception
     */
    private function testModernFileUpload()
    {
        $fieldSelector = "[data-field-id='{$this->fileUploadFieldIds['modern']}']";
        
        // Wait for field to be ready
        $this->i->waitForElementStable($fieldSelector);
        
        // Upload file using enhanced method
        $this->uploadFileToField($fieldSelector, 'test-image.png');
        
        // Verify file was uploaded
        $this->i->waitForElementVisible("{$fieldSelector} .wpforms-file-upload-preview", 10);
    }

    /**
     * Test classic file upload field
     *
     * @throws Exception
     */
    private function testClassicFileUpload()
    {
        $fieldSelector = "[data-field-id='{$this->fileUploadFieldIds['classic']}']";
        
        // Wait for field to be ready
        $this->i->waitForElementStable($fieldSelector);
        
        // Upload file using enhanced method
        $this->uploadFileToField($fieldSelector, 'test-image.jpg');
        
        // Verify file was uploaded
        $this->i->waitForElementVisible("{$fieldSelector} .wpforms-file-upload-file", 10);
    }

    /**
     * Upload file to a specific field with proper error handling
     *
     * @param string $fieldSelector Field selector
     * @param string $fileName File name to upload
     * @throws Exception
     */
    private function uploadFileToField($fieldSelector, $fileName)
    {
        $fileInputSelector = "{$fieldSelector} input[type='file']";
        
        // Wait for file input to be ready
        $this->i->waitForClickableElement($fileInputSelector);
        
        // Get test file path
        $testFilePath = codecept_data_dir("testFiles/{$fileName}");
        
        // Ensure test file exists
        if (!file_exists($testFilePath)) {
            throw new Exception("Test file not found: {$testFilePath}");
        }
        
        // Upload file with retry logic
        $this->i->retryAction(function() use ($fileInputSelector, $testFilePath) {
            $this->i->attachFile($fileInputSelector, $testFilePath);
        });
        
        // Wait for upload to process
        $this->i->waitForAjax();
    }

    /**
     * Cleanup after test
     *
     * @param AcceptanceTester $i Actor
     * @throws Exception
     */
    public function _after(AcceptanceTester $i)
    {
        // Clean up any uploaded files or test data if needed
        if ($this->formId) {
            // Could add cleanup logic here if needed
        }
    }
}
