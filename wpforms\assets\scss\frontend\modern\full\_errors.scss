// WPForms Modern Full styles.
//
// Errors. Adjustments for base errors styles.
//
// @since 1.8.1

div.wpforms-container-full .wpforms-form {

	label,
	em {
		&.wpforms-error {
			font-weight: 400;
			font-size: var( --wpforms-label-size-sublabel-font-size );
			line-height: var( --wpforms-label-size-sublabel-line-height );
			margin-top: var( --wpforms-field-size-input-spacing );
			color: var( --wpforms-label-error-color );
			padding: 0 0 0 5px;
			position: relative;

			&:before {
				-webkit-mask-image: url( "data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2217%22%20height%3D%2215%22%20viewBox%3D%220%200%2017%2015%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Cpath%20d%3D%22M16.0264%2012.3086L9.46387%200.90625C8.97168%200.0585938%207.68652%200.03125%207.19434%200.90625L0.631836%2012.3086C0.139648%2013.1562%200.768555%2014.25%201.78027%2014.25H14.8779C15.8896%2014.25%2016.5186%2013.1836%2016.0264%2012.3086ZM8.34277%209.92969C9.02637%209.92969%209.60059%2010.5039%209.60059%2011.1875C9.60059%2011.8984%209.02637%2012.4453%208.34277%2012.4453C7.63184%2012.4453%207.08496%2011.8984%207.08496%2011.1875C7.08496%2010.5039%207.63184%209.92969%208.34277%209.92969ZM7.13965%205.41797C7.1123%205.22656%207.27637%205.0625%207.46777%205.0625H9.19043C9.38184%205.0625%209.5459%205.22656%209.51855%205.41797L9.32715%209.13672C9.2998%209.32812%209.16309%209.4375%208.99902%209.4375H7.65918C7.49512%209.4375%207.3584%209.32812%207.33105%209.13672L7.13965%205.41797Z%22%20fill%3D%22currentColor%22%2F%3E%0A%3C%2Fsvg%3E%0A" );
				mask-image: url( "data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2217%22%20height%3D%2215%22%20viewBox%3D%220%200%2017%2015%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Cpath%20d%3D%22M16.0264%2012.3086L9.46387%200.90625C8.97168%200.0585938%207.68652%200.03125%207.19434%200.90625L0.631836%2012.3086C0.139648%2013.1562%200.768555%2014.25%201.78027%2014.25H14.8779C15.8896%2014.25%2016.5186%2013.1836%2016.0264%2012.3086ZM8.34277%209.92969C9.02637%209.92969%209.60059%2010.5039%209.60059%2011.1875C9.60059%2011.8984%209.02637%2012.4453%208.34277%2012.4453C7.63184%2012.4453%207.08496%2011.8984%207.08496%2011.1875C7.08496%2010.5039%207.63184%209.92969%208.34277%209.92969ZM7.13965%205.41797C7.1123%205.22656%207.27637%205.0625%207.46777%205.0625H9.19043C9.38184%205.0625%209.5459%205.22656%209.51855%205.41797L9.32715%209.13672C9.2998%209.32812%209.16309%209.4375%208.99902%209.4375H7.65918C7.49512%209.4375%207.3584%209.32812%207.33105%209.13672L7.13965%205.41797Z%22%20fill%3D%22currentColor%22%2F%3E%0A%3C%2Fsvg%3E%0A" );
				content: '';
				position: relative;
				display: inline-block;
				right: 5px;
				top: 1.5px;
				width: 16px;
				height: 14px;
				background-color: var( --wpforms-label-error-color );
			}
		}
	}

	.wpforms-field-address,
	.wpforms-field-credit-card {
		.wpforms-field-sublabel + .wpforms-error {
			margin-top: calc( 1.5 * var( --wpforms-field-size-sublabel-spacing ) );
		}
	}

	.wpforms-field {
		input,
		textarea,
		select {
			&.wpforms-error,
			&.user-invalid {
				@include wpforms-input-error();

				&:hover {
					@include wpforms-input-error-hover();
				}

				&:focus {
					@include wpforms-input-error-focus();
				}
			}
		}

		input[type=checkbox],
		input[type=radio] {
			&.wpforms-error,
			&.user-invalid {
				border: none;
				box-shadow: none;

				&:hover,
				&:focus {
					border: none;
					box-shadow: none;
				}
			}
		}
	}

	// Un-reset styles for form error container.
	.wpforms-error-container {
		color: var( --wpforms-label-error-color );
		font-size: var( --wpforms-label-size-font-size );
		line-height: var( --wpforms-label-size-line-height );

		ul li {
			list-style: inside !important;
		}

		ol li {
			list-style: inside decimal !important;
		}

		a {
			color: var( --wpforms-label-error-color );
			text-decoration: underline !important;

			&:hover {
				text-decoration: none !important;
			}
		}

		del {
			text-decoration: line-through !important;
		}

		blockquote {
			padding-left: $spacing_ml;
			border-left: 4px solid;
			font-style: italic;
		}

		&.wpforms-error-styled-container {
			padding: $spacing_m 0;

			.wpforms-error {
				padding: 11px;
				border: 1px solid var( --wpforms-label-error-color );
				border-left: 5px solid;
			}
		}
	}

	// This error alert is used in CC fields.
	.wpforms-error-alert {
		color: var( --wpforms-label-error-color );
		border-color: transparent;
		border-radius: var( --wpforms-field-border-radius );
		font-size: var( --wpforms-label-size-sublabel-font-size );
		padding: var( --wpforms-field-size-input-spacing );

		// This is the hack to make background with transparency from given RGBA color.
		background: linear-gradient( 90deg, var( --wpforms-label-error-color ) -3000%, transparent 500% );
	}
}
