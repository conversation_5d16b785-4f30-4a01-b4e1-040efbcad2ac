let WPFormsCaptcha=window.WPFormsCaptcha||((a,t,c)=>{let e={init(){c(e.ready),t.addEventListener("elementor/popup/show",function(){e.ready()})},ready(){c(".wpforms-captcha-equation").each(function(){var a=c(this).parent(),t=wpforms_captcha.cal[Math.floor(Math.random()*wpforms_captcha.cal.length)],r=e.randomNumber(wpforms_captcha.min,wpforms_captcha.max),n=e.randomNumber(wpforms_captcha.min,wpforms_captcha.max);a.find("span.n1").text(r),a.find("input.n1").val(r),a.find("span.n2").text(n),a.find("input.n2").val(n),a.find("span.cal").text(t),a.find("input.cal").val(t),a.find("input.a").attr({"data-cal":t,"data-n1":r,"data-n2":n})}),a.addEventListener("om.Html.append.after",function(){e.ready()}),e.loadValidation()},loadValidation(){void 0!==c.fn.validate&&c.validator.addMethod("wpf-captcha",function(a,t,r){var n=c(t);let e,o;if("math"===r){var r=Number(n.attr("data-n1")),i=Number(n.attr("data-n2")),d=n.attr("data-cal"),m=["-","+","*"],p={"+":(a,t)=>a+t,"-":(a,t)=>a-t,"*":(a,t)=>a*t};if(e=Number(a),o=!1,!m.includes(d))return!1;o=p[d](r,i)}else e=a.toString().toLowerCase().trim(),o=n.attr("data-a").toString().toLowerCase().trim();return this.optional(t)||e===o},c.validator.format(wpforms_captcha.errorMsg))},randomNumber(a,t){return Math.floor(Math.random()*(Number(t)-Number(a)+1))+Number(a)}};return e})(document,window,jQuery);WPFormsCaptcha.init();