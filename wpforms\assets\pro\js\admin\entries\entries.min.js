var WPFormsPagesEntries=window.WPFormsPagesEntries||(r=>{var e={init:function(){r(e.ready)},ready:function(){e.initFlatpickr(),e.bindResetButtons(),e.handleOnPrint()},htmxAfterSettle(){e.initFlatpickr(),e.bindResetButtons()},initFlatpickr:function(){var t={rangeSeparator:" - "},e={altInput:!0,altFormat:"M j, Y",dateFormat:"Y-m-d",mode:"range",defaultDate:wpforms_admin.default_date};"undefined"!==flatpickr&&Object.prototype.hasOwnProperty.call(flatpickr,"l10ns")&&Object.prototype.hasOwnProperty.call(flatpickr.l10ns,wpforms_admin.lang_code)&&((t=flatpickr.l10ns[wpforms_admin.lang_code]).rangeSeparator=" - "),e.locale=t,r(".wpforms-filter-date-selector").flatpickr(e)},reset:function(t){switch(t.prop("tagName").toLowerCase()){case"input":t.val("");break;case"select":t.val(t.find("option").first().val())}},isIgnoredForReset:function(t){return-1!==["submit","hidden"].indexOf((t.attr("type")||"").toLowerCase())&&!t.hasClass("flatpickr-input")},bindResetButtons:function(){r("#wpforms-reset-filter .reset").on("click",function(){var t=r(this).parents("form");t.find(r(this).data("scope")).find("input,select").each(function(){var t=r(this);e.isIgnoredForReset(t)||e.reset(t)}),t.trigger("submit")})},handleOnPrint:function(){r("#wpforms-entries-list").on("submit",'form[target="_blank"]',function(t){t.preventDefault(),this.submit(),r(this).removeAttr("target").trigger("reset")}).on("change","#bulk-action-selector-top",function(){var t=this.value,e=r(this).closest("form");"print"===t?e.attr("target","_blank"):e.attr("target")&&e.removeAttr("target")})}};return e})((document,window,jQuery));WPFormsPagesEntries.init();