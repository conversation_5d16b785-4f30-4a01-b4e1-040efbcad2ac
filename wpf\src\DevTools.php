<?php

namespace WPForms\DevTools;

use Parsedown;
use WPForms\DevTools\ErrorHandler\ErrorHandler;
use WPForms\Requirements\Requirements;

/**
 * DevTools service.
 *
 * @since 0.5
 */
class DevTools {

	/**
	 * The slug of a page.
	 *
	 * @since 0.10
	 */
	const PAGE_SLUG = 'wpforms_dev_tools';

	/**
	 * Nonce.
	 *
	 * @since 0.5
	 *
	 * @var string
	 */
	const NONCE = 'wpf_devtools_nonce';

	/**
	 * View slug.
	 *
	 * @since 0.7
	 *
	 * @var string
	 */
	public $view;

	/**
	 * Upgrades instance.
	 *
	 * @since 0.32
	 *
	 * @var Upgrades
	 */
	private $upgrades;

	/**
	 * Constructor.
	 *
	 * @since 0.7
	 */
	private function __construct() {

		$this->init();
	}

	/**
	 * Instantiate class.
	 *
	 * @since 0.7
	 */
	public static function instance() {

		new DevTools();
	}

	/**
	 * Init class.
	 *
	 * @since 0.7
	 */
	private function init() {

		if ( ! wpforms_current_user_can() ) {
			return;
		}

		if ( ! apply_filters( 'wpf_admin_devtools', '__return_true' ) ) {
			return;
		}

		// phpcs:ignore WordPress.Security.NonceVerification.Recommended
		$this->view = ! empty( $_GET['view'] ) ? sanitize_key( wp_unslash( $_GET['view'] ) ) : 'entries';

		$this->hooks();

		$this->upgrades = new Upgrades();
	}

	/**
	 * Hooks.
	 *
	 * @since 0.7
	 */
	private function hooks() {

		add_action( 'admin_menu', [ $this, 'add_menu' ] );
	}

	/**
	 * Add dev tools menu item.
	 *
	 * @since 0.5
	 */
	public function add_menu() {

		add_menu_page(
			'WPForms Dev',
			'WPForms Dev',
			'manage_options',
			self::PAGE_SLUG,
			[ $this, 'page' ],
			'dashicons-lightbulb',
			'58.91'
		);
	}

	/**
	 * Dev tools page render.
	 *
	 * @since 0.5
	 */
	public function page() {

		$tabs = $this->get_tabs();

		$child_tabs = array_filter(
			$tabs,
			static function ( $tab ) {

				return isset( $tab['parent'] );
			}
		);

		$parent_tabs = array_filter(
			$tabs,
			static function ( $tab ) {

				return ! isset( $tab['parent'] );
			}
		);

		// phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
		echo wpforms_render(
			'dev_tools_page',
			[
				'logo'    => WPFORMS_DEV_TOOLS_URL . '/assets/images/logo.png',
				'tabs'    => wpforms_render(
					'dev_tools_page_tabs',
					[
						'child_tabs'  => $child_tabs,
						'parent_tabs' => $parent_tabs,
						'active'      => $this->view,
					],
					true
				),
				'content' => $this->get_content(),
				'view'    => $this->view,
			],
			true
		);
	}

	/**
	 * Page tabs data.
	 *
	 * @since 0.7
	 *
	 * @return array Tabs data.
	 */
	private function get_tabs(): array {

		return [
			'generators'     => [
				'url'       => admin_url( 'admin.php?page=wpforms_dev_tools&view=entries' ),
				'title'     => 'Generators',
				'has_child' => true,
			],
			'entries'        => [
				'url'    => admin_url( 'admin.php?page=wpforms_dev_tools&view=entries' ),
				'title'  => 'Entries Generator',
				'parent' => 'generators',
			],
			'forms'          => [
				'url'    => admin_url( 'admin.php?page=wpforms_dev_tools&view=forms' ),
				'title'  => 'Forms Generator',
				'parent' => 'generators',
			],
			'modals'         => [
				'url'    => admin_url( 'admin.php?page=wpforms_dev_tools&view=modals' ),
				'title'  => 'Modal Generator',
				'parent' => 'generators',
			],
			'detector'       => [
				'url'   => admin_url( 'admin.php?page=wpforms_dev_tools&view=detector' ),
				'title' => 'Detector',
			],
			'lite_connect'   => [
				'url'   => admin_url( 'admin.php?page=wpforms_dev_tools&view=lite_connect' ),
				'title' => 'Lite Connect',
			],
			'splash_preview' => [
				'url'   => admin_url( 'admin.php?page=wpforms_dev_tools&view=splash_preview' ),
				'title' => 'What\'s New',
			],
			'error_handler'  => [
				'url'   => admin_url( 'admin.php?page=wpforms_dev_tools&view=error_handler' ),
				'title' => 'Error Handler',
			],
			'changelog'      => [
				'url'   => admin_url( 'admin.php?page=wpforms_dev_tools&view=changelog' ),
				'title' => 'CHANGELOG',
			],
			'hooks'          => [
				'url'   => admin_url( 'admin.php?page=wpforms_dev_tools&view=hooks' ),
				'title' => 'Hooks',
			],
			'requirements'   => [
				'url'   => admin_url( 'admin.php?page=wpforms_dev_tools&view=requirements' ),
				'title' => 'Requirements',
			],
			'upgrades'       => [
				'url'   => admin_url( 'admin.php?page=wpforms_dev_tools&view=upgrades' ),
				'title' => 'Upgrades',
			],
		];
	}

	/**
	 * Page content.
	 *
	 * @since 0.7
	 *
	 * @return string Page content.
	 */
	public function get_content() {

		$method = $this->view . '_content';

		if ( method_exists( $this, $method ) ) {
			$content = $this->{$method}();
		} else {
			$content = 'Something went wrong. Click on one of the links above.';
		}

		return $content;
	}

	/**
	 * Generator tab content.
	 *
	 * @since 0.9
	 *
	 * @return string Generator content.
	 */
	public function entries_content() {

		$posts = Main::wpforms_obj( 'form' )->get(
			'',
			[
				'content_only'     => true,
				'numberposts'      => - 1,
				'orderby'          => 'date',
				'order'            => 'DESC',
				'suppress_filters' => true,
			]
		);

		return wpforms_render(
			'tab_entries',
			[
				'forms'  => array_map(
					static function ( $post ) {

						return array_intersect_key( (array) $post, array_flip( [ 'ID', 'post_title' ] ) );
					},
					array_filter( (array) $posts )
				),
				'nonce'  => wp_create_nonce( self::NONCE ),
				'is_pro' => wpforms()->is_pro(),
			],
			true
		);
	}

	/**
	 * Generator tab content.
	 *
	 * @since 0.9
	 *
	 * @return string Generator content.
	 */
	public function forms_content() {

		$templates = Main::wpforms_obj( 'builder_templates' )->get_templates();

		return wpforms_render(
			'tab_forms',
			[
				'nonce'     => wp_create_nonce( self::NONCE ),
				'templates' => $templates,
			],
			true
		);
	}

	/**
	 * Modals Generator tab content.
	 *
	 * @since 0.18
	 *
	 * @return string Modals Generator content.
	 */
	public function modals_content() {

		return wpforms_render(
			'tab_modals',
			[
				'types' => [
					'orange' => '#e27730',
					'blue'   => '#0399ed',
					'green'  => '#00a32a',
					'red'    => '#d63638',
					'yellow' => '#ffb900',
				],
				'sizes' => [
					'400px',
					'550px',
				],
			],
			true
		);
	}

	/**
	 * Controller for detector.
	 *
	 * @since 0.7
	 *
	 * @return array
	 */
	private function detector_controller() {

		$nonce = filter_input( INPUT_POST, 'detector_nonce', FILTER_SANITIZE_FULL_SPECIAL_CHARS );

		if ( ! wp_verify_nonce( $nonce, 'detector' ) ) {
			return [];
		}

		$url = filter_input( INPUT_POST, 'url', FILTER_SANITIZE_URL );

		if ( ! $url ) {
			return [];
		}

		$detector = new Detector( $url );

		return array_merge(
			[
				'full_url' => $url,
			],
			$detector->run()
		);
	}

	/**
	 * Controller for hooks.
	 *
	 * @since 0.7
	 *
	 * @return array
	 */
	private function hooks_controller() {

		$hooks = new Hooks();

		return $hooks->get_output();
	}

	/**
	 * Detector tab content.
	 *
	 * @since 0.7
	 *
	 * @return string Detector content.
	 */
	public function detector_content() {

		return wpforms_render(
			'tab_detector',
			$this->detector_controller(),
			true
		);
	}

	/**
	 * Lite Connect tab content.
	 *
	 * @since 0.19
	 *
	 * @return string Lite Connect content.
	 */
	public function lite_connect_content() {

		return wpforms_render(
			'tab_lite_connect',
			$this->lite_connect_controller(),
			true
		);
	}

	/**
	 * Controller for Lite Connect.
	 *
	 * @since 0.19
	 *
	 * @return array
	 */
	private function lite_connect_controller() {

		$nonce = filter_input( INPUT_POST, 'lite_connect_nonce', FILTER_SANITIZE_FULL_SPECIAL_CHARS );

		if ( ! wp_verify_nonce( $nonce, 'lite_connect' ) ) {
			return [];
		}

		$query = filter_input( INPUT_POST, 'query', FILTER_SANITIZE_STRING );

		if ( ! $query ) {
			return [];
		}

		$lite_connect = new LiteConnect( $query );

		return array_merge(
			[
				'query' => $query,
			],
			$lite_connect->run()
		);
	}

	/**
	 * What's New tab content.
	 *
	 * @since 0.23
	 *
	 * @return string What's New content.
	 */
	public function splash_preview_content(): string {

		$cached_data = Main::wpforms_obj( 'splash_cache' )->get();
		$options     = Utils::get_option( 'splash_screen_preview' );
		$options     = $options !== false ? $options : [];

		$default = [
			'version' => '',
			'license' => '',
		];

		$preview_data = [
			'license_types' => [ 'lite', 'basic', 'plus', 'pro', 'elite', 'agency', 'ultimate' ],
			'options'       => array_merge( $default, $options ),
			'error'         => empty( $cached_data ) ? 'We couldn\'t find any data for this version and license.' : '',
		];

		// phpcs:ignore WPForms.Formatting.EmptyLineBeforeReturn.RemoveEmptyLineBeforeReturnStatement
		return wpforms_render(
			'tab_splash_preview',
			$preview_data,
			true
		);
	}

	/**
	 * Error Handler tab content.
	 *
	 * @since 0.22
	 *
	 * @return string Error Handler content.
	 */
	public function error_handler_content(): string {

		if ( ! wpf()->get( 'error_handler' ) ) {
			return '';
		}

		return wpforms_render(
			'tab_error_handler',
			$this->error_handler_controller(),
			true
		);
	}

	/**
	 * Controller for Error Handler.
	 *
	 * @since 0.22
	 *
	 * @return array
	 */
	private function error_handler_controller(): array {

		$dirs = Utils::get_option( ErrorHandler::OPTION_KEY );

		$args = [
			'dirs'  => $dirs !== false ? $dirs : [],
			'nonce' => wp_create_nonce( self::NONCE ),
		];

		return $this->update_error_handler_option_on_post_request( $args );
	}

	/**
	 * Update error handler option on POST request.
	 *
	 * @since 0.22
	 *
	 * @param array $args Arguments.
	 *
	 * @return array
	 */
	private function update_error_handler_option_on_post_request( $args ): array {

		$request_method = isset( $_SERVER['REQUEST_METHOD'] ) ? sanitize_text_field( wp_unslash( $_SERVER['REQUEST_METHOD'] ) ) : '';

		if ( strtolower( $request_method ) !== 'post' ) {
			return $args;
		}

		$nonce = isset( $_POST['nonce'] ) ? sanitize_text_field( wp_unslash( $_POST['nonce'] ) ) : '';

		if ( ! wp_verify_nonce( $nonce, self::NONCE ) ) {
			return $args;
		}

		$dirs_str = isset( $_POST['dirs'] ) ? sanitize_textarea_field( wp_unslash( $_POST['dirs'] ) ) : '';
		$dirs_str = str_replace( "\r", "\n", $dirs_str );
		$dirs     = array_filter( array_map( 'trim', explode( "\n", $dirs_str ) ) );

		Utils::update_option( ErrorHandler::OPTION_KEY, $dirs );

		$args['dirs'] = $dirs;

		return $args;
	}

	/**
	 * Changelog tab content.
	 *
	 * @since 0.7
	 *
	 * @return string Changelog tab content.
	 */
	public function changelog_content() {

		if ( ! defined( 'WPFORMS_PLUGIN_DIR' ) ) {
			return '';
		}

		$changelog_file = trailingslashit( WPFORMS_PLUGIN_DIR ) . 'CHANGELOG.md';

		if ( ! is_readable( $changelog_file ) ) {
			return '';
		}

		$changelog = file_get_contents( $changelog_file );

		return Parsedown::instance()->text( $changelog );
	}

	/**
	 * Hooks tab content.
	 *
	 * @since 0.8
	 *
	 * @return string Hooks content.
	 */
	public function hooks_content() {

		return wpforms_render(
			'tab_hooks',
			$this->hooks_controller(),
			true
		);
	}

	/**
	 * Requirements tab content.
	 *
	 * @since 0.23
	 *
	 * @return string Requirements content.
	 */
	public function requirements_content(): string {

		return wpforms_render(
			'tab_requirements',
			$this->requirements_controller(),
			true
		);
	}

	/**
	 * Upgrades tab content.
	 *
	 * @since 0.32
	 *
	 * @return string Upgrades content.
	 */
	public function upgrades_content(): string {

		return wpforms_render(
			'tab_upgrades',
			$this->upgrades_controller(),
			true
		);
	}

	/**
	 * Controller for Upgrades.
	 *
	 * @since 0.32
	 *
	 * @return array
	 */
	private function upgrades_controller(): array {

		return [
			'nonce'    => wp_create_nonce( self::NONCE ),
			'notices'  => $this->upgrades->get_notices(),
			'upgrades' => get_option( $this->upgrades::UPGRADES ),
		];
	}

	/**
	 * Controller for Requirements.
	 *
	 * @since 0.23
	 *
	 * @return array
	 */
	private function requirements_controller(): array {

		$addons_req       = [];
		$raw_requirements = array_filter(
			Requirements::get_instance()->get_requirements(),
			static function ( $sub_array ) {

				// Look for active addons only.
				return array_key_exists( 'file', $sub_array );
			}
		);

		foreach ( $raw_requirements as $requirements ) {
			$item                        = get_plugin_data( $requirements['file'] );
			$addons_req[ $item['Name'] ] = $this->get_formatted_reqs( $requirements );
		}

		return [
			'addons_req' => $addons_req,
		];
	}

	/**
	 * Prepare requirements data to print.
	 *
	 * @since 0.23
	 *
	 * @param array $requirements Requirements data.
	 *
	 * @return array
	 */
	private function get_formatted_reqs( array $requirements ): array {

		$requirements_instance  = Requirements::get_instance();
		$allowed_requirements   = [
			'php'     => 'PHP',
			'wp'      => 'WordPress',
			'wpforms' => 'WPForms',
			'license' => 'License',
			'ext'     => 'Extensions',
		];
		$formatted_requirements = [];

		foreach ( $requirements as $name => $requirement ) {
			if ( ! array_key_exists( $name, $allowed_requirements ) || empty( $requirement ) ) {
				continue;
			}

			switch ( $name ) {
				case 'wpforms':
				case 'wp':
				case 'php':
					$value = $requirements_instance->list_version( $requirement );
					break;

				case 'license':
				case 'ext':
					$value = implode( ', ', $requirement );
					break;

				default:
					$value = '';
			}

			$formatted_requirements[ $allowed_requirements[ $name ] ] = $value;
		}

		return $formatted_requirements;
	}
}
