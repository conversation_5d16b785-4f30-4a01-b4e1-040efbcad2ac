var WPFormsStripePaymentElement=window.WPFormsStripePaymentElement||((t,i,d)=>{let s,m={stripe:null,forms:{},init(){m.stripe=Stripe(wpforms_stripe.publishable_key,{locale:wpforms_stripe.data.element_locale,betas:["elements_enable_deferred_intent_beta_1"]}),d(t).on("wpformsReady",function(){d(".wpforms-stripe form").each(m.setupStripeForm).on("wpformsConvFormsFieldActivationAfter",m.convFormsFieldActivationAfter)}),d(t).on("wpformsBeforePageChange",m.pageChange).on("wpformsAmountTotalCalculated",m.updateElementsTotalAmount).on("wpformsProcessConditionalsField",function(e,o,r,t,n){m.processConditionalsField(o,r,t,n)})},initializeFormsDefaultObject(){console.warn('WARNING! Function "WPFormsStripePaymentElement.initializeFormsDefaultObject()" has been deprecated, please use the "WPFormsStripePaymentElement.initializeFormDefaultObject( formId )" function instead!'),d(".wpforms-stripe form").each(function(){m.initializeFormDefaultObject(d(this).data("formid"))})},initializeFormDefaultObject(e){m.forms[e]={elements:null,paymentElement:null,elementsModified:!1,linkElement:null,linkEmail:"",linkDestroyed:!1,paymentType:"",lockedPageToSwitch:0,paymentMethodId:"",total:"",customVars:null}},setupStripeForm(){var e,o,r=d(this);WPFormsUtils.triggerEvent(d(t),"wpformsBeforeStripePaymentElementSetup",[r]).isDefaultPrevented()||(e=r.data("formid"),void 0===m.forms[e]&&(m.initializeFormDefaultObject(e),(e=r.find(".wpforms-field-stripe-credit-card")).find(".wpforms-field-row").length)&&(o=r.data("validator"))&&(s=o.settings.submitHandler,o.settings.submitHandler=m.submitHandler,r.on("wpformsAjaxSubmitActionRequired",m.confirmPaymentActionCallback),e.hasClass("wpforms-conditional-field")||m.setupPaymentElement(r)))},async confirmPaymentActionCallback(e,r){if(r.success&&r.data.action_required){let o=d(this);var t=new URL(i.location.href),n=o.data("formid");await m.stripe.confirmPayment({clientSecret:r.data.payment_intent_client_secret,confirmParams:{return_url:t.toString(),payment_method:m.forms[n].paymentMethodId},redirect:"if_required"}).then(function(e){m.handleConfirmPayment(o,e)})}},handleConfirmPayment(e,o){var r;o.error?m.displayStripeError(e,o.error.message):(r=e.data("formid"),o.paymentIntent&&"succeeded"===o.paymentIntent.status?(e.find(".wpforms-stripe-payment-method-id").remove(),e.find(".wpforms-stripe-payment-intent-id").remove(),e.append('<input type="hidden" class="wpforms-stripe-payment-intent-id" name="wpforms[payment_intent_id]" value="'+o.paymentIntent.id+'">'),e.append('<input type="hidden" class="wpforms-stripe-payment-link-email" name="wpforms[payment_link_email]" value="'+m.forms[r].linkEmail+'">'),wpforms.formSubmitAjax(e)):m.formAjaxUnblock(e))},setupPaymentElement(e){var o=e.data("formid");d.isEmptyObject(m.forms)&&m.initializeFormDefaultObject(o),m.forms[o].paymentElement||(m.forms[o].elements=m.stripe.elements({currency:wpforms.getCurrency().code.toLowerCase(),mode:"payment",amount:7777777,loader:"always",locale:wpforms_stripe.data.element_locale,appearance:m.getElementAppearanceOptions(e)}),m.initializePaymentElement(e),m.linkEmailMappedFieldTriggers(e),wpforms.amountTotalCalc(e),m.updatePaymentElementStylesModern(e),WPFormsUtils.triggerEvent(d(t),"wpformsStripePaymentElementInitialized",[e,m.forms]))},processConditionalsField(e,o,r,t){var n=d("#wpforms-form-"+e),i=n.find(".wpforms-field-stripe-credit-card"),r=r&&"hide"===t||!r&&"hide"!==t,t=(m.forms[e]||[]).paymentElement||null;!i.length||i.data("field-id").toString()!==o||t||r||m.setupPaymentElement(n)},getElementAppearanceOptions(e){var o,r,t,n,i,l=m.getCustomAppearanceOptions();return d.isEmptyObject(l)?(n=e.find(".wpforms-stripe-credit-card-hidden-input"),r=!(o=e.find(".wpforms-field-stripe-credit-card .wpforms-field-row")).hasClass("wpforms-sublabel-hide"),t=m.getElementPrimaryColor(n),e=m.getCustomAppearanceVariables(e),(n={borderColor:m.getCssPropertyValue(n,"--field-border")||m.getCssPropertyValue(n,"border-color"),borderRadius:m.getCssPropertyValue(n,"border-radius"),fontSize:m.getCssPropertyValue(n,"font-size"),colorText:m.getCssPropertyValue(n,"--secondary-color")||m.getCssPropertyValue(n,"color"),colorTextPlaceholder:m.getCssPropertyValue(n,"--secondary-color-50")||WPFormsUtils.cssColorsUtils.getColorWithOpacity(m.getCssPropertyValue(n,"color"),"0.5"),colorBackground:m.getCssPropertyValue(n,"--background-color")||m.getCssPropertyValue(n,"background-color"),fontFamily:m.getCssPropertyValue(n,"font-family"),errorColor:"#990000"}).colorBackground=WPFormsUtils.cssColorsUtils.rgbaToHex(n.colorBackground),n.borderColor=WPFormsUtils.cssColorsUtils.isValidColor(n.borderColor)?n.borderColor:n.colorText,i=r?{}:{opacity:0},{theme:"stripe",labels:o.data("sublabel-position"),variables:{colorPrimary:t,colorBackground:n.colorBackground,colorText:n.colorText,colorDanger:n.errorColor,fontFamily:n.fontFamily,spacingUnit:"4px",spacingGridRow:"8px",fontSizeSm:"13px",fontWeightNormal:"400",borderRadius:n.borderRadius,colorTextPlaceholder:n.colorTextPlaceholder,colorIcon:n.colorText,logoColor:"light"},rules:{".Input--invalid":{color:n.colorText,borderColor:"#cc0000"},".Input:disabled":{backgroundColor:n.colorBackground,borderColor:"unset"},".Input":{border:"none",borderRadius:n.borderRadius,boxShadow:"0 0 0 1px "+n.borderColor,fontSize:n.fontSize,padding:"12px 14px",lineHeight:parseInt(n.fontSize,10)+5+"px",transition:"none",color:n.colorText,backgroundColor:n.colorBackground},".Input:focus, .Input:hover":{border:"none",boxShadow:"0 0 0 2px "+e.focusColor,outline:"none"},".Label":{fontFamily:n.fontFamily,lineHeight:r?"1.3":"0",color:t},".Label, .Label--floating":i,".CheckboxInput, .CodeInput, .PickerItem":{border:"1px solid "+n.borderColor},[m.getPickerItemSelectors().join(", ")]:{color:t,boxShadow:"none",borderColor:n.borderColor,backgroundColor:n.colorBackground},".Block":{border:"1px solid "+n.borderColor,borderRadius:n.borderRadius},".Tab":{color:n.colorText},".InstantBankPayment":{display:"none"},".TabLabel, .TabIcon":{color:n.colorText},".Tab--selected":{borderColor:"#999999",color:n.colorText},".Action":{marginLeft:"6px"},".Action, .MenuAction":{border:"none",backgroundColor:"transparent"},".Action:hover, .MenuAction:hover":{border:"none",backgroundColor:"transparent"},".Error, .RedirectText":{color:n.errorColor},".TabIcon--selected":{fill:n.colorText},".AccordionItem":{border:0,boxShadow:"none"}}}):l},getCustomAppearanceOptions(){return"object"==typeof i.wpformsStripePaymentElementAppearance?i.wpformsStripePaymentElementAppearance:d.isEmptyObject(wpforms_stripe.data.element_appearance)?{}:wpforms_stripe.data.element_appearance},getCssPropertyValue(e,o){try{return e.css(o)}catch(e){return""}},initializePaymentElement(o,e=""){let r=o.find(".wpforms-field-stripe-credit-card .wpforms-field-row"),t=o.data("formid");m.forms[t].paymentElement&&m.forms[t].paymentElement.destroy(),m.forms[t].paymentElement=m.forms[t].elements.create("payment",{defaultValues:{billingDetails:{email:e}}}),m.mountPaymentElement(o),m.forms[t].paymentElement.on("change",function(e){m.forms[t].paymentType=e.value.type,r.data("link-email")||("google_pay"===e.value.type||"apple_pay"===e.value.type?(m.forms[t].linkElement.destroy(),m.forms[t].linkDestroyed=!0):m.forms[t].linkDestroyed&&(m.initializeLinkAuthenticationElement(o),m.forms[t].linkDestroyed=!1)),r.data("type",e.value.type),e.empty?(r.data("completed",!1),r.find("label.wpforms-error").toggle("card"===e.value.type)):(m.forms[t].elementsModified=!0,e.complete?(r.data("completed",!0),m.hideStripeFieldError(o)):r.data("completed",!1))}),m.forms[t].paymentElement.on("loaderror",function(e){m.displayStripeLoadError(o,e.error.message)}),m.forms[t].paymentElement.on("focus",function(){m.triggerPaymentElementFocusEvent(o)})},triggerPaymentElementFocusEvent(e){d(t).trigger("wpformsStripePaymentElementFocus",[e])},mountPaymentElement(e){var e=e.data("formid"),o="#wpforms-field-stripe-payment-element-"+e;m.forms[e].paymentElement.mount(o)},linkEmailMappedFieldTriggers(o){let r=o.find(".wpforms-field-stripe-credit-card .wpforms-field-row");var t=m.getMappedLinkEmailField(o);if(t){let e=o.data("formid");t.on("change",function(){m.forms[e].linkEmail=d(this).val(),r.data("completed")||m.initializePaymentElement(o,d(this).val())})}else r.data("linkCompleted",!1),m.initializeLinkAuthenticationElement(o)},getMappedLinkEmailField(e){var o=e.find(".wpforms-field-stripe-credit-card .wpforms-field-row").data("link-email");return o?(e=e.data("formid"),d(`#wpforms-${e}-field_`+o)):null},initializeLinkAuthenticationElement(o){let r=o.find(".wpforms-field-stripe-credit-card .wpforms-field-row"),t=o.data("formid");m.forms[t].linkElement=m.forms[t].elements.create("linkAuthentication"),m.mountLinkElement(o),m.forms[t].linkElement.on("change",function(e){e.empty||(m.forms[t].elementsModified=!0,e.complete?(void 0!==e.value.email&&(m.forms[t].linkEmail=e.value.email),r.data("linkCompleted",!0),m.hideStripeFieldError(o)):r.data("linkCompleted",!1))}),m.forms[t].linkElement.on("loaderror",function(e){m.displayStripeLoadError(o,e.error.message)}),m.forms[t].linkElement.on("focus",function(){m.triggerPaymentElementFocusEvent(o)})},mountLinkElement(e){var e=e.data("formid"),o="#wpforms-field-stripe-link-element-"+e;m.forms[e].linkElement.mount(o)},submitHandler(e){var e=d(e),o=e.find(".wpforms-field-stripe-credit-card"),r=o.find(".wpforms-field-row"),t=e.validate().form(),n=e.data("formid"),i=r.data("required"),l=-1!==["google_pay","apple_pay"].indexOf(m.forms[n].paymentType),n=!r.data("link-email")&&m.forms[n].elementsModified||r.data("completed")||l;let a=!1;o.hasClass("wpforms-conditional-hide")||(a=i||n&&!i),t&&a?(e.find(".wpforms-submit").prop("disabled",!0),e.find(".wpforms-submit-spinner").show(),m.createPaymentMethod(e)):t?s(e):(e.find(".wpforms-submit").prop("disabled",!1),e.validate().cancelSubmit=!0)},updateElementsTotalAmount(e,o,r){var t;r&&(o=o.data("formid"),m.forms[o])&&m.forms[o].elements&&(t=wpforms.getCurrency(),m.forms[o].total=r,m.forms[o].elements.update({amount:parseInt(wpforms.numberFormat(r,t.decimals,"",""),10)}))},async createPaymentMethod(r){let e=r.data("formid");m.forms[e].total?await m.stripe.createPaymentMethod({elements:m.forms[e].elements}).then(function(o){if(o.error){let e=["incomplete_email","email_invalid","incomplete_number","invalid_number","incomplete_expiry","invalid_expiry_year_past","invalid_expiry_year","incomplete_cvc","incomplete_name","incomplete_phone_number","empty_phone_number","invalid_postal_code"].includes(o.error.code)?"":o.error.message;"token_already_used"===o.error.code&&(e=wpforms_stripe.i18n.token_already_used),void m.displayStripeFieldError(r,e)}else m.forms[e].paymentMethodId=o.paymentMethod.id,r.append('<input type="hidden" class="wpforms-stripe-payment-method-id" name="wpforms[payment_method_id]" value="'+m.forms[e].paymentMethodId+'">'),s(r)}):s(r)},formAjaxUnblock(e){var o=e.find(".wpforms-submit"),r=o.data("submit-text");r&&o.text(r),o.prop("disabled",!1),o.removeClass("wpforms-disabled"),e.closest(".wpforms-container").css("opacity",""),e.find(".wpforms-submit-spinner").hide()},displayStripeError(e,o){wpforms.clearFormAjaxGeneralErrors(e),wpforms.displayFormAjaxErrors(e,o),wpforms.resetFormRecaptcha(e),m.formAjaxUnblock(e)},displayStripeFieldError(e,o){var r=e.find(".wpforms-stripe-credit-card-hidden-input").attr("name"),t=e.find(".wpforms-field-stripe-credit-card"),n={};o&&(n[r]=o),wpforms.displayFormAjaxFieldErrors(e,n),!t.is(":visible")&&0<e.find(".wpforms-page-indicator-steps").length&&wpforms.setCurrentPage(e,{}),wpforms.scrollToError(t),m.formAjaxUnblock(e)},hideStripeFieldError(e){e.find(".wpforms-field-stripe-credit-card .wpforms-error").hide()},displayStripeLoadError(e,o){o=wpforms_stripe.i18n.element_load_error+"<br /> "+o,m.displayStripeError(e,o)},pageChange(e,o,r,t){var n=r.find(".wpforms-field-stripe-credit-card .wpforms-field-row"),i=r.data("formid");n.length&&-1!==["card","link"].indexOf(m.forms[i].paymentType)&&(m.forms[i].elementsModified||"card"!==m.forms[i].paymentType||(m.forms[i].paymentElement.unmount(),m.mountPaymentElement(r),n.data("link-email"))||(m.forms[i].linkElement.unmount(),m.mountLinkElement(r)),!n.is(":visible")||!n.data("required")&&!m.forms[i].elementsModified||m.forms[i].lockedPageToSwitch&&m.forms[i].lockedPageToSwitch!==o||"prev"===t||(t=void 0===n.data("linkCompleted")||n.data("linkCompleted"),n.data("completed")&&t?m.hideStripeFieldError(r):(m.forms[i].lockedPageToSwitch=o,m.displayStripeFieldError(r,wpforms_stripe.i18n.empty_details),e.preventDefault())))},convFormsFieldActivationAfter(e,o){m.setupPaymentElement(o.$el.closest("form"))},getCssVar(e,o){return console.warn('WARNING! Function "WPFormsStripePaymentElement.getCssVar()" has been deprecated, please use the "WPForms.FrontendModern.getCssVar()" function instead!'),WPForms?.FrontendModern?.getCssVar()},updatePaymentElementStylesModern(e){var o;d.isEmptyObject(m.getCustomAppearanceOptions())&&i.WPForms&&WPForms.FrontendModern&&e&&0!==e.length&&!e.closest(".wpforms-container").hasClass("wpforms-lead-forms-container")&&(o=e.data("formid"),m.forms[o])&&(o=m.forms[o].elements,e=WPForms.FrontendModern.getCssVars(e),m.updateFormElementsAppearance(o,e))},updateFormElementsAppearance(e,o){var r,t;e&&e._commonOptions&&(r=e._commonOptions.appearance,o["field-size-padding-v"]=(parseInt(o["field-size-input-height"],10)-parseInt(o["field-size-font-size"],10)-6)/2+"px",r.variables.spacingGridRow=o["field-size-input-spacing"],r.variables.spacingGridColumn="20px",r.variables.spacingTab="10px",r.variables.colorText=WPForms.FrontendModern.getSolidColor(o["field-text-color"]),t=WPFormsUtils.cssColorsUtils.isTransparentColor(o["field-background-color"])?o["field-menu-color"]:o["field-background-color"],t=WPForms.FrontendModern.getSolidColor(t),r.rules={".Input":{border:o["field-border-size"]+" "+o["field-border-style"]+" "+o["field-border-color"],borderRadius:o["field-border-radius"],padding:o["field-size-padding-v"]+" "+o["field-size-padding-h"],fontSize:o["field-size-font-size"],lineHeight:o["field-size-font-size"],backgroundColor:o["field-background-color"],boxShadow:"none",outline:"none"},".Input:focus":{backgroundColor:t,borderColor:o["button-background-color"],borderStyle:"solid",boxShadow:"0 0 0 1px "+o["button-background-color"],outline:"none"},".Input--invalid":{borderColor:o["label-error-color"],boxShadow:"none",color:r.variables.colorText,outline:"none"},".Input--invalid:focus":{borderColor:o["label-error-color"],boxShadow:"0 0 0 1px "+o["label-error-color"],outline:"none"},".Input:disabled":{...r.rules[".Input:disabled"]},".Input::placeholder":{color:WPForms.FrontendModern.getColorWithOpacity(o["field-text-color"],"0.5"),fontSize:o["field-size-font-size"]},".CheckboxInput":{border:"1px solid "+o["field-border-color"],backgroundColor:o["field-background-color"]},".CheckboxInput--checked":{borderColor:o["button-background-color"],backgroundColor:o["button-background-color"]},".CodeInput":{border:"1px solid "+o["field-text-color"],backgroundColor:t},".CodeInput:focus":{borderWidth:"2px",boxShadow:"0 0 0 1px "+o["button-background-color"],outline:"none"},".Label":{fontSize:o["label-size-sublabel-font-size"],margin:`0 0 ${o["field-size-sublabel-spacing"]} 0`,color:o["label-sublabel-color"],lineHeight:r.rules[".Label"].lineHeight},".Label, .Label--floating":{...r.rules[".Label, .Label--floating"]},".Error":{fontSize:o["label-size-sublabel-font-size"],margin:o["field-size-sublabel-spacing"]+" 0 0 0",color:o["label-error-color"]},".Tab":{border:"1px solid "+WPForms.FrontendModern.getColorWithOpacity(o["field-border-color"],"0.5"),borderRadius:o["field-border-radius"],backgroundColor:"transparent",boxShadow:"none",marginTop:"0"},".Tab:focus":{border:"1px solid "+WPForms.FrontendModern.getColorWithOpacity(o["button-background-color"],"0.5"),boxShadow:"0 0 0 3px "+WPForms.FrontendModern.getColorWithOpacity(o["button-background-color"],"0.25"),outline:"none"},".Tab:hover":{border:"1px solid "+o["field-border-color"]},".Tab--selected":{borderColor:o["button-background-color"],boxShadow:"0 0 0 1px "+o["button-background-color"],backgroundColor:o["field-background-color"]},".Tab--selected:hover":{borderColor:o["button-background-color"]},".Tab--selected:focus":{borderColor:o["button-background-color"],boxShadow:"0 0 0 1px "+o["button-background-color"]},".TabLabel":{color:o["field-text-color"]},".TabIcon":{fill:WPForms.FrontendModern.getColorWithOpacity(o["field-text-color"],"0.75")},".TabIcon--selected":{fill:o["field-text-color"]},".TabIcon:hover":{color:o["field-text-color"],fill:o["field-text-color"]},".TabLabel--selected":{color:o["button-background-color"]},".Block":{border:"1px solid "+WPForms.FrontendModern.getColorWithOpacity(o["field-border-color"],"0.5"),backgroundColor:t,borderRadius:o["field-border-radius"],boxShadow:"none"},".AccordionItem":{...r.rules[".AccordionItem"],backgroundColor:t,paddingLeft:0,paddingRight:0,color:o["field-text-color"]},[m.getPickerItemSelectors().join(", ")]:{border:0,boxShadow:"none",backgroundColor:t}},e.update({appearance:r}))},getCustomAppearanceVariables(e){var o,r=e.data("formid");return m.forms[r]?.customVars||(e=e.find(".wpforms-stripe-credit-card-hidden-input"),o=m.getElementPrimaryColor(e),m.forms[r].customVars={focusColor:m.getCssPropertyValue(e,"--accent-color")||m.getCssPropertyValue(e,"color"),borderColorWithOpacity:WPFormsUtils.cssColorsUtils.getColorWithOpacity(o,"0.1")}),m.forms[r].customVars},getElementPrimaryColor(e){e=m.getCssPropertyValue(e,"--primary-color")||m.getCssPropertyValue(e,"color");return i?.WPForms?.FrontendModern?WPForms.FrontendModern.getSolidColor(e):e},getPickerItemSelectors(){return[".PickerItem",".PickerItem:hover",".PickerItem--selected",".PickerItem--selected:hover",".PickerItem--highlight",".PickerItem--highlight:hover"]}};return m})(document,window,jQuery);WPFormsStripePaymentElement.init();