// WPForms Modern Full styles.
//
// File Upload field.
//
// @since 1.8.1

div.wpforms-container-full {

	.wpforms-field-file-upload {
		.wpforms-hide {
			display: none !important;
		}

		// Classic input.
		input[type=file] {
			font-style: normal;
			font-weight: 400;
			font-size: var( --wpforms-label-size-sublabel-font-size );
			line-height: var( --wpforms-label-size-sublabel-line-height );
			color: var( --wpforms-label-sublabel-color );
			padding: 1px;
			height: auto;
			width: 60%;
			border: none !important;
			box-shadow: none;
			background-color: transparent;
			cursor: pointer;

			// File Upload classic button.
			//
			// @since 1.8.1
			//
			@mixin wpforms-file-upload-classic-button() {
				background-color: var( --wpforms-field-background-color );
				background-clip: padding-box;
				border-width: var( --wpforms-field-border-size );
				border-style: var( --wpforms-field-border-style );
				border-color: var( --wpforms-field-border-color );
				border-radius: var( --wpforms-field-border-radius );
				color: var( --wpforms-field-text-color );
				padding: calc( var( --wpforms-field-size-input-height ) / 6 ) var( --wpforms-field-size-padding-h );
				font-weight: 400;
				font-size: calc( var( --wpforms-label-size-sublabel-font-size ) - 2px );
				line-height: 1.1;
				margin-inline-end: $spacing_s;
				cursor: pointer;
				transition: all $transition_normal ease-out;
			}

			@mixin wpforms-file-upload-classic-button-hover() {
				background: linear-gradient( 0deg, rgba( 0, 0, 0, 0.03 ), rgba( 0, 0, 0, 0.03 ) ), var( --wpforms-field-background-color );
				background-clip: padding-box;
			}

			// Webkit.
			&::-webkit-file-upload-button {
				@include wpforms-file-upload-classic-button();

				&:focus,
				&:active {
					@include wpforms-input-focus();
				}
			}

			// Firefox.
			&::file-selector-button {
				@include wpforms-file-upload-classic-button();

				&:focus,
				&:active {
					@include wpforms-input-focus();
				}
			}

			&:hover {
				&::-webkit-file-upload-button {
					@include wpforms-file-upload-classic-button-hover();
				}

				&::file-selector-button {
					@include wpforms-file-upload-classic-button-hover();
				}
			}

			&:focus {
				outline: none;

				&::-webkit-file-upload-button {
					@include wpforms-input-focus();
				}

				&::file-selector-button {
					@include wpforms-input-focus();
				}
			}
		}

		p.wpforms-file-upload-capture-camera-classic {
			margin-top: 20px;
			display: block;

			a.camera {
				color: var( --wpforms-field-text-color );
				text-decoration: underline;
			}
		}

		// Modern input (dropzone).
		.wpforms-uploader {
			background-color: var( --wpforms-field-background-color );
			background-clip: padding-box;
			border-radius: var( --wpforms-field-border-radius );
			color: var( --wpforms-field-text-color );
			border-width: var( --wpforms-field-border-size );
			border-style: dashed;
			border-color: var( --wpforms-field-border-color );
			font-size: var( --wpforms-field-size-font-size );
			padding: 30px 15px;

			&.wpforms-focus,
			&:focus-within {
				@include wpforms-input-focus();
			}

			.modern-title {
				font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
				font-size: var( --wpforms-label-size-sublabel-font-size );
				color: #777777;

				& span {
					text-decoration: underline;

					&:hover {
						text-decoration: none;
					}
				}
			}

			.dz-message {
				font-size: var( --wpforms-field-size-font-size );
				color: var( --wpforms-field-text-color );

				.modern-hint {
					font-size: var( --wpforms-label-size-sublabel-font-size );
					line-height: var( --wpforms-label-size-sublabel-line-height );
					color: var( --wpforms-field-text-color );
					opacity: 0.6;
				}
			}

			.dz-size {
				font-size: var( --wpforms-label-size-sublabel-font-size );
				line-height: var( --wpforms-label-size-sublabel-line-height );
				color: var( --wpforms-label-sublabel-color );
			}

			.dz-filename {
				font-size: var( --wpforms-label-size-sublabel-font-size );
				line-height: var( --wpforms-label-size-sublabel-line-height );
				color: var( --wpforms-field-text-color );
			}

			.dz-error-message {
				span {
					color: var( --wpforms-label-error-color );
					font-size: var( --wpforms-label-size-sublabel-font-size );
					line-height: var( --wpforms-label-size-sublabel-line-height );
				}
			}

			.dz-remove {
				&:focus {
					opacity: 1;

					&:before,
					&:after {
						background-color: var( --wpforms-button-background-color );
					}
				}
			}

			.dz-preview.dz-error {
				.dz-image {
					border-color: var( --wpforms-label-error-color );
				}

				.dz-remove {
					&:before,
					&:after {
						background-color: var( --wpforms-label-error-color );
					}
				}
			}
		}

		// Error state.
		&.wpforms-has-error {

			// Classic input.
			input[type=file] {
				border: none !important;
				box-shadow: none;

				&::-webkit-file-upload-button {
					@include wpforms-input-error();
				}

				&:hover {
					border: none !important;
					box-shadow: none;

					&::-webkit-file-upload-button {
						@include wpforms-input-error-hover();
					}
				}

				&:focus {
					border: none !important;
					box-shadow: none;

					&::-webkit-file-upload-button {
						@include wpforms-input-error-focus();
					}
				}
			}

			// Modern input (dropzone).
			.wpforms-uploader {
				@include wpforms-input-error();

				&:hover {
					@include wpforms-input-error-hover();
				}

				&.wpforms-focus,
				&:focus-within {
					@include wpforms-input-error-focus();
				}
			}
		}
	}
}
