<?php
/**
 * Plugin Check filters for WPForms.
 */

// phpcs:disable Generic.Commenting.DocComment.MissingShort, WPForms.Comments.DescriptionStopSymbol.MissDescription
/** @noinspection AutoloadingIssuesInspection */
/** @noinspection PhpMultipleClassesDeclarationsInOneFile */
/** @noinspection PhpIllegalPsrClassPathInspection */
/** @noinspection PhpUndefinedNamespaceInspection */
/** @noinspection PhpUndefinedClassInspection */
/** @noinspection PhpMultipleClassDeclarationsInspection */
/** @noinspection PhpUndefinedConstantInspection */
// phpcs:enable Generic.Commenting.DocComment.MissingShort, WPForms.Comments.DescriptionStopSymbol.MissDescription

// phpcs:disable Generic.Classes.DuplicateClassName.Found, Generic.Files.OneObjectStructurePerFile.MultipleFound, Universal.Files.SeparateFunctionsFromOO.Mixed

namespace WPForms\DevTools\PCP;

use WordPress\Plugin_Check\Checker\Check_Result;
use WordPress\Plugin_Check\Checker\Checks\Plugin_Repo\File_Type_Check as PluginFileTypeCheck;
use WordPress\Plugin_Check\Checker\Checks\Plugin_Repo\Plugin_Review_PHPCS_Check as PluginReviewPHPCSCheck;
use WordPress\Plugin_Check\Checker\Checks\General\I18n_Usage_Check as I18nUsageCheck;
use WordPress\Plugin_Check\Checker\Runtime_Check;
use WP_CLI;
use WP_Error;
use WordPress\Plugin_Check\CLI\Plugin_Check_Command;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) && ! ( defined( 'WP_CLI' ) && WP_CLI ) ) {
	exit;
}

if ( defined( 'WP_CLI' ) && WP_CLI ) {
	// If PCP was already loaded with `--require=plugin-check/cli.php` parameter, we cannot use our filters.
	if ( class_exists( Plugin_Check_Command::class ) ) {
		return;
	}

	// phpcs:ignore WordPress.Security.ValidatedSanitizedInput.MissingUnslash, WordPress.Security.ValidatedSanitizedInput.InputNotSanitized
	$command_string = implode( ' ', $_SERVER['argv'] ?? [] );

	// Do not load our filters for other commands.
	if ( strpos( $command_string, 'plugin check' ) === false ) {
		return;
	}

	require_once __DIR__ . '/../plugins/plugin-check/cli.php';

	WP_CLI::add_hook(
		'after_wp_config_load',
		static function () {
			new WPFormsChecks();
		}
	);
}

if ( defined( 'WPFORMS_DEV_TOOLS_PCP' ) ) {
	// Do not load this mu-plugin twice. For the second time, it can be loaded from the `object-cache.php` file.
	return;
}

/**
 * WPF PCP mu-plugin constant.
 *
 * @since 0.34
 */
define( 'WPFORMS_DEV_TOOLS_PCP', true );

// We cannot define WP constants here because WP-CLI mode must define them.
// phpcs:disable WPForms.Comments.PHPDocDefine.MissPHPDoc
define( 'WPF_PCP_ABSPATH', dirname( __DIR__, 2 ) . '/' );
define( 'WPF_PCP_CONTENT_DIR', WPF_PCP_ABSPATH . 'wp-content' );
define( 'WPF_PCP_PLUGIN_DIR', WPF_PCP_CONTENT_DIR . '/plugins' );
// phpcs:enable WPForms.Comments.PHPDocDefine.MissPHPDoc

/**
 * Check if the PCP plugin is active.
 *
 * @since 0.34
 *
 * @param string $basename PCP plugin basename.
 *
 * @return bool
 */
function wpf_pcp_active( string $basename ): bool {

	// When the drop-in is used, the constant is defined.
	// In this case, we cannot use `is_plugin_active` function - too many WP Core files are absent.
	if ( defined( 'WP_PLUGIN_CHECK_OBJECT_CACHE_DROPIN_VERSION' ) ) {
		return true;
	}

	// We need `is_plugin_active` function.
	require_once WPF_PCP_ABSPATH . '/wp-admin/includes/plugin.php';

	return is_plugin_active( $basename );
}

/**
 * Load PCP plugin.
 *
 * @since 0.34
 *
 * @return bool
 */
function wpf_pcp_load(): bool {

	if ( ! defined( 'WP_PLUGIN_DIR' ) ) {
		// phpcs:disable WPForms.Comments.PHPDocDefine.MissPHPDoc
		define( 'WP_PLUGIN_DIR', WPF_PCP_CONTENT_DIR . '/plugins' );
		// phpcs:enable WPForms.Comments.PHPDocDefine.MissPHPDoc
	}

	$basename = 'plugin-check/plugin.php';

	if ( ! wpf_pcp_active( $basename ) ) {
		return false;
	}

	$main_file  = WP_PLUGIN_DIR . '/' . $basename;
	$data       = get_file_data( $main_file, [ 'Version' ], 'plugin' );
	$version    = $data[0] ?? '';
	$autoloader = WP_PLUGIN_DIR . '/plugin-check/vendor/squizlabs/php_codesniffer/autoload.php';

	// In version 1.2.0, PCP made backward compatibility breaking change by adding a parameter to get_args() method.
	if ( ! $version || ! file_exists( $autoloader ) || version_compare( $version, '1.2.0', '<' ) ) {
		wp_die( esc_html( 'WPForms Dev Tool Plugin requires Plugin Check Plugin (PCP) version 1.2.0 or higher.' ) );
	}

	// Load PCP plugin classes.
	require_once $autoloader;

	return true;
}

// In WP-CLI mode, we cannot check if plugin is loaded - too many WP Core files are not loaded yet.
if ( ! ( defined( 'WP_CLI' ) && WP_CLI ) && ! wpf_pcp_load() ) {
	return;
}

/**
 * Custom PHPCS check for WPForms.
 *
 * @since 0.33
 */
class WPFormsPluginReviewPHPCSCheck extends PluginReviewPHPCSCheck {

	// phpcs:disable WPForms.PHP.HooksMethod.InvalidPlaceForAddingHooks

	/**
	 * Get args.
	 *
	 * @since        0.28
	 * @since        0.33 Added the $result parameter.
	 *
	 * @param Check_Result $result The check result to amend, including the plugin context to check.
	 *
	 * @return array
	 * @noinspection ReturnTypeCanBeDeclaredInspection
	 * @noinspection PhpMissingReturnTypeInspection
	 */
	protected function get_args( Check_Result $result ) {

		return [
			'extensions' => 'php',
			'standard'   => WP_PLUGIN_DIR . '/wpf/src/PCP/plugin-review-wpforms.xml',
		];
	}
}

/**
 * Class WPFormsI18nUsageCheck.
 *
 * @since 0.34
 */
class WPFormsI18nUsageCheck extends I18nUsageCheck {

	/**
	 * Returns an associative array of arguments to pass to PHPCS.
	 *
	 * @since 0.34
	 *
	 * @param Check_Result $result The check result to amend, including the plugin context to check.
	 *
	 * @return array An associative array of PHPCS CLI arguments.
	 */
	protected function get_args( Check_Result $result ): array {

		$args = parent::get_args( $result );

		if ( $args['runtime-set']['text_domain'] === 'wpforms' ) {
			$args['runtime-set']['text_domain'] = 'wpforms, wpforms-lite';
		}

		return $args;
	}
}

/**
 * WPFormsChecks class.
 * Customize PCP checks for WPForms.
 *
 * @since 0.34
 */
class WPFormsChecks {

	/**
	 * Object cache file.
	 *
	 * @since 0.34
	 *
	 * @var string
	 */
	private $object_cache_file;

	/**
	 * WPFormsChecks constructor.
	 *
	 * @since 0.34
	 */
	public function __construct() {

		$this->object_cache_file = WPF_PCP_CONTENT_DIR . '/object-cache.php';

		require_once WPF_PCP_ABSPATH . 'wp-includes/plugin.php';

		$this->hooks();
	}

	/**
	 * Hooks.
	 *
	 * @since 0.34
	 *
	 * @return void
	 */
	public function hooks() {

		add_filter( 'wp_plugin_check_checks', [ $this, 'checks_filter' ] );
		add_filter( 'wp_plugin_check_ignore_directories', [ $this, 'ignore_directories_filter' ] );

		add_action( 'wp_ajax_plugin_check_clean_up_environment', [ $this, 'clean_up_environment' ], 0 );
	}

	/**
	 * Filter the checks to run.
	 *
	 * @since 0.34
	 *
	 * @param array|mixed $checks Checks.
	 *
	 * @return array
	 */
	public function checks_filter( $checks ): array {

		$checks = (array) $checks;

		/**
		 * 1. The readme.txt file for core wpforms-lite plugin exists in a different folder. We do not provide a readme file for addons.
		 * 2. We add late-escaping check to the plugin-review-wpforms.xml check, but excluding wpforms safe functions.
		 * 3. We do not have the plugin updater in the lite version.
		 * 4. We use the same file wpforms.php for Pro and Lite versions. It contains the proper text domain wpforms-lite.
		 * 5. We cannot add plugin images in admin via wp_get_attachment_image() or similar function.
		 */
		unset(
			$checks['plugin_readme'],
			$checks['late_escaping'],
			$checks['plugin_updater'],
			$checks['plugin_header_text_domain'],
			$checks['image_functions']
		);

		// Do not check library core files - we need our versions.
		$checks['file_type'] = new PluginFileTypeCheck( PluginFileTypeCheck::TYPE_ALL & ~PluginFileTypeCheck::TYPE_LIBRARY_CORE );

		// Replace the standard with the custom wpforms-related ruleset.
		$checks['plugin_review_phpcs'] = new WPFormsPluginReviewPHPCSCheck();

		// Replace the i18n_usage check to allow wpforms-lite text domain.
		$checks['i18n_usage'] = new WPFormsI18nUsageCheck();

		$action = filter_input( INPUT_POST, 'action' ) ?? '';

		if ( $action === 'plugin_check_set_up_environment' && $this->has_runtime_check( $checks ) && $this->validate_request() ) {
			$this->manage_object_cache();
		}

		return $checks;
	}

	/**
	 * Filter the directories to ignore.
	 *
	 * @since 0.34
	 *
	 * @param array|mixed $default_ignore_directories Default ignore directories.
	 *
	 * @return array
	 */
	public function ignore_directories_filter( $default_ignore_directories ): array {

		$wpforms_ignore_directories = [
			'assets/images',
			'assets/lite/images',
			'assets/pro/images',
			'build',
			'pro/libs',
			'vendor_prefixed',
		];

		return array_unique( array_merge( (array) $default_ignore_directories, $wpforms_ignore_directories ) );
	}

	/**
	 * Clean up the environment.
	 *
	 * @since 0.34
	 *
	 * @return void
	 */
	public function clean_up_environment() {

		$this->validate_request();
		$this->delete_object_cache();
	}

	/**
	 * Validate request.
	 *
	 * @since 0.34
	 *
	 * @return bool
	 */
	private function validate_request(): bool {

		$nonce         = (string) filter_input( INPUT_POST, 'nonce', FILTER_SANITIZE_FULL_SPECIAL_CHARS );
		$valid_request = $this->verify_request( $nonce );

		if ( is_wp_error( $valid_request ) ) {
			wp_send_json_error( $valid_request, 403 );
		}

		return true;
	}

	/**
	 * Verify the request.
	 *
	 * @since 0.34
	 *
	 * @param string $nonce The request nonce passed.
	 *
	 * @return bool|WP_Error True if the nonce is valid. WP_Error if invalid.
	 */
	private function verify_request( string $nonce ) {

		// phpcs:disable WPForms.PHP.ValidateDomain.InvalidDomain
		if ( ! wp_verify_nonce( $nonce, 'plugin-check-run-checks' ) ) {
			return new WP_Error( 'invalid-nonce', __( 'Invalid nonce', 'plugin-check' ) );
		}

		if ( ! current_user_can( 'activate_plugins' ) ) {
			return new WP_Error(
				'invalid-permissions',
				__( 'Invalid user permissions, you are not allowed to perform this request.', 'plugin-check' )
			);
		}

		// phpcs:enable WPForms.PHP.ValidateDomain.InvalidDomain

		return true;
	}

	/**
	 * Create the object cache file and add our hooks.
	 * PCP plugin uses the hack with object-cache.php to run its code as early as possible.
	 * They proceed this way because on wordpress.com the mu-plugins folder is protected from writing.
	 *
	 * @since 0.34
	 *
	 * @return void
	 */
	private function manage_object_cache() {

		$this->create_object_cache();
		$this->update_object_cache();
	}

	/**
	 * Create the object cache file.
	 *
	 * @since 0.34
	 *
	 * @return void
	 */
	private function create_object_cache() {

		// phpcs:ignore WordPress.PHP.NoSilencedErrors.Discouraged
		if ( ! @file_exists( $this->object_cache_file ) ) {
			if ( ! defined( 'WP_PLUGIN_CHECK_PLUGIN_DIR_PATH' ) ) {
				// phpcs:disable WPForms.Comments.PHPDocDefine.MissPHPDoc
				define( 'WP_PLUGIN_CHECK_PLUGIN_DIR_PATH', WP_PLUGIN_DIR . '/plugin-check/' );
				// phpcs:enable WPForms.Comments.PHPDocDefine.MissPHPDoc
			}

			$object_cache_file_copy = WP_PLUGIN_CHECK_PLUGIN_DIR_PATH . 'drop-ins/object-cache.copy.php';

			copy( $object_cache_file_copy, $this->object_cache_file );
		}
	}

	/**
	 * Update the object cache file. Add our mu-plugin invocation.
	 *
	 * @since 0.34
	 *
	 * @return void
	 */
	private function update_object_cache() {

		// No object-cache.php file created, or it is not writable, bail out.
		// phpcs:ignore WordPress.WP.AlternativeFunctions.file_system_operations_is_writable
		if ( ! is_writable( $this->object_cache_file ) ) {
			return;
		}

		// phpcs:ignore WordPress.WP.AlternativeFunctions.file_system_read_file_get_contents
		$object_cache_file_content   = (string) file_get_contents( $this->object_cache_file );
		$pcp_dropin_version_constant = 'WP_PLUGIN_CHECK_OBJECT_CACHE_DROPIN_VERSION';

		// Not a PCP drop-in, bail out.
		if ( false === strpos( $object_cache_file_content, $pcp_dropin_version_constant ) ) {
			return;
		}

		$wpf_pcp_file = __FILE__;

		// Already patched, bail out.
		if ( false !== strpos( $object_cache_file_content, $wpf_pcp_file ) ) {
			return;
		}

		// Get the PCP drop-in version.
		if (
			! preg_match(
				"/define\( '$pcp_dropin_version_constant', (.+) \);/",
				$object_cache_file_content,
				$m
			)
		) {
			return;
		}

		$pcp_dropin_version = $m[1];

		// Add invocation of this mu-plugin to the object-cache.php file.
		$object_cache_file_content = str_replace(
			[ "\r\n", $m[0] ],
			[ "\n", "$m[0]\n\nrequire_once '$wpf_pcp_file';" ],
			$object_cache_file_content
		);

		// phpcs:ignore WordPress.WP.AlternativeFunctions.file_system_operations_file_put_contents
		file_put_contents( $this->object_cache_file, $object_cache_file_content );

		// We need this to make \WordPress\Plugin_Check\Checker\Runtime_Environment_Setup::can_set_up() return true.
		// phpcs:disable WPForms.Comments.PHPDocDefine.MissPHPDoc
		define( $pcp_dropin_version_constant, (int) $pcp_dropin_version );
		// phpcs:enable WPForms.Comments.PHPDocDefine.MissPHPDoc
	}

	/**
	 * Delete the object cache file.
	 *
	 * @since 0.34
	 *
	 * @return void
	 */
	private function delete_object_cache() {

		// No object-cache.php file created, or it is not readable, bail out.
		// phpcs:ignore WordPress.WP.AlternativeFunctions.file_system_operations_is_writable
		if ( ! is_readable( $this->object_cache_file ) ) {
			return;
		}

		// phpcs:ignore WordPress.WP.AlternativeFunctions.file_system_read_file_get_contents
		$object_cache_file_content   = (string) file_get_contents( $this->object_cache_file );
		$pcp_dropin_version_constant = 'WP_PLUGIN_CHECK_OBJECT_CACHE_DROPIN_VERSION';

		// Not a PCP drop-in, bail out.
		if ( false === strpos( $object_cache_file_content, $pcp_dropin_version_constant ) ) {
			return;
		}

		$wpf_pcp_file = __FILE__;

		// Not patched, bail out.
		if ( false === strpos( $object_cache_file_content, $wpf_pcp_file ) ) {
			return;
		}

		// phpcs:ignore WordPress.PHP.NoSilencedErrors.Discouraged, WordPress.WP.AlternativeFunctions.unlink_unlink
		@unlink( $this->object_cache_file );
	}

	/**
	 * Check for a Runtime_Check in a list of checks.
	 *
	 * @since 0.34
	 *
	 * @param array $checks An array of Check instances.
	 *
	 * @return bool True if a Runtime_Check exists in the array, false if not.
	 */
	private function has_runtime_check( array $checks ): bool {

		foreach ( $checks as $check ) {
			if ( $check instanceof Runtime_Check ) {
				return true;
			}
		}

		return false;
	}
}

new WPFormsChecks();
