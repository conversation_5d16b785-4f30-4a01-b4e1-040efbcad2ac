:root {
  --background-white: #fff;
  --background-grey: #f6f7f7;
  --green: #00ba37;
  --orange: #e27730;
  --text-grey: #50575e;
  --meta-border: 1px solid #ddd;
}

.whats-new-preview {
  display: grid;
  max-width: 1000px;
  background: var(--background-white);
  border: 1px solid #c3c4c7;
  box-sizing: border-box;
}

.whats-new-preview.fifty-fifty {
  grid-template-columns: repeat(2, 1fr);
}

tr:nth-child(even) .whats-new-preview.one-third-two-thirds {
  grid-template-columns: 1fr 2fr;
}

tr:nth-child(odd) .whats-new-preview.one-third-two-thirds {
  grid-template-columns: 2fr 1fr;
}

.whats-new-preview.full-width {
  grid-template-columns: 1fr;
  text-align: center;
  background: var(--background-grey);
}

.whats-new-preview.full-width .whats-new-content .whats-new-new-feature {
  background: var(--background-white);
  color: var(--green);
}

.whats-new-preview.full-width .whats-new-image {
  background: linear-gradient(
    to bottom,
    var(--background-grey) 0%,
    var(--background-grey) 50%,
    var(--background-white) 50%
  );
  padding-bottom: 25px;
}

.whats-new-preview.full-width .whats-new-content,
.whats-new-preview.full-width .whats-new-image {
  padding: 25px 120px;
}

.whats-new-preview .whats-new-content,
.whats-new-preview .whats-new-image img {
  box-sizing: border-box;
}

tr:nth-child(odd) .whats-new-preview.fifty-fifty .whats-new-content,
tr:nth-child(odd) .whats-new-preview.one-third-two-thirds .whats-new-content,
tr:nth-child(even) .whats-new-preview.fifty-fifty .whats-new-image,
tr:nth-child(even) .whats-new-preview.one-third-two-thirds .whats-new-image {
  grid-column: 1 / 2;
  grid-row: 1 / 2;
  padding: 25px 25px 25px 120px;
}

tr:nth-child(even) .whats-new-preview.fifty-fifty .whats-new-content,
tr:nth-child(even) .whats-new-preview.one-third-two-thirds .whats-new-content,
tr:nth-child(odd) .whats-new-preview.fifty-fifty .whats-new-image,
tr:nth-child(odd) .whats-new-preview.one-third-two-thirds .whats-new-image {
  grid-column: 2 / 3;
  grid-row: 1 / 2;
  padding: 25px 120px 25px 25px;
}

.whats-new-preview .whats-new-content .whats-new-new-feature {
  padding: 8px 10px;
  border-radius: 3px;
  background: #edfaef;
  color: var(--green);
  font-size: 10px;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  display: inline-block;
}

.whats-new-preview .whats-new-content .whats-new-title {
  color: #1d2327;
  font-size: 28px;
  font-weight: 500;
  line-height: 36px;
  margin: 15px 0 10px;
}

.whats-new-preview .whats-new-content .whats-new-description {
  color: var(--text-grey);
  font-size: 16px;
  font-weight: 400;
  line-height: 25px;
  margin: 0 0 20px;
}

.whats-new-preview .whats-new-content .button {
  padding: 10px 15px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
  margin-right: 20px;
}

.whats-new-preview .whats-new-content .button-primary {
  background: var(--orange);
  color: var(--background-white);
  border: 1px solid var(--orange);
}

.whats-new-preview .whats-new-content .button-secondary {
  border: 1px solid #8c8f94;
  background: var(--background-white);
  color: var(--text-grey);
}

.whats-new-preview img {
  max-width: 100%;
}

.whats-new-preview img.add-shadow {
  border-radius: var(--radius-xl, 9px);
  background: var(--background-white);
  box-shadow: 0px 14.25px 47.5px 0px rgba(0, 0, 0, 0.15);
  padding: 10px;
}

.whats-new-preview img.has-shadow {
  margin: -20px -30px -38px;
}

.whats-new-meta div {
  margin-top: 10px;
  margin-bottom: 5px;
}

.whats-new-meta div span {
  display: inline-block;
  margin: 0 5px 0 0;
  padding: 5px;
  background: #eee;
  border-right: var(--meta-border);
  border-bottom: var(--meta-border);
  font-size: 11px;
  border-radius: 3px;
  line-height: 1;
}
