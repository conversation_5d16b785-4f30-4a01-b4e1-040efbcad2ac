// WPForms Modern Full styles.
//
// Number Slider field.
//
// @since 1.8.1

// Slider thumb.
//
// @since 1.8.1
//
@mixin wpforms-slider-thumb() {
	appearance: none;
	width: calc( var( --wpforms-field-size-input-height ) * 0.6 );
	height: calc( var( --wpforms-field-size-input-height ) * 0.6 );
	margin-top: calc( -1 * var( --wpforms-field-size-input-height ) * 0.18 );
	background-color: var( --wpforms-button-background-color );
	background-clip: padding-box;
	cursor: pointer;
	border-radius: 100%;
	border-width: var( --wpforms-button-border-size );
	border-style: var( --wpforms-button-border-style );
	border-color: var( --wpforms-button-border-color );
}

div.wpforms-container-full {
	.wpforms-form {

		.wpforms-field-number-slider {
			input[type=range] {
				appearance: none;
				height: calc( var( --wpforms-field-size-input-height ) / 4 );
				padding: 0;
				margin-top: calc( var( --wpforms-field-size-input-spacing ) + var( --wpforms-field-size-input-height ) / 4 );
				margin-bottom: calc( var( --wpforms-field-size-input-height ) / 4 );
				border-radius: var( --wpforms-field-border-radius );
				border-width: var( --wpforms-field-border-size );
				border-style: var( --wpforms-field-border-style );
				border-color: var( --wpforms-field-border-color );

				&:first-child {
					margin-top: calc( var( --wpforms-field-size-input-height ) * 0.25 );
				}

				&:focus {
					@include wpforms-input-focus();

					&:invalid {
						@include wpforms-input-focus();
						@include wpforms-input-invalid();
					}
				}

				&::-webkit-slider-runnable-track {
					height: calc( var( --wpforms-field-size-input-height ) / 4 );
					box-shadow: none;
				}

				&::-webkit-slider-thumb {
					@include wpforms-slider-thumb();
				}

				&::-moz-range-thumb {
					@include wpforms-slider-thumb();
				}

				&::-ms-thumb {
					@include wpforms-slider-thumb();
				}
			}

			.wpforms-field-number-slider-hint {
				font-size: var( --wpforms-label-size-sublabel-font-size );
				line-height: var( --wpforms-label-size-sublabel-line-height );
				color: var( --wpforms-label-sublabel-color );
				padding: var( --wpforms-field-size-sublabel-spacing ) 0 0 0;
				margin: 0;

				b, strong {
					color: var( --wpforms-label-sublabel-color );
				}
			}
		}
	}
}
