.wpforms-admin-empty-state-container {
  color: #50575e;
  font-style: normal;
  padding: 30px;
  text-align: center;
}

.wpforms-admin-empty-state-container .waving-hand-emoji {
  background-image: url(../images/empty-states/waving-hand-emoji.png);
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: 24px 24px;
  display: inline-block;
  padding-left: 34px;
}

.wpforms-admin-empty-state-container h2 {
  color: #1d2327;
  font-family: inherit;
  font-size: 24px;
  font-weight: 600;
  line-height: 29px;
  margin: 0 0 10px;
}

.wpforms-admin-empty-state-container h4 {
  color: #32373c;
  font-family: inherit;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
  margin-block: 0;
}

.wpforms-admin-empty-state-container img {
  max-width: 428px;
  width: 100%;
  margin: 30px auto;
}

.wpforms-admin-empty-state-container p {
  font-family: inherit;
  font-size: 16px;
  line-height: 24px;
  margin-block: 0;
  text-align: center;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms p:first-of-type {
  font-weight: 600;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn, .wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:active {
  border: none;
  border-radius: 3px;
  font-family: inherit;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
  text-align: center;
  color: #ffffff;
  padding: 15px 30px;
  margin: 0;
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:hover, .wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:active:hover {
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:focus, .wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-btn:active:focus {
  outline: none;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-forms .wpforms-admin-no-forms-footer {
  margin-top: 30px;
  font-size: 14px;
  line-height: 16px;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms {
  font-family: 'Helvetica Neue', sans-serif;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid #71d7d7;
  box-sizing: border-box;
  padding: 20px;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms img {
  max-width: 240px;
  width: 100%;
  margin: 0 auto;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms p {
  font-family: inherit;
  font-weight: normal;
  font-size: 14px;
  line-height: 18px;
  text-align: center;
  color: #495157;
  max-width: 450px;
  margin: 20px auto 0;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms a {
  color: inherit;
  text-decoration: underline;
  position: relative;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms a:hover {
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms .wpforms-btn {
  background: #525962;
  border: none;
  border-radius: 3px;
  font-family: inherit;
  font-weight: 600;
  font-size: 12px;
  line-height: 1.5;
  text-align: center;
  text-transform: uppercase;
  color: #ffffff;
  padding: 7px 17px;
  margin: 20px 0 0;
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms .wpforms-btn:hover {
  text-decoration: none;
  background: #2b2c31;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms .wpforms-btn:focus {
  outline: none;
}

.wpforms-admin-empty-state-container.wpforms-elementor-no-forms .wpforms-admin-no-forms-footer {
  font-size: 12px;
  line-height: 1.5;
  color: #6d7882;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-entries img {
  max-width: 413px;
  display: block;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments {
  max-width: 640px;
  margin: 0 auto;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments img {
  display: block;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments .wpforms-btn-lg {
  border-radius: 4px;
  padding: 14px 20px;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments a:not([class]) {
  color: #056aab;
  text-decoration: underline;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments a:not([class]):hover {
  text-decoration: none;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-payments .wpforms-admin-no-forms-footer {
  margin-top: 30px;
  font-size: 14px;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-user-templates h2, .wpforms-admin-empty-state-container.wpforms-admin-no-user-templates h4 {
  font-weight: 500;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-user-templates p {
  font-size: 14px;
  color: #50575e;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-user-templates p.wpforms-admin-no-forms-footer {
  color: #50575e;
}

.wpforms-admin-empty-state-container.wpforms-admin-no-user-templates img {
  max-width: 560px;
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

#wpforms-panel-setup .wpforms-panel-content {
  align-items: stretch;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  overflow: hidden;
  padding: 0;
}

#wpforms-setup-form-name {
  align-items: center;
  background-color: #f6f7f7;
  border-bottom: 1px solid #dcdcde;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  padding: 30px;
}

#wpforms-setup-form-name label {
  display: inline-block;
  font-size: 20px;
  font-weight: 600;
  min-width: 200px;
  padding: 0;
}

#wpforms-setup-name {
  display: inline-block;
  font-size: 20px;
  height: 50px;
  margin-inline-start: 30px;
  padding: 13px 10px;
  width: calc( 100% - 290px);
}

.wpforms-setup-title {
  font-size: 20px;
  font-weight: 600;
  margin: 30px 30px 0 30px;
  color: #3c434a;
}

.wpforms-setup-title .count {
  color: #b0b2b3;
}

.wpforms-setup-desc {
  margin: 10px 30px 30px 30px;
  color: #50575e;
  font-size: 16px !important;
}

.wpforms-setup-desc a {
  color: #50575e;
}

.wpforms-setup-desc a:hover {
  color: #3c434a;
}

.wpforms-setup-templates {
  align-items: stretch;
  border-top: 1px solid #dcdcde;
  display: flex;
  flex-grow: 2;
  justify-content: space-between;
  margin: 0 30px;
  padding: 0 0 30px 0;
}

.wpforms-setup-templates-sidebar {
  border-inline-end: 1px solid #dcdcde;
  padding-inline-end: 30px;
  padding-top: 30px;
  width: 224px;
  box-sizing: content-box;
}

#wpforms-setup-template-search {
  font-size: 16px;
  font-weight: 400;
  height: 40px;
  line-height: 20px;
  padding-inline-start: 35px;
  width: 100%;
  color: #3c434a;
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  box-shadow: none;
}

#wpforms-setup-template-search::placeholder {
  color: #b0b2b3;
  font-weight: 400;
}

#wpforms-setup-template-search:focus {
  border-color: #036aab;
  box-shadow: 0 0 0 1px #036aab;
}

.wpforms-setup-templates-search-wrap {
  position: relative;
}

.wpforms-setup-templates-search-wrap i.fa {
  color: #b0b2b3;
  display: block;
  font-size: 16px;
  inset-inline-start: 10px;
  position: absolute;
  top: 12px;
  width: 16px;
}

.wpforms-setup-templates-categories {
  margin-top: 30px;
  width: 224px;
  box-sizing: content-box;
}

.wpforms-setup-templates-categories > li {
  color: #3c434a;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  line-height: 16px;
  margin: 0;
}

.wpforms-setup-templates-categories > li div {
  border-radius: 4px;
  padding: 10px;
}

.wpforms-setup-templates-categories > li div:hover {
  color: #3c434a;
  text-decoration: underline;
}

.wpforms-setup-templates-categories > li div > span {
  float: inline-end;
  color: #50575e;
  opacity: 0.5;
  font-weight: 400;
}

.wpforms-setup-templates-categories > li.divider {
  border-bottom: 1px solid #dcdcde;
  margin: 30px 0;
  padding: 0;
}

.wpforms-setup-templates-categories > li.active div {
  background: #ebf3fc;
  color: #0399ed;
  position: relative;
}

.wpforms-setup-templates-categories > li.active div span {
  color: #0399ed;
  opacity: 1;
  font-weight: 600;
}

.wpforms-setup-templates-categories > li.active div:hover {
  text-decoration: none;
}

.wpforms-setup-templates-categories > li.active.opened .wpforms-setup-templates-subcategories {
  display: block;
}

.wpforms-setup-templates-categories > li.active .chevron {
  color: #0399ed;
}

.wpforms-setup-templates-categories > li i.chevron {
  display: none;
}

.wpforms-setup-templates-categories > li:has(ul) i.chevron {
  display: inline-block;
  position: relative;
  top: -1px;
  padding: 0 5px;
  font-size: 12px;
  color: #50575e;
}

.wpforms-setup-templates-categories > li.opened i.chevron {
  transform: rotate(180deg);
}

.wpforms-setup-templates-categories > li.opened ul.wpforms-setup-templates-subcategories {
  display: block;
}

.wpforms-setup-templates-subcategories {
  display: none;
}

.wpforms-setup-templates-subcategories li {
  display: flex;
  justify-content: space-between;
  color: #50575e;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  margin: 0;
  padding-block: 5px;
  padding-inline-end: 10px;
  padding-inline-start: 20px;
  position: relative;
}

.wpforms-setup-templates-subcategories li:hover span:first-child {
  text-decoration: underline;
}

.wpforms-setup-templates-subcategories li.active {
  color: #0399ed;
  font-weight: 400;
}

.wpforms-setup-templates-subcategories li.active span:last-child {
  color: #0399ed;
  font-weight: 400;
  opacity: 1;
}

.wpforms-setup-templates-subcategories li i {
  color: #999c9e;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 10px;
}

.wpforms-setup-templates-subcategories li span:first-child {
  flex-grow: 1;
  padding-inline-end: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wpforms-setup-templates-subcategories li span:last-child {
  min-width: 32px;
  color: #50575e;
  opacity: 0.5;
  font-weight: 400;
  text-align: end;
}

#wpforms-setup-templates-list {
  padding-top: 30px;
  width: calc( 100% - 250px);
}

#wpforms-setup-templates-list .wpforms-admin-empty-state-container .waving-hand-emoji {
  background-image: url(../../images/empty-states/waving-hand-emoji.png);
}

#wpforms-setup-templates-list .list {
  display: grid;
  grid-gap: 30px;
  grid-template-columns: repeat(auto-fill, minmax(Max(200px, 260px), 1fr));
  padding-inline-start: 30px;
}

#wpforms-setup-templates-list .wpforms-template {
  border-radius: 6px;
  height: 100%;
  overflow: hidden;
  padding: 0 0 15px;
  position: relative;
  box-shadow: 0 0 0 1px #c3c4c7;
  transition: box-shadow 0.15s ease-in-out;
}

#wpforms-setup-templates-list .wpforms-template:hover, #wpforms-setup-templates-list .wpforms-template.active {
  box-shadow: 0 0 0 2px #50575e, 0 3px 4px rgba(0, 0, 0, 0.15);
  outline: none;
}

#wpforms-setup-templates-list .wpforms-template:hover .wpforms-template-buttons,
#wpforms-setup-templates-list .wpforms-template:hover .wpforms-template-favorite,
#wpforms-setup-templates-list .wpforms-template:hover .wpforms-template-remove, #wpforms-setup-templates-list .wpforms-template.active .wpforms-template-buttons,
#wpforms-setup-templates-list .wpforms-template.active .wpforms-template-favorite,
#wpforms-setup-templates-list .wpforms-template.active .wpforms-template-remove {
  opacity: 1;
}

#wpforms-setup-templates-list .wpforms-template:hover .wpforms-badge, #wpforms-setup-templates-list .wpforms-template.active .wpforms-badge {
  opacity: 0;
}

#wpforms-setup-templates-list .wpforms-template.badge h3 {
  padding-inline-end: 45px;
}

#wpforms-setup-templates-list .wpforms-template.selected {
  box-shadow: 0 0 0 2px #e27730, 0 3px 4px rgba(0, 0, 0, 0.15);
}

#wpforms-setup-templates-list .wpforms-template .wpforms-template-thumbnail {
  background-color: #F5F9FD;
  border-bottom: 1px solid #EBEEF1;
  overflow: hidden;
  padding: 20px 54px 0;
}

#wpforms-setup-templates-list .wpforms-template .wpforms-template-thumbnail > img {
  border-radius: 2px 2px 0 0;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.1);
  display: block;
  margin: 0 auto;
  max-width: 100%;
}

#wpforms-setup-templates-list .wpforms-template .wpforms-template-thumbnail .wpforms-template-thumbnail-placeholder {
  align-items: center;
  aspect-ratio: 1;
  background: #fff;
  border-radius: 2px 2px 0 0;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  margin: 0 auto;
  max-width: 350px;
  min-height: 100%;
  width: 100%;
}

#wpforms-setup-templates-list .wpforms-template h3 {
  font-size: 16px;
  font-weight: 600;
  line-height: 18px;
  padding: 20px 20px 2px;
  margin: 0;
  overflow: hidden;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #3c434a;
}

#wpforms-setup-templates-list .wpforms-template .wpforms-template-favorite,
#wpforms-setup-templates-list .wpforms-template .wpforms-template-remove {
  display: block;
  position: absolute;
  inset-inline-end: 10px;
  top: 10px;
  font-size: 18px;
  line-height: 18px;
  opacity: 0;
  color: #aaaaaa;
  transition-property: all;
  transition-duration: 0.15s;
  transition-timing-function: ease-out;
}

#wpforms-setup-templates-list .wpforms-template .wpforms-template-favorite:hover,
#wpforms-setup-templates-list .wpforms-template .wpforms-template-remove:hover {
  color: #d63637;
  cursor: pointer;
  transition-property: all;
  transition-duration: 0.05s;
  transition-timing-function: ease-out;
}

#wpforms-setup-templates-list .wpforms-template .wpforms-template-favorite .fa-heart,
#wpforms-setup-templates-list .wpforms-template .wpforms-template-remove .fa-heart {
  color: #d63638;
}

#wpforms-setup-templates-list .wpforms-template.selected h3 {
  padding-inline-end: 60px;
}

#wpforms-setup-templates-list .wpforms-template.pro h3 {
  padding-inline-end: 40px;
}

#wpforms-setup-templates-list .wpforms-template p {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  color: #50575e;
  font-size: 14px;
  line-height: 18px;
  margin: 10px 0 0;
  max-height: 55px;
  min-height: 45px;
  padding: 0 20px;
}

#wpforms-setup-templates-list .wpforms-template-upgrade-banner {
  background: rgba(226, 119, 48, 0.08);
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  grid-column: 1 / -1;
  justify-content: space-between;
  align-items: center;
  padding-inline-end: 20px;
}

#wpforms-setup-templates-list .wpforms-template-upgrade-banner .wpforms-template-content {
  padding: 20px;
  width: 80%;
  color: #3c434a;
}

#wpforms-setup-templates-list .wpforms-template-upgrade-banner .wpforms-template-content h3 {
  font-weight: 600;
  font-size: 16px;
  line-height: 18px;
  margin-top: 0;
  margin-bottom: 5px;
}

#wpforms-setup-templates-list .wpforms-template-upgrade-banner .wpforms-template-content p {
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  margin: 0;
}

#wpforms-setup-templates-list .wpforms-template-upgrade-banner .wpforms-template-upgrade-button {
  width: 20%;
  text-align: end;
}

#wpforms-setup-templates-list .wpforms-template-upgrade-banner .wpforms-template-upgrade-button a {
  min-height: 13px;
}

@media screen and (max-width: 1279px) {
  #wpforms-setup-templates-list .wpforms-template-upgrade-banner .wpforms-template-upgrade-button a {
    box-sizing: border-box;
    width: 100%;
  }
}

#wpforms-setup-templates-list .wpforms-templates-no-results {
  display: none;
  margin-inline-start: 30px;
}

#wpforms-setup-templates-list .wpforms-templates-no-results p {
  font-size: 16px;
  line-height: 40px;
  margin: 0;
  color: #3c434a;
}

.rtl #wpforms-setup-templates-list .wpforms-badge {
  border-radius: 0 0 3px 0;
  left: 0;
  right: auto;
}

.wpforms-template-buttons {
  background-color: #ffffff;
  border-radius: 6px;
  display: flex;
  opacity: 0;
  padding: 15px 15px 0;
  width: 100%;
  align-items: flex-start;
  gap: 10px;
  position: absolute;
  bottom: 15px;
  transition-property: all;
  transition-duration: 0.15s;
  transition-timing-function: ease-out;
}

.wpforms-template-buttons .wpforms-btn {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 600;
  font-size: 13px;
  line-height: 16px;
  padding: 11px 10px;
}

.wpforms-template-buttons .visible {
  opacity: 1;
}

@media (max-width: 1439px) {
  #wpforms-setup-name {
    width: calc( 100% - 250px);
  }
}

@media (max-width: 1369px) {
  #wpforms-setup-templates-list .list {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1279px) {
  #wpforms-setup-templates-list .list {
    grid-template-columns: 1fr;
  }
  #wpforms-setup-templates-list .list .wpforms-template .wpforms-template-buttons {
    position: relative;
    bottom: 0;
    margin: 10px 0 0 0;
    opacity: 1;
    padding: 0 20px;
  }
  #wpforms-setup-templates-list .list .wpforms-template p {
    min-height: auto;
  }
  #wpforms-setup-templates-list .list .wpforms-template-upgrade-banner {
    flex-direction: column;
    padding: 20px;
  }
  #wpforms-setup-templates-list .list .wpforms-template-upgrade-banner .wpforms-template-content,
  #wpforms-setup-templates-list .list .wpforms-template-upgrade-banner .wpforms-template-upgrade-button {
    width: 100%;
  }
  #wpforms-setup-templates-list .list .wpforms-template-upgrade-banner .wpforms-template-content {
    padding: 0;
  }
  #wpforms-setup-templates-list .list .wpforms-template-upgrade-banner .wpforms-template-upgrade-button {
    text-align: center;
    margin-top: 20px;
  }
}

#wpforms-panel-settings .wpforms-panel-sidebar-section {
  height: auto;
}

#wpforms-panel-settings .wpforms-builder-settings-block {
  border: 1px solid #c3c4c7;
  margin: 0 0 20px 0;
  background-color: #ffffff;
}

#wpforms-panel-settings .wpforms-builder-settings-block .wpforms-panel-field,
#wpforms-panel-settings .wpforms-builder-settings-block .wpforms-field-map-table {
  padding: 0 20px;
}

#wpforms-panel-settings .wpforms-builder-settings-block .wpforms-panel-field:last-child {
  padding-bottom: 20px;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: space-between;
  background-color: #f6f7f7;
  border-bottom: 1px solid #c3c4c7;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  margin: 0 0 -1px 0;
  padding: 15px 20px;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-name-holder {
  width: calc( 100% - 65px);
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-name-edit {
  display: none;
  margin-bottom: -7px;
  margin-top: -6px;
  width: calc( 100% - 32px);
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-name-edit.active {
  display: inline-block;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-name-edit input {
  width: 100%;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-actions {
  display: flex;
  justify-content: flex-end;
  min-width: 165px;
  order: 2;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-actions .wpforms-builder-settings-block-status {
  margin-inline-end: 15px;
  border-radius: 3px;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-actions .wpforms-status-button .wpforms-status-label {
  font-style: normal;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-actions .wpforms-status-button.wpforms-badge-green {
  border: 1px solid #30b450;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-actions .wpforms-status-button.wpforms-badge-green:hover {
  color: #008a20;
  border-color: currentColor;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-actions .wpforms-status-button.wpforms-badge-silver {
  border: 1px solid #c3c4c7;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-actions .wpforms-status-button.wpforms-badge-silver:hover {
  color: #50575e;
  border-color: currentColor;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-actions .wpforms-status-button:hover {
  cursor: pointer;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-actions .wpforms-status-button i:first-child {
  position: relative;
  top: 0.5px;
  margin-inline-end: 4px;
  font-size: 10px;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-actions .wpforms-status-button i:last-child {
  margin-inline-end: 0;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header button {
  background-color: transparent;
  border: none;
  color: #999c9e;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  margin-inline-end: 10px;
  padding: 0;
  appearance: none;
  -webkit-appearance: none;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header button:hover {
  color: #50575e;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header button.wpforms-builder-settings-block-delete {
  color: #d63638;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header button.wpforms-builder-settings-block-delete:hover {
  color: #b32d2e;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header button:last-of-type {
  margin-inline-end: 0;
}

#wpforms-panel-settings .wpforms-builder-settings-block-header .wpforms-builder-settings-block-edit {
  margin-inline-start: 6px;
}

#wpforms-panel-settings .wpforms-builder-settings-block-content {
  margin-top: 20px;
  padding-bottom: 0;
}

#wpforms-panel-settings .wpforms-builder-settings-block-default .wpforms-builder-settings-block-toggle {
  padding-inline-end: 0;
}

#wpforms-panel-settings .wpforms-builder-settings-block-default .wpforms-builder-settings-block-delete {
  display: none;
}

#wpforms-panel-settings .wpforms-builder-settings-block-default.wpforms-confirmation .wpforms-conditional-block-panel {
  display: none;
}

#wpforms-panel-settings .wpforms-panel-field-radio .row > label {
  margin-bottom: 0;
}

#wpforms-panel-settings .toggle-unfoldable-cont,
#wpforms-panel-settings .unfoldable-cont {
  margin-inline-end: 0;
}

#wpforms-panel-settings .toggle-unfoldable-cont i {
  color: #b0b2b3;
  margin-inline-end: 6px;
}

#wpforms-panel-settings .toggle-unfoldable-cont span {
  color: #50575e;
}

#wpforms-panel-settings .wpforms-panel-field-tinymce > label {
  margin-bottom: -20px;
  margin-inline-end: 100px;
  position: relative;
  z-index: 2;
}

#wpforms-panel-settings .wpforms-panel-field-tinymce .toggle-smart-tag-display {
  float: none;
  margin-top: 10px;
  display: inline-block;
}

#wpforms-panel-settings .wpforms-panel-field-tinymce .smart-tags-list-display {
  margin-top: 10px;
}

#wpforms-panel-settings .wpforms-panel-field-tinymce .wp-editor-wrap .quicktags-toolbar {
  border: none;
  border-bottom: 1px solid #dcdcde;
}

#wpforms-panel-settings .wpforms-panel-field-tinymce .wp-editor-wrap textarea {
  border: none;
}

#wpforms-panel-settings .wpforms-panel-field-tinymce .wp-editor-wrap textarea:focus {
  border: none;
  box-shadow: none;
}

#wpforms-panel-settings .wpforms-panel-field-tinymce .quicktags-toolbar {
  border: 1px solid #dcdcde;
  z-index: 2;
}

#wpforms-panel-settings .wpforms-panel-field-tinymce textarea {
  border-radius: 0;
  border-top: 0;
}

#wpforms-panel-settings .wpforms-panel-field-tinymce textarea:focus {
  border: 1px solid #dcdcde;
  border-top: 0;
  box-shadow: none;
}

#wpforms-panel-settings .wpforms-panel-field-warning label,
#wpforms-panel-settings .wpforms-panel-field-warning input,
#wpforms-panel-settings .wpforms-panel-field-warning .wpforms-smart-tags-widget-container,
#wpforms-panel-settings .wpforms-panel-field-warning .wpforms-alert,
#wpforms-panel-settings .wpforms-panel-field-warning .smart-tags-list-display {
  max-width: 410px;
}

#wpforms-panel-settings .wpforms-panel-field-warning .wpforms-alert-warning-wide {
  max-width: 100%;
}

#wpforms-panel-settings .wpforms-panel-field-warning label {
  position: relative;
}

#wpforms-panel-settings .wpforms-panel-field-warning label:after {
  color: #ffb900;
  content: "\f071";
  font-family: FontAwesome;
  font-size: 16px;
  position: absolute;
  inset-inline-end: -26px;
  top: 35px;
}

#wpforms-panel-settings .wpforms-panel-field-warning .wpforms-alert {
  margin-top: 20px;
}

#wpforms-panel-settings .wpforms-panel-field-confirmations-page-choicesjs-unflippable .is-flipped .choices__list--dropdown {
  top: 100%;
  bottom: auto;
  margin-top: -1px;
  margin-bottom: 0;
}

#wpforms-panel-settings .wpforms-panel-content-also-available {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  /* Magic numbers below are to compensate for 1px box-shadow used to draw a "dynamic" border. */
  gap: 32px;
  margin: 0 0 -19px 1px;
  padding-top: 1px;
}

#wpforms-panel-settings .wpforms-panel-content-also-available-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0;
  padding: 20px;
  border-radius: 6px;
  background: #ffffff;
  box-shadow: 0 0 0 1px #c3c4c7;
  transition: box-shadow 0.15s ease-in-out;
}

#wpforms-panel-settings .wpforms-panel-content-also-available-item:hover {
  box-shadow: 0 0 0 2px #50575e, 0 3px 4px rgba(0, 0, 0, 0.15);
}

#wpforms-panel-settings .wpforms-panel-content-also-available-item-logo {
  margin: 0 0 20px 0;
}

#wpforms-panel-settings .wpforms-panel-content-also-available-item-logo img {
  display: flex;
  height: 68px;
  width: 68px;
  padding: 10px 10px;
  border-radius: 4px;
  border: 1px solid #dcdcde;
}

#wpforms-panel-settings .wpforms-panel-content-also-available-item-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

#wpforms-panel-settings .wpforms-panel-content-also-available-item-info h3 {
  margin: 0;
  color: #3c434a;
  font-size: 16px;
  line-height: 16px;
  font-weight: 500;
}

#wpforms-panel-settings .wpforms-panel-content-also-available-item-info p {
  font-size: 13px;
  line-height: 18px;
  text-align: center;
  color: #50575e;
  margin: 10px 0;
}

#wpforms-panel-settings .wpforms-panel-content-also-available-item-info a {
  display: block;
  margin: auto 0 0 0;
  font-size: 14px;
  line-height: 21px;
  text-decoration: none;
}

#wpforms-panel-settings .wpforms-panel-content-also-available-item-info a:hover {
  color: #215d8f;
  text-decoration: underline;
}

#wpforms-panel-settings .wpforms-panel-content-also-available-item-info a.wpforms-panel-content-also-available-item-upgrade-to-pro {
  color: #e27730;
}

#wpforms-panel-settings .wpforms-panel-content-also-available-item-info a.wpforms-panel-content-also-available-item-upgrade-to-pro:hover {
  color: #cd6622;
}

#wpforms-panel-settings .wpforms-panel-field-country-filter-body {
  max-width: 100%;
}

#wpforms-panel-settings .wpforms-panel-field-keyword-filter-body > p {
  margin: 0 0 20px;
}

#wpforms-panel-settings .wpforms-panel-field-keyword-filter-body .note {
  margin: 0 0 10px;
  font-size: 12px;
}

#wpforms-panel-settings .wpforms-panel-field-keyword-filter-body .wpforms-panel-field-keyword-filter-message {
  margin-top: 20px;
}

#wpforms-panel-settings .wpforms-panel-field-keyword-filter-keywords-container {
  display: none;
}

#wpforms-panel-settings .wpforms-panel-field-keyword-filter-keywords-container .wpforms-panel-field {
  margin-bottom: 10px;
}

#wpforms-panel-settings .wpforms-panel-field-keyword-filter-keywords-container .wpforms-panel-field textarea {
  height: 200px;
}

#wpforms-panel-settings .wpforms-panel-field-country-filter-block-row {
  display: flex;
  margin-top: 10px;
  align-items: start;
  margin-bottom: 20px;
}

#wpforms-panel-settings .wpforms-panel-field-country-filter-block-row-action {
  max-width: 125px;
  width: 100%;
  margin: 0;
}

#wpforms-panel-settings .wpforms-panel-field-country-filter-block-row-separator {
  max-width: 95px;
  width: 100%;
  margin: 0;
  text-align: center;
  font-style: italic;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  color: #50575e;
  white-space: nowrap;
  display: block;
}

#wpforms-panel-settings .wpforms-panel-field-country-filter-block-row-countries {
  min-width: 230px;
  max-width: 100%;
}

#wpforms-panel-settings .wpforms-panel-field-keyword-filter-body .wpforms-alert {
  max-width: 450px;
  margin: 0 0 10px;
}

#wpforms-panel-settings .wpforms-builder-provider-connections-error {
  color: #b32d2e;
  font-size: 16px;
  background-color: #fcf0f1;
  border-radius: 4px;
  width: fit-content;
  margin: 0 auto;
  padding: 7px 15px;
  line-height: 24px;
  text-align: center;
}

#wpforms-panel-settings .wpforms-builder-provider-connections-error a {
  color: inherit;
}

@media (max-height: 864px) {
  #wpforms-panel-settings .wpforms-panel-content-section-themes {
    position: unset;
  }
  #wpforms-panel-settings .wpforms-panel-content-section-themes .wpforms-panel-content-section-themes-inner {
    height: auto;
    display: block;
  }
  #wpforms-panel-settings .wpforms-panel-content-section-themes .wpforms-panel-content-section-themes-inner .wpforms-panel-content-section-themes-bottom {
    position: unset;
  }
}

@media (max-width: 1082px) {
  #wpforms-panel-settings .wpforms-panel-field-warning input {
    padding-inline-end: 36px;
  }
  #wpforms-panel-settings .wpforms-panel-field-warning label:after {
    inset-inline-end: 11px;
  }
}

.wpforms_page_wpforms-builder .jconfirm-box-container .wpforms-modal-content-box.jconfirm-box .jconfirm-content-pane {
  margin-bottom: 15px;
  max-height: Min(calc(100vh - 135px), 544px);
  overflow-y: hidden;
}

.wpforms-modal-content-box .jconfirm-content {
  height: 100%;
}

.wpforms-modal-content-box .jconfirm-content > div {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.wpforms-modal-content-box .wpforms-modal-content {
  background-color: #f6f7f7;
  border-radius: 4px;
  overflow-y: auto;
  padding: 20px;
}

.wpforms-modal-content-box .wpforms-modal-content::-webkit-scrollbar {
  width: 3px;
}

.wpforms-modal-content-box .wpforms-modal-content::-webkit-scrollbar-thumb {
  background-color: #6a6f76;
}

.wpforms-modal-header {
  padding-bottom: 30px;
  padding-top: 5px;
  line-height: 22px;
}

.wpforms-modal-header h1 {
  font-weight: 500;
  margin-bottom: 15px;
  margin-top: 0;
}

.wpforms-email-template-modal-content {
  gap: 15px;
}

.wpforms-email-template-modal-content .wpforms-card-image-overlay {
  aspect-ratio: 31/36;
  background-position: center;
  background-size: contain;
  border: 1px solid #c3c4c7;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  order: -1;
  position: relative;
  transition: box-shadow .15s ease-in-out, border .15s ease-in-out;
  width: 100%;
  padding: 12px;
}

.wpforms-email-template-modal-content .wpforms-card-image-overlay:before {
  background-color: #ffffff;
  content: "";
  height: 100%;
  left: 0;
  opacity: 0;
  position: absolute;
  top: 0;
  width: 100%;
  border-radius: inherit;
  transition: opacity .15s ease-in-out;
}

.wpforms-email-template-modal-content .wpforms-card-image-overlay .wpforms-btn {
  opacity: 0;
  transition: all .05s ease-in-out, opacity .15s ease-in-out;
  width: 100%;
  z-index: 2;
}

.wpforms-email-template-modal-content .wpforms-card-image-group .wpforms-setting-field label {
  align-items: center;
  color: #50575e;
  display: flex;
  font-size: 14px;
  flex-wrap: wrap;
  gap: 12px 10px;
  justify-content: center;
  text-align: center;
}

.wpforms-email-template-modal-content .wpforms-card-image-group .wpforms-setting-field label:hover .wpforms-card-image-overlay {
  border: 2px solid #ffffff;
  box-shadow: 0 0 0 2px #50575e, 0 2px 4px 2px rgba(0, 0, 0, 0.07);
}

.wpforms-email-template-modal-content .wpforms-card-image-group .wpforms-setting-field label:hover .wpforms-card-image-overlay:before {
  opacity: .7;
}

.wpforms-email-template-modal-content .wpforms-card-image-group .wpforms-setting-field label:hover .wpforms-card-image-overlay .wpforms-btn {
  opacity: 1;
}

.wpforms-email-template-modal-content .wpforms-card-image-group .wpforms-setting-field input[type=radio] {
  display: none;
}

.wpforms-email-template-modal-content .wpforms-card-image-group .wpforms-setting-field input[type=radio]:checked + label {
  font-weight: 500;
  color: #2c3338;
}

.wpforms-email-template-modal-content .wpforms-card-image-group .wpforms-setting-field input[type=radio]:checked + label .wpforms-card-image-overlay {
  border: 2px solid #ffffff;
  box-shadow: 0 0 0 2px #e27730, 0 2px 4px 2px rgba(0, 0, 0, 0.07);
}

.wpforms-email-template-modal-content .wpforms-btn {
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
  margin: 0;
  text-decoration: none;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  box-shadow: none;
  transition-property: all;
  transition-duration: 0.05s;
  transition-timing-function: ease-in;
  align-items: center;
  justify-content: center;
}

.wpforms-email-template-modal-content .wpforms-btn {
  display: flex;
}

.wpforms-email-template-modal-content .wpforms-btn-md {
  font-size: 13px;
  font-weight: 600;
  line-height: 13px;
  min-height: 35px;
  padding: 5px 15px;
}

.wpforms-email-template-modal-content .wpforms-btn-orange {
  background-color: #e27730;
  border-color: #e27730;
  color: #ffffff;
}

.wpforms-email-template-modal-content .wpforms-btn-orange:hover {
  background-color: #cd6622;
  border-color: #cd6622;
  color: #ffffff;
}

.wpforms-email-template-modal-content .wpforms-btn-light-grey {
  background-color: #f6f7f7;
  border-color: #c3c4c7;
  color: #50575e;
}

.wpforms-email-template-modal-content .wpforms-btn-light-grey:hover {
  background-color: #e8e9e9;
  border-color: #c3c4c7;
  color: #3c434a;
}

.wpforms-email-template-modal-content .wpforms-card-image-group .wpforms-setting-field {
  display: grid;
  gap: 20px 17px;
  grid-template-columns: repeat(5, 1fr);
}

.wpforms-email-template-modal-content .wpforms-card-image {
  position: relative;
}

.wpforms-email-template-modal-content .wpforms-card-image:nth-child(5n+1):before {
  background: url(../../images/email/template-placeholder.svg) space;
  background-size: calc(20% - 13.5px);
  content: "";
  height: 100%;
  inset-inline-start: 0;
  opacity: .5;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: calc(500% + 68px);
}

.wpforms-card-image-overlay {
  padding: 10px;
}

.option-default .wpforms-card-image-overlay {
  background-image: url(../../images/email/template-classic.svg);
}

.option-classic .wpforms-card-image-overlay {
  background-image: url(../../images/email/template-classic.svg);
}

.option-compact .wpforms-card-image-overlay {
  background-image: url(../../images/email/template-compact.svg);
}

.option-modern .wpforms-card-image-overlay {
  background-image: url(../../images/email/template-modern.svg);
}

.option-elegant .wpforms-card-image-overlay {
  background-image: url(../../images/email/template-elegant.svg);
}

.option-tech .wpforms-card-image-overlay {
  background-image: url(../../images/email/template-tech.svg);
}

.option-none .wpforms-card-image-overlay {
  background-image: url(../../images/email/template-plaintext.svg);
}

.wpforms-builder-provider .wpforms-builder-provider-title {
  justify-content: flex-start;
  gap: 20px;
  min-height: 68px;
}

.wpforms-builder-provider .wpforms-builder-provider-title button {
  margin-inline-start: auto;
}

.wpforms-builder-provider .wpforms-builder-provider-title-spinner {
  color: #50575e;
  display: none;
  font-size: 20px;
  line-height: 20px;
}

.wpforms-builder-provider .wpforms-builder-provider-connections > :last-child {
  margin-bottom: 0;
}

.wpforms-builder-provider .wpforms-builder-provider-connection {
  border: 1px solid #c3c4c7;
  margin: 0 0 20px 0;
}

.wpforms-builder-provider .wpforms-builder-provider-connection > :last-child {
  margin-bottom: 20px;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-title {
  background-color: #f6f7f7;
  border-bottom: 1px solid #c3c4c7;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  margin: 0 0 -1px 0;
  padding: 15px 20px 14px 20px;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-delete {
  background-color: transparent;
  border: none;
  color: #d63638;
  cursor: pointer;
  float: inline-end;
  font-size: 16px;
  line-height: 1;
  margin-top: -2px;
  appearance: none;
  -webkit-appearance: none;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-block {
  margin: 20px 0 0 0;
  padding: 0 20px;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-block h3 {
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  margin-bottom: 10px;
  margin-inline-start: 1px;
  margin-top: 0;
  padding: 0;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-block h4, .wpforms-builder-provider .wpforms-builder-provider-connection-block label {
  display: block;
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  margin-bottom: 10px;
  margin-inline-start: 1px;
  margin-top: 0;
  padding: 0;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-block p.description {
  color: #50575e;
  font-size: 13px;
  line-height: 18px;
  margin: 5px 0 0 0;
  max-width: 450px;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-block p.description.before {
  margin: 0 0 10px 0;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-block:last-child {
  margin-bottom: 20px;
}

.wpforms-builder-provider input[type=text],
.wpforms-builder-provider input[type=url],
.wpforms-builder-provider select,
.wpforms-builder-provider textarea,
.wpforms-builder-provider .wpforms-builder-provider-connection-block .choices {
  max-width: 450px;
  width: 100%;
}

.wpforms-builder-provider input[type=checkbox] + label,
.wpforms-builder-provider input[type=radio] + label {
  display: inline-block;
  margin: 0;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-setting {
  margin: 20px 0 0 0;
  max-width: 450px;
  padding: 0;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-setting label {
  display: block;
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  margin-bottom: 10px;
  margin-inline-start: 1px;
  padding: 0;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-setting label.wpforms-toggle-control-icon, .wpforms-builder-provider .wpforms-builder-provider-connection-setting label.wpforms-toggle-control-label {
  display: inline-block;
  margin: 0;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-setting .text-btn-inlined {
  display: table;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-setting .text-btn-inlined > .text-btn-inlined-text {
  display: table-cell;
  padding-inline-end: 10px;
  vertical-align: middle;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-setting .text-btn-inlined > .text-btn-inlined-btn {
  display: table-cell;
  vertical-align: middle;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-setting .wpforms-panel-field:only-child {
  margin-bottom: 20px !important;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-setting .choices {
  margin-bottom: 0;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-setting .choices[data-type*="select-multiple"] .choices__input:focus {
  border: none !important;
  box-shadow: none !important;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-fields-table {
  border: 1px solid #c3c4c7;
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-fields-table thead {
  background-color: #f6f7f7;
  color: #50575e;
  font-size: 14px;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-fields-table thead th {
  font-weight: 400;
  padding: 8px 10px;
  text-align: start;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-fields-table td {
  width: 50%;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-fields-table td.add button, .wpforms-builder-provider .wpforms-builder-provider-connection-fields-table td.delete button {
  background: none;
  border: none;
  box-shadow: none;
  color: #036aab;
  height: auto;
  line-height: 1;
  margin: 0;
  padding: 0;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-fields-table td.add button i, .wpforms-builder-provider .wpforms-builder-provider-connection-fields-table td.delete button i {
  font-size: 16px;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-fields-table td.add button:hover, .wpforms-builder-provider .wpforms-builder-provider-connection-fields-table td.delete button:hover {
  color: #215d8f;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-fields-table td.delete button {
  color: #d63638;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-fields-table td.delete button:hover {
  color: #b32d2e;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-fields-table tbody td {
  border-top: 1px solid #c3c4c7;
  padding-block: 10px;
  padding-inline-start: 10px;
  padding-inline-end: 0;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-fields-table tbody td:last-of-type {
  padding-inline-end: 10px;
}

.wpforms-builder-provider .wpforms-builder-provider-connection-fields-table + p.description {
  max-width: 100%;
}

.wpforms-builder-provider .required {
  color: #d63638;
  font-weight: 700;
  margin-inline-start: 5px;
}

.wpforms-builder-provider .wpforms-conditional-block {
  margin: 20px 0 0 0;
  padding: 0 20px;
}

.wpforms-builder-provider .wpforms-builder-provider-connections-error {
  color: #b32d2e;
  font-size: 16px;
  background-color: #fcf0f1;
  border-radius: 4px;
  width: fit-content;
  margin: 0 auto;
  padding: 7px 15px;
  line-height: 24px;
  text-align: center;
}

.wpforms-builder-provider .wpforms-builder-provider-connections-error a {
  color: inherit;
}

.wpforms-builder-provider.loading .wpforms-builder-provider-title .wpforms-builder-provider-title-spinner {
  display: inline;
}

.wpforms-builder-provider.loading .wpforms-builder-provider-connections {
  opacity: .5;
  pointer-events: none;
}

.wpforms-builder-provider-connections-default {
  padding: 30px 50px 50px 50px;
  position: relative;
  text-align: center;
}

.wpforms-builder-provider-title:has(button:not(.hidden)) + .wpforms-builder-provider-connections-default::before {
  background: url("../../images/builder/default-arrow.svg") no-repeat 0 0;
  background-size: 97px 81px;
  content: "";
  height: 83px;
  position: absolute;
  inset-inline-end: 60px;
  top: 20px;
  transform: rotate(90deg);
  width: 97px;
}

.wpforms-builder-provider-connections-default img {
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  height: 140px;
  margin: 0 0 30px 0;
  width: 140px;
}

.wpforms-builder-provider-connections-default h2:first-child {
  margin-top: 0;
}

.wpforms-builder-provider-connections-default p {
  font-size: 16px;
  line-height: 24px;
  max-width: 615px;
  margin: 0 auto 20px;
}

.wpforms-provider-connections > :last-child {
  margin-bottom: 0;
}

.wpforms-provider-connections-header {
  background-color: #f6f7f7;
  padding: 10px 20px;
}

.wpforms-provider-connections-header h5 {
  float: inline-start;
  font-size: 18px;
  margin: 0;
  padding: 5px 0 0 0;
}

.wpforms-provider-connection {
  border: 1px solid #c3c4c7;
  margin: 0 0 20px 0;
}

.wpforms-provider-connection .wpforms-provider-connection-header {
  background-color: #f6f7f7;
  border-bottom: 1px solid #c3c4c7;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  margin: 0 0 -1px 0;
  padding: 15px 20px 14px 20px;
}

.wpforms-provider-connection .wpforms-provider-connection-delete {
  background-color: transparent;
  border: none;
  color: #d63638;
  cursor: pointer;
  float: inline-end;
  font-size: 16px;
  line-height: 1;
  margin-top: -2px;
  appearance: none;
  -webkit-appearance: none;
}

.wpforms-provider-connection .wpforms-provider-account-add input[type=text] {
  margin: 0 0 10px 0;
  display: block;
}

.wpforms-provider-connection .wpforms-provider-account-add button {
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
  margin: 0;
  text-decoration: none;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  box-shadow: none;
  transition-property: all;
  transition-duration: 0.05s;
  transition-timing-function: ease-in;
  font-size: 13px;
  font-weight: 500;
  padding: 0 10px;
  height: 32px;
  line-height: 30px;
  background-color: #036aab;
  border-color: #036aab;
  color: #ffffff;
  display: block;
}

.wpforms-provider-connection .wpforms-provider-account-add button:hover {
  background-color: #215d8f;
  border-color: #215d8f;
  color: #ffffff;
}

.wpforms-provider-connection .wpforms-provider-account-add button .wpforms-loading-spinner.wpforms-loading-inline {
  margin-inline-end: 8px;
  margin-inline-start: 0;
  margin-top: -2px;
}

.wpforms-provider-connection input[type=text],
.wpforms-provider-connection select {
  max-width: 450px;
  width: 100%;
}

.wpforms-provider-connection input[type=text]::before,
.wpforms-provider-connection select::before {
  content: "";
  display: block;
  width: 100%;
}

.wpforms-provider-connection .wpforms-connection-block {
  margin: 20px 0 0 0;
  padding: 0 20px;
}

.wpforms-provider-connection .wpforms-connection-block h3 {
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  margin-bottom: 10px;
  margin-inline-start: 1px;
  margin-top: 0;
  padding: 0;
}

.wpforms-provider-connection .wpforms-connection-block h4, .wpforms-provider-connection .wpforms-connection-block label {
  display: block;
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  margin-bottom: 10px;
  margin-inline-start: 1px;
  margin-top: 0;
  padding: 0;
}

.wpforms-provider-connection .wpforms-connection-block:last-child {
  margin-bottom: 20px;
}

.wpforms-provider-connection .wpforms-connection-block .wpforms-loading-spinner.wpforms-loading-inline {
  margin-inline-start: 20px;
  vertical-align: middle;
}

.wpforms-provider-connection .wpforms-provider-groups-list p {
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
}

.wpforms-provider-connection .wpforms-provider-groups-list span {
  display: block;
  font-size: 13px;
  margin-bottom: 4 5px;
}

.wpforms-provider-connection .wpforms-provider-groups-list input {
  margin-inline-end: 10px;
}

.wpforms-provider-connection .wpforms-provider-fields table {
  border: 1px solid #c3c4c7;
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

.wpforms-provider-connection .wpforms-provider-fields table select {
  width: 100%;
}

.wpforms-provider-connection .wpforms-provider-fields table thead {
  background-color: #f6f7f7;
  color: #50575e;
  font-size: 14px;
}

.wpforms-provider-connection .wpforms-provider-fields table thead th {
  font-weight: 400;
  padding: 8px 10px;
  text-align: start;
}

.wpforms-provider-connection .wpforms-provider-fields table tbody td {
  border-top: 1px solid #c3c4c7;
  padding: 10px;
}

.wpforms-provider-connection .required {
  color: #d63638;
  font-weight: 700;
  margin-inline-start: 5px;
}

.wpforms-provider-connection .wpforms-conditional-block {
  margin: 20px 0;
  padding: 0 20px;
}

.wpforms-provider-connection .wpforms-conditional-block .value input:disabled,
.wpforms-provider-connection .wpforms-conditional-block .value select:disabled {
  background-color: #f0f0f1;
  cursor: not-allowed;
}

.wpforms-provider-connection .wpforms-provider-options label.block {
  display: block;
  margin: 0 0 5px 0;
}

.wpforms-provider-connection .wpforms-provider-options input[type=text] {
  width: 100%;
}

.wpforms-panel-content-section-mailerlite .wpforms-builder-provider-connection-block:has(h4:first-child:last-child) {
  display: none;
}

.rtl .wpforms-builder-provider-title:has(button:not(.hidden)) + .wpforms-builder-provider-connections-default::before {
  transform: scale(-1, 1) rotate(90deg);
}

.rtl .wpforms-builder-provider .choices.is-open .choices__inner,
.rtl .wpforms-builder-provider .choices .choices__inner {
  background-position: left 5px top 55%;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-sidebar-section:not(.wpforms-panel-sidebar-section-default) {
  display: flex;
  align-items: center;
  padding-inline-start: 20px;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-sidebar-section:not(.wpforms-panel-sidebar-section-default) img {
  position: unset;
  margin-right: 15px;
  flex-shrink: 0;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-sidebar-section:not(.wpforms-panel-sidebar-section-default) .wpforms-panel-sidebar-info {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-sidebar-section:not(.wpforms-panel-sidebar-section-default) .wpforms-panel-sidebar-recommended {
  margin-top: 5px;
  margin-inline-end: auto;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-sidebar-section:not(.wpforms-panel-sidebar-section-default) .wpforms-panel-sidebar-controls {
  margin-inline-start: auto;
  margin-left: auto;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-sidebar-section:not(.wpforms-panel-sidebar-section-default) .wpforms-panel-sidebar-controls .wpforms-toggle-arrow {
  margin-inline-start: 10px;
  margin-left: 10px;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-sidebar-section:not(.wpforms-panel-sidebar-section-default) .wpforms-toggle-arrow {
  margin-left: auto;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-title {
  margin-bottom: 20px;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content h2 {
  margin: 20px 0;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content img {
  border: 1px solid #c3c4c7;
  border-radius: 4px;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content select:disabled {
  cursor: default;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content .wpforms-panel-field-text input:disabled {
  background-color: #ffffff;
  opacity: 0.5;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content .wpforms-alert-dismissible {
  margin-top: 0;
  margin-right: 0;
  margin-left: 0;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content .wpforms-builder-payment-settings-alert-icon {
  width: 140px;
  height: 140px;
  margin: 30px auto 0 auto;
  display: block;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content .wpforms-panel-field-select .wpforms-required-field-error {
  border-color: #d63638;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content .wpforms-panel-field-select .wpforms-required-field-error:focus {
  box-shadow: 0 0 0 1px #d63638;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content .wpforms-builder-payment-settings-default-content {
  display: block;
  margin: 30px auto 0 auto;
  width: 100%;
  text-align: center;
  color: #3c434a;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content .wpforms-builder-payment-settings-default-content p {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 10px;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content .wpforms-builder-payment-settings-default-content p a.secondary-text {
  font-size: 16px;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content .wpforms-builder-payment-settings-default-content .wpforms-builder-payment-settings-learn-more {
  margin-bottom: 20px;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content .wpforms-builder-payment-settings-default-content .wpforms-builder-payment-settings-learn-more a.secondary-text {
  font-size: 14px;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content .wpforms-builder-payment-settings-default-content .wpforms-builder-payment-settings-error-title {
  color: #b32d2e;
  font-size: 16px;
  background-color: #fcf0f1;
  border-radius: 4px;
  width: fit-content;
  margin: 0 auto 15px;
  padding: 7px 15px;
  line-height: 24px;
  text-align: center;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content .wpforms-builder-payment-settings-default-content .wpforms-builder-payment-settings-error-title a {
  color: inherit;
}

#wpforms-builder #wpforms-panel-payments #wpforms-stripe-credit-card-alert,
#wpforms-builder #wpforms-panel-payments #wpforms-square-credit-card-alert {
  margin: 0;
  padding: 0;
  background-color: #ffffff;
  border: none;
}

#wpforms-builder #wpforms-panel-payments .wpforms-stripe-notice-info,
#wpforms-builder #wpforms-panel-payments .wpforms-square-notice-info {
  color: #3c434a;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment {
  position: relative;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment + .wpforms-panel-content-section-payment {
  padding-top: 20px;
  margin: 20px 0;
  border-top: 1px solid #dcdcde;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment-subtitle {
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  text-align: start;
  margin-top: 0 !important;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment-content {
  display: none;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment.wpforms-panel-content-section-payment-open .wpforms-panel-content-section-payment-button {
  display: inline-block;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment-button {
  background: #036aab;
  display: none;
  padding: 10px 20px;
  font-size: 14px;
  line-height: 17px;
  font-weight: 500;
  color: #ffffff;
  text-decoration: none;
  border-radius: 4px;
  position: absolute;
  inset-inline-end: 0;
  top: 20px;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment-button:hover {
  background-color: #215d8f;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment-plan {
  border: 1px solid #dcdcde;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment-plan-head {
  background-color: #f6f7f7;
  padding: 15px 20px;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  text-align: start;
  display: flex;
  justify-content: space-between;
  white-space: nowrap;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment-plan-head-title {
  width: 80%;
  min-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment-plan-head-buttons {
  font-weight: 400;
  color: #999c9e;
  display: flex;
  align-items: center;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment-plan-head-buttons .fa {
  margin-inline-start: 10px;
  cursor: pointer;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment-plan-head-buttons .fa-trash-o {
  margin-inline-start: 10px;
  color: #dc3232;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment-plan-body {
  border-top: 1px solid #dcdcde;
  padding: 20px;
}

#wpforms-builder #wpforms-panel-payments .wpforms-panel-content-section-payment-plan + .wpforms-panel-content-section-payment-plan {
  margin-top: 20px;
}

.wpforms-panels-toggle .wpforms-panel-revisions-button-spacer {
  margin-top: auto;
}

.wpforms-panels-toggle .wpforms-panel-revisions-button {
  position: relative;
  width: 55px;
  height: 44px;
  margin: 20px auto;
  background-color: #282e32;
  border: 1px solid #3c434a;
  border-radius: 4px;
}

.wpforms-panels-toggle .wpforms-panel-revisions-button:hover {
  background-color: #3c434a;
}

.wpforms-panels-toggle .wpforms-panel-revisions-button:hover .fa {
  color: #ffffff;
  transition: color ease-in 0.05s;
}

.wpforms-panels-toggle .wpforms-panel-revisions-button.active {
  background-color: #e27730;
  border-color: #e27730;
}

.wpforms-panels-toggle .wpforms-panel-revisions-button .fa {
  margin-top: -1px;
  font-size: 24px;
  line-height: 24px;
}

.wpforms-panels-toggle .wpforms-panel-revisions-button .badge-exclamation {
  position: absolute;
  top: -5px;
  inset-inline-end: -5px;
  width: 16px;
  height: 16px;
  border-radius: 8px;
  background: #d63638;
  box-shadow: 0 0 0 2px #3c434a;
  display: flex;
  align-items: center;
  justify-content: center;
}

#wpforms-panel-revisions .wpforms-revisions-header {
  margin: 20px;
  border-bottom: 1px solid #ced7e0;
}

#wpforms-panel-revisions .wpforms-revisions-header h3 {
  font-weight: 600;
  font-size: 15px;
  line-height: 18px;
  color: #444444;
  margin: 20px 0 5px 0;
}

#wpforms-panel-revisions .wpforms-revisions-header p {
  font-weight: normal;
  font-size: 12px;
  line-height: 16px;
  color: #86919e;
  margin: 5px 0 20px 0;
}

#wpforms-panel-revisions .wpforms-revisions-notice {
  margin: 20px;
  padding: 10px;
  background-color: #ffffff;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05);
}

#wpforms-panel-revisions .wpforms-revisions-notice h2 {
  margin: 0;
  color: #444444;
  font-size: 14px;
  line-height: 17px;
}

#wpforms-panel-revisions .wpforms-revisions-notice p {
  margin: 5px 0 10px 0;
  color: #777777;
  font-size: 14px;
  line-height: 18px;
}

#wpforms-panel-revisions .wpforms-revisions-notice.wpforms-revisions-notice-error {
  border-inline-start: 4px solid #d63638;
}

#wpforms-panel-revisions .wpforms-revisions-notice.wpforms-revisions-notice-warning {
  border-inline-start: 4px solid #ffb900;
}

#wpforms-panel-revisions .wpforms-revisions-content {
  margin: 20px;
}

#wpforms-panel-revisions .wpforms-revision-current-version a,
#wpforms-panel-revisions .wpforms-revision a {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #e0e8f0;
  border: 2px solid #e0e8f0;
  border-radius: 4px;
  text-decoration: none;
}

#wpforms-panel-revisions .wpforms-revision-current-version a:hover,
#wpforms-panel-revisions .wpforms-revision a:hover {
  background-color: #ced7e0;
  border-color: #ced7e0;
}

#wpforms-panel-revisions .wpforms-revision-current-version.active a,
#wpforms-panel-revisions .wpforms-revision.active a {
  background-color: #ffffff;
  border: 2px solid #e27730;
}

#wpforms-panel-revisions .wpforms-revision {
  margin: 0 0 0 10px;
  padding-top: 20px;
  padding-inline-end: 0;
  padding-inline-start: 10px;
  border-inline-start: 2px solid #e0e8f0;
  position: relative;
}

#wpforms-panel-revisions .wpforms-revision.active a:before {
  content: "";
  position: absolute;
  top: calc( 50% + 5px);
  inset-inline-start: -6px;
  display: block;
  width: 10px;
  height: 10px;
  border: 2px solid #e27730;
  border-radius: 5px;
  background-color: #ffffff;
}

#wpforms-panel-revisions .wpforms-revision.active a:after {
  content: "";
  position: absolute;
  top: calc( 50% + 9px);
  inset-inline-start: 4px;
  display: block;
  width: 6px;
  height: 2px;
  background-color: #e27730;
}

#wpforms-panel-revisions .wpforms-revision-gravatar {
  width: 40px;
  height: 40px;
  margin-inline-end: 15px;
  border-radius: 20px;
  overflow: hidden;
}

#wpforms-panel-revisions .wpforms-revision-details p {
  margin: 3px 0;
  line-height: 16px;
}

#wpforms-panel-revisions .wpforms-revision-created {
  font-size: 12px;
  color: #6b6d6f;
}

#wpforms-panel-revisions .wpforms-revision-created strong {
  font-weight: 600;
  font-size: 14px;
  color: #444444;
}

#wpforms-panel-revisions .wpforms-revision-author {
  font-size: 13px;
  color: #86919e;
}

#wpforms-panel-revisions .wpforms-preview * {
  pointer-events: none;
}

.wpforms-is-revision .wpforms-revision-notice {
  position: fixed;
  z-index: 10;
  top: calc( 76px + var( --wpforms-admin-bar-height ));
  inset-inline-start: 95px;
  inset-inline-end: 0;
  height: 40px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: #fdf6e7;
  box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.2);
  text-align: center;
  font-size: 14px;
  line-height: 16px;
  color: #444444;
  transition-property: top;
  transition-duration: 0.25s;
  transition-timing-function: ease-out;
}

.wpforms-is-revision .wpforms-revision-notice p {
  margin: 0;
}

.wpforms-is-revision .wpforms-revision-notice a {
  color: #444444;
}

.wpforms-is-revision .wpforms-revision-notice a:hover {
  color: #e27730;
}

.wpforms-is-revision .wpforms-revision-notice .fa {
  margin: 0 10px;
  font-size: 16px;
  opacity: .35;
}

.wpforms-is-revision .wpforms-panel-sidebar-content .wpforms-panel-sidebar,
.wpforms-is-revision .wpforms-panel-sidebar-content .wpforms-panel-content-wrap,
.wpforms-is-revision .wpforms-panel-full-content .wpforms-panel-content-wrap,
.wpforms-is-revision #wpforms-panel-fields .wpforms-tabs {
  top: calc( 76px + var( --wpforms-admin-bar-height ) + 40px);
}

.wpforms-is-revision #wpforms-panel-fields .wpforms-panel-sidebar,
.wpforms-is-revision .wpforms-field-option-group-toggle {
  top: calc( 124px + var( --wpforms-admin-bar-height ) + 40px);
}

.wpforms-is-revision #wpforms-field-options:before {
  top: calc( 125px + var( --wpforms-admin-bar-height ) + 40px);
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview p {
  margin: 0;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview p.wpforms-panel-content-section-themes-preview-description {
  margin-bottom: 20px;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview p.wpforms-panel-content-section-themes-preview-description a {
  white-space: nowrap;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview .wpforms-builder-themes-preview-notice {
  margin-bottom: 30px;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview .wpforms-builder-themes-style-notice {
  position: relative;
  padding-inline-end: 170px;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview .wpforms-builder-themes-style-notice a {
  position: absolute;
  inset-inline-end: 10px;
  bottom: calc(50% - 16px);
  padding: 9px 10px;
  color: #777;
  text-decoration: none;
  display: inline-block;
  background: #fff;
  border: 1px solid rgba(68, 68, 68, 0.35);
  font-size: 13px;
  border-radius: 4px;
  line-height: 1;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview .wpforms-builder-themes-style-notice a:hover {
  color: #86919e;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview {
  margin-top: 0;
  position: relative;
  max-width: 800px;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview * {
  cursor: default;
  user-select: none;
  pointer-events: none;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview .wpforms-container-full {
  margin-top: 0;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview .wpforms-container-full input[type=checkbox], #wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview .wpforms-container-full input[type=radio] {
  margin-inline-end: 0;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview #builder-themes-preview-default-name-container {
  padding-top: 0;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview .wpforms-field-medium:not(textarea) {
  max-width: 396px;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview textarea.wpforms-field-medium {
  height: 151px;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview .wpforms-field-row .wpforms-field-row-block.wpforms-one-half.wpforms-first {
  padding-inline-end: 7px;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview .wpforms-field-row .wpforms-field-row-block.wpforms-one-half:not(.wpforms-first) {
  padding-inline-start: 7px;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview .wpforms-one-half {
  width: 50%;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview .wpforms-submit-container {
  margin-top: 18px;
}

#wpforms-panel-settings .wpforms-panel-content-section-themes-preview #wpforms-builder-themes-preview .wpforms-submit-container button {
  pointer-events: none;
}

#wpforms-panel-settings #wpforms-page-forms-fbst-notice {
  position: relative;
  padding-inline-end: 170px;
}

#wpforms-panel-settings #wpforms-page-forms-fbst-notice a {
  position: absolute;
  inset-inline-end: 10px;
  bottom: calc(50% - 16px);
  padding: 9px 10px;
  color: #777;
  text-decoration: none;
  display: inline-block;
  background: #fff;
  border: 1px solid rgba(68, 68, 68, 0.35);
  font-size: 13px;
  border-radius: 4px;
  line-height: 1;
}

#wpforms-panel-settings #wpforms-page-forms-fbst-notice a:hover {
  color: #86919e;
}

#wpforms-builder-themes-sidebar {
  position: fixed;
  top: calc(76px + var(--wpforms-admin-bar-height));
  bottom: 0;
  inset-inline-start: 95px;
  width: 400px;
  background: #EBF3FC;
  overflow: hidden;
  padding-bottom: 50px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-head {
  height: 46px;
  background-color: #e0e8f0;
  border-bottom: 1px solid #ced7e0;
}

#wpforms-builder-themes-sidebar #wpforms-builder-themes-back {
  background-color: transparent;
  border: none;
  color: #3c434a;
  width: auto;
  text-align: left;
  padding-top: 15px;
  padding-bottom: 15px;
  padding-inline: 43px 15px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  position: relative;
}

#wpforms-builder-themes-sidebar #wpforms-builder-themes-back:hover {
  text-decoration: underline;
}

#wpforms-builder-themes-sidebar #wpforms-builder-themes-back:before {
  content: '';
  position: absolute;
  inset-inline-start: 20px;
  inset-inline-end: auto;
  background-image: url("../../images/builder/back.svg");
  background-size: 13px 11px;
  width: 13px;
  height: 11px;
  top: 50%;
  transform: translateY(-50%);
}

#wpforms-builder-themes-sidebar #wpforms-builder-themes-sidebar-tabs {
  margin: 0 20px;
  border-bottom: 1px solid #ced7e0;
  display: flex;
  align-items: center;
}

#wpforms-builder-themes-sidebar #wpforms-builder-themes-sidebar-tabs a {
  display: block;
  position: relative;
  color: #444444;
  text-decoration: none;
  font-size: 15px;
  padding: 12px;
}

#wpforms-builder-themes-sidebar #wpforms-builder-themes-sidebar-tabs a:hover {
  color: #86919e;
}

#wpforms-builder-themes-sidebar #wpforms-builder-themes-sidebar-tabs a.active {
  font-weight: 500;
  color: #444444;
}

#wpforms-builder-themes-sidebar #wpforms-builder-themes-sidebar-tabs a.active:after {
  content: '';
  position: absolute;
  inset-inline-start: 0;
  inset-inline-end: 0;
  bottom: -1px;
  height: 3px;
  background-color: #e27730;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content {
  padding-inline-end: 6px;
  overflow-y: auto;
  scroll-behavior: smooth;
  scrollbar-gutter: stable;
  height: calc( 100% - 40px);
  padding: 20px 20px 0;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content.wpforms-is-mac {
  padding-inline-end: 20px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-fields-row {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  align-items: center;
  margin-bottom: 20px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-fields-row > .wpforms-panel-field {
  width: 100%;
  margin-bottom: 0;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-fields-row.wpforms-hidden {
  display: none;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-hidden {
  display: none;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-add-fields-group:last-child {
  border-bottom: none;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-number-input-wrapper {
  position: relative;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-number-input-wrapper:after {
  content: 'px';
  position: absolute;
  inset-inline-end: 10px;
  bottom: 10px;
  width: 12px;
  height: 12px;
  color: #b0b6bd;
  font-size: 12px;
  line-height: 12px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-number-input-wrapper .wpforms-builder-themes-number-input {
  width: 100%;
  padding-inline-end: 26px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-number-input-wrapper .wpforms-builder-themes-number-input::-webkit-outer-spin-button, #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-number-input-wrapper .wpforms-builder-themes-number-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-number-input-wrapper .wpforms-builder-themes-number-input {
  -moz-appearance: textfield;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content #wpforms-panel-field-themes-fieldTextColor-wrap .minicolors-position-left .minicolors-panel,
#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content #wpforms-panel-field-themes-labelErrorColor-wrap .minicolors-position-left .minicolors-panel,
#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content #wpforms-panel-field-themes-buttonTextColor-wrap .minicolors-position-left .minicolors-panel {
  left: -60px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-panel-field-colorpicker .minicolors {
  width: 100%;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-panel-field-colorpicker .minicolors .minicolors-input {
  width: 100%;
  padding-inline-start: 30px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-panel-field-colorpicker .minicolors .minicolors-swatch {
  width: 20px;
  height: 20px;
  top: 6px;
  left: 6px;
  border-radius: 50%;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-panel-field-colorpicker .minicolors .minicolors-swatch:after {
  border-radius: 50%;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-panel-field-colorpicker .minicolors .minicolors-swatch:hover {
  cursor: pointer;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-panel-field-colorpicker .minicolors .minicolors-swatch .minicolors-swatch-color {
  border-radius: 50%;
  box-shadow: none;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content input.minicolors-input.wpforms-builder-themes-disabled + .minicolors-swatch {
  opacity: 0.5;
  pointer-events: none;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-bg-image-preview {
  width: 100%;
  height: 240px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  box-shadow: inset 0 0 1px 1px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  margin-bottom: 20px;
  cursor: pointer;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content button.wpforms-builder-themes-bg-image-choose, #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content button.wpforms-builder-themes-bg-image-remove {
  width: auto;
  padding-inline-start: 12px;
  padding-inline-end: 12px;
  font-size: 13px;
  font-weight: 500;
  height: 32px;
  padding: 0 12px;
  line-height: 32px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content button.wpforms-builder-themes-bg-image-remove {
  border: 1px solid #d63638;
  background-color: #fcf0f1;
  color: #d63638;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content button.wpforms-builder-themes-bg-image-remove:hover, #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content button.wpforms-builder-themes-bg-image-remove:focus {
  border-color: #b32d2e;
  color: #b32d2e;
  background-color: #f9e1e1;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content button.wpforms-builder-themes-bg-image-remove:focus {
  outline: 1px solid #b32d2e;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-add-fields-group .wpforms-add-fields-buttons {
  display: block;
  margin-bottom: 20px;
  overflow: visible;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-add-fields-group .wpforms-add-fields-buttons .wpforms-builder-themes-fields-row:last-child {
  margin-bottom: 0;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control {
  margin-bottom: 20px;
  min-height: 190px;
  background-color: #ffffff;
  border-radius: 4px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group {
  width: 100%;
  max-height: 190px;
  overflow-y: auto;
  border: 1px solid #b0b6bd;
  border-radius: 4px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 0;
  padding: 12px;
  height: 42px;
  font-size: 13px;
  gap: 12px;
  border-top: none;
  border-right: none;
  border-bottom: 1px solid #dddddd;
  border-left: none;
  background-color: #ffffff;
  color: #1e1e1e;
  border-radius: 0;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button:last-child {
  border-bottom: none;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button:hover {
  box-shadow: inset 0 0 0 8px #ffffff;
  cursor: pointer;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button:hover div {
  color: #036aab;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button .wpforms-builder-themes-indicators {
  min-width: 80px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button .component-color-indicator {
  min-width: 20px;
  height: 20px;
  width: 20px;
  margin-inline-end: -9px;
  position: relative;
  border-radius: 50%;
  box-shadow: inset 0 0 0 1px #0003;
  display: inline-block;
  padding: 0;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button .component-color-indicator[data-index="0"] {
  z-index: 5;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button .component-color-indicator[data-index="1"] {
  z-index: 4;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button .component-color-indicator[data-index="2"] {
  z-index: 3;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button .component-color-indicator[data-index="3"] {
  z-index: 2;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button .component-color-indicator[data-index="4"] {
  z-index: 1;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button .component-color-indicator:last-child {
  margin-inline-end: 0;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button.is-active {
  box-shadow: inset 0 0 0 3px #ffffff;
  background-color: #ebedef;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button .wpforms-builder-themes-radio {
  flex-grow: 1;
  text-align: left;
  color: #1e1e1e;
  overflow: hidden;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button .wpforms-builder-themes-radio-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button .wpforms-builder-themes-radio-disabled {
  position: relative;
  padding-right: 40px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control .wpforms-builder-themes-radio-group button .wpforms-builder-themes-radio-disabled::after {
  content: 'pro';
  position: absolute;
  text-transform: uppercase;
  background-color: #ededed;
  padding: 6px 8px;
  border-radius: 3px;
  font-size: 8px;
  line-height: 10px;
  font-weight: 700;
  color: #999999;
  right: 0;
  top: -3px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control-theme-name {
  margin-top: 24px;
  margin-bottom: 8px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control-delete {
  color: #d63638;
  text-decoration: underline;
  line-height: 18px;
  padding: 0;
  box-shadow: none;
  height: auto;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-control-delete:hover {
  box-shadow: none;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content #wpforms-panel-field-themes-themeName-wrap {
  margin-bottom: 5px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content #wpforms-builder-themer-remove-theme {
  width: auto;
  background-color: transparent;
  padding: 5px 0 0 0;
  color: #d63638;
  font-size: 12px;
  margin-top: 0;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content #wpforms-builder-themer-remove-theme:hover {
  color: #b32d2e;
  text-decoration: underline;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-panel-field-after {
  display: block;
  color: #86919E;
  margin-left: 0;
  margin-top: 5px;
  line-height: 18px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-disabled, #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-disabled-pro {
  opacity: .5;
  pointer-events: none;
  position: relative;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-disabled input.wpforms-builder-themes-disabled, #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-disabled select.wpforms-builder-themes-disabled, #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-disabled-pro input.wpforms-builder-themes-disabled, #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-disabled-pro select.wpforms-builder-themes-disabled {
  background-color: #ffffff;
  opacity: 1;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-disabled:before, #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-disabled-pro:before {
  content: '';
  position: absolute;
  inset: 0;
  pointer-events: auto;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content a.wpforms-builder-themes-pro-blocked {
  position: relative;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content a.wpforms-builder-themes-pro-blocked::after {
  content: 'pro';
  position: absolute;
  text-transform: uppercase;
  background-color: #dbe4ee;
  padding: 6px 8px;
  border-radius: 3px;
  font-size: 8px;
  line-height: 10px;
  font-weight: 700;
  color: #9BA4AF;
  right: 27px;
  top: 19px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-restricted {
  padding-top: 20px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-builder-themes-restricted .wpforms-add-fields-group:nth-last-child(2) {
  border-bottom: none;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content input[type=text],
#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content input[type=number],
#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content textarea,
#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content select {
  border-color: #b0b6bd;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content input[type=text]:disabled,
#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content input[type=number]:disabled,
#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content textarea:disabled,
#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content select:disabled {
  background-color: #ffffff;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group::-webkit-scrollbar {
  background: transparent;
  width: 5px;
  height: 5px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group::-webkit-scrollbar-track {
  background: transparent;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 5px 5px rgba(0, 0, 0, 0.1);
  background: transparent;
  border-radius: 5px;
  border: solid -1px transparent;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group::-webkit-resizer, #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group::-webkit-scrollbar-button, #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group::-webkit-scrollbar-corner {
  display: none;
}

@-moz-document url-prefix() {
  #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group {
    scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
    scrollbar-gutter: initial !important;
    scrollbar-width: thin;
  }
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group:hover::-webkit-scrollbar {
  background: transparent;
  width: 5px;
  height: 5px;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group:hover::-webkit-scrollbar-track {
  background: transparent;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group:hover::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 5px 5px rgba(0, 0, 0, 0.4);
  background: transparent;
  border-radius: 5px;
  border: solid -1px transparent;
}

#wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group:hover::-webkit-resizer, #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group:hover::-webkit-scrollbar-button, #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group:hover::-webkit-scrollbar-corner {
  display: none;
}

@-moz-document url-prefix() {
  #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content:not(.wpforms-is-mac) .wpforms-builder-themes-radio-group:hover {
    scrollbar-color: rgba(0, 0, 0, 0.4) transparent;
    scrollbar-gutter: initial !important;
    scrollbar-width: thin;
  }
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal .wpforms-exclamation-circle {
  display: block;
  width: 48px;
  height: 48px;
  mask-image: url("../../images/exclamation-circle.svg");
  mask-size: 48px 48px;
  margin: 0 auto;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal.jconfirm-type-orange .wpforms-exclamation-circle {
  background-color: #e27730;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal.jconfirm-type-red .wpforms-exclamation-circle {
  background-color: #d63638;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal button[disabled] {
  opacity: .75 !important;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal .wpforms-theme-delete-text {
  margin: 0 auto 10px auto !important;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal.jconfirm-box.jconfirm-type-picture-selector {
  height: 540px;
  border-top-width: 0;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal.jconfirm-box.jconfirm-type-picture-selector .jconfirm-title {
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 22px;
  color: #3c434a;
  margin: 0 0 10px 0 !important;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal.jconfirm-box.jconfirm-type-picture-selector .jconfirm-title p {
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  margin: 15px 0 0 0;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal.jconfirm-box.jconfirm-type-picture-selector .jconfirm-closeIcon {
  width: 12px;
  height: 12px;
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-image: url("../../images/cross-inverse.svg");
  opacity: .3;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal.jconfirm-box.jconfirm-type-picture-selector .jconfirm-closeIcon:after {
  display: none;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal.jconfirm-box.jconfirm-type-picture-selector .jconfirm-closeIcon:hover {
  opacity: .5;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal.jconfirm-box.jconfirm-type-picture-selector .wpforms-builder-stock-photos-pictures-wrap {
  display: grid;
  grid-template-columns: repeat(5, 124px);
  grid-row-gap: 20px;
  grid-column-gap: 20px;
  justify-content: center;
  background-color: #f6f7f7;
  border-radius: 4px;
  padding: 20px;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal.jconfirm-box.jconfirm-type-picture-selector .wpforms-builder-stock-photos-picture {
  width: 124px;
  height: 124px;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  border: none;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.15);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: box-shadow 0.15s ease-in-out;
}

.jconfirm-modern .jconfirm-box-container .wpforms-builder-themes-modal.jconfirm-box.jconfirm-type-picture-selector .wpforms-builder-stock-photos-picture:hover {
  box-shadow: inset 0 0 0 1px #282e32, 0 0 0 1px #282e32, 0 2px 4px rgba(0, 0, 0, 0.15);
}

.rtl #wpforms-panel-field-themes-fieldBackgroundColor-wrap .minicolors-position-left .minicolors-panel,
.rtl #wpforms-panel-field-themes-labelColor-wrap .minicolors-position-left .minicolors-panel,
.rtl #wpforms-panel-field-themes-buttonBackgroundColor-wrap .minicolors-position-left .minicolors-panel {
  left: -60px;
}

.rtl #wpforms-panel-field-themes-buttonTextColor-wrap .minicolors-position-left .minicolors-panel,
.rtl #wpforms-panel-field-themes-labelErrorColor-wrap .minicolors-position-left .minicolors-panel,
.rtl #wpforms-panel-field-themes-buttonTextColor-wrap .minicolors-position-left .minicolors-panel,
.rtl #wpforms-panel-field-themes-fieldTextColor-wrap .minicolors-position-left .minicolors-panel {
  left: 0 !important;
}

.rtl #wpforms-builder-themes-sidebar .wpforms-builder-themes-sidebar-content .wpforms-panel-field-colorpicker .minicolors .minicolors-input {
  padding-inline-start: 10px;
}
