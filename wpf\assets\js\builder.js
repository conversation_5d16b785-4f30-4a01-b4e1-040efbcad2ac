/* global WPForms, wpfBuilder */

/**
 * @param wpfBuilder.ajaxUrl
 * @param wpfBuilder.bulkEmbeds
 * @param wpfBuilder.hideContextMenu
 */

/**
 * Hooks Docs.
 *
 * @since 0.23
 */

const WPFBuilder = window.WPFBuilder || ( function( $ ) {
	/**
	 * Public functions and properties.
	 *
	 * @since 0.23
	 *
	 * @type {Object}
	 */
	const app = {

		/**
		 * Start the engine.
		 *
		 * @since 0.23
		 */
		init() {
			$( app.ready );
		},

		/**
		 * DOM is fully loaded.
		 *
		 * @since 1.8.6
		 */
		ready() {
			app.events();

			app.addBulkEmbedButton();
		},

		/**
		 * Bind events.
		 *
		 * @since 0.23
		 */
		events() {
			if ( wpfBuilder.hideContextMenu ) {
				$( document ).off( 'contextmenu', WPForms.Admin.Builder.ContextMenu.rightClickContextMenuHandler );
				$( document ).off( 'click', WPForms.Admin.Builder.ContextMenu.hideMenuOnClick );
			}

			$( document ).on( 'click', '.wpf-bulk-embed', app.bulkEmbed );
		},

		/**
		 * Add bulk embed button into the context menu in the Builder.
		 *
		 * @since {VERSION}
		 */
		addBulkEmbedButton() {
			const after = $( '.wpforms-context-menu-list li[data-action="save-as-template"]' ),
				button = `<li class="wpforms-context-menu-list-item wpf-bulk-embed"
				data-action="wpf-bulk-embed"
				data-action-url=""
				>
					<span class="wpforms-context-menu-list-item-icon">
						<i class="fa fa-code"></i>
					</span>

					<span class="wpforms-context-menu-list-item-text">
						Bulk Embed
					</span>
				</li>`;

			after.after( button );
		},

		/**
		 * Bulk embed event.
		 *
		 * @since {VERSION}
		 *
		 * @param {Object} e Event object.
		 */
		bulkEmbed( e ) {
			e.preventDefault();

			// Display the modal using jconfirm.
			$.confirm( {
				title: 'Embedding Form Into New Pages',
				content: 'Loading...',
				boxWidth: '650px',
				type: 'blue',
				buttons: {
					cancel: {
						text: 'Close',
						btnClass: 'btn-cancel',
					},
				},
				onContentReady() {
					const $modal = $( this.$content );
					const $table = $( '<table class="wpf-bulk-embed-table"></table>' );

					$modal.html( '' );
					$modal.append( $table );

					$.each( wpfBuilder.bulkEmbeds, ( id, text ) => {
						const $tr = $( `<tr data-id="${ id }" class="wpf-bulk-embed-list"></tr>` );
						$tr.append( `<td>${ text }:</td><td> <span class="status"><span class="spinner"></span></span></td><td class="edit"></td>` );
						$modal.find( '.wpf-bulk-embed-table' ).append( $tr );

						$.post( wpfBuilder.ajaxUrl, {
							action: 'wpf_builder_bulk_embed',
							nonce: wpfBuilder.nonce,
							embed: id,
							form_id: $( '#wpforms-builder-form' ).data( 'id' ), // eslint-disable-line camelcase
						} ).done( ( response ) => {
							/**
							 * @param response.data.edit_url
							 * @param response.data.page_url
							 */
							if ( response.success ) {
								const link = $( `<a href="${ response.data.page_url }" class="wpf-bulk-embed-page-link" target="_blank"><i class="fa fa-file-text-o"></i> ${ response.data.page_title }</a>` );
								const $trId = $( `tr[data-id="${ id }"]` );
								const editLink = $( `<a href="${ response.data.edit_url }" class="wpf-bulk-embed-page-link" target="_blank"><i class="fa fa-pencil"></i> Edit</a>` );

								$trId.find( '.status' ).html( link );
								$trId.find( '.edit' ).html( editLink );
							}
						} );
					} );
				},
			} );
		},
	};

	return app;
}( jQuery ) );

// Initialize.
WPFBuilder.init();
