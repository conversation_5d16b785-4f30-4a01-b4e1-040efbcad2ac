/* Base */
.body-inner {
	padding-top: 80px;
	padding-bottom: 80px;
}

.wrapper {
	max-width: 700px;
}

.wrapper-inner {
	background-color: $backgroundContent;
	padding: 30px 50px 30px 50px;
}

.header {
	text-align: center;
	padding-bottom: 30px;
	border-bottom: 1px solid lighten($fontColor, 65%);

	.header-image {
		/* This is needed to center the logo in Outlook. */
		margin: 0 auto 0 auto;
	}
}

.footer {
	font-size: 14px;
	padding-top: 30px;
	line-height: 24px;
	border-top: 1px solid lighten($fontColor, 65%);
}

/* Typography */
body,
table.body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
td:not(.header),
th,
a {
	line-height: 24px;
}

body,
table.body,
p,
td,
th {
	color: rgba($fontColor, 80%);
}

/* Content */
.content {
	padding-top: 35px;
	padding-bottom: 25px;

	tr:first-child > & {
		padding-top: 15px;
	}

	.field-name {
		color: $fontColor;
		padding-top: 15px;
		padding-bottom: $marginBottom;

		&:not(.field-value) {
			font-size: 18px;
			line-height: 19.8px;
		}

		/* Repeater & Layout */
		&.field-repeater-name,
		&.field-layout-name {
			font-size: 22px;
			padding-top: 25px;
			padding-bottom: 25px;
		}
	}

	.field-value {
		padding-bottom: 25px;
	}

	.field-name.field-value {
		line-height: 24px;
	}
}

// Order summary table.
.field-payment-total .wpforms-order-summary-container table.wpforms-order-summary-preview {
	border-radius: 0;
}
