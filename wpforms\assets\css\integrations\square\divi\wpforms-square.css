.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form .sq-card-iframe-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form .sq-card-iframe-container::before {
  border-width: 1px;
  border-radius: 2px;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form .sq-card-wrapper {
  min-width: auto;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form .sq-card-wrapper.sq-focus .sq-card-iframe-container::before {
  border-color: #999;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form .sq-card-wrapper.sq-error .sq-card-iframe-container::before {
  border-color: #cc0000;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form .sq-card-component {
  position: relative;
  height: inherit;
  width: 100%;
  border: 1px solid #ccc;
  background-color: #fff;
  border-radius: 2px;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form .wpforms-field-square-consent {
  border-left: 3px solid;
  padding-left: 15px;
  font-size: 0.75em;
  color: #666;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form .wpforms-field-square .wpforms-field-row.wpforms-field-small .wpforms-field-square-cardname {
  width: 100%;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full .wpforms-form .wpforms-field-layout .wpforms-layout-column .wpforms-field-row.wpforms-field-small .wpforms-field-square-cardname {
  min-width: 100%;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper .sq-card-iframe-container {
  border-radius: var(--wpforms-field-border-radius);
  border: var(--wpforms-field-border-size, 1px) var(--wpforms-field-border-style, solid) var(--wpforms-field-border-color);
  border-color: var(--wpforms-field-border-color);
  background-color: var(--wpforms-field-background-color);
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper .sq-card-iframe-container::before {
  border-color: transparent;
  border-radius: var(--wpforms-field-border-radius);
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper .sq-card-iframe-container iframe {
  border: none !important;
  border-radius: var(--wpforms-field-border-radius);
  background: transparent;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper.sq-focus .sq-card-iframe-container {
  border-width: var(--wpforms-field-border-size, 0);
  border-style: solid;
  border-color: var(--wpforms-button-background-color);
  box-shadow: 0 0 0 1px var(--wpforms-button-background-color), 0px 1px 2px rgba(0, 0, 0, 0.15);
  outline: none;
  border-color: var(--wpforms-button-background-color) !important;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper.sq-focus .sq-card-iframe-container::before {
  border: none;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper.sq-error .sq-card-iframe-container {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  border-color: var(--wpforms-label-error-color) !important;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper.sq-error:hover .sq-card-iframe-container {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  box-shadow: 0 0 2px 0 var(--wpforms-label-error-color);
  border-color: var(--wpforms-label-error-color) !important;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper.sq-error.sq-focus .sq-card-iframe-container {
  border-width: var(--wpforms-field-border-size);
  border-style: solid;
  border-color: var(--wpforms-label-error-color);
  box-shadow: 0 0 0 1px var(--wpforms-label-error-color);
  border-color: var(--wpforms-label-error-color) !important;
}

.et-db #et-boc .et-l .et_pb_module div.wpforms-container-full.wpforms-render-modern .wpforms-form .wpforms-field-square .wpforms-field-sublabel + .wpforms-error {
  margin-top: calc( 1.5 * var( --wpforms-field-size-sublabel-spacing ));
}

.et-db #et-boc .et-l .et_pb_module .wpforms-field-square .sq-card-message {
  display: none;
}

.et-db #et-boc .et-l .et_pb_module #wpforms-conversational-form-page .sq-card-iframe-container,
.et-db #et-boc .et-l .et_pb_module #wpforms-conversational-form-page .sq-card-component {
  background-color: transparent;
  border: none;
}

.et-db #et-boc .et-l .et_pb_module #sq-nudata-modal {
  display: block !important;
  position: fixed !important;
  left: 0 !important;
  top: 0 !important;
}

@media only screen and (max-width: 600px) {
  .et-db #et-boc .et-l .et_pb_module div.wpforms-container-full.wpforms-render-modern .wpforms-form .sq-card-wrapper {
    max-width: calc( 100% - 2px);
  }
}
