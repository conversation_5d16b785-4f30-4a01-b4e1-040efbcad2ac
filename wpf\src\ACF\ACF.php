<?php

namespace WPForms\DevTools\ACF;

/**
 * General miscellaneous adjustments for Advanced Custom Fields functionality.
 *
 * @since 0.39
 */
class ACF {

	/**
	 * Core constructor.
	 *
	 * @since 0.39
	 */
	public function __construct() {

		$this->hooks();
	}

	/**
	 * All the actions and filters are registered here.
	 *
	 * @since 0.39
	 */
	public function hooks(): void {

		add_filter( 'acf/settings/save_json', [ $this, 'get_json_path' ] );
		add_filter( 'acf/settings/load_json', [ $this, 'add_json_path' ] );
	}

	/**
	 * Define where the local JSON fields are saved.
	 *
	 * @since 0.39
	 *
	 * @return string
	 */
	public function get_json_path(): string {

		return wp_normalize_path( WPFORMS_DEV_TOOLS_DIR . '/src/ACF' );
	}

	/**
	 * Add our path for the local JSON.
	 *
	 * @since 0.39
	 *
	 * @param array|mixed $paths JSON field paths.
	 *
	 * @return array
	 */
	public function add_json_path( $paths ): array {

		$paths = (array) $paths;

		unset( $paths[0] );

		$paths[] = wp_normalize_path( WPFORMS_DEV_TOOLS_DIR . 'src/ACF' );

		return $paths;
	}
}
